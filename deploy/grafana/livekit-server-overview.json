{"__inputs": [], "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "8.2.2"}, {"type": "panel", "id": "timeseries", "name": "Time series", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "gnetId": null, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "sum(livekit_room_total)", "interval": "", "legendFormat": "Rooms", "refId": "A"}], "thresholds": [], "title": "Rooms", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "sum(livekit_participant_total)", "interval": "", "legendFormat": "Participants", "refId": "A"}], "title": "Participants", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 9, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "sum(irate(livekit_node_messages{type=\"signal\"}[5m]))", "interval": "", "legendFormat": "Signal", "refId": "Signal"}, {"exemplar": true, "expr": "sum(irate(livekit_node_messages{type=\"rtc\"}[5m]))", "hide": false, "interval": "", "legendFormat": "RTC", "refId": "RTC"}, {"exemplar": true, "expr": "sum(irate(livekit_node_messages{status=\"failure\"}[5m]))", "hide": false, "interval": "", "legendFormat": "Failure", "refId": "Failure"}, {"exemplar": true, "expr": "sum(irate(livekit_messagebus_messages{type=\"out\"}[5m]))", "hide": false, "interval": "", "legendFormat": "Out", "refId": "Out"}, {"exemplar": true, "expr": "sum(irate(livekit_messagebus_messages{type=\"out\", status=\"failure\"}[5m]))", "hide": false, "interval": "", "legendFormat": "Out Failure", "refId": "Out Failure"}], "thresholds": [{"colorMode": "critical", "op": "gt", "value": 0, "visible": true}], "title": "Message Rate", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 11, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "sum(livekit_track_published_total)", "interval": "", "legendFormat": "Tracks", "refId": "A"}], "title": "Tracks Published", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 13, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "sum(irate(livekit_packet_total[5m]))", "interval": "", "legendFormat": "Packets", "refId": "Packets"}, {"exemplar": true, "expr": "sum(irate(livekit_nack_total[5m]))", "hide": false, "interval": "", "legendFormat": "NACK", "refId": "NACK"}, {"exemplar": true, "expr": "sum(irate(livekit_pli_total[5m]))", "hide": false, "interval": "", "legendFormat": "PLI", "refId": "PLI"}, {"exemplar": true, "expr": "sum(irate(livekit_fir_total[5m]))", "hide": false, "interval": "", "legendFormat": "FIR", "refId": "FIR"}], "title": "Network Rate", "type": "timeseries"}], "refresh": "5m", "schemaVersion": 31, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "LiveKit Server Overview", "uid": "z_GO3t5nz", "version": 2}