package livekit

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/livekit/livekit-server/pkg/rtc/types"
	"github.com/livekit/protocol/livekit"
	"github.com/livekit/protocol/rpc"
	"github.com/livekit/protocol/utils"
	"github.com/livekit/protocol/utils/guid"
	"github.com/livekit/psrpc"
	"github.com/pion/webrtc/v4"
	"github.com/redis/go-redis/v9"
	"golang.org/x/exp/maps"
	"google.golang.org/protobuf/encoding/protojson"
	"log"
	"os"
	"testing"
	"time"
)

func getRedis() redis.UniversalClient {
	rcOptions := &redis.UniversalOptions{
		Addrs:    []string{"*************:6379"},
		Password: "123456",
		DB:       0,
	}
	return redis.NewUniversalClient(rcOptions)
}

func getMessageBus(rc redis.UniversalClient) psrpc.MessageBus {
	return psrpc.NewRedisMessageBus(rc)
}

// ---------------------------------------------------------

func TestTemp(t *testing.T) {
	rp := webrtc.RTPParameters{}
	rp.HeaderExtensions = append(rp.HeaderExtensions, webrtc.RTPHeaderExtensionParameter{
		URI: "https://aomediacodec.github.io/av1-rtp-spec/#dependency-descriptor-rtp-header-extension",
		ID:  1,
	})

	rpBytes, _ := json.Marshal(rp)

	rp2 := webrtc.RTPParameters{}
	_ = json.Unmarshal(rpBytes, &rp2)

	fmt.Println(rp2)
}

func TestCase1(t *testing.T) {
	remoteParticipants := make(map[livekit.ParticipantIdentity]types.Participant)
	values := maps.Values(remoteParticipants)
	fmt.Println(values)
}

func TestMultiRpc(t *testing.T) {
	rc := getRedis()
	logger := log.New(os.Stdout, "[INFO] ", log.Ldate|log.Ltime|log.Lmicroseconds)

	s1, _ := rpc.NewTypedBroadcastSignalServer(&broadcastSignalServerImpl{}, getMessageBus(rc))
	s1.RegisterAllRoomTopics("测试房间")
	logger.Printf("启动服务器1")

	s2, _ := rpc.NewTypedBroadcastSignalServer(&broadcastSignalServerImpl{}, getMessageBus(rc))
	s2.RegisterAllRoomTopics("测试房间")
	logger.Printf("启动服务器2")

	s3, _ := rpc.NewTypedBroadcastSignalServer(&broadcastSignalServerImpl2{}, getMessageBus(rc))
	s3.RegisterAllRoomTopics("测试房间")
	logger.Printf("启动服务器3")

	c, _ := rpc.NewTypedBroadcastSignalClient(rpc.ClientParams{
		Bus:         getMessageBus(rc),
		PSRPCConfig: rpc.PSRPCConfig{MaxAttempts: 0, Timeout: 500 * time.Millisecond, Backoff: 500 * time.Millisecond},
	})
	ch, _ := c.GetAllParticipant(context.Background(), "测试房间", &rpc.GetAllParticipantRequest{})
	logger.Printf("请求获取所有参与者")

	for resp := range ch {
		if resp.Err != nil {
			logger.Printf("获取所有参与者错误 %s\n", resp.Err)
		}

		json := ""
		for _, p := range resp.Result.Participants {
			jsonBytes, _ := protojson.Marshal(p)
			json += string(jsonBytes)
		}
		logger.Printf("获取所有参与者 %s\n", json)
	}

	logger.Printf("结束")
}

type broadcastSignalServerImpl struct {
}

func (b *broadcastSignalServerImpl) GetAllParticipant(ctx context.Context, request *rpc.GetAllParticipantRequest) (*rpc.GetAllParticipantResponse, error) {
	nodeID1 := guid.New(utils.NodePrefix)
	sid1 := livekit.ParticipantID(guid.New(utils.ParticipantPrefix))
	nodeID2 := guid.New(utils.NodePrefix)
	sid2 := livekit.ParticipantID(guid.New(utils.ParticipantPrefix))
	return &rpc.GetAllParticipantResponse{
		Participants: []*livekit.RemoteParticipantInfo{
			{
				ParticipantInfo: &livekit.ParticipantInfo{
					Identity: string(sid1),
				},
				NodeId: nodeID1,
			},
			{
				ParticipantInfo: &livekit.ParticipantInfo{
					Identity: string(sid2),
				},
				NodeId: nodeID2,
			},
		},
	}, nil
}

type broadcastSignalServerImpl2 struct {
}

func (b *broadcastSignalServerImpl2) GetAllParticipant(ctx context.Context, request *rpc.GetAllParticipantRequest) (*rpc.GetAllParticipantResponse, error) {
	return nil, nil
}

// ---------------------------------------------------------

func TestPubSub(t *testing.T) {
	rc := getRedis()
	bus := getMessageBus(rc)

	logger := log.New(os.Stdout, "[INFO] ", log.Ldate|log.Ltime|log.Lmicroseconds)
	sps, _ := rpc.NewBroadcastSignalPubSub(rpc.ClientParams{Bus: bus})

	// 启动发布者
	startPub(logger, sps)
	logger.Println("启动发布者")

	// 订阅者1
	startSub("1", logger, sps)
	logger.Println("启动订阅者1")

	time.Sleep(17 * time.Second)

	// 订阅者2
	logger.Println("启动订阅者2")
	startSub("2", logger, sps)

	time.Sleep(1 * time.Hour)
}

func genResponse() *rpc.BroadcastUpdate {
	sid := livekit.ParticipantID(guid.New(utils.ParticipantPrefix))

	broadcastUpdate := &rpc.BroadcastUpdate{
		Update: &rpc.BroadcastUpdateMessage{
			Message: &rpc.BroadcastUpdateMessage_RemoteParticipantUpdate{
				RemoteParticipantUpdate: &livekit.RemoteParticipantUpdate{
					Participants: []*livekit.RemoteParticipantInfo{{
						ParticipantInfo: &livekit.ParticipantInfo{
							Identity: string(sid),
						},
					}},
				},
			},
		},
	}

	return broadcastUpdate
}

func startPub(logger *log.Logger, sps rpc.BroadcastSignalPubSub) {
	done := make(chan struct{})
	// 发布者 每隔5秒发送一条响应消息
	go func() {
		for {
			select {
			case <-done:
				return
			case <-time.After(5 * time.Second):
				resp := genResponse()
				logger.Println("")
				logger.Println("发送消息")
				_ = sps.PublishUpdate(context.Background(), "测试房间", resp)
			}
		}
	}()
}

func startSub(name string, logger *log.Logger, sps rpc.BroadcastSignalPubSub) {
	su, _ := sps.SubscribeUpdate(context.Background(), "测试房间")
	go func() {
		for update := range su.Channel() {
			updateJson, _ := protojson.Marshal(update)
			logger.Printf("订阅者 %s 收到 %s\n", name, updateJson)
		}
	}()
}
