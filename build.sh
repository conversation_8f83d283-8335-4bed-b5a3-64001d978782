#!/bin/bash

set -ex;

# Docker 打包
docker build --tag nexus.metathought.cc:5000/livekit/livekit-server:v1.9.0 .
# Docker 推送
docker push nexus.metathought.cc:5000/livekit/livekit-server:v1.9.0


# 本地测试
#cd livekit
#docker run -d --name livekit7880 \
#  -p 7880:7880 -p 7881:7881 -p 7885:7885/udp \
#  -v ./config-7880.yaml:/config.yaml \
#  nexus.metathought.cc:5000/livekit/livekit-server:v1.9.0 \
#  --config ./config.yaml --node-ip=*************
#
#docker run -d --name livekit8880 \
#  -p 8880:8880 -p 8881:8881 -p 8885:8885/udp \
#  -v ./config-8880.yaml:/config.yaml \
#  nexus.metathought.cc:5000/livekit/livekit-server:v1.9.0 \
#  --config ./config.yaml --node-ip=*************
#
#docker run -d --name livekit9880 \
#  -p 9880:9880 -p 9881:9881 -p 9885:9885/udp \
#  -v ./config-9880.yaml:/config.yaml \
#  nexus.metathought.cc:5000/livekit/livekit-server:v1.9.0 \
#  --config ./config.yaml --node-ip=*************