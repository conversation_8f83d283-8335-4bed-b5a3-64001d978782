package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"sync"

	"github.com/livekit/livekit-server/pkg/routing"
	"github.com/livekit/livekit-server/pkg/rtc"
	"github.com/livekit/livekit-server/pkg/sfu"
	"github.com/livekit/livekit-server/pkg/sfu/mime"
	sutils "github.com/livekit/livekit-server/pkg/utils"
	"github.com/livekit/protocol/livekit"
	"github.com/livekit/protocol/logger"
	"github.com/livekit/protocol/rpc"
	"github.com/livekit/psrpc"
	"github.com/pion/webrtc/v4"
	"go.uber.org/atomic"
)

type messageSink struct {
	udpConn *net.UDPConn
	channel *sfu.MessageChannel
}

// ------------------------------------------

type MediaRelayServer struct {
	currentNode routing.LocalNode
	roomManager *RoomManager
	server      rpc.TypedMediaRelayServer

	localConn          *net.UDPConn
	lock               sync.RWMutex
	closed             atomic.Bool
	connChanCache      map[livekit.NodeID]*connChan
	connDownTrackCache map[livekit.ConnectionID]*sfu.RemoteDownTrack
}

func NewMediaRelayServer(currentNode routing.LocalNode, bus psrpc.MessageBus, roomManager *RoomManager) (*MediaRelayServer, error) {
	rs := &MediaRelayServer{
		currentNode: currentNode,
		roomManager: roomManager,

		connChanCache:      make(map[livekit.NodeID]*connChan),
		connDownTrackCache: make(map[livekit.ConnectionID]*sfu.RemoteDownTrack),
	}
	s, err := rpc.NewTypedMediaRelayServer(currentNode.NodeID(), rs, bus)
	if err != nil {
		return nil, err
	}
	rs.server = s

	return rs, nil
}

func (m *MediaRelayServer) Stop() {
	m.closed.Store(true)
	for _, c := range m.connChanCache {
		c.close()
	}
}

func (m *MediaRelayServer) Start() error {
	if err := m.server.RegisterAllNodeTopics(m.currentNode.NodeID()); err != nil {
		return err
	}

	// 启动UDP中继服务
	localAddr, err := net.ResolveUDPAddr("udp", fmt.Sprintf(":%d", m.currentNode.RelayPort()))
	// DEBUG-zjx 媒体中继UDP使用回环网卡模拟不丢包
	//localAddr, err := net.ResolveUDPAddr("udp", fmt.Sprintf("localhost:%d", m.currentNode.RelayPort()))
	if err != nil {
		return err
	}
	localConn, err := net.ListenUDP("udp", localAddr)
	if err != nil {
		return err
	}
	m.localConn = localConn
	go m.mediaRelayAcceptor()

	return nil
}

func (m *MediaRelayServer) mediaRelayAcceptor() {
	buf := make([]byte, 2048)
	for {
		n, remoteAddr, err := m.localConn.ReadFromUDP(buf)
		req, err := decodeRequest(buf, n)
		if err != nil {
			logger.Errorw("媒体中继UDP读取失败", err)
			continue
		}

		if cr := req.CloseRequest; cr != nil {
			logger.Infow("接收到媒体中继停止请求",
				"roomName", cr.RoomName,
				"trackId", cr.TrackId)
			m.CloseRelayMediaSink(cr)

			connectionID := livekit.GetMediaRelayConnectionID(cr.RoomName, cr.TrackId)
			m.lock.RLock()
			dt := m.connDownTrackCache[connectionID]
			m.lock.RUnlock()
			if dt != nil {
				delete(m.connDownTrackCache, connectionID)
				dt.Close()
			}
		}

		if sr := req.StartRequest; sr != nil {
			logger.Infow("接收到媒体中继开始请求",
				"roomName", sr.RoomName,
				"subscriberIdentity", sr.SubscriberIdentity,
				"publisherIdentity", sr.PublisherIdentity,
				"trackId", sr.TrackId)

			// 获取本地轨道的接收器（WebRTCReceiver）
			trackID := livekit.TrackID(sr.TrackId)
			room, track := m.getLocalTrack(livekit.RoomName(sr.RoomName), livekit.ParticipantIdentity(sr.PublisherIdentity), trackID)
			receiver := track.Receivers()[0]

			// 创建 MessageSink
			sink, err := m.GetRelayMediaSink(sr, remoteAddr)
			if err != nil {
				logger.Errorw("媒体中继UDP创建Sink失败", err)
				continue
			}

			// 创建 RemoteDownTrack
			subLogger := rtc.LoggerWithParticipant(room.Logger(), livekit.ParticipantIdentity(sr.SubscriberIdentity), "", true)
			trackLogger := rtc.LoggerWithTrack(subLogger.WithComponent(sutils.ComponentSub), trackID, true)
			dt := sfu.NewRemoteDownTrack(sfu.RemoteDownTrackParams{
				RoomName:           livekit.RoomName(sr.RoomName),
				SubscriberIdentity: livekit.ParticipantIdentity(sr.SubscriberIdentity),
				PublisherIdentity:  livekit.ParticipantIdentity(sr.PublisherIdentity),
				TrackId:            trackID,
				Receiver:           receiver,
				Logger:             trackLogger,
			}, sink)

			// 将 RemoteDownTrack 添加到接收器广播器中
			err = receiver.AddDownTrack(dt)
			if err != nil {
				continue
			}

			// 将 RemoteDownTrack 添加到连接缓存中，以便后续连接关闭时同步关闭 RemoteDownTrack
			connectionID := livekit.GetMediaRelayConnectionID(sr.RoomName, sr.TrackId)
			m.lock.Lock()
			m.connDownTrackCache[connectionID] = dt
			m.lock.Unlock()
		}

	}
}

func (m *MediaRelayServer) GetRelayMediaSink(request *rpc.StartRequest, remoteAddr *net.UDPAddr) (sfu.MessageSink, error) {
	if m.closed.Load() {
		return nil, ErrClosed
	}

	remoteNodeID := livekit.NodeID(request.SubscriberNodeId)
	//remoteNodeIp := request.SubscriberNodeIp

	m.lock.RLock()
	c := m.connChanCache[remoteNodeID]
	m.lock.RUnlock()
	if c != nil {
		return c.getOrCreateChanForSink(request, remoteAddr)
	}

	m.lock.Lock()
	defer m.lock.Unlock()

	if c = m.connChanCache[remoteNodeID]; c != nil {
		return c.getOrCreateChanForSink(request, remoteAddr)
	}

	c = newConnChan(m.localConn)
	// go c.startWriter()

	// 方法结束前检查关闭状态
	defer func() {
		if m.closed.Load() {
			c.close()
		}
	}()
	m.connChanCache[remoteNodeID] = c
	return c.getOrCreateChanForSink(request, remoteAddr)
}

func (m *MediaRelayServer) CloseRelayMediaSink(request *rpc.CloseRequest) {
	remoteNodeID := livekit.NodeID(request.SubscriberNodeId)

	m.lock.RLock()
	c := m.connChanCache[remoteNodeID]
	m.lock.RUnlock()
	if c != nil {
		c.closeChanForSink(request)
	}
}

// 获取本地轨道
func (m *MediaRelayServer) getLocalTrack(roomName livekit.RoomName, publisherIdentity livekit.ParticipantIdentity, trackID livekit.TrackID) (*rtc.Room, *rtc.MediaTrack) {
	room := m.roomManager.GetRoom(context.Background(), roomName)
	if room == nil {
		return nil, nil
	}
	publisher := room.GetParticipant(publisherIdentity)
	if publisher == nil {
		return nil, nil
	}
	track := publisher.GetPublishedTrack(trackID)
	if track == nil {
		return nil, nil
	}
	// 在媒体中继服务器（发布者），轨道只可能是 MediaTrack 而不会是 RemoteMediaTrack（只出现在订阅者）
	return room, track.(*rtc.MediaTrack)
}

func (m *MediaRelayServer) RelayTrackReceiver(ctx context.Context, request *rpc.RelayTrackReceiverRequest) (*rpc.RelayTrackReceiverResponse, error) {
	_, track := m.getLocalTrack(livekit.RoomName(request.RoomName), livekit.ParticipantIdentity(request.ParticipantIdentity), livekit.TrackID(request.TrackId))
	if track == nil {
		return nil, errors.New("轨道不存在，跳过 RelayTrackReceiver处理")
	}
	receivers := track.Receivers()

	switch pm := request.Request.Message.(type) {
	case *rpc.TrackReceiverRequest_CodecRequest:
		bytes, err := json.Marshal(receivers[0].Codec())
		if err != nil {
			return nil, err
		}
		return &rpc.RelayTrackReceiverResponse{
			Message: &rpc.RelayTrackReceiverResponse_CodecResponse{
				CodecResponse: &rpc.CodecResponse{
					Bytes: bytes,
				},
			},
		}, nil
	case *rpc.TrackReceiverRequest_HeaderExtensionsRequest:
		bytes, err := json.Marshal(receivers[0].HeaderExtensions())
		if err != nil {
			return nil, err
		}
		return &rpc.RelayTrackReceiverResponse{
			Message: &rpc.RelayTrackReceiverResponse_HeaderExtensionsResponse{
				HeaderExtensionsResponse: &rpc.HeaderExtensionsResponse{
					Bytes: bytes,
				},
			},
		}, nil
	case *rpc.TrackReceiverRequest_SendPliRequest:
		req := pm.SendPliRequest
		receivers[0].SendPLI(req.Layer, req.Force)
		return &rpc.RelayTrackReceiverResponse{
			Message: nil,
		}, nil
	case *rpc.TrackReceiverRequest_OnSetupReceiverRequest:
		req := pm.OnSetupReceiverRequest
		if onSetupReceiver := track.MediaTrackReceiver.GetOnSetupReceiver(); onSetupReceiver != nil {
			onSetupReceiver(mime.MimeType(req.MimeType))
		}
		return &rpc.RelayTrackReceiverResponse{
			Message: nil,
		}, nil
	case *rpc.TrackReceiverRequest_OnSubscriberMaxQualityChangeRequest:
		req := pm.OnSubscriberMaxQualityChangeRequest
		if onSubscriberMaxQualityChange := track.MediaTrackReceiver.GetOnSubscriberMaxQualityChange(); onSubscriberMaxQualityChange != nil {
			onSubscriberMaxQualityChange(livekit.ParticipantID(req.SubscriberId), mime.MimeType(req.MimeType), req.Layer)
		}
		return &rpc.RelayTrackReceiverResponse{
			Message: nil,
		}, nil
	case *rpc.TrackReceiverRequest_OnCodecRegressionRequest:
		req := pm.OnCodecRegressionRequest
		oldCodec, newCodec := webrtc.RTPCodecParameters{}, webrtc.RTPCodecParameters{}
		if err := json.Unmarshal(req.Old, &oldCodec); err != nil {
			return nil, err
		}
		if err := json.Unmarshal(req.New, &newCodec); err != nil {
			return nil, err
		}
		if onCodecRegression := track.GetOnCodecRegression(); onCodecRegression != nil {
			onCodecRegression(oldCodec, newCodec)
		}
	case *rpc.TrackReceiverRequest_OnTrackSubscribedRequest:
		track.OnTrackSubscribed()
		return &rpc.RelayTrackReceiverResponse{
			Message: nil,
		}, nil
	case *rpc.TrackReceiverRequest_GetAudioLevelRequest:
		level, active := receivers[0].GetAudioLevel()
		return &rpc.RelayTrackReceiverResponse{
			Message: &rpc.RelayTrackReceiverResponse_GetAudioLevelResponse{
				GetAudioLevelResponse: &rpc.GetAudioLevelResponse{
					Level:  level,
					Active: active,
				},
			},
		}, nil
	case *rpc.TrackReceiverRequest_GetTemporalLayerFpsForSpatialRequest:
		req := pm.GetTemporalLayerFpsForSpatialRequest
		fps := receivers[0].GetTemporalLayerFpsForSpatial(req.Spatial)
		return &rpc.RelayTrackReceiverResponse{
			Message: &rpc.RelayTrackReceiverResponse_GetTemporalLayerFpsForSpatialResponse{
				GetTemporalLayerFpsForSpatialResponse: &rpc.GetTemporalLayerFpsForSpatialResponse{
					Fps: fps,
				},
			},
		}, nil
	case *rpc.TrackReceiverRequest_SetUpTrackPausedRequest:
		req := pm.SetUpTrackPausedRequest
		receivers[0].SetUpTrackPaused(req.Paused)
		return &rpc.RelayTrackReceiverResponse{
			Message: nil,
		}, nil
	case *rpc.TrackReceiverRequest_SetMaxExpectedSpatialLayerRequest:
		req := pm.SetMaxExpectedSpatialLayerRequest
		receivers[0].SetMaxExpectedSpatialLayer(req.Layer)
		return &rpc.RelayTrackReceiverResponse{
			Message: nil,
		}, nil
	}

	return nil, errors.New("不支持的RPC请求")
}
