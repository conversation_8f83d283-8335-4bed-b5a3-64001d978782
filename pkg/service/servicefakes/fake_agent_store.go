// Code generated by counterfeiter. DO NOT EDIT.
package servicefakes

import (
	"context"
	"sync"

	"github.com/livekit/livekit-server/pkg/service"
	"github.com/livekit/protocol/livekit"
)

type FakeAgentStore struct {
	DeleteAgentDispatchStub        func(context.Context, *livekit.AgentDispatch) error
	deleteAgentDispatchMutex       sync.RWMutex
	deleteAgentDispatchArgsForCall []struct {
		arg1 context.Context
		arg2 *livekit.AgentDispatch
	}
	deleteAgentDispatchReturns struct {
		result1 error
	}
	deleteAgentDispatchReturnsOnCall map[int]struct {
		result1 error
	}
	DeleteAgentJobStub        func(context.Context, *livekit.Job) error
	deleteAgentJobMutex       sync.RWMutex
	deleteAgentJobArgsForCall []struct {
		arg1 context.Context
		arg2 *livekit.Job
	}
	deleteAgentJobReturns struct {
		result1 error
	}
	deleteAgentJobReturnsOnCall map[int]struct {
		result1 error
	}
	ListAgentDispatchesStub        func(context.Context, livekit.RoomName) ([]*livekit.AgentDispatch, error)
	listAgentDispatchesMutex       sync.RWMutex
	listAgentDispatchesArgsForCall []struct {
		arg1 context.Context
		arg2 livekit.RoomName
	}
	listAgentDispatchesReturns struct {
		result1 []*livekit.AgentDispatch
		result2 error
	}
	listAgentDispatchesReturnsOnCall map[int]struct {
		result1 []*livekit.AgentDispatch
		result2 error
	}
	StoreAgentDispatchStub        func(context.Context, *livekit.AgentDispatch) error
	storeAgentDispatchMutex       sync.RWMutex
	storeAgentDispatchArgsForCall []struct {
		arg1 context.Context
		arg2 *livekit.AgentDispatch
	}
	storeAgentDispatchReturns struct {
		result1 error
	}
	storeAgentDispatchReturnsOnCall map[int]struct {
		result1 error
	}
	StoreAgentJobStub        func(context.Context, *livekit.Job) error
	storeAgentJobMutex       sync.RWMutex
	storeAgentJobArgsForCall []struct {
		arg1 context.Context
		arg2 *livekit.Job
	}
	storeAgentJobReturns struct {
		result1 error
	}
	storeAgentJobReturnsOnCall map[int]struct {
		result1 error
	}
	invocations      map[string][][]interface{}
	invocationsMutex sync.RWMutex
}

func (fake *FakeAgentStore) DeleteAgentDispatch(arg1 context.Context, arg2 *livekit.AgentDispatch) error {
	fake.deleteAgentDispatchMutex.Lock()
	ret, specificReturn := fake.deleteAgentDispatchReturnsOnCall[len(fake.deleteAgentDispatchArgsForCall)]
	fake.deleteAgentDispatchArgsForCall = append(fake.deleteAgentDispatchArgsForCall, struct {
		arg1 context.Context
		arg2 *livekit.AgentDispatch
	}{arg1, arg2})
	stub := fake.DeleteAgentDispatchStub
	fakeReturns := fake.deleteAgentDispatchReturns
	fake.recordInvocation("DeleteAgentDispatch", []interface{}{arg1, arg2})
	fake.deleteAgentDispatchMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeAgentStore) DeleteAgentDispatchCallCount() int {
	fake.deleteAgentDispatchMutex.RLock()
	defer fake.deleteAgentDispatchMutex.RUnlock()
	return len(fake.deleteAgentDispatchArgsForCall)
}

func (fake *FakeAgentStore) DeleteAgentDispatchCalls(stub func(context.Context, *livekit.AgentDispatch) error) {
	fake.deleteAgentDispatchMutex.Lock()
	defer fake.deleteAgentDispatchMutex.Unlock()
	fake.DeleteAgentDispatchStub = stub
}

func (fake *FakeAgentStore) DeleteAgentDispatchArgsForCall(i int) (context.Context, *livekit.AgentDispatch) {
	fake.deleteAgentDispatchMutex.RLock()
	defer fake.deleteAgentDispatchMutex.RUnlock()
	argsForCall := fake.deleteAgentDispatchArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeAgentStore) DeleteAgentDispatchReturns(result1 error) {
	fake.deleteAgentDispatchMutex.Lock()
	defer fake.deleteAgentDispatchMutex.Unlock()
	fake.DeleteAgentDispatchStub = nil
	fake.deleteAgentDispatchReturns = struct {
		result1 error
	}{result1}
}

func (fake *FakeAgentStore) DeleteAgentDispatchReturnsOnCall(i int, result1 error) {
	fake.deleteAgentDispatchMutex.Lock()
	defer fake.deleteAgentDispatchMutex.Unlock()
	fake.DeleteAgentDispatchStub = nil
	if fake.deleteAgentDispatchReturnsOnCall == nil {
		fake.deleteAgentDispatchReturnsOnCall = make(map[int]struct {
			result1 error
		})
	}
	fake.deleteAgentDispatchReturnsOnCall[i] = struct {
		result1 error
	}{result1}
}

func (fake *FakeAgentStore) DeleteAgentJob(arg1 context.Context, arg2 *livekit.Job) error {
	fake.deleteAgentJobMutex.Lock()
	ret, specificReturn := fake.deleteAgentJobReturnsOnCall[len(fake.deleteAgentJobArgsForCall)]
	fake.deleteAgentJobArgsForCall = append(fake.deleteAgentJobArgsForCall, struct {
		arg1 context.Context
		arg2 *livekit.Job
	}{arg1, arg2})
	stub := fake.DeleteAgentJobStub
	fakeReturns := fake.deleteAgentJobReturns
	fake.recordInvocation("DeleteAgentJob", []interface{}{arg1, arg2})
	fake.deleteAgentJobMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeAgentStore) DeleteAgentJobCallCount() int {
	fake.deleteAgentJobMutex.RLock()
	defer fake.deleteAgentJobMutex.RUnlock()
	return len(fake.deleteAgentJobArgsForCall)
}

func (fake *FakeAgentStore) DeleteAgentJobCalls(stub func(context.Context, *livekit.Job) error) {
	fake.deleteAgentJobMutex.Lock()
	defer fake.deleteAgentJobMutex.Unlock()
	fake.DeleteAgentJobStub = stub
}

func (fake *FakeAgentStore) DeleteAgentJobArgsForCall(i int) (context.Context, *livekit.Job) {
	fake.deleteAgentJobMutex.RLock()
	defer fake.deleteAgentJobMutex.RUnlock()
	argsForCall := fake.deleteAgentJobArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeAgentStore) DeleteAgentJobReturns(result1 error) {
	fake.deleteAgentJobMutex.Lock()
	defer fake.deleteAgentJobMutex.Unlock()
	fake.DeleteAgentJobStub = nil
	fake.deleteAgentJobReturns = struct {
		result1 error
	}{result1}
}

func (fake *FakeAgentStore) DeleteAgentJobReturnsOnCall(i int, result1 error) {
	fake.deleteAgentJobMutex.Lock()
	defer fake.deleteAgentJobMutex.Unlock()
	fake.DeleteAgentJobStub = nil
	if fake.deleteAgentJobReturnsOnCall == nil {
		fake.deleteAgentJobReturnsOnCall = make(map[int]struct {
			result1 error
		})
	}
	fake.deleteAgentJobReturnsOnCall[i] = struct {
		result1 error
	}{result1}
}

func (fake *FakeAgentStore) ListAgentDispatches(arg1 context.Context, arg2 livekit.RoomName) ([]*livekit.AgentDispatch, error) {
	fake.listAgentDispatchesMutex.Lock()
	ret, specificReturn := fake.listAgentDispatchesReturnsOnCall[len(fake.listAgentDispatchesArgsForCall)]
	fake.listAgentDispatchesArgsForCall = append(fake.listAgentDispatchesArgsForCall, struct {
		arg1 context.Context
		arg2 livekit.RoomName
	}{arg1, arg2})
	stub := fake.ListAgentDispatchesStub
	fakeReturns := fake.listAgentDispatchesReturns
	fake.recordInvocation("ListAgentDispatches", []interface{}{arg1, arg2})
	fake.listAgentDispatchesMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeAgentStore) ListAgentDispatchesCallCount() int {
	fake.listAgentDispatchesMutex.RLock()
	defer fake.listAgentDispatchesMutex.RUnlock()
	return len(fake.listAgentDispatchesArgsForCall)
}

func (fake *FakeAgentStore) ListAgentDispatchesCalls(stub func(context.Context, livekit.RoomName) ([]*livekit.AgentDispatch, error)) {
	fake.listAgentDispatchesMutex.Lock()
	defer fake.listAgentDispatchesMutex.Unlock()
	fake.ListAgentDispatchesStub = stub
}

func (fake *FakeAgentStore) ListAgentDispatchesArgsForCall(i int) (context.Context, livekit.RoomName) {
	fake.listAgentDispatchesMutex.RLock()
	defer fake.listAgentDispatchesMutex.RUnlock()
	argsForCall := fake.listAgentDispatchesArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeAgentStore) ListAgentDispatchesReturns(result1 []*livekit.AgentDispatch, result2 error) {
	fake.listAgentDispatchesMutex.Lock()
	defer fake.listAgentDispatchesMutex.Unlock()
	fake.ListAgentDispatchesStub = nil
	fake.listAgentDispatchesReturns = struct {
		result1 []*livekit.AgentDispatch
		result2 error
	}{result1, result2}
}

func (fake *FakeAgentStore) ListAgentDispatchesReturnsOnCall(i int, result1 []*livekit.AgentDispatch, result2 error) {
	fake.listAgentDispatchesMutex.Lock()
	defer fake.listAgentDispatchesMutex.Unlock()
	fake.ListAgentDispatchesStub = nil
	if fake.listAgentDispatchesReturnsOnCall == nil {
		fake.listAgentDispatchesReturnsOnCall = make(map[int]struct {
			result1 []*livekit.AgentDispatch
			result2 error
		})
	}
	fake.listAgentDispatchesReturnsOnCall[i] = struct {
		result1 []*livekit.AgentDispatch
		result2 error
	}{result1, result2}
}

func (fake *FakeAgentStore) StoreAgentDispatch(arg1 context.Context, arg2 *livekit.AgentDispatch) error {
	fake.storeAgentDispatchMutex.Lock()
	ret, specificReturn := fake.storeAgentDispatchReturnsOnCall[len(fake.storeAgentDispatchArgsForCall)]
	fake.storeAgentDispatchArgsForCall = append(fake.storeAgentDispatchArgsForCall, struct {
		arg1 context.Context
		arg2 *livekit.AgentDispatch
	}{arg1, arg2})
	stub := fake.StoreAgentDispatchStub
	fakeReturns := fake.storeAgentDispatchReturns
	fake.recordInvocation("StoreAgentDispatch", []interface{}{arg1, arg2})
	fake.storeAgentDispatchMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeAgentStore) StoreAgentDispatchCallCount() int {
	fake.storeAgentDispatchMutex.RLock()
	defer fake.storeAgentDispatchMutex.RUnlock()
	return len(fake.storeAgentDispatchArgsForCall)
}

func (fake *FakeAgentStore) StoreAgentDispatchCalls(stub func(context.Context, *livekit.AgentDispatch) error) {
	fake.storeAgentDispatchMutex.Lock()
	defer fake.storeAgentDispatchMutex.Unlock()
	fake.StoreAgentDispatchStub = stub
}

func (fake *FakeAgentStore) StoreAgentDispatchArgsForCall(i int) (context.Context, *livekit.AgentDispatch) {
	fake.storeAgentDispatchMutex.RLock()
	defer fake.storeAgentDispatchMutex.RUnlock()
	argsForCall := fake.storeAgentDispatchArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeAgentStore) StoreAgentDispatchReturns(result1 error) {
	fake.storeAgentDispatchMutex.Lock()
	defer fake.storeAgentDispatchMutex.Unlock()
	fake.StoreAgentDispatchStub = nil
	fake.storeAgentDispatchReturns = struct {
		result1 error
	}{result1}
}

func (fake *FakeAgentStore) StoreAgentDispatchReturnsOnCall(i int, result1 error) {
	fake.storeAgentDispatchMutex.Lock()
	defer fake.storeAgentDispatchMutex.Unlock()
	fake.StoreAgentDispatchStub = nil
	if fake.storeAgentDispatchReturnsOnCall == nil {
		fake.storeAgentDispatchReturnsOnCall = make(map[int]struct {
			result1 error
		})
	}
	fake.storeAgentDispatchReturnsOnCall[i] = struct {
		result1 error
	}{result1}
}

func (fake *FakeAgentStore) StoreAgentJob(arg1 context.Context, arg2 *livekit.Job) error {
	fake.storeAgentJobMutex.Lock()
	ret, specificReturn := fake.storeAgentJobReturnsOnCall[len(fake.storeAgentJobArgsForCall)]
	fake.storeAgentJobArgsForCall = append(fake.storeAgentJobArgsForCall, struct {
		arg1 context.Context
		arg2 *livekit.Job
	}{arg1, arg2})
	stub := fake.StoreAgentJobStub
	fakeReturns := fake.storeAgentJobReturns
	fake.recordInvocation("StoreAgentJob", []interface{}{arg1, arg2})
	fake.storeAgentJobMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeAgentStore) StoreAgentJobCallCount() int {
	fake.storeAgentJobMutex.RLock()
	defer fake.storeAgentJobMutex.RUnlock()
	return len(fake.storeAgentJobArgsForCall)
}

func (fake *FakeAgentStore) StoreAgentJobCalls(stub func(context.Context, *livekit.Job) error) {
	fake.storeAgentJobMutex.Lock()
	defer fake.storeAgentJobMutex.Unlock()
	fake.StoreAgentJobStub = stub
}

func (fake *FakeAgentStore) StoreAgentJobArgsForCall(i int) (context.Context, *livekit.Job) {
	fake.storeAgentJobMutex.RLock()
	defer fake.storeAgentJobMutex.RUnlock()
	argsForCall := fake.storeAgentJobArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeAgentStore) StoreAgentJobReturns(result1 error) {
	fake.storeAgentJobMutex.Lock()
	defer fake.storeAgentJobMutex.Unlock()
	fake.StoreAgentJobStub = nil
	fake.storeAgentJobReturns = struct {
		result1 error
	}{result1}
}

func (fake *FakeAgentStore) StoreAgentJobReturnsOnCall(i int, result1 error) {
	fake.storeAgentJobMutex.Lock()
	defer fake.storeAgentJobMutex.Unlock()
	fake.StoreAgentJobStub = nil
	if fake.storeAgentJobReturnsOnCall == nil {
		fake.storeAgentJobReturnsOnCall = make(map[int]struct {
			result1 error
		})
	}
	fake.storeAgentJobReturnsOnCall[i] = struct {
		result1 error
	}{result1}
}

func (fake *FakeAgentStore) Invocations() map[string][][]interface{} {
	fake.invocationsMutex.RLock()
	defer fake.invocationsMutex.RUnlock()
	fake.deleteAgentDispatchMutex.RLock()
	defer fake.deleteAgentDispatchMutex.RUnlock()
	fake.deleteAgentJobMutex.RLock()
	defer fake.deleteAgentJobMutex.RUnlock()
	fake.listAgentDispatchesMutex.RLock()
	defer fake.listAgentDispatchesMutex.RUnlock()
	fake.storeAgentDispatchMutex.RLock()
	defer fake.storeAgentDispatchMutex.RUnlock()
	fake.storeAgentJobMutex.RLock()
	defer fake.storeAgentJobMutex.RUnlock()
	copiedInvocations := map[string][][]interface{}{}
	for key, value := range fake.invocations {
		copiedInvocations[key] = value
	}
	return copiedInvocations
}

func (fake *FakeAgentStore) recordInvocation(key string, args []interface{}) {
	fake.invocationsMutex.Lock()
	defer fake.invocationsMutex.Unlock()
	if fake.invocations == nil {
		fake.invocations = map[string][][]interface{}{}
	}
	if fake.invocations[key] == nil {
		fake.invocations[key] = [][]interface{}{}
	}
	fake.invocations[key] = append(fake.invocations[key], args)
}

var _ service.AgentStore = new(FakeAgentStore)
