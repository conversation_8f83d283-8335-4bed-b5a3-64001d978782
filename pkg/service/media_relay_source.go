package service

import (
	"crypto/rand"
	"encoding/binary"
	"fmt"
	"math/big"
	"net"
	"sync"

	"github.com/livekit/livekit-server/pkg/routing"
	"github.com/livekit/livekit-server/pkg/sfu"
	"github.com/livekit/protocol/livekit"
	"github.com/livekit/protocol/logger"
	"github.com/livekit/protocol/rpc"
	"github.com/pkg/errors"
	"go.uber.org/atomic"
	"google.golang.org/protobuf/proto"
)

var (
	ErrDataPacketOverflow = errors.New("数据报长度溢出")
	ErrClosed             = errors.New("已关闭")
	ErrSinkExists         = errors.New("MessageSink已存在")
	ErrLocalPortExhaust   = errors.New("本地端口耗尽")
)

type MediaRelaySource struct {
	currentNode routing.LocalNode

	lock          sync.RWMutex
	closed        atomic.Bool
	connChanCache map[livekit.NodeID]*connChan
}

func NewMediaRelaySource(currentNode routing.LocalNode) *MediaRelaySource {
	return &MediaRelaySource{
		currentNode:   currentNode,
		connChanCache: make(map[livekit.NodeID]*connChan),
	}
}

func (m *MediaRelaySource) GetRelayMediaSource(request *rpc.StartRequest) (sfu.MessageSource, error) {
	if m.closed.Load() {
		return nil, ErrClosed
	}

	nodeID := livekit.NodeID(request.PublisherNodeId)

	m.lock.RLock()
	c := m.connChanCache[nodeID]
	m.lock.RUnlock()
	if c != nil {
		return c.getOrCreateChanForSource(request)
	}

	m.lock.Lock()
	defer m.lock.Unlock()

	if c = m.connChanCache[nodeID]; c != nil {
		return c.getOrCreateChanForSource(request)
	}

	remoteAddr, err := net.ResolveUDPAddr("udp", fmt.Sprintf("%s:%d", request.PublisherNodeIp, request.PublisherNodeRelayPort))
	// DEBUG-zjx 媒体中继UDP使用回环网卡模拟不丢包
	//remoteAddr, err := net.ResolveUDPAddr("udp", fmt.Sprintf("%s:%d", "localhost", request.PublisherNodeRelayPort))
	if err != nil {
		return nil, err
	}

	// 从指定范围获取一个可用端口与发布端建立连接，范围 relayPort=5104 [relayPort*10, relayPort*10 + 1000] => [51040, 52040]
	relayPort := m.currentNode.RelayPort()
	localPortMin := relayPort * 10
	localPortRange := 1000

	var conn *net.UDPConn
	for i := 0; i < 100; i++ {
		randNum, err := rand.Int(rand.Reader, big.NewInt(int64(localPortRange)+1))
		if err != nil {
			logger.Warnw("媒体中继连接-生成随机端口失败", err)
			continue
		}
		localPort := localPortMin + int(randNum.Int64())
		localAddr, err := net.ResolveUDPAddr("udp", fmt.Sprintf(":%d", localPort))
		// DEBUG-zjx 媒体中继UDP使用回环网卡模拟不丢包
		//localAddr, err := net.ResolveUDPAddr("udp", fmt.Sprintf("localhost:%d", localPort))
		if err != nil {
			logger.Warnw("媒体中继连接-本地UDP地址解析失败", err, "retries", i)
			continue
		}
		conn, err = net.DialUDP("udp", localAddr, remoteAddr)
		if err != nil {
			logger.Warnw("媒体中继连接-创建UDP连接失败", err, "retries", i)
			continue
		}
		break
	}
	if conn == nil {
		return nil, ErrLocalPortExhaust
	}
	logger.Infow("媒体中继连接-建立成功",
		"protocol", "udp",
		"localAddr", conn.LocalAddr(),
		"remoteAddr", conn.RemoteAddr(),
	)

	c = newConnChan(conn)
	go c.startReader()
	// 方法结束前检查关闭状态
	defer func() {
		if m.closed.Load() {
			c.close()
		}
	}()
	m.connChanCache[nodeID] = c
	return c.getOrCreateChanForSource(request)
}

func (m *MediaRelaySource) Close() {
	m.closed.Store(true)
	for _, c := range m.connChanCache {
		c.close()
	}
}

// ------------------------------------------

type connChan struct {
	udpConn  *net.UDPConn
	channels map[livekit.ConnectionID]*sfu.MessageChannel

	lock   sync.Mutex
	closed atomic.Bool
}

func newConnChan(udpConn *net.UDPConn) *connChan {
	c := &connChan{
		udpConn:  udpConn,
		channels: make(map[livekit.ConnectionID]*sfu.MessageChannel),
	}

	return c
}

func (c *connChan) startReader() {
	// TODO-zjx 为了极致性能，缓冲区应该设置为 1460（小于MTU）避免网络层（IP）分片和重组的开销，WebRTC也是这么做的，即将RTP的大小设计为小于MTU，两种做法：
	//  1. 在应用层重组切分为 <1460 大小的包，自定义顺序和重传机制，可以参考 KCP 算法（空间换时间）
	//  2. 舍弃 extPacket，这是RTP的包装，因此整体大小可能会超过RTP，RTP已经是设计过最大不超过MTU，因此包装后会超过 MTU，舍弃后就需要在接收端处理各种编码问题，参考 buffer.go
	buf := make([]byte, 2096)
	for !c.closed.Load() {
		n, err := c.udpConn.Read(buf)
		if err != nil {
			logger.Errorw("媒体中继UDP读取失败-读取", err)
			continue
		}

		msg, err := decodeResponse(buf, n)
		if err != nil {
			logger.Errorw("媒体中继UDP读取失败-反序列化", err)
			continue
		}

		// 分发
		connectionID := livekit.ConnectionID(msg.ConnectionId)
		if ch := c.channels[connectionID]; ch != nil {
			_ = ch.WriteMessage(msg)
		} else {
			logger.Debugw("未找到连接通道，丢弃媒体中继包", "connectionID", connectionID)
		}
	}
}

func (c *connChan) getOrCreateChanForSource(request *rpc.StartRequest) (sfu.MessageSource, error) {
	c.lock.Lock()
	defer c.lock.Unlock()

	connectionID := livekit.GetMediaRelayConnectionID(request.RoomName, request.TrackId)
	if c.channels[connectionID] == nil {
		source := sfu.NewMessageChannel(connectionID, 500)
		source.OnClose(func() {
			delete(c.channels, connectionID)
			// 发送结束请求
			cr, _ := encodeProto(&rpc.RelayMediaRequest{
				CloseRequest: &rpc.CloseRequest{
					RoomName:         request.RoomName,
					SubscriberNodeId: request.SubscriberNodeId,
					TrackId:          request.TrackId,
				},
			})
			_, _ = c.udpConn.Write(cr)
		})
		c.channels[connectionID] = source

		// TODO-zjx 由于UDP是不可靠的，需要增加相应的重试机制，这里需要阻塞等待第一条消息回来，同时发布端收到 StartRequest 后需要先返回
		//  一条 StartResponse，具体可以参考 rtcservice.go -> "s.startConnection" 的确认机制
		// 发送开始请求
		sr, err := encodeProto(&rpc.RelayMediaRequest{StartRequest: request})
		if err != nil {
			c.close()
			return nil, err
		}
		_, err = c.udpConn.Write(sr)
		if err != nil {
			c.close()
			return nil, err
		}
	}

	return c.channels[connectionID], nil
}

func (c *connChan) getOrCreateChanForSink(request *rpc.StartRequest, remoteAddr *net.UDPAddr) (sfu.MessageSink, error) {
	c.lock.Lock()
	defer c.lock.Unlock()

	connectionID := livekit.GetMediaRelayConnectionID(request.RoomName, request.TrackId)
	if c.channels[connectionID] != nil {
		return nil, ErrSinkExists
	}

	sink := sfu.NewMessageChannel(connectionID, 500)
	sink.OnClose(func() {
		delete(c.channels, connectionID)
	})
	c.channels[connectionID] = sink
	go func() {
		for msg := range sink.ReadChan() {
			msgBytes, err := encodeProto(msg)
			if err != nil {
				logger.Errorw("媒体中继UDP写入失败-序列化", err)
			}

			_, err = c.udpConn.WriteToUDP(msgBytes, remoteAddr)
			if err != nil {
				c.close()
				return
			}
		}
	}()

	return sink, nil
}

func (c *connChan) closeChanForSink(request *rpc.CloseRequest) {
	c.lock.Lock()
	defer c.lock.Unlock()

	connectionID := livekit.GetMediaRelayConnectionID(request.RoomName, request.TrackId)
	if sink := c.channels[connectionID]; sink != nil {
		sink.Close()
	}
}

func (c *connChan) close() {
	c.lock.Lock()
	defer c.lock.Unlock()

	c.closed.Store(true)

	_ = c.udpConn.Close()
	for _, ch := range c.channels {
		ch.Close()
	}
	c.channels = make(map[livekit.ConnectionID]*sfu.MessageChannel)
}

// ------------------------------------------

// 编码：Protobuf消息 -> 长度前缀（2字节大端序）+ 消息字节流
func encodeProto(msg proto.Message) ([]byte, error) {
	// Protobuf序列化
	data, err := proto.Marshal(msg)
	if err != nil {
		return nil, err
	}

	// 添加2字节长度前缀（大端序）
	length := uint16(len(data))
	buf := make([]byte, 2+length)
	binary.BigEndian.PutUint16(buf[:2], length)
	copy(buf[2:], data)

	return buf, nil
}

func decodeResponse(buf []byte, n int) (*rpc.RelayMediaResponse, error) {
	// 读取整个UDP数据报
	// 校验数据完整性
	dataLength := int(binary.BigEndian.Uint16(buf[:2]))
	if dataLength != (n - 2) {
		logger.Errorw("媒体中继UDP读取失败-长度溢出", ErrDataPacketOverflow,
			"expectDataLength", dataLength,
			"actualDataLength", n-2,
		)
		return nil, ErrDataPacketOverflow
	}
	// 反序列化消息
	dataBuf := buf[2:n]

	msg := &rpc.RelayMediaResponse{}
	err := proto.Unmarshal(dataBuf, msg)
	if err != nil {
		logger.Errorw("媒体中继UDP读取失败-反序列化", err)
		return nil, err
	}
	return msg, nil
}

func decodeRequest(buf []byte, n int) (*rpc.RelayMediaRequest, error) {
	// 校验数据完整性
	dataLength := int(binary.BigEndian.Uint16(buf[:2]))
	if dataLength != (n - 2) {
		return nil, ErrDataPacketOverflow
	}
	// 反序列化消息
	dataBuf := buf[2:n]
	msg := &rpc.RelayMediaRequest{}
	err := proto.Unmarshal(dataBuf, msg)
	if err != nil {
		return nil, err
	}

	return msg, err
}
