package service

import (
	"github.com/livekit/livekit-server/pkg/routing"
	"github.com/livekit/protocol/livekit"
	"github.com/livekit/protocol/rpc"
	"google.golang.org/protobuf/encoding/protojson"
	"log"
	"net"
	"os"
	"sync"
	"testing"
	"time"
)

func TestTemp(t *testing.T) {
	wg := sync.WaitGroup{}
	wg.Add(1)

	logger := log.New(os.Stdout, "[INFO] ", log.Ldate|log.Ltime|log.Lmicroseconds|log.Lshortfile)

	remoteAddr, _ := net.ResolveUDPAddr("udp", "*************:5104")
	remoteConn, _ := net.ListenUDP("udp", remoteAddr)
	go func() {
		// 直接读取全部
		buf := make([]byte, 2048)
		for {
			n, udpAddr, err := remoteConn.ReadFromUDP(buf)
			req, err := decodeRequest(buf, n)
			if err != nil {
				logger.Println("读取请求失败", err)
			}
			reqJson, _ := protojson.Marshal(req)
			logger.Printf("读取请求 %s %s\n", udpAddr, reqJson)
			//wg.Done()

			ticker := time.NewTicker(2 * time.Second)
			go func() {
				for {
					<-ticker.C
					response := &rpc.RelayMediaResponse{
						ConnectionId: string(livekit.GetMediaRelayConnectionID(req.StartRequest.RoomName, req.StartRequest.TrackId)),
						Message: &rpc.MediaResponse{
							Message: &rpc.MediaResponse_StartResponse{
								StartResponse: &rpc.StartResponse{
									Msg: "success" + req.StartRequest.SubscriberIdentity,
								},
							},
						},
					}
					responseBytes, _ := encodeProto(response)
					remoteConn.WriteToUDP(responseBytes, udpAddr)
				}
			}()
		}
	}()
	logger.Println("UDP服务端启动成功")

	//localAddr5, _ := net.ResolveUDPAddr("udp", "*************:5105")
	//localAddr6, _ := net.ResolveUDPAddr("udp", "*************:5106")
	time.Sleep(2 * time.Second)
	source := NewMediaRelaySource(&routing.LocalNodeImpl{})

	// 王五
	mediaSource5, err := source.GetRelayMediaSource(&rpc.StartRequest{
		RoomName:           "测试房间",
		SubscriberIdentity: "王五",
		PublisherNodeId:    "remote_node",
		PublisherNodeIp:    "*************",
		PublisherIdentity:  "张三",
		TrackId:            "track_id5",
	})
	if err != nil {
		logger.Println(err)
	}
	go func() {
		for read := range mediaSource5.ReadChan() {
			response := read.(*rpc.RelayMediaResponse)
			responseJson, _ := protojson.Marshal(response)
			logger.Printf("王五收到响应 %s\n", responseJson)
		}
	}()

	// 赵六
	mediaSource6, err := source.GetRelayMediaSource(&rpc.StartRequest{
		RoomName:           "测试房间",
		SubscriberIdentity: "赵六",
		PublisherNodeId:    "remote_node",
		PublisherNodeIp:    "*************",
		PublisherIdentity:  "张三",
		TrackId:            "track_id6",
	})
	if err != nil {
		logger.Println(err)
	}
	go func() {
		for read := range mediaSource6.ReadChan() {
			response := read.(*rpc.RelayMediaResponse)
			responseJson, _ := protojson.Marshal(response)
			logger.Printf("赵六收到响应 %s\n", responseJson)
		}
	}()

	wg.Wait()
}
