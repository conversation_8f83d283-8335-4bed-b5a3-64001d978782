// Code generated by counterfeiter. DO NOT EDIT.
package telemetryfakes

import (
	"context"
	"sync"

	"github.com/livekit/livekit-server/pkg/telemetry"
	"github.com/livekit/protocol/livekit"
	"github.com/livekit/protocol/observability/roomobs"
)

type FakeAnalyticsService struct {
	RoomProjectReporterStub        func(context.Context) roomobs.ProjectReporter
	roomProjectReporterMutex       sync.RWMutex
	roomProjectReporterArgsForCall []struct {
		arg1 context.Context
	}
	roomProjectReporterReturns struct {
		result1 roomobs.ProjectReporter
	}
	roomProjectReporterReturnsOnCall map[int]struct {
		result1 roomobs.ProjectReporter
	}
	SendEventStub        func(context.Context, *livekit.AnalyticsEvent)
	sendEventMutex       sync.RWMutex
	sendEventArgsForCall []struct {
		arg1 context.Context
		arg2 *livekit.AnalyticsEvent
	}
	SendNodeRoomStatesStub        func(context.Context, *livekit.AnalyticsNodeRooms)
	sendNodeRoomStatesMutex       sync.RWMutex
	sendNodeRoomStatesArgsForCall []struct {
		arg1 context.Context
		arg2 *livekit.AnalyticsNodeRooms
	}
	SendStatsStub        func(context.Context, []*livekit.AnalyticsStat)
	sendStatsMutex       sync.RWMutex
	sendStatsArgsForCall []struct {
		arg1 context.Context
		arg2 []*livekit.AnalyticsStat
	}
	invocations      map[string][][]interface{}
	invocationsMutex sync.RWMutex
}

func (fake *FakeAnalyticsService) RoomProjectReporter(arg1 context.Context) roomobs.ProjectReporter {
	fake.roomProjectReporterMutex.Lock()
	ret, specificReturn := fake.roomProjectReporterReturnsOnCall[len(fake.roomProjectReporterArgsForCall)]
	fake.roomProjectReporterArgsForCall = append(fake.roomProjectReporterArgsForCall, struct {
		arg1 context.Context
	}{arg1})
	stub := fake.RoomProjectReporterStub
	fakeReturns := fake.roomProjectReporterReturns
	fake.recordInvocation("RoomProjectReporter", []interface{}{arg1})
	fake.roomProjectReporterMutex.Unlock()
	if stub != nil {
		return stub(arg1)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeAnalyticsService) RoomProjectReporterCallCount() int {
	fake.roomProjectReporterMutex.RLock()
	defer fake.roomProjectReporterMutex.RUnlock()
	return len(fake.roomProjectReporterArgsForCall)
}

func (fake *FakeAnalyticsService) RoomProjectReporterCalls(stub func(context.Context) roomobs.ProjectReporter) {
	fake.roomProjectReporterMutex.Lock()
	defer fake.roomProjectReporterMutex.Unlock()
	fake.RoomProjectReporterStub = stub
}

func (fake *FakeAnalyticsService) RoomProjectReporterArgsForCall(i int) context.Context {
	fake.roomProjectReporterMutex.RLock()
	defer fake.roomProjectReporterMutex.RUnlock()
	argsForCall := fake.roomProjectReporterArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeAnalyticsService) RoomProjectReporterReturns(result1 roomobs.ProjectReporter) {
	fake.roomProjectReporterMutex.Lock()
	defer fake.roomProjectReporterMutex.Unlock()
	fake.RoomProjectReporterStub = nil
	fake.roomProjectReporterReturns = struct {
		result1 roomobs.ProjectReporter
	}{result1}
}

func (fake *FakeAnalyticsService) RoomProjectReporterReturnsOnCall(i int, result1 roomobs.ProjectReporter) {
	fake.roomProjectReporterMutex.Lock()
	defer fake.roomProjectReporterMutex.Unlock()
	fake.RoomProjectReporterStub = nil
	if fake.roomProjectReporterReturnsOnCall == nil {
		fake.roomProjectReporterReturnsOnCall = make(map[int]struct {
			result1 roomobs.ProjectReporter
		})
	}
	fake.roomProjectReporterReturnsOnCall[i] = struct {
		result1 roomobs.ProjectReporter
	}{result1}
}

func (fake *FakeAnalyticsService) SendEvent(arg1 context.Context, arg2 *livekit.AnalyticsEvent) {
	fake.sendEventMutex.Lock()
	fake.sendEventArgsForCall = append(fake.sendEventArgsForCall, struct {
		arg1 context.Context
		arg2 *livekit.AnalyticsEvent
	}{arg1, arg2})
	stub := fake.SendEventStub
	fake.recordInvocation("SendEvent", []interface{}{arg1, arg2})
	fake.sendEventMutex.Unlock()
	if stub != nil {
		fake.SendEventStub(arg1, arg2)
	}
}

func (fake *FakeAnalyticsService) SendEventCallCount() int {
	fake.sendEventMutex.RLock()
	defer fake.sendEventMutex.RUnlock()
	return len(fake.sendEventArgsForCall)
}

func (fake *FakeAnalyticsService) SendEventCalls(stub func(context.Context, *livekit.AnalyticsEvent)) {
	fake.sendEventMutex.Lock()
	defer fake.sendEventMutex.Unlock()
	fake.SendEventStub = stub
}

func (fake *FakeAnalyticsService) SendEventArgsForCall(i int) (context.Context, *livekit.AnalyticsEvent) {
	fake.sendEventMutex.RLock()
	defer fake.sendEventMutex.RUnlock()
	argsForCall := fake.sendEventArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeAnalyticsService) SendNodeRoomStates(arg1 context.Context, arg2 *livekit.AnalyticsNodeRooms) {
	fake.sendNodeRoomStatesMutex.Lock()
	fake.sendNodeRoomStatesArgsForCall = append(fake.sendNodeRoomStatesArgsForCall, struct {
		arg1 context.Context
		arg2 *livekit.AnalyticsNodeRooms
	}{arg1, arg2})
	stub := fake.SendNodeRoomStatesStub
	fake.recordInvocation("SendNodeRoomStates", []interface{}{arg1, arg2})
	fake.sendNodeRoomStatesMutex.Unlock()
	if stub != nil {
		fake.SendNodeRoomStatesStub(arg1, arg2)
	}
}

func (fake *FakeAnalyticsService) SendNodeRoomStatesCallCount() int {
	fake.sendNodeRoomStatesMutex.RLock()
	defer fake.sendNodeRoomStatesMutex.RUnlock()
	return len(fake.sendNodeRoomStatesArgsForCall)
}

func (fake *FakeAnalyticsService) SendNodeRoomStatesCalls(stub func(context.Context, *livekit.AnalyticsNodeRooms)) {
	fake.sendNodeRoomStatesMutex.Lock()
	defer fake.sendNodeRoomStatesMutex.Unlock()
	fake.SendNodeRoomStatesStub = stub
}

func (fake *FakeAnalyticsService) SendNodeRoomStatesArgsForCall(i int) (context.Context, *livekit.AnalyticsNodeRooms) {
	fake.sendNodeRoomStatesMutex.RLock()
	defer fake.sendNodeRoomStatesMutex.RUnlock()
	argsForCall := fake.sendNodeRoomStatesArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeAnalyticsService) SendStats(arg1 context.Context, arg2 []*livekit.AnalyticsStat) {
	var arg2Copy []*livekit.AnalyticsStat
	if arg2 != nil {
		arg2Copy = make([]*livekit.AnalyticsStat, len(arg2))
		copy(arg2Copy, arg2)
	}
	fake.sendStatsMutex.Lock()
	fake.sendStatsArgsForCall = append(fake.sendStatsArgsForCall, struct {
		arg1 context.Context
		arg2 []*livekit.AnalyticsStat
	}{arg1, arg2Copy})
	stub := fake.SendStatsStub
	fake.recordInvocation("SendStats", []interface{}{arg1, arg2Copy})
	fake.sendStatsMutex.Unlock()
	if stub != nil {
		fake.SendStatsStub(arg1, arg2)
	}
}

func (fake *FakeAnalyticsService) SendStatsCallCount() int {
	fake.sendStatsMutex.RLock()
	defer fake.sendStatsMutex.RUnlock()
	return len(fake.sendStatsArgsForCall)
}

func (fake *FakeAnalyticsService) SendStatsCalls(stub func(context.Context, []*livekit.AnalyticsStat)) {
	fake.sendStatsMutex.Lock()
	defer fake.sendStatsMutex.Unlock()
	fake.SendStatsStub = stub
}

func (fake *FakeAnalyticsService) SendStatsArgsForCall(i int) (context.Context, []*livekit.AnalyticsStat) {
	fake.sendStatsMutex.RLock()
	defer fake.sendStatsMutex.RUnlock()
	argsForCall := fake.sendStatsArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeAnalyticsService) Invocations() map[string][][]interface{} {
	fake.invocationsMutex.RLock()
	defer fake.invocationsMutex.RUnlock()
	fake.roomProjectReporterMutex.RLock()
	defer fake.roomProjectReporterMutex.RUnlock()
	fake.sendEventMutex.RLock()
	defer fake.sendEventMutex.RUnlock()
	fake.sendNodeRoomStatesMutex.RLock()
	defer fake.sendNodeRoomStatesMutex.RUnlock()
	fake.sendStatsMutex.RLock()
	defer fake.sendStatsMutex.RUnlock()
	copiedInvocations := map[string][][]interface{}{}
	for key, value := range fake.invocations {
		copiedInvocations[key] = value
	}
	return copiedInvocations
}

func (fake *FakeAnalyticsService) recordInvocation(key string, args []interface{}) {
	fake.invocationsMutex.Lock()
	defer fake.invocationsMutex.Unlock()
	if fake.invocations == nil {
		fake.invocations = map[string][][]interface{}{}
	}
	if fake.invocations[key] == nil {
		fake.invocations[key] = [][]interface{}{}
	}
	fake.invocations[key] = append(fake.invocations[key], args)
}

var _ telemetry.AnalyticsService = new(FakeAnalyticsService)
