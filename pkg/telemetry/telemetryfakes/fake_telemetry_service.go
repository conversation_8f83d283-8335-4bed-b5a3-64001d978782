// Code generated by counterfeiter. DO NOT EDIT.
package telemetryfakes

import (
	"context"
	"sync"

	"github.com/livekit/livekit-server/pkg/sfu/mime"
	"github.com/livekit/livekit-server/pkg/telemetry"
	"github.com/livekit/protocol/livekit"
	"github.com/livekit/protocol/observability/roomobs"
)

type FakeTelemetryService struct {
	APICallStub        func(context.Context, *livekit.APICallInfo)
	aPICallMutex       sync.RWMutex
	aPICallArgsForCall []struct {
		arg1 context.Context
		arg2 *livekit.APICallInfo
	}
	EgressEndedStub        func(context.Context, *livekit.EgressInfo)
	egressEndedMutex       sync.RWMutex
	egressEndedArgsForCall []struct {
		arg1 context.Context
		arg2 *livekit.EgressInfo
	}
	EgressStartedStub        func(context.Context, *livekit.EgressInfo)
	egressStartedMutex       sync.RWMutex
	egressStartedArgsForCall []struct {
		arg1 context.Context
		arg2 *livekit.EgressInfo
	}
	EgressUpdatedStub        func(context.Context, *livekit.EgressInfo)
	egressUpdatedMutex       sync.RWMutex
	egressUpdatedArgsForCall []struct {
		arg1 context.Context
		arg2 *livekit.EgressInfo
	}
	FlushStatsStub        func()
	flushStatsMutex       sync.RWMutex
	flushStatsArgsForCall []struct {
	}
	IngressCreatedStub        func(context.Context, *livekit.IngressInfo)
	ingressCreatedMutex       sync.RWMutex
	ingressCreatedArgsForCall []struct {
		arg1 context.Context
		arg2 *livekit.IngressInfo
	}
	IngressDeletedStub        func(context.Context, *livekit.IngressInfo)
	ingressDeletedMutex       sync.RWMutex
	ingressDeletedArgsForCall []struct {
		arg1 context.Context
		arg2 *livekit.IngressInfo
	}
	IngressEndedStub        func(context.Context, *livekit.IngressInfo)
	ingressEndedMutex       sync.RWMutex
	ingressEndedArgsForCall []struct {
		arg1 context.Context
		arg2 *livekit.IngressInfo
	}
	IngressStartedStub        func(context.Context, *livekit.IngressInfo)
	ingressStartedMutex       sync.RWMutex
	ingressStartedArgsForCall []struct {
		arg1 context.Context
		arg2 *livekit.IngressInfo
	}
	IngressUpdatedStub        func(context.Context, *livekit.IngressInfo)
	ingressUpdatedMutex       sync.RWMutex
	ingressUpdatedArgsForCall []struct {
		arg1 context.Context
		arg2 *livekit.IngressInfo
	}
	LocalRoomStateStub        func(context.Context, *livekit.AnalyticsNodeRooms)
	localRoomStateMutex       sync.RWMutex
	localRoomStateArgsForCall []struct {
		arg1 context.Context
		arg2 *livekit.AnalyticsNodeRooms
	}
	NotifyEgressEventStub        func(context.Context, string, *livekit.EgressInfo)
	notifyEgressEventMutex       sync.RWMutex
	notifyEgressEventArgsForCall []struct {
		arg1 context.Context
		arg2 string
		arg3 *livekit.EgressInfo
	}
	ParticipantActiveStub        func(context.Context, *livekit.Room, *livekit.ParticipantInfo, *livekit.AnalyticsClientMeta, bool)
	participantActiveMutex       sync.RWMutex
	participantActiveArgsForCall []struct {
		arg1 context.Context
		arg2 *livekit.Room
		arg3 *livekit.ParticipantInfo
		arg4 *livekit.AnalyticsClientMeta
		arg5 bool
	}
	ParticipantJoinedStub        func(context.Context, *livekit.Room, *livekit.ParticipantInfo, *livekit.ClientInfo, *livekit.AnalyticsClientMeta, bool)
	participantJoinedMutex       sync.RWMutex
	participantJoinedArgsForCall []struct {
		arg1 context.Context
		arg2 *livekit.Room
		arg3 *livekit.ParticipantInfo
		arg4 *livekit.ClientInfo
		arg5 *livekit.AnalyticsClientMeta
		arg6 bool
	}
	ParticipantLeftStub        func(context.Context, *livekit.Room, *livekit.ParticipantInfo, bool)
	participantLeftMutex       sync.RWMutex
	participantLeftArgsForCall []struct {
		arg1 context.Context
		arg2 *livekit.Room
		arg3 *livekit.ParticipantInfo
		arg4 bool
	}
	ParticipantResumedStub        func(context.Context, *livekit.Room, *livekit.ParticipantInfo, livekit.NodeID, livekit.ReconnectReason)
	participantResumedMutex       sync.RWMutex
	participantResumedArgsForCall []struct {
		arg1 context.Context
		arg2 *livekit.Room
		arg3 *livekit.ParticipantInfo
		arg4 livekit.NodeID
		arg5 livekit.ReconnectReason
	}
	ReportStub        func(context.Context, *livekit.ReportInfo)
	reportMutex       sync.RWMutex
	reportArgsForCall []struct {
		arg1 context.Context
		arg2 *livekit.ReportInfo
	}
	RoomEndedStub        func(context.Context, *livekit.Room)
	roomEndedMutex       sync.RWMutex
	roomEndedArgsForCall []struct {
		arg1 context.Context
		arg2 *livekit.Room
	}
	RoomProjectReporterStub        func(context.Context) roomobs.ProjectReporter
	roomProjectReporterMutex       sync.RWMutex
	roomProjectReporterArgsForCall []struct {
		arg1 context.Context
	}
	roomProjectReporterReturns struct {
		result1 roomobs.ProjectReporter
	}
	roomProjectReporterReturnsOnCall map[int]struct {
		result1 roomobs.ProjectReporter
	}
	RoomStartedStub        func(context.Context, *livekit.Room)
	roomStartedMutex       sync.RWMutex
	roomStartedArgsForCall []struct {
		arg1 context.Context
		arg2 *livekit.Room
	}
	SendEventStub        func(context.Context, *livekit.AnalyticsEvent)
	sendEventMutex       sync.RWMutex
	sendEventArgsForCall []struct {
		arg1 context.Context
		arg2 *livekit.AnalyticsEvent
	}
	SendNodeRoomStatesStub        func(context.Context, *livekit.AnalyticsNodeRooms)
	sendNodeRoomStatesMutex       sync.RWMutex
	sendNodeRoomStatesArgsForCall []struct {
		arg1 context.Context
		arg2 *livekit.AnalyticsNodeRooms
	}
	SendStatsStub        func(context.Context, []*livekit.AnalyticsStat)
	sendStatsMutex       sync.RWMutex
	sendStatsArgsForCall []struct {
		arg1 context.Context
		arg2 []*livekit.AnalyticsStat
	}
	TrackMaxSubscribedVideoQualityStub        func(context.Context, livekit.ParticipantID, *livekit.TrackInfo, mime.MimeType, livekit.VideoQuality)
	trackMaxSubscribedVideoQualityMutex       sync.RWMutex
	trackMaxSubscribedVideoQualityArgsForCall []struct {
		arg1 context.Context
		arg2 livekit.ParticipantID
		arg3 *livekit.TrackInfo
		arg4 mime.MimeType
		arg5 livekit.VideoQuality
	}
	TrackMutedStub        func(context.Context, livekit.ParticipantID, *livekit.TrackInfo)
	trackMutedMutex       sync.RWMutex
	trackMutedArgsForCall []struct {
		arg1 context.Context
		arg2 livekit.ParticipantID
		arg3 *livekit.TrackInfo
	}
	TrackPublishRTPStatsStub        func(context.Context, livekit.ParticipantID, livekit.TrackID, mime.MimeType, int, *livekit.RTPStats)
	trackPublishRTPStatsMutex       sync.RWMutex
	trackPublishRTPStatsArgsForCall []struct {
		arg1 context.Context
		arg2 livekit.ParticipantID
		arg3 livekit.TrackID
		arg4 mime.MimeType
		arg5 int
		arg6 *livekit.RTPStats
	}
	TrackPublishRequestedStub        func(context.Context, livekit.ParticipantID, livekit.ParticipantIdentity, *livekit.TrackInfo)
	trackPublishRequestedMutex       sync.RWMutex
	trackPublishRequestedArgsForCall []struct {
		arg1 context.Context
		arg2 livekit.ParticipantID
		arg3 livekit.ParticipantIdentity
		arg4 *livekit.TrackInfo
	}
	TrackPublishedStub        func(context.Context, livekit.ParticipantID, livekit.ParticipantIdentity, *livekit.TrackInfo)
	trackPublishedMutex       sync.RWMutex
	trackPublishedArgsForCall []struct {
		arg1 context.Context
		arg2 livekit.ParticipantID
		arg3 livekit.ParticipantIdentity
		arg4 *livekit.TrackInfo
	}
	TrackPublishedUpdateStub        func(context.Context, livekit.ParticipantID, *livekit.TrackInfo)
	trackPublishedUpdateMutex       sync.RWMutex
	trackPublishedUpdateArgsForCall []struct {
		arg1 context.Context
		arg2 livekit.ParticipantID
		arg3 *livekit.TrackInfo
	}
	TrackStatsStub        func(telemetry.StatsKey, *livekit.AnalyticsStat)
	trackStatsMutex       sync.RWMutex
	trackStatsArgsForCall []struct {
		arg1 telemetry.StatsKey
		arg2 *livekit.AnalyticsStat
	}
	TrackSubscribeFailedStub        func(context.Context, livekit.ParticipantID, livekit.TrackID, error, bool)
	trackSubscribeFailedMutex       sync.RWMutex
	trackSubscribeFailedArgsForCall []struct {
		arg1 context.Context
		arg2 livekit.ParticipantID
		arg3 livekit.TrackID
		arg4 error
		arg5 bool
	}
	TrackSubscribeRTPStatsStub        func(context.Context, livekit.ParticipantID, livekit.TrackID, mime.MimeType, *livekit.RTPStats)
	trackSubscribeRTPStatsMutex       sync.RWMutex
	trackSubscribeRTPStatsArgsForCall []struct {
		arg1 context.Context
		arg2 livekit.ParticipantID
		arg3 livekit.TrackID
		arg4 mime.MimeType
		arg5 *livekit.RTPStats
	}
	TrackSubscribeRequestedStub        func(context.Context, livekit.ParticipantID, *livekit.TrackInfo)
	trackSubscribeRequestedMutex       sync.RWMutex
	trackSubscribeRequestedArgsForCall []struct {
		arg1 context.Context
		arg2 livekit.ParticipantID
		arg3 *livekit.TrackInfo
	}
	TrackSubscribedStub        func(context.Context, livekit.ParticipantID, *livekit.TrackInfo, *livekit.ParticipantInfo, bool)
	trackSubscribedMutex       sync.RWMutex
	trackSubscribedArgsForCall []struct {
		arg1 context.Context
		arg2 livekit.ParticipantID
		arg3 *livekit.TrackInfo
		arg4 *livekit.ParticipantInfo
		arg5 bool
	}
	TrackUnmutedStub        func(context.Context, livekit.ParticipantID, *livekit.TrackInfo)
	trackUnmutedMutex       sync.RWMutex
	trackUnmutedArgsForCall []struct {
		arg1 context.Context
		arg2 livekit.ParticipantID
		arg3 *livekit.TrackInfo
	}
	TrackUnpublishedStub        func(context.Context, livekit.ParticipantID, livekit.ParticipantIdentity, *livekit.TrackInfo, bool)
	trackUnpublishedMutex       sync.RWMutex
	trackUnpublishedArgsForCall []struct {
		arg1 context.Context
		arg2 livekit.ParticipantID
		arg3 livekit.ParticipantIdentity
		arg4 *livekit.TrackInfo
		arg5 bool
	}
	TrackUnsubscribedStub        func(context.Context, livekit.ParticipantID, *livekit.TrackInfo, bool)
	trackUnsubscribedMutex       sync.RWMutex
	trackUnsubscribedArgsForCall []struct {
		arg1 context.Context
		arg2 livekit.ParticipantID
		arg3 *livekit.TrackInfo
		arg4 bool
	}
	WebhookStub        func(context.Context, *livekit.WebhookInfo)
	webhookMutex       sync.RWMutex
	webhookArgsForCall []struct {
		arg1 context.Context
		arg2 *livekit.WebhookInfo
	}
	invocations      map[string][][]interface{}
	invocationsMutex sync.RWMutex
}

func (fake *FakeTelemetryService) APICall(arg1 context.Context, arg2 *livekit.APICallInfo) {
	fake.aPICallMutex.Lock()
	fake.aPICallArgsForCall = append(fake.aPICallArgsForCall, struct {
		arg1 context.Context
		arg2 *livekit.APICallInfo
	}{arg1, arg2})
	stub := fake.APICallStub
	fake.recordInvocation("APICall", []interface{}{arg1, arg2})
	fake.aPICallMutex.Unlock()
	if stub != nil {
		fake.APICallStub(arg1, arg2)
	}
}

func (fake *FakeTelemetryService) APICallCallCount() int {
	fake.aPICallMutex.RLock()
	defer fake.aPICallMutex.RUnlock()
	return len(fake.aPICallArgsForCall)
}

func (fake *FakeTelemetryService) APICallCalls(stub func(context.Context, *livekit.APICallInfo)) {
	fake.aPICallMutex.Lock()
	defer fake.aPICallMutex.Unlock()
	fake.APICallStub = stub
}

func (fake *FakeTelemetryService) APICallArgsForCall(i int) (context.Context, *livekit.APICallInfo) {
	fake.aPICallMutex.RLock()
	defer fake.aPICallMutex.RUnlock()
	argsForCall := fake.aPICallArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeTelemetryService) EgressEnded(arg1 context.Context, arg2 *livekit.EgressInfo) {
	fake.egressEndedMutex.Lock()
	fake.egressEndedArgsForCall = append(fake.egressEndedArgsForCall, struct {
		arg1 context.Context
		arg2 *livekit.EgressInfo
	}{arg1, arg2})
	stub := fake.EgressEndedStub
	fake.recordInvocation("EgressEnded", []interface{}{arg1, arg2})
	fake.egressEndedMutex.Unlock()
	if stub != nil {
		fake.EgressEndedStub(arg1, arg2)
	}
}

func (fake *FakeTelemetryService) EgressEndedCallCount() int {
	fake.egressEndedMutex.RLock()
	defer fake.egressEndedMutex.RUnlock()
	return len(fake.egressEndedArgsForCall)
}

func (fake *FakeTelemetryService) EgressEndedCalls(stub func(context.Context, *livekit.EgressInfo)) {
	fake.egressEndedMutex.Lock()
	defer fake.egressEndedMutex.Unlock()
	fake.EgressEndedStub = stub
}

func (fake *FakeTelemetryService) EgressEndedArgsForCall(i int) (context.Context, *livekit.EgressInfo) {
	fake.egressEndedMutex.RLock()
	defer fake.egressEndedMutex.RUnlock()
	argsForCall := fake.egressEndedArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeTelemetryService) EgressStarted(arg1 context.Context, arg2 *livekit.EgressInfo) {
	fake.egressStartedMutex.Lock()
	fake.egressStartedArgsForCall = append(fake.egressStartedArgsForCall, struct {
		arg1 context.Context
		arg2 *livekit.EgressInfo
	}{arg1, arg2})
	stub := fake.EgressStartedStub
	fake.recordInvocation("EgressStarted", []interface{}{arg1, arg2})
	fake.egressStartedMutex.Unlock()
	if stub != nil {
		fake.EgressStartedStub(arg1, arg2)
	}
}

func (fake *FakeTelemetryService) EgressStartedCallCount() int {
	fake.egressStartedMutex.RLock()
	defer fake.egressStartedMutex.RUnlock()
	return len(fake.egressStartedArgsForCall)
}

func (fake *FakeTelemetryService) EgressStartedCalls(stub func(context.Context, *livekit.EgressInfo)) {
	fake.egressStartedMutex.Lock()
	defer fake.egressStartedMutex.Unlock()
	fake.EgressStartedStub = stub
}

func (fake *FakeTelemetryService) EgressStartedArgsForCall(i int) (context.Context, *livekit.EgressInfo) {
	fake.egressStartedMutex.RLock()
	defer fake.egressStartedMutex.RUnlock()
	argsForCall := fake.egressStartedArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeTelemetryService) EgressUpdated(arg1 context.Context, arg2 *livekit.EgressInfo) {
	fake.egressUpdatedMutex.Lock()
	fake.egressUpdatedArgsForCall = append(fake.egressUpdatedArgsForCall, struct {
		arg1 context.Context
		arg2 *livekit.EgressInfo
	}{arg1, arg2})
	stub := fake.EgressUpdatedStub
	fake.recordInvocation("EgressUpdated", []interface{}{arg1, arg2})
	fake.egressUpdatedMutex.Unlock()
	if stub != nil {
		fake.EgressUpdatedStub(arg1, arg2)
	}
}

func (fake *FakeTelemetryService) EgressUpdatedCallCount() int {
	fake.egressUpdatedMutex.RLock()
	defer fake.egressUpdatedMutex.RUnlock()
	return len(fake.egressUpdatedArgsForCall)
}

func (fake *FakeTelemetryService) EgressUpdatedCalls(stub func(context.Context, *livekit.EgressInfo)) {
	fake.egressUpdatedMutex.Lock()
	defer fake.egressUpdatedMutex.Unlock()
	fake.EgressUpdatedStub = stub
}

func (fake *FakeTelemetryService) EgressUpdatedArgsForCall(i int) (context.Context, *livekit.EgressInfo) {
	fake.egressUpdatedMutex.RLock()
	defer fake.egressUpdatedMutex.RUnlock()
	argsForCall := fake.egressUpdatedArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeTelemetryService) FlushStats() {
	fake.flushStatsMutex.Lock()
	fake.flushStatsArgsForCall = append(fake.flushStatsArgsForCall, struct {
	}{})
	stub := fake.FlushStatsStub
	fake.recordInvocation("FlushStats", []interface{}{})
	fake.flushStatsMutex.Unlock()
	if stub != nil {
		fake.FlushStatsStub()
	}
}

func (fake *FakeTelemetryService) FlushStatsCallCount() int {
	fake.flushStatsMutex.RLock()
	defer fake.flushStatsMutex.RUnlock()
	return len(fake.flushStatsArgsForCall)
}

func (fake *FakeTelemetryService) FlushStatsCalls(stub func()) {
	fake.flushStatsMutex.Lock()
	defer fake.flushStatsMutex.Unlock()
	fake.FlushStatsStub = stub
}

func (fake *FakeTelemetryService) IngressCreated(arg1 context.Context, arg2 *livekit.IngressInfo) {
	fake.ingressCreatedMutex.Lock()
	fake.ingressCreatedArgsForCall = append(fake.ingressCreatedArgsForCall, struct {
		arg1 context.Context
		arg2 *livekit.IngressInfo
	}{arg1, arg2})
	stub := fake.IngressCreatedStub
	fake.recordInvocation("IngressCreated", []interface{}{arg1, arg2})
	fake.ingressCreatedMutex.Unlock()
	if stub != nil {
		fake.IngressCreatedStub(arg1, arg2)
	}
}

func (fake *FakeTelemetryService) IngressCreatedCallCount() int {
	fake.ingressCreatedMutex.RLock()
	defer fake.ingressCreatedMutex.RUnlock()
	return len(fake.ingressCreatedArgsForCall)
}

func (fake *FakeTelemetryService) IngressCreatedCalls(stub func(context.Context, *livekit.IngressInfo)) {
	fake.ingressCreatedMutex.Lock()
	defer fake.ingressCreatedMutex.Unlock()
	fake.IngressCreatedStub = stub
}

func (fake *FakeTelemetryService) IngressCreatedArgsForCall(i int) (context.Context, *livekit.IngressInfo) {
	fake.ingressCreatedMutex.RLock()
	defer fake.ingressCreatedMutex.RUnlock()
	argsForCall := fake.ingressCreatedArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeTelemetryService) IngressDeleted(arg1 context.Context, arg2 *livekit.IngressInfo) {
	fake.ingressDeletedMutex.Lock()
	fake.ingressDeletedArgsForCall = append(fake.ingressDeletedArgsForCall, struct {
		arg1 context.Context
		arg2 *livekit.IngressInfo
	}{arg1, arg2})
	stub := fake.IngressDeletedStub
	fake.recordInvocation("IngressDeleted", []interface{}{arg1, arg2})
	fake.ingressDeletedMutex.Unlock()
	if stub != nil {
		fake.IngressDeletedStub(arg1, arg2)
	}
}

func (fake *FakeTelemetryService) IngressDeletedCallCount() int {
	fake.ingressDeletedMutex.RLock()
	defer fake.ingressDeletedMutex.RUnlock()
	return len(fake.ingressDeletedArgsForCall)
}

func (fake *FakeTelemetryService) IngressDeletedCalls(stub func(context.Context, *livekit.IngressInfo)) {
	fake.ingressDeletedMutex.Lock()
	defer fake.ingressDeletedMutex.Unlock()
	fake.IngressDeletedStub = stub
}

func (fake *FakeTelemetryService) IngressDeletedArgsForCall(i int) (context.Context, *livekit.IngressInfo) {
	fake.ingressDeletedMutex.RLock()
	defer fake.ingressDeletedMutex.RUnlock()
	argsForCall := fake.ingressDeletedArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeTelemetryService) IngressEnded(arg1 context.Context, arg2 *livekit.IngressInfo) {
	fake.ingressEndedMutex.Lock()
	fake.ingressEndedArgsForCall = append(fake.ingressEndedArgsForCall, struct {
		arg1 context.Context
		arg2 *livekit.IngressInfo
	}{arg1, arg2})
	stub := fake.IngressEndedStub
	fake.recordInvocation("IngressEnded", []interface{}{arg1, arg2})
	fake.ingressEndedMutex.Unlock()
	if stub != nil {
		fake.IngressEndedStub(arg1, arg2)
	}
}

func (fake *FakeTelemetryService) IngressEndedCallCount() int {
	fake.ingressEndedMutex.RLock()
	defer fake.ingressEndedMutex.RUnlock()
	return len(fake.ingressEndedArgsForCall)
}

func (fake *FakeTelemetryService) IngressEndedCalls(stub func(context.Context, *livekit.IngressInfo)) {
	fake.ingressEndedMutex.Lock()
	defer fake.ingressEndedMutex.Unlock()
	fake.IngressEndedStub = stub
}

func (fake *FakeTelemetryService) IngressEndedArgsForCall(i int) (context.Context, *livekit.IngressInfo) {
	fake.ingressEndedMutex.RLock()
	defer fake.ingressEndedMutex.RUnlock()
	argsForCall := fake.ingressEndedArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeTelemetryService) IngressStarted(arg1 context.Context, arg2 *livekit.IngressInfo) {
	fake.ingressStartedMutex.Lock()
	fake.ingressStartedArgsForCall = append(fake.ingressStartedArgsForCall, struct {
		arg1 context.Context
		arg2 *livekit.IngressInfo
	}{arg1, arg2})
	stub := fake.IngressStartedStub
	fake.recordInvocation("IngressStarted", []interface{}{arg1, arg2})
	fake.ingressStartedMutex.Unlock()
	if stub != nil {
		fake.IngressStartedStub(arg1, arg2)
	}
}

func (fake *FakeTelemetryService) IngressStartedCallCount() int {
	fake.ingressStartedMutex.RLock()
	defer fake.ingressStartedMutex.RUnlock()
	return len(fake.ingressStartedArgsForCall)
}

func (fake *FakeTelemetryService) IngressStartedCalls(stub func(context.Context, *livekit.IngressInfo)) {
	fake.ingressStartedMutex.Lock()
	defer fake.ingressStartedMutex.Unlock()
	fake.IngressStartedStub = stub
}

func (fake *FakeTelemetryService) IngressStartedArgsForCall(i int) (context.Context, *livekit.IngressInfo) {
	fake.ingressStartedMutex.RLock()
	defer fake.ingressStartedMutex.RUnlock()
	argsForCall := fake.ingressStartedArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeTelemetryService) IngressUpdated(arg1 context.Context, arg2 *livekit.IngressInfo) {
	fake.ingressUpdatedMutex.Lock()
	fake.ingressUpdatedArgsForCall = append(fake.ingressUpdatedArgsForCall, struct {
		arg1 context.Context
		arg2 *livekit.IngressInfo
	}{arg1, arg2})
	stub := fake.IngressUpdatedStub
	fake.recordInvocation("IngressUpdated", []interface{}{arg1, arg2})
	fake.ingressUpdatedMutex.Unlock()
	if stub != nil {
		fake.IngressUpdatedStub(arg1, arg2)
	}
}

func (fake *FakeTelemetryService) IngressUpdatedCallCount() int {
	fake.ingressUpdatedMutex.RLock()
	defer fake.ingressUpdatedMutex.RUnlock()
	return len(fake.ingressUpdatedArgsForCall)
}

func (fake *FakeTelemetryService) IngressUpdatedCalls(stub func(context.Context, *livekit.IngressInfo)) {
	fake.ingressUpdatedMutex.Lock()
	defer fake.ingressUpdatedMutex.Unlock()
	fake.IngressUpdatedStub = stub
}

func (fake *FakeTelemetryService) IngressUpdatedArgsForCall(i int) (context.Context, *livekit.IngressInfo) {
	fake.ingressUpdatedMutex.RLock()
	defer fake.ingressUpdatedMutex.RUnlock()
	argsForCall := fake.ingressUpdatedArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeTelemetryService) LocalRoomState(arg1 context.Context, arg2 *livekit.AnalyticsNodeRooms) {
	fake.localRoomStateMutex.Lock()
	fake.localRoomStateArgsForCall = append(fake.localRoomStateArgsForCall, struct {
		arg1 context.Context
		arg2 *livekit.AnalyticsNodeRooms
	}{arg1, arg2})
	stub := fake.LocalRoomStateStub
	fake.recordInvocation("LocalRoomState", []interface{}{arg1, arg2})
	fake.localRoomStateMutex.Unlock()
	if stub != nil {
		fake.LocalRoomStateStub(arg1, arg2)
	}
}

func (fake *FakeTelemetryService) LocalRoomStateCallCount() int {
	fake.localRoomStateMutex.RLock()
	defer fake.localRoomStateMutex.RUnlock()
	return len(fake.localRoomStateArgsForCall)
}

func (fake *FakeTelemetryService) LocalRoomStateCalls(stub func(context.Context, *livekit.AnalyticsNodeRooms)) {
	fake.localRoomStateMutex.Lock()
	defer fake.localRoomStateMutex.Unlock()
	fake.LocalRoomStateStub = stub
}

func (fake *FakeTelemetryService) LocalRoomStateArgsForCall(i int) (context.Context, *livekit.AnalyticsNodeRooms) {
	fake.localRoomStateMutex.RLock()
	defer fake.localRoomStateMutex.RUnlock()
	argsForCall := fake.localRoomStateArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeTelemetryService) NotifyEgressEvent(arg1 context.Context, arg2 string, arg3 *livekit.EgressInfo) {
	fake.notifyEgressEventMutex.Lock()
	fake.notifyEgressEventArgsForCall = append(fake.notifyEgressEventArgsForCall, struct {
		arg1 context.Context
		arg2 string
		arg3 *livekit.EgressInfo
	}{arg1, arg2, arg3})
	stub := fake.NotifyEgressEventStub
	fake.recordInvocation("NotifyEgressEvent", []interface{}{arg1, arg2, arg3})
	fake.notifyEgressEventMutex.Unlock()
	if stub != nil {
		fake.NotifyEgressEventStub(arg1, arg2, arg3)
	}
}

func (fake *FakeTelemetryService) NotifyEgressEventCallCount() int {
	fake.notifyEgressEventMutex.RLock()
	defer fake.notifyEgressEventMutex.RUnlock()
	return len(fake.notifyEgressEventArgsForCall)
}

func (fake *FakeTelemetryService) NotifyEgressEventCalls(stub func(context.Context, string, *livekit.EgressInfo)) {
	fake.notifyEgressEventMutex.Lock()
	defer fake.notifyEgressEventMutex.Unlock()
	fake.NotifyEgressEventStub = stub
}

func (fake *FakeTelemetryService) NotifyEgressEventArgsForCall(i int) (context.Context, string, *livekit.EgressInfo) {
	fake.notifyEgressEventMutex.RLock()
	defer fake.notifyEgressEventMutex.RUnlock()
	argsForCall := fake.notifyEgressEventArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeTelemetryService) ParticipantActive(arg1 context.Context, arg2 *livekit.Room, arg3 *livekit.ParticipantInfo, arg4 *livekit.AnalyticsClientMeta, arg5 bool) {
	fake.participantActiveMutex.Lock()
	fake.participantActiveArgsForCall = append(fake.participantActiveArgsForCall, struct {
		arg1 context.Context
		arg2 *livekit.Room
		arg3 *livekit.ParticipantInfo
		arg4 *livekit.AnalyticsClientMeta
		arg5 bool
	}{arg1, arg2, arg3, arg4, arg5})
	stub := fake.ParticipantActiveStub
	fake.recordInvocation("ParticipantActive", []interface{}{arg1, arg2, arg3, arg4, arg5})
	fake.participantActiveMutex.Unlock()
	if stub != nil {
		fake.ParticipantActiveStub(arg1, arg2, arg3, arg4, arg5)
	}
}

func (fake *FakeTelemetryService) ParticipantActiveCallCount() int {
	fake.participantActiveMutex.RLock()
	defer fake.participantActiveMutex.RUnlock()
	return len(fake.participantActiveArgsForCall)
}

func (fake *FakeTelemetryService) ParticipantActiveCalls(stub func(context.Context, *livekit.Room, *livekit.ParticipantInfo, *livekit.AnalyticsClientMeta, bool)) {
	fake.participantActiveMutex.Lock()
	defer fake.participantActiveMutex.Unlock()
	fake.ParticipantActiveStub = stub
}

func (fake *FakeTelemetryService) ParticipantActiveArgsForCall(i int) (context.Context, *livekit.Room, *livekit.ParticipantInfo, *livekit.AnalyticsClientMeta, bool) {
	fake.participantActiveMutex.RLock()
	defer fake.participantActiveMutex.RUnlock()
	argsForCall := fake.participantActiveArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3, argsForCall.arg4, argsForCall.arg5
}

func (fake *FakeTelemetryService) ParticipantJoined(arg1 context.Context, arg2 *livekit.Room, arg3 *livekit.ParticipantInfo, arg4 *livekit.ClientInfo, arg5 *livekit.AnalyticsClientMeta, arg6 bool) {
	fake.participantJoinedMutex.Lock()
	fake.participantJoinedArgsForCall = append(fake.participantJoinedArgsForCall, struct {
		arg1 context.Context
		arg2 *livekit.Room
		arg3 *livekit.ParticipantInfo
		arg4 *livekit.ClientInfo
		arg5 *livekit.AnalyticsClientMeta
		arg6 bool
	}{arg1, arg2, arg3, arg4, arg5, arg6})
	stub := fake.ParticipantJoinedStub
	fake.recordInvocation("ParticipantJoined", []interface{}{arg1, arg2, arg3, arg4, arg5, arg6})
	fake.participantJoinedMutex.Unlock()
	if stub != nil {
		fake.ParticipantJoinedStub(arg1, arg2, arg3, arg4, arg5, arg6)
	}
}

func (fake *FakeTelemetryService) ParticipantJoinedCallCount() int {
	fake.participantJoinedMutex.RLock()
	defer fake.participantJoinedMutex.RUnlock()
	return len(fake.participantJoinedArgsForCall)
}

func (fake *FakeTelemetryService) ParticipantJoinedCalls(stub func(context.Context, *livekit.Room, *livekit.ParticipantInfo, *livekit.ClientInfo, *livekit.AnalyticsClientMeta, bool)) {
	fake.participantJoinedMutex.Lock()
	defer fake.participantJoinedMutex.Unlock()
	fake.ParticipantJoinedStub = stub
}

func (fake *FakeTelemetryService) ParticipantJoinedArgsForCall(i int) (context.Context, *livekit.Room, *livekit.ParticipantInfo, *livekit.ClientInfo, *livekit.AnalyticsClientMeta, bool) {
	fake.participantJoinedMutex.RLock()
	defer fake.participantJoinedMutex.RUnlock()
	argsForCall := fake.participantJoinedArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3, argsForCall.arg4, argsForCall.arg5, argsForCall.arg6
}

func (fake *FakeTelemetryService) ParticipantLeft(arg1 context.Context, arg2 *livekit.Room, arg3 *livekit.ParticipantInfo, arg4 bool) {
	fake.participantLeftMutex.Lock()
	fake.participantLeftArgsForCall = append(fake.participantLeftArgsForCall, struct {
		arg1 context.Context
		arg2 *livekit.Room
		arg3 *livekit.ParticipantInfo
		arg4 bool
	}{arg1, arg2, arg3, arg4})
	stub := fake.ParticipantLeftStub
	fake.recordInvocation("ParticipantLeft", []interface{}{arg1, arg2, arg3, arg4})
	fake.participantLeftMutex.Unlock()
	if stub != nil {
		fake.ParticipantLeftStub(arg1, arg2, arg3, arg4)
	}
}

func (fake *FakeTelemetryService) ParticipantLeftCallCount() int {
	fake.participantLeftMutex.RLock()
	defer fake.participantLeftMutex.RUnlock()
	return len(fake.participantLeftArgsForCall)
}

func (fake *FakeTelemetryService) ParticipantLeftCalls(stub func(context.Context, *livekit.Room, *livekit.ParticipantInfo, bool)) {
	fake.participantLeftMutex.Lock()
	defer fake.participantLeftMutex.Unlock()
	fake.ParticipantLeftStub = stub
}

func (fake *FakeTelemetryService) ParticipantLeftArgsForCall(i int) (context.Context, *livekit.Room, *livekit.ParticipantInfo, bool) {
	fake.participantLeftMutex.RLock()
	defer fake.participantLeftMutex.RUnlock()
	argsForCall := fake.participantLeftArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3, argsForCall.arg4
}

func (fake *FakeTelemetryService) ParticipantResumed(arg1 context.Context, arg2 *livekit.Room, arg3 *livekit.ParticipantInfo, arg4 livekit.NodeID, arg5 livekit.ReconnectReason) {
	fake.participantResumedMutex.Lock()
	fake.participantResumedArgsForCall = append(fake.participantResumedArgsForCall, struct {
		arg1 context.Context
		arg2 *livekit.Room
		arg3 *livekit.ParticipantInfo
		arg4 livekit.NodeID
		arg5 livekit.ReconnectReason
	}{arg1, arg2, arg3, arg4, arg5})
	stub := fake.ParticipantResumedStub
	fake.recordInvocation("ParticipantResumed", []interface{}{arg1, arg2, arg3, arg4, arg5})
	fake.participantResumedMutex.Unlock()
	if stub != nil {
		fake.ParticipantResumedStub(arg1, arg2, arg3, arg4, arg5)
	}
}

func (fake *FakeTelemetryService) ParticipantResumedCallCount() int {
	fake.participantResumedMutex.RLock()
	defer fake.participantResumedMutex.RUnlock()
	return len(fake.participantResumedArgsForCall)
}

func (fake *FakeTelemetryService) ParticipantResumedCalls(stub func(context.Context, *livekit.Room, *livekit.ParticipantInfo, livekit.NodeID, livekit.ReconnectReason)) {
	fake.participantResumedMutex.Lock()
	defer fake.participantResumedMutex.Unlock()
	fake.ParticipantResumedStub = stub
}

func (fake *FakeTelemetryService) ParticipantResumedArgsForCall(i int) (context.Context, *livekit.Room, *livekit.ParticipantInfo, livekit.NodeID, livekit.ReconnectReason) {
	fake.participantResumedMutex.RLock()
	defer fake.participantResumedMutex.RUnlock()
	argsForCall := fake.participantResumedArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3, argsForCall.arg4, argsForCall.arg5
}

func (fake *FakeTelemetryService) Report(arg1 context.Context, arg2 *livekit.ReportInfo) {
	fake.reportMutex.Lock()
	fake.reportArgsForCall = append(fake.reportArgsForCall, struct {
		arg1 context.Context
		arg2 *livekit.ReportInfo
	}{arg1, arg2})
	stub := fake.ReportStub
	fake.recordInvocation("Report", []interface{}{arg1, arg2})
	fake.reportMutex.Unlock()
	if stub != nil {
		fake.ReportStub(arg1, arg2)
	}
}

func (fake *FakeTelemetryService) ReportCallCount() int {
	fake.reportMutex.RLock()
	defer fake.reportMutex.RUnlock()
	return len(fake.reportArgsForCall)
}

func (fake *FakeTelemetryService) ReportCalls(stub func(context.Context, *livekit.ReportInfo)) {
	fake.reportMutex.Lock()
	defer fake.reportMutex.Unlock()
	fake.ReportStub = stub
}

func (fake *FakeTelemetryService) ReportArgsForCall(i int) (context.Context, *livekit.ReportInfo) {
	fake.reportMutex.RLock()
	defer fake.reportMutex.RUnlock()
	argsForCall := fake.reportArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeTelemetryService) RoomEnded(arg1 context.Context, arg2 *livekit.Room) {
	fake.roomEndedMutex.Lock()
	fake.roomEndedArgsForCall = append(fake.roomEndedArgsForCall, struct {
		arg1 context.Context
		arg2 *livekit.Room
	}{arg1, arg2})
	stub := fake.RoomEndedStub
	fake.recordInvocation("RoomEnded", []interface{}{arg1, arg2})
	fake.roomEndedMutex.Unlock()
	if stub != nil {
		fake.RoomEndedStub(arg1, arg2)
	}
}

func (fake *FakeTelemetryService) RoomEndedCallCount() int {
	fake.roomEndedMutex.RLock()
	defer fake.roomEndedMutex.RUnlock()
	return len(fake.roomEndedArgsForCall)
}

func (fake *FakeTelemetryService) RoomEndedCalls(stub func(context.Context, *livekit.Room)) {
	fake.roomEndedMutex.Lock()
	defer fake.roomEndedMutex.Unlock()
	fake.RoomEndedStub = stub
}

func (fake *FakeTelemetryService) RoomEndedArgsForCall(i int) (context.Context, *livekit.Room) {
	fake.roomEndedMutex.RLock()
	defer fake.roomEndedMutex.RUnlock()
	argsForCall := fake.roomEndedArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeTelemetryService) RoomProjectReporter(arg1 context.Context) roomobs.ProjectReporter {
	fake.roomProjectReporterMutex.Lock()
	ret, specificReturn := fake.roomProjectReporterReturnsOnCall[len(fake.roomProjectReporterArgsForCall)]
	fake.roomProjectReporterArgsForCall = append(fake.roomProjectReporterArgsForCall, struct {
		arg1 context.Context
	}{arg1})
	stub := fake.RoomProjectReporterStub
	fakeReturns := fake.roomProjectReporterReturns
	fake.recordInvocation("RoomProjectReporter", []interface{}{arg1})
	fake.roomProjectReporterMutex.Unlock()
	if stub != nil {
		return stub(arg1)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeTelemetryService) RoomProjectReporterCallCount() int {
	fake.roomProjectReporterMutex.RLock()
	defer fake.roomProjectReporterMutex.RUnlock()
	return len(fake.roomProjectReporterArgsForCall)
}

func (fake *FakeTelemetryService) RoomProjectReporterCalls(stub func(context.Context) roomobs.ProjectReporter) {
	fake.roomProjectReporterMutex.Lock()
	defer fake.roomProjectReporterMutex.Unlock()
	fake.RoomProjectReporterStub = stub
}

func (fake *FakeTelemetryService) RoomProjectReporterArgsForCall(i int) context.Context {
	fake.roomProjectReporterMutex.RLock()
	defer fake.roomProjectReporterMutex.RUnlock()
	argsForCall := fake.roomProjectReporterArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeTelemetryService) RoomProjectReporterReturns(result1 roomobs.ProjectReporter) {
	fake.roomProjectReporterMutex.Lock()
	defer fake.roomProjectReporterMutex.Unlock()
	fake.RoomProjectReporterStub = nil
	fake.roomProjectReporterReturns = struct {
		result1 roomobs.ProjectReporter
	}{result1}
}

func (fake *FakeTelemetryService) RoomProjectReporterReturnsOnCall(i int, result1 roomobs.ProjectReporter) {
	fake.roomProjectReporterMutex.Lock()
	defer fake.roomProjectReporterMutex.Unlock()
	fake.RoomProjectReporterStub = nil
	if fake.roomProjectReporterReturnsOnCall == nil {
		fake.roomProjectReporterReturnsOnCall = make(map[int]struct {
			result1 roomobs.ProjectReporter
		})
	}
	fake.roomProjectReporterReturnsOnCall[i] = struct {
		result1 roomobs.ProjectReporter
	}{result1}
}

func (fake *FakeTelemetryService) RoomStarted(arg1 context.Context, arg2 *livekit.Room) {
	fake.roomStartedMutex.Lock()
	fake.roomStartedArgsForCall = append(fake.roomStartedArgsForCall, struct {
		arg1 context.Context
		arg2 *livekit.Room
	}{arg1, arg2})
	stub := fake.RoomStartedStub
	fake.recordInvocation("RoomStarted", []interface{}{arg1, arg2})
	fake.roomStartedMutex.Unlock()
	if stub != nil {
		fake.RoomStartedStub(arg1, arg2)
	}
}

func (fake *FakeTelemetryService) RoomStartedCallCount() int {
	fake.roomStartedMutex.RLock()
	defer fake.roomStartedMutex.RUnlock()
	return len(fake.roomStartedArgsForCall)
}

func (fake *FakeTelemetryService) RoomStartedCalls(stub func(context.Context, *livekit.Room)) {
	fake.roomStartedMutex.Lock()
	defer fake.roomStartedMutex.Unlock()
	fake.RoomStartedStub = stub
}

func (fake *FakeTelemetryService) RoomStartedArgsForCall(i int) (context.Context, *livekit.Room) {
	fake.roomStartedMutex.RLock()
	defer fake.roomStartedMutex.RUnlock()
	argsForCall := fake.roomStartedArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeTelemetryService) SendEvent(arg1 context.Context, arg2 *livekit.AnalyticsEvent) {
	fake.sendEventMutex.Lock()
	fake.sendEventArgsForCall = append(fake.sendEventArgsForCall, struct {
		arg1 context.Context
		arg2 *livekit.AnalyticsEvent
	}{arg1, arg2})
	stub := fake.SendEventStub
	fake.recordInvocation("SendEvent", []interface{}{arg1, arg2})
	fake.sendEventMutex.Unlock()
	if stub != nil {
		fake.SendEventStub(arg1, arg2)
	}
}

func (fake *FakeTelemetryService) SendEventCallCount() int {
	fake.sendEventMutex.RLock()
	defer fake.sendEventMutex.RUnlock()
	return len(fake.sendEventArgsForCall)
}

func (fake *FakeTelemetryService) SendEventCalls(stub func(context.Context, *livekit.AnalyticsEvent)) {
	fake.sendEventMutex.Lock()
	defer fake.sendEventMutex.Unlock()
	fake.SendEventStub = stub
}

func (fake *FakeTelemetryService) SendEventArgsForCall(i int) (context.Context, *livekit.AnalyticsEvent) {
	fake.sendEventMutex.RLock()
	defer fake.sendEventMutex.RUnlock()
	argsForCall := fake.sendEventArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeTelemetryService) SendNodeRoomStates(arg1 context.Context, arg2 *livekit.AnalyticsNodeRooms) {
	fake.sendNodeRoomStatesMutex.Lock()
	fake.sendNodeRoomStatesArgsForCall = append(fake.sendNodeRoomStatesArgsForCall, struct {
		arg1 context.Context
		arg2 *livekit.AnalyticsNodeRooms
	}{arg1, arg2})
	stub := fake.SendNodeRoomStatesStub
	fake.recordInvocation("SendNodeRoomStates", []interface{}{arg1, arg2})
	fake.sendNodeRoomStatesMutex.Unlock()
	if stub != nil {
		fake.SendNodeRoomStatesStub(arg1, arg2)
	}
}

func (fake *FakeTelemetryService) SendNodeRoomStatesCallCount() int {
	fake.sendNodeRoomStatesMutex.RLock()
	defer fake.sendNodeRoomStatesMutex.RUnlock()
	return len(fake.sendNodeRoomStatesArgsForCall)
}

func (fake *FakeTelemetryService) SendNodeRoomStatesCalls(stub func(context.Context, *livekit.AnalyticsNodeRooms)) {
	fake.sendNodeRoomStatesMutex.Lock()
	defer fake.sendNodeRoomStatesMutex.Unlock()
	fake.SendNodeRoomStatesStub = stub
}

func (fake *FakeTelemetryService) SendNodeRoomStatesArgsForCall(i int) (context.Context, *livekit.AnalyticsNodeRooms) {
	fake.sendNodeRoomStatesMutex.RLock()
	defer fake.sendNodeRoomStatesMutex.RUnlock()
	argsForCall := fake.sendNodeRoomStatesArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeTelemetryService) SendStats(arg1 context.Context, arg2 []*livekit.AnalyticsStat) {
	var arg2Copy []*livekit.AnalyticsStat
	if arg2 != nil {
		arg2Copy = make([]*livekit.AnalyticsStat, len(arg2))
		copy(arg2Copy, arg2)
	}
	fake.sendStatsMutex.Lock()
	fake.sendStatsArgsForCall = append(fake.sendStatsArgsForCall, struct {
		arg1 context.Context
		arg2 []*livekit.AnalyticsStat
	}{arg1, arg2Copy})
	stub := fake.SendStatsStub
	fake.recordInvocation("SendStats", []interface{}{arg1, arg2Copy})
	fake.sendStatsMutex.Unlock()
	if stub != nil {
		fake.SendStatsStub(arg1, arg2)
	}
}

func (fake *FakeTelemetryService) SendStatsCallCount() int {
	fake.sendStatsMutex.RLock()
	defer fake.sendStatsMutex.RUnlock()
	return len(fake.sendStatsArgsForCall)
}

func (fake *FakeTelemetryService) SendStatsCalls(stub func(context.Context, []*livekit.AnalyticsStat)) {
	fake.sendStatsMutex.Lock()
	defer fake.sendStatsMutex.Unlock()
	fake.SendStatsStub = stub
}

func (fake *FakeTelemetryService) SendStatsArgsForCall(i int) (context.Context, []*livekit.AnalyticsStat) {
	fake.sendStatsMutex.RLock()
	defer fake.sendStatsMutex.RUnlock()
	argsForCall := fake.sendStatsArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeTelemetryService) TrackMaxSubscribedVideoQuality(arg1 context.Context, arg2 livekit.ParticipantID, arg3 *livekit.TrackInfo, arg4 mime.MimeType, arg5 livekit.VideoQuality) {
	fake.trackMaxSubscribedVideoQualityMutex.Lock()
	fake.trackMaxSubscribedVideoQualityArgsForCall = append(fake.trackMaxSubscribedVideoQualityArgsForCall, struct {
		arg1 context.Context
		arg2 livekit.ParticipantID
		arg3 *livekit.TrackInfo
		arg4 mime.MimeType
		arg5 livekit.VideoQuality
	}{arg1, arg2, arg3, arg4, arg5})
	stub := fake.TrackMaxSubscribedVideoQualityStub
	fake.recordInvocation("TrackMaxSubscribedVideoQuality", []interface{}{arg1, arg2, arg3, arg4, arg5})
	fake.trackMaxSubscribedVideoQualityMutex.Unlock()
	if stub != nil {
		fake.TrackMaxSubscribedVideoQualityStub(arg1, arg2, arg3, arg4, arg5)
	}
}

func (fake *FakeTelemetryService) TrackMaxSubscribedVideoQualityCallCount() int {
	fake.trackMaxSubscribedVideoQualityMutex.RLock()
	defer fake.trackMaxSubscribedVideoQualityMutex.RUnlock()
	return len(fake.trackMaxSubscribedVideoQualityArgsForCall)
}

func (fake *FakeTelemetryService) TrackMaxSubscribedVideoQualityCalls(stub func(context.Context, livekit.ParticipantID, *livekit.TrackInfo, mime.MimeType, livekit.VideoQuality)) {
	fake.trackMaxSubscribedVideoQualityMutex.Lock()
	defer fake.trackMaxSubscribedVideoQualityMutex.Unlock()
	fake.TrackMaxSubscribedVideoQualityStub = stub
}

func (fake *FakeTelemetryService) TrackMaxSubscribedVideoQualityArgsForCall(i int) (context.Context, livekit.ParticipantID, *livekit.TrackInfo, mime.MimeType, livekit.VideoQuality) {
	fake.trackMaxSubscribedVideoQualityMutex.RLock()
	defer fake.trackMaxSubscribedVideoQualityMutex.RUnlock()
	argsForCall := fake.trackMaxSubscribedVideoQualityArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3, argsForCall.arg4, argsForCall.arg5
}

func (fake *FakeTelemetryService) TrackMuted(arg1 context.Context, arg2 livekit.ParticipantID, arg3 *livekit.TrackInfo) {
	fake.trackMutedMutex.Lock()
	fake.trackMutedArgsForCall = append(fake.trackMutedArgsForCall, struct {
		arg1 context.Context
		arg2 livekit.ParticipantID
		arg3 *livekit.TrackInfo
	}{arg1, arg2, arg3})
	stub := fake.TrackMutedStub
	fake.recordInvocation("TrackMuted", []interface{}{arg1, arg2, arg3})
	fake.trackMutedMutex.Unlock()
	if stub != nil {
		fake.TrackMutedStub(arg1, arg2, arg3)
	}
}

func (fake *FakeTelemetryService) TrackMutedCallCount() int {
	fake.trackMutedMutex.RLock()
	defer fake.trackMutedMutex.RUnlock()
	return len(fake.trackMutedArgsForCall)
}

func (fake *FakeTelemetryService) TrackMutedCalls(stub func(context.Context, livekit.ParticipantID, *livekit.TrackInfo)) {
	fake.trackMutedMutex.Lock()
	defer fake.trackMutedMutex.Unlock()
	fake.TrackMutedStub = stub
}

func (fake *FakeTelemetryService) TrackMutedArgsForCall(i int) (context.Context, livekit.ParticipantID, *livekit.TrackInfo) {
	fake.trackMutedMutex.RLock()
	defer fake.trackMutedMutex.RUnlock()
	argsForCall := fake.trackMutedArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeTelemetryService) TrackPublishRTPStats(arg1 context.Context, arg2 livekit.ParticipantID, arg3 livekit.TrackID, arg4 mime.MimeType, arg5 int, arg6 *livekit.RTPStats) {
	fake.trackPublishRTPStatsMutex.Lock()
	fake.trackPublishRTPStatsArgsForCall = append(fake.trackPublishRTPStatsArgsForCall, struct {
		arg1 context.Context
		arg2 livekit.ParticipantID
		arg3 livekit.TrackID
		arg4 mime.MimeType
		arg5 int
		arg6 *livekit.RTPStats
	}{arg1, arg2, arg3, arg4, arg5, arg6})
	stub := fake.TrackPublishRTPStatsStub
	fake.recordInvocation("TrackPublishRTPStats", []interface{}{arg1, arg2, arg3, arg4, arg5, arg6})
	fake.trackPublishRTPStatsMutex.Unlock()
	if stub != nil {
		fake.TrackPublishRTPStatsStub(arg1, arg2, arg3, arg4, arg5, arg6)
	}
}

func (fake *FakeTelemetryService) TrackPublishRTPStatsCallCount() int {
	fake.trackPublishRTPStatsMutex.RLock()
	defer fake.trackPublishRTPStatsMutex.RUnlock()
	return len(fake.trackPublishRTPStatsArgsForCall)
}

func (fake *FakeTelemetryService) TrackPublishRTPStatsCalls(stub func(context.Context, livekit.ParticipantID, livekit.TrackID, mime.MimeType, int, *livekit.RTPStats)) {
	fake.trackPublishRTPStatsMutex.Lock()
	defer fake.trackPublishRTPStatsMutex.Unlock()
	fake.TrackPublishRTPStatsStub = stub
}

func (fake *FakeTelemetryService) TrackPublishRTPStatsArgsForCall(i int) (context.Context, livekit.ParticipantID, livekit.TrackID, mime.MimeType, int, *livekit.RTPStats) {
	fake.trackPublishRTPStatsMutex.RLock()
	defer fake.trackPublishRTPStatsMutex.RUnlock()
	argsForCall := fake.trackPublishRTPStatsArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3, argsForCall.arg4, argsForCall.arg5, argsForCall.arg6
}

func (fake *FakeTelemetryService) TrackPublishRequested(arg1 context.Context, arg2 livekit.ParticipantID, arg3 livekit.ParticipantIdentity, arg4 *livekit.TrackInfo) {
	fake.trackPublishRequestedMutex.Lock()
	fake.trackPublishRequestedArgsForCall = append(fake.trackPublishRequestedArgsForCall, struct {
		arg1 context.Context
		arg2 livekit.ParticipantID
		arg3 livekit.ParticipantIdentity
		arg4 *livekit.TrackInfo
	}{arg1, arg2, arg3, arg4})
	stub := fake.TrackPublishRequestedStub
	fake.recordInvocation("TrackPublishRequested", []interface{}{arg1, arg2, arg3, arg4})
	fake.trackPublishRequestedMutex.Unlock()
	if stub != nil {
		fake.TrackPublishRequestedStub(arg1, arg2, arg3, arg4)
	}
}

func (fake *FakeTelemetryService) TrackPublishRequestedCallCount() int {
	fake.trackPublishRequestedMutex.RLock()
	defer fake.trackPublishRequestedMutex.RUnlock()
	return len(fake.trackPublishRequestedArgsForCall)
}

func (fake *FakeTelemetryService) TrackPublishRequestedCalls(stub func(context.Context, livekit.ParticipantID, livekit.ParticipantIdentity, *livekit.TrackInfo)) {
	fake.trackPublishRequestedMutex.Lock()
	defer fake.trackPublishRequestedMutex.Unlock()
	fake.TrackPublishRequestedStub = stub
}

func (fake *FakeTelemetryService) TrackPublishRequestedArgsForCall(i int) (context.Context, livekit.ParticipantID, livekit.ParticipantIdentity, *livekit.TrackInfo) {
	fake.trackPublishRequestedMutex.RLock()
	defer fake.trackPublishRequestedMutex.RUnlock()
	argsForCall := fake.trackPublishRequestedArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3, argsForCall.arg4
}

func (fake *FakeTelemetryService) TrackPublished(arg1 context.Context, arg2 livekit.ParticipantID, arg3 livekit.ParticipantIdentity, arg4 *livekit.TrackInfo) {
	fake.trackPublishedMutex.Lock()
	fake.trackPublishedArgsForCall = append(fake.trackPublishedArgsForCall, struct {
		arg1 context.Context
		arg2 livekit.ParticipantID
		arg3 livekit.ParticipantIdentity
		arg4 *livekit.TrackInfo
	}{arg1, arg2, arg3, arg4})
	stub := fake.TrackPublishedStub
	fake.recordInvocation("TrackPublished", []interface{}{arg1, arg2, arg3, arg4})
	fake.trackPublishedMutex.Unlock()
	if stub != nil {
		fake.TrackPublishedStub(arg1, arg2, arg3, arg4)
	}
}

func (fake *FakeTelemetryService) TrackPublishedCallCount() int {
	fake.trackPublishedMutex.RLock()
	defer fake.trackPublishedMutex.RUnlock()
	return len(fake.trackPublishedArgsForCall)
}

func (fake *FakeTelemetryService) TrackPublishedCalls(stub func(context.Context, livekit.ParticipantID, livekit.ParticipantIdentity, *livekit.TrackInfo)) {
	fake.trackPublishedMutex.Lock()
	defer fake.trackPublishedMutex.Unlock()
	fake.TrackPublishedStub = stub
}

func (fake *FakeTelemetryService) TrackPublishedArgsForCall(i int) (context.Context, livekit.ParticipantID, livekit.ParticipantIdentity, *livekit.TrackInfo) {
	fake.trackPublishedMutex.RLock()
	defer fake.trackPublishedMutex.RUnlock()
	argsForCall := fake.trackPublishedArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3, argsForCall.arg4
}

func (fake *FakeTelemetryService) TrackPublishedUpdate(arg1 context.Context, arg2 livekit.ParticipantID, arg3 *livekit.TrackInfo) {
	fake.trackPublishedUpdateMutex.Lock()
	fake.trackPublishedUpdateArgsForCall = append(fake.trackPublishedUpdateArgsForCall, struct {
		arg1 context.Context
		arg2 livekit.ParticipantID
		arg3 *livekit.TrackInfo
	}{arg1, arg2, arg3})
	stub := fake.TrackPublishedUpdateStub
	fake.recordInvocation("TrackPublishedUpdate", []interface{}{arg1, arg2, arg3})
	fake.trackPublishedUpdateMutex.Unlock()
	if stub != nil {
		fake.TrackPublishedUpdateStub(arg1, arg2, arg3)
	}
}

func (fake *FakeTelemetryService) TrackPublishedUpdateCallCount() int {
	fake.trackPublishedUpdateMutex.RLock()
	defer fake.trackPublishedUpdateMutex.RUnlock()
	return len(fake.trackPublishedUpdateArgsForCall)
}

func (fake *FakeTelemetryService) TrackPublishedUpdateCalls(stub func(context.Context, livekit.ParticipantID, *livekit.TrackInfo)) {
	fake.trackPublishedUpdateMutex.Lock()
	defer fake.trackPublishedUpdateMutex.Unlock()
	fake.TrackPublishedUpdateStub = stub
}

func (fake *FakeTelemetryService) TrackPublishedUpdateArgsForCall(i int) (context.Context, livekit.ParticipantID, *livekit.TrackInfo) {
	fake.trackPublishedUpdateMutex.RLock()
	defer fake.trackPublishedUpdateMutex.RUnlock()
	argsForCall := fake.trackPublishedUpdateArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeTelemetryService) TrackStats(arg1 telemetry.StatsKey, arg2 *livekit.AnalyticsStat) {
	fake.trackStatsMutex.Lock()
	fake.trackStatsArgsForCall = append(fake.trackStatsArgsForCall, struct {
		arg1 telemetry.StatsKey
		arg2 *livekit.AnalyticsStat
	}{arg1, arg2})
	stub := fake.TrackStatsStub
	fake.recordInvocation("TrackStats", []interface{}{arg1, arg2})
	fake.trackStatsMutex.Unlock()
	if stub != nil {
		fake.TrackStatsStub(arg1, arg2)
	}
}

func (fake *FakeTelemetryService) TrackStatsCallCount() int {
	fake.trackStatsMutex.RLock()
	defer fake.trackStatsMutex.RUnlock()
	return len(fake.trackStatsArgsForCall)
}

func (fake *FakeTelemetryService) TrackStatsCalls(stub func(telemetry.StatsKey, *livekit.AnalyticsStat)) {
	fake.trackStatsMutex.Lock()
	defer fake.trackStatsMutex.Unlock()
	fake.TrackStatsStub = stub
}

func (fake *FakeTelemetryService) TrackStatsArgsForCall(i int) (telemetry.StatsKey, *livekit.AnalyticsStat) {
	fake.trackStatsMutex.RLock()
	defer fake.trackStatsMutex.RUnlock()
	argsForCall := fake.trackStatsArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeTelemetryService) TrackSubscribeFailed(arg1 context.Context, arg2 livekit.ParticipantID, arg3 livekit.TrackID, arg4 error, arg5 bool) {
	fake.trackSubscribeFailedMutex.Lock()
	fake.trackSubscribeFailedArgsForCall = append(fake.trackSubscribeFailedArgsForCall, struct {
		arg1 context.Context
		arg2 livekit.ParticipantID
		arg3 livekit.TrackID
		arg4 error
		arg5 bool
	}{arg1, arg2, arg3, arg4, arg5})
	stub := fake.TrackSubscribeFailedStub
	fake.recordInvocation("TrackSubscribeFailed", []interface{}{arg1, arg2, arg3, arg4, arg5})
	fake.trackSubscribeFailedMutex.Unlock()
	if stub != nil {
		fake.TrackSubscribeFailedStub(arg1, arg2, arg3, arg4, arg5)
	}
}

func (fake *FakeTelemetryService) TrackSubscribeFailedCallCount() int {
	fake.trackSubscribeFailedMutex.RLock()
	defer fake.trackSubscribeFailedMutex.RUnlock()
	return len(fake.trackSubscribeFailedArgsForCall)
}

func (fake *FakeTelemetryService) TrackSubscribeFailedCalls(stub func(context.Context, livekit.ParticipantID, livekit.TrackID, error, bool)) {
	fake.trackSubscribeFailedMutex.Lock()
	defer fake.trackSubscribeFailedMutex.Unlock()
	fake.TrackSubscribeFailedStub = stub
}

func (fake *FakeTelemetryService) TrackSubscribeFailedArgsForCall(i int) (context.Context, livekit.ParticipantID, livekit.TrackID, error, bool) {
	fake.trackSubscribeFailedMutex.RLock()
	defer fake.trackSubscribeFailedMutex.RUnlock()
	argsForCall := fake.trackSubscribeFailedArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3, argsForCall.arg4, argsForCall.arg5
}

func (fake *FakeTelemetryService) TrackSubscribeRTPStats(arg1 context.Context, arg2 livekit.ParticipantID, arg3 livekit.TrackID, arg4 mime.MimeType, arg5 *livekit.RTPStats) {
	fake.trackSubscribeRTPStatsMutex.Lock()
	fake.trackSubscribeRTPStatsArgsForCall = append(fake.trackSubscribeRTPStatsArgsForCall, struct {
		arg1 context.Context
		arg2 livekit.ParticipantID
		arg3 livekit.TrackID
		arg4 mime.MimeType
		arg5 *livekit.RTPStats
	}{arg1, arg2, arg3, arg4, arg5})
	stub := fake.TrackSubscribeRTPStatsStub
	fake.recordInvocation("TrackSubscribeRTPStats", []interface{}{arg1, arg2, arg3, arg4, arg5})
	fake.trackSubscribeRTPStatsMutex.Unlock()
	if stub != nil {
		fake.TrackSubscribeRTPStatsStub(arg1, arg2, arg3, arg4, arg5)
	}
}

func (fake *FakeTelemetryService) TrackSubscribeRTPStatsCallCount() int {
	fake.trackSubscribeRTPStatsMutex.RLock()
	defer fake.trackSubscribeRTPStatsMutex.RUnlock()
	return len(fake.trackSubscribeRTPStatsArgsForCall)
}

func (fake *FakeTelemetryService) TrackSubscribeRTPStatsCalls(stub func(context.Context, livekit.ParticipantID, livekit.TrackID, mime.MimeType, *livekit.RTPStats)) {
	fake.trackSubscribeRTPStatsMutex.Lock()
	defer fake.trackSubscribeRTPStatsMutex.Unlock()
	fake.TrackSubscribeRTPStatsStub = stub
}

func (fake *FakeTelemetryService) TrackSubscribeRTPStatsArgsForCall(i int) (context.Context, livekit.ParticipantID, livekit.TrackID, mime.MimeType, *livekit.RTPStats) {
	fake.trackSubscribeRTPStatsMutex.RLock()
	defer fake.trackSubscribeRTPStatsMutex.RUnlock()
	argsForCall := fake.trackSubscribeRTPStatsArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3, argsForCall.arg4, argsForCall.arg5
}

func (fake *FakeTelemetryService) TrackSubscribeRequested(arg1 context.Context, arg2 livekit.ParticipantID, arg3 *livekit.TrackInfo) {
	fake.trackSubscribeRequestedMutex.Lock()
	fake.trackSubscribeRequestedArgsForCall = append(fake.trackSubscribeRequestedArgsForCall, struct {
		arg1 context.Context
		arg2 livekit.ParticipantID
		arg3 *livekit.TrackInfo
	}{arg1, arg2, arg3})
	stub := fake.TrackSubscribeRequestedStub
	fake.recordInvocation("TrackSubscribeRequested", []interface{}{arg1, arg2, arg3})
	fake.trackSubscribeRequestedMutex.Unlock()
	if stub != nil {
		fake.TrackSubscribeRequestedStub(arg1, arg2, arg3)
	}
}

func (fake *FakeTelemetryService) TrackSubscribeRequestedCallCount() int {
	fake.trackSubscribeRequestedMutex.RLock()
	defer fake.trackSubscribeRequestedMutex.RUnlock()
	return len(fake.trackSubscribeRequestedArgsForCall)
}

func (fake *FakeTelemetryService) TrackSubscribeRequestedCalls(stub func(context.Context, livekit.ParticipantID, *livekit.TrackInfo)) {
	fake.trackSubscribeRequestedMutex.Lock()
	defer fake.trackSubscribeRequestedMutex.Unlock()
	fake.TrackSubscribeRequestedStub = stub
}

func (fake *FakeTelemetryService) TrackSubscribeRequestedArgsForCall(i int) (context.Context, livekit.ParticipantID, *livekit.TrackInfo) {
	fake.trackSubscribeRequestedMutex.RLock()
	defer fake.trackSubscribeRequestedMutex.RUnlock()
	argsForCall := fake.trackSubscribeRequestedArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeTelemetryService) TrackSubscribed(arg1 context.Context, arg2 livekit.ParticipantID, arg3 *livekit.TrackInfo, arg4 *livekit.ParticipantInfo, arg5 bool) {
	fake.trackSubscribedMutex.Lock()
	fake.trackSubscribedArgsForCall = append(fake.trackSubscribedArgsForCall, struct {
		arg1 context.Context
		arg2 livekit.ParticipantID
		arg3 *livekit.TrackInfo
		arg4 *livekit.ParticipantInfo
		arg5 bool
	}{arg1, arg2, arg3, arg4, arg5})
	stub := fake.TrackSubscribedStub
	fake.recordInvocation("TrackSubscribed", []interface{}{arg1, arg2, arg3, arg4, arg5})
	fake.trackSubscribedMutex.Unlock()
	if stub != nil {
		fake.TrackSubscribedStub(arg1, arg2, arg3, arg4, arg5)
	}
}

func (fake *FakeTelemetryService) TrackSubscribedCallCount() int {
	fake.trackSubscribedMutex.RLock()
	defer fake.trackSubscribedMutex.RUnlock()
	return len(fake.trackSubscribedArgsForCall)
}

func (fake *FakeTelemetryService) TrackSubscribedCalls(stub func(context.Context, livekit.ParticipantID, *livekit.TrackInfo, *livekit.ParticipantInfo, bool)) {
	fake.trackSubscribedMutex.Lock()
	defer fake.trackSubscribedMutex.Unlock()
	fake.TrackSubscribedStub = stub
}

func (fake *FakeTelemetryService) TrackSubscribedArgsForCall(i int) (context.Context, livekit.ParticipantID, *livekit.TrackInfo, *livekit.ParticipantInfo, bool) {
	fake.trackSubscribedMutex.RLock()
	defer fake.trackSubscribedMutex.RUnlock()
	argsForCall := fake.trackSubscribedArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3, argsForCall.arg4, argsForCall.arg5
}

func (fake *FakeTelemetryService) TrackUnmuted(arg1 context.Context, arg2 livekit.ParticipantID, arg3 *livekit.TrackInfo) {
	fake.trackUnmutedMutex.Lock()
	fake.trackUnmutedArgsForCall = append(fake.trackUnmutedArgsForCall, struct {
		arg1 context.Context
		arg2 livekit.ParticipantID
		arg3 *livekit.TrackInfo
	}{arg1, arg2, arg3})
	stub := fake.TrackUnmutedStub
	fake.recordInvocation("TrackUnmuted", []interface{}{arg1, arg2, arg3})
	fake.trackUnmutedMutex.Unlock()
	if stub != nil {
		fake.TrackUnmutedStub(arg1, arg2, arg3)
	}
}

func (fake *FakeTelemetryService) TrackUnmutedCallCount() int {
	fake.trackUnmutedMutex.RLock()
	defer fake.trackUnmutedMutex.RUnlock()
	return len(fake.trackUnmutedArgsForCall)
}

func (fake *FakeTelemetryService) TrackUnmutedCalls(stub func(context.Context, livekit.ParticipantID, *livekit.TrackInfo)) {
	fake.trackUnmutedMutex.Lock()
	defer fake.trackUnmutedMutex.Unlock()
	fake.TrackUnmutedStub = stub
}

func (fake *FakeTelemetryService) TrackUnmutedArgsForCall(i int) (context.Context, livekit.ParticipantID, *livekit.TrackInfo) {
	fake.trackUnmutedMutex.RLock()
	defer fake.trackUnmutedMutex.RUnlock()
	argsForCall := fake.trackUnmutedArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeTelemetryService) TrackUnpublished(arg1 context.Context, arg2 livekit.ParticipantID, arg3 livekit.ParticipantIdentity, arg4 *livekit.TrackInfo, arg5 bool) {
	fake.trackUnpublishedMutex.Lock()
	fake.trackUnpublishedArgsForCall = append(fake.trackUnpublishedArgsForCall, struct {
		arg1 context.Context
		arg2 livekit.ParticipantID
		arg3 livekit.ParticipantIdentity
		arg4 *livekit.TrackInfo
		arg5 bool
	}{arg1, arg2, arg3, arg4, arg5})
	stub := fake.TrackUnpublishedStub
	fake.recordInvocation("TrackUnpublished", []interface{}{arg1, arg2, arg3, arg4, arg5})
	fake.trackUnpublishedMutex.Unlock()
	if stub != nil {
		fake.TrackUnpublishedStub(arg1, arg2, arg3, arg4, arg5)
	}
}

func (fake *FakeTelemetryService) TrackUnpublishedCallCount() int {
	fake.trackUnpublishedMutex.RLock()
	defer fake.trackUnpublishedMutex.RUnlock()
	return len(fake.trackUnpublishedArgsForCall)
}

func (fake *FakeTelemetryService) TrackUnpublishedCalls(stub func(context.Context, livekit.ParticipantID, livekit.ParticipantIdentity, *livekit.TrackInfo, bool)) {
	fake.trackUnpublishedMutex.Lock()
	defer fake.trackUnpublishedMutex.Unlock()
	fake.TrackUnpublishedStub = stub
}

func (fake *FakeTelemetryService) TrackUnpublishedArgsForCall(i int) (context.Context, livekit.ParticipantID, livekit.ParticipantIdentity, *livekit.TrackInfo, bool) {
	fake.trackUnpublishedMutex.RLock()
	defer fake.trackUnpublishedMutex.RUnlock()
	argsForCall := fake.trackUnpublishedArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3, argsForCall.arg4, argsForCall.arg5
}

func (fake *FakeTelemetryService) TrackUnsubscribed(arg1 context.Context, arg2 livekit.ParticipantID, arg3 *livekit.TrackInfo, arg4 bool) {
	fake.trackUnsubscribedMutex.Lock()
	fake.trackUnsubscribedArgsForCall = append(fake.trackUnsubscribedArgsForCall, struct {
		arg1 context.Context
		arg2 livekit.ParticipantID
		arg3 *livekit.TrackInfo
		arg4 bool
	}{arg1, arg2, arg3, arg4})
	stub := fake.TrackUnsubscribedStub
	fake.recordInvocation("TrackUnsubscribed", []interface{}{arg1, arg2, arg3, arg4})
	fake.trackUnsubscribedMutex.Unlock()
	if stub != nil {
		fake.TrackUnsubscribedStub(arg1, arg2, arg3, arg4)
	}
}

func (fake *FakeTelemetryService) TrackUnsubscribedCallCount() int {
	fake.trackUnsubscribedMutex.RLock()
	defer fake.trackUnsubscribedMutex.RUnlock()
	return len(fake.trackUnsubscribedArgsForCall)
}

func (fake *FakeTelemetryService) TrackUnsubscribedCalls(stub func(context.Context, livekit.ParticipantID, *livekit.TrackInfo, bool)) {
	fake.trackUnsubscribedMutex.Lock()
	defer fake.trackUnsubscribedMutex.Unlock()
	fake.TrackUnsubscribedStub = stub
}

func (fake *FakeTelemetryService) TrackUnsubscribedArgsForCall(i int) (context.Context, livekit.ParticipantID, *livekit.TrackInfo, bool) {
	fake.trackUnsubscribedMutex.RLock()
	defer fake.trackUnsubscribedMutex.RUnlock()
	argsForCall := fake.trackUnsubscribedArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3, argsForCall.arg4
}

func (fake *FakeTelemetryService) Webhook(arg1 context.Context, arg2 *livekit.WebhookInfo) {
	fake.webhookMutex.Lock()
	fake.webhookArgsForCall = append(fake.webhookArgsForCall, struct {
		arg1 context.Context
		arg2 *livekit.WebhookInfo
	}{arg1, arg2})
	stub := fake.WebhookStub
	fake.recordInvocation("Webhook", []interface{}{arg1, arg2})
	fake.webhookMutex.Unlock()
	if stub != nil {
		fake.WebhookStub(arg1, arg2)
	}
}

func (fake *FakeTelemetryService) WebhookCallCount() int {
	fake.webhookMutex.RLock()
	defer fake.webhookMutex.RUnlock()
	return len(fake.webhookArgsForCall)
}

func (fake *FakeTelemetryService) WebhookCalls(stub func(context.Context, *livekit.WebhookInfo)) {
	fake.webhookMutex.Lock()
	defer fake.webhookMutex.Unlock()
	fake.WebhookStub = stub
}

func (fake *FakeTelemetryService) WebhookArgsForCall(i int) (context.Context, *livekit.WebhookInfo) {
	fake.webhookMutex.RLock()
	defer fake.webhookMutex.RUnlock()
	argsForCall := fake.webhookArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeTelemetryService) Invocations() map[string][][]interface{} {
	fake.invocationsMutex.RLock()
	defer fake.invocationsMutex.RUnlock()
	fake.aPICallMutex.RLock()
	defer fake.aPICallMutex.RUnlock()
	fake.egressEndedMutex.RLock()
	defer fake.egressEndedMutex.RUnlock()
	fake.egressStartedMutex.RLock()
	defer fake.egressStartedMutex.RUnlock()
	fake.egressUpdatedMutex.RLock()
	defer fake.egressUpdatedMutex.RUnlock()
	fake.flushStatsMutex.RLock()
	defer fake.flushStatsMutex.RUnlock()
	fake.ingressCreatedMutex.RLock()
	defer fake.ingressCreatedMutex.RUnlock()
	fake.ingressDeletedMutex.RLock()
	defer fake.ingressDeletedMutex.RUnlock()
	fake.ingressEndedMutex.RLock()
	defer fake.ingressEndedMutex.RUnlock()
	fake.ingressStartedMutex.RLock()
	defer fake.ingressStartedMutex.RUnlock()
	fake.ingressUpdatedMutex.RLock()
	defer fake.ingressUpdatedMutex.RUnlock()
	fake.localRoomStateMutex.RLock()
	defer fake.localRoomStateMutex.RUnlock()
	fake.notifyEgressEventMutex.RLock()
	defer fake.notifyEgressEventMutex.RUnlock()
	fake.participantActiveMutex.RLock()
	defer fake.participantActiveMutex.RUnlock()
	fake.participantJoinedMutex.RLock()
	defer fake.participantJoinedMutex.RUnlock()
	fake.participantLeftMutex.RLock()
	defer fake.participantLeftMutex.RUnlock()
	fake.participantResumedMutex.RLock()
	defer fake.participantResumedMutex.RUnlock()
	fake.reportMutex.RLock()
	defer fake.reportMutex.RUnlock()
	fake.roomEndedMutex.RLock()
	defer fake.roomEndedMutex.RUnlock()
	fake.roomProjectReporterMutex.RLock()
	defer fake.roomProjectReporterMutex.RUnlock()
	fake.roomStartedMutex.RLock()
	defer fake.roomStartedMutex.RUnlock()
	fake.sendEventMutex.RLock()
	defer fake.sendEventMutex.RUnlock()
	fake.sendNodeRoomStatesMutex.RLock()
	defer fake.sendNodeRoomStatesMutex.RUnlock()
	fake.sendStatsMutex.RLock()
	defer fake.sendStatsMutex.RUnlock()
	fake.trackMaxSubscribedVideoQualityMutex.RLock()
	defer fake.trackMaxSubscribedVideoQualityMutex.RUnlock()
	fake.trackMutedMutex.RLock()
	defer fake.trackMutedMutex.RUnlock()
	fake.trackPublishRTPStatsMutex.RLock()
	defer fake.trackPublishRTPStatsMutex.RUnlock()
	fake.trackPublishRequestedMutex.RLock()
	defer fake.trackPublishRequestedMutex.RUnlock()
	fake.trackPublishedMutex.RLock()
	defer fake.trackPublishedMutex.RUnlock()
	fake.trackPublishedUpdateMutex.RLock()
	defer fake.trackPublishedUpdateMutex.RUnlock()
	fake.trackStatsMutex.RLock()
	defer fake.trackStatsMutex.RUnlock()
	fake.trackSubscribeFailedMutex.RLock()
	defer fake.trackSubscribeFailedMutex.RUnlock()
	fake.trackSubscribeRTPStatsMutex.RLock()
	defer fake.trackSubscribeRTPStatsMutex.RUnlock()
	fake.trackSubscribeRequestedMutex.RLock()
	defer fake.trackSubscribeRequestedMutex.RUnlock()
	fake.trackSubscribedMutex.RLock()
	defer fake.trackSubscribedMutex.RUnlock()
	fake.trackUnmutedMutex.RLock()
	defer fake.trackUnmutedMutex.RUnlock()
	fake.trackUnpublishedMutex.RLock()
	defer fake.trackUnpublishedMutex.RUnlock()
	fake.trackUnsubscribedMutex.RLock()
	defer fake.trackUnsubscribedMutex.RUnlock()
	fake.webhookMutex.RLock()
	defer fake.webhookMutex.RUnlock()
	copiedInvocations := map[string][][]interface{}{}
	for key, value := range fake.invocations {
		copiedInvocations[key] = value
	}
	return copiedInvocations
}

func (fake *FakeTelemetryService) recordInvocation(key string, args []interface{}) {
	fake.invocationsMutex.Lock()
	defer fake.invocationsMutex.Unlock()
	if fake.invocations == nil {
		fake.invocations = map[string][][]interface{}{}
	}
	if fake.invocations[key] == nil {
		fake.invocations[key] = [][]interface{}{}
	}
	fake.invocations[key] = append(fake.invocations[key], args)
}

var _ telemetry.TelemetryService = new(FakeTelemetryService)
