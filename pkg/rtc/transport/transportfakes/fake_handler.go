// Code generated by counterfeiter. DO NOT EDIT.
package transportfakes

import (
	"sync"

	"github.com/livekit/livekit-server/pkg/rtc/transport"
	"github.com/livekit/livekit-server/pkg/rtc/types"
	"github.com/livekit/livekit-server/pkg/sfu/streamallocator"
	"github.com/livekit/protocol/livekit"
	webrtc "github.com/pion/webrtc/v4"
)

type FakeHandler struct {
	OnAnswerStub        func(webrtc.SessionDescription, uint32) error
	onAnswerMutex       sync.RWMutex
	onAnswerArgsForCall []struct {
		arg1 webrtc.SessionDescription
		arg2 uint32
	}
	onAnswerReturns struct {
		result1 error
	}
	onAnswerReturnsOnCall map[int]struct {
		result1 error
	}
	OnDataMessageStub        func(livekit.DataPacket_Kind, []byte)
	onDataMessageMutex       sync.RWMutex
	onDataMessageArgsForCall []struct {
		arg1 livekit.DataPacket_Kind
		arg2 []byte
	}
	OnDataMessageUnlabeledStub        func([]byte)
	onDataMessageUnlabeledMutex       sync.RWMutex
	onDataMessageUnlabeledArgsForCall []struct {
		arg1 []byte
	}
	OnDataSendErrorStub        func(error)
	onDataSendErrorMutex       sync.RWMutex
	onDataSendErrorArgsForCall []struct {
		arg1 error
	}
	OnFailedStub        func(bool, *types.ICEConnectionInfo)
	onFailedMutex       sync.RWMutex
	onFailedArgsForCall []struct {
		arg1 bool
		arg2 *types.ICEConnectionInfo
	}
	OnFullyEstablishedStub        func()
	onFullyEstablishedMutex       sync.RWMutex
	onFullyEstablishedArgsForCall []struct {
	}
	OnICECandidateStub        func(*webrtc.ICECandidate, livekit.SignalTarget) error
	onICECandidateMutex       sync.RWMutex
	onICECandidateArgsForCall []struct {
		arg1 *webrtc.ICECandidate
		arg2 livekit.SignalTarget
	}
	onICECandidateReturns struct {
		result1 error
	}
	onICECandidateReturnsOnCall map[int]struct {
		result1 error
	}
	OnInitialConnectedStub        func()
	onInitialConnectedMutex       sync.RWMutex
	onInitialConnectedArgsForCall []struct {
	}
	OnNegotiationFailedStub        func()
	onNegotiationFailedMutex       sync.RWMutex
	onNegotiationFailedArgsForCall []struct {
	}
	OnNegotiationStateChangedStub        func(transport.NegotiationState)
	onNegotiationStateChangedMutex       sync.RWMutex
	onNegotiationStateChangedArgsForCall []struct {
		arg1 transport.NegotiationState
	}
	OnOfferStub        func(webrtc.SessionDescription, uint32) error
	onOfferMutex       sync.RWMutex
	onOfferArgsForCall []struct {
		arg1 webrtc.SessionDescription
		arg2 uint32
	}
	onOfferReturns struct {
		result1 error
	}
	onOfferReturnsOnCall map[int]struct {
		result1 error
	}
	OnStreamStateChangeStub        func(*streamallocator.StreamStateUpdate) error
	onStreamStateChangeMutex       sync.RWMutex
	onStreamStateChangeArgsForCall []struct {
		arg1 *streamallocator.StreamStateUpdate
	}
	onStreamStateChangeReturns struct {
		result1 error
	}
	onStreamStateChangeReturnsOnCall map[int]struct {
		result1 error
	}
	OnTrackStub        func(*webrtc.TrackRemote, *webrtc.RTPReceiver)
	onTrackMutex       sync.RWMutex
	onTrackArgsForCall []struct {
		arg1 *webrtc.TrackRemote
		arg2 *webrtc.RTPReceiver
	}
	invocations      map[string][][]interface{}
	invocationsMutex sync.RWMutex
}

func (fake *FakeHandler) OnAnswer(arg1 webrtc.SessionDescription, arg2 uint32) error {
	fake.onAnswerMutex.Lock()
	ret, specificReturn := fake.onAnswerReturnsOnCall[len(fake.onAnswerArgsForCall)]
	fake.onAnswerArgsForCall = append(fake.onAnswerArgsForCall, struct {
		arg1 webrtc.SessionDescription
		arg2 uint32
	}{arg1, arg2})
	stub := fake.OnAnswerStub
	fakeReturns := fake.onAnswerReturns
	fake.recordInvocation("OnAnswer", []interface{}{arg1, arg2})
	fake.onAnswerMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeHandler) OnAnswerCallCount() int {
	fake.onAnswerMutex.RLock()
	defer fake.onAnswerMutex.RUnlock()
	return len(fake.onAnswerArgsForCall)
}

func (fake *FakeHandler) OnAnswerCalls(stub func(webrtc.SessionDescription, uint32) error) {
	fake.onAnswerMutex.Lock()
	defer fake.onAnswerMutex.Unlock()
	fake.OnAnswerStub = stub
}

func (fake *FakeHandler) OnAnswerArgsForCall(i int) (webrtc.SessionDescription, uint32) {
	fake.onAnswerMutex.RLock()
	defer fake.onAnswerMutex.RUnlock()
	argsForCall := fake.onAnswerArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeHandler) OnAnswerReturns(result1 error) {
	fake.onAnswerMutex.Lock()
	defer fake.onAnswerMutex.Unlock()
	fake.OnAnswerStub = nil
	fake.onAnswerReturns = struct {
		result1 error
	}{result1}
}

func (fake *FakeHandler) OnAnswerReturnsOnCall(i int, result1 error) {
	fake.onAnswerMutex.Lock()
	defer fake.onAnswerMutex.Unlock()
	fake.OnAnswerStub = nil
	if fake.onAnswerReturnsOnCall == nil {
		fake.onAnswerReturnsOnCall = make(map[int]struct {
			result1 error
		})
	}
	fake.onAnswerReturnsOnCall[i] = struct {
		result1 error
	}{result1}
}

func (fake *FakeHandler) OnDataMessage(arg1 livekit.DataPacket_Kind, arg2 []byte) {
	var arg2Copy []byte
	if arg2 != nil {
		arg2Copy = make([]byte, len(arg2))
		copy(arg2Copy, arg2)
	}
	fake.onDataMessageMutex.Lock()
	fake.onDataMessageArgsForCall = append(fake.onDataMessageArgsForCall, struct {
		arg1 livekit.DataPacket_Kind
		arg2 []byte
	}{arg1, arg2Copy})
	stub := fake.OnDataMessageStub
	fake.recordInvocation("OnDataMessage", []interface{}{arg1, arg2Copy})
	fake.onDataMessageMutex.Unlock()
	if stub != nil {
		fake.OnDataMessageStub(arg1, arg2)
	}
}

func (fake *FakeHandler) OnDataMessageCallCount() int {
	fake.onDataMessageMutex.RLock()
	defer fake.onDataMessageMutex.RUnlock()
	return len(fake.onDataMessageArgsForCall)
}

func (fake *FakeHandler) OnDataMessageCalls(stub func(livekit.DataPacket_Kind, []byte)) {
	fake.onDataMessageMutex.Lock()
	defer fake.onDataMessageMutex.Unlock()
	fake.OnDataMessageStub = stub
}

func (fake *FakeHandler) OnDataMessageArgsForCall(i int) (livekit.DataPacket_Kind, []byte) {
	fake.onDataMessageMutex.RLock()
	defer fake.onDataMessageMutex.RUnlock()
	argsForCall := fake.onDataMessageArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeHandler) OnDataMessageUnlabeled(arg1 []byte) {
	var arg1Copy []byte
	if arg1 != nil {
		arg1Copy = make([]byte, len(arg1))
		copy(arg1Copy, arg1)
	}
	fake.onDataMessageUnlabeledMutex.Lock()
	fake.onDataMessageUnlabeledArgsForCall = append(fake.onDataMessageUnlabeledArgsForCall, struct {
		arg1 []byte
	}{arg1Copy})
	stub := fake.OnDataMessageUnlabeledStub
	fake.recordInvocation("OnDataMessageUnlabeled", []interface{}{arg1Copy})
	fake.onDataMessageUnlabeledMutex.Unlock()
	if stub != nil {
		fake.OnDataMessageUnlabeledStub(arg1)
	}
}

func (fake *FakeHandler) OnDataMessageUnlabeledCallCount() int {
	fake.onDataMessageUnlabeledMutex.RLock()
	defer fake.onDataMessageUnlabeledMutex.RUnlock()
	return len(fake.onDataMessageUnlabeledArgsForCall)
}

func (fake *FakeHandler) OnDataMessageUnlabeledCalls(stub func([]byte)) {
	fake.onDataMessageUnlabeledMutex.Lock()
	defer fake.onDataMessageUnlabeledMutex.Unlock()
	fake.OnDataMessageUnlabeledStub = stub
}

func (fake *FakeHandler) OnDataMessageUnlabeledArgsForCall(i int) []byte {
	fake.onDataMessageUnlabeledMutex.RLock()
	defer fake.onDataMessageUnlabeledMutex.RUnlock()
	argsForCall := fake.onDataMessageUnlabeledArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeHandler) OnDataSendError(arg1 error) {
	fake.onDataSendErrorMutex.Lock()
	fake.onDataSendErrorArgsForCall = append(fake.onDataSendErrorArgsForCall, struct {
		arg1 error
	}{arg1})
	stub := fake.OnDataSendErrorStub
	fake.recordInvocation("OnDataSendError", []interface{}{arg1})
	fake.onDataSendErrorMutex.Unlock()
	if stub != nil {
		fake.OnDataSendErrorStub(arg1)
	}
}

func (fake *FakeHandler) OnDataSendErrorCallCount() int {
	fake.onDataSendErrorMutex.RLock()
	defer fake.onDataSendErrorMutex.RUnlock()
	return len(fake.onDataSendErrorArgsForCall)
}

func (fake *FakeHandler) OnDataSendErrorCalls(stub func(error)) {
	fake.onDataSendErrorMutex.Lock()
	defer fake.onDataSendErrorMutex.Unlock()
	fake.OnDataSendErrorStub = stub
}

func (fake *FakeHandler) OnDataSendErrorArgsForCall(i int) error {
	fake.onDataSendErrorMutex.RLock()
	defer fake.onDataSendErrorMutex.RUnlock()
	argsForCall := fake.onDataSendErrorArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeHandler) OnFailed(arg1 bool, arg2 *types.ICEConnectionInfo) {
	fake.onFailedMutex.Lock()
	fake.onFailedArgsForCall = append(fake.onFailedArgsForCall, struct {
		arg1 bool
		arg2 *types.ICEConnectionInfo
	}{arg1, arg2})
	stub := fake.OnFailedStub
	fake.recordInvocation("OnFailed", []interface{}{arg1, arg2})
	fake.onFailedMutex.Unlock()
	if stub != nil {
		fake.OnFailedStub(arg1, arg2)
	}
}

func (fake *FakeHandler) OnFailedCallCount() int {
	fake.onFailedMutex.RLock()
	defer fake.onFailedMutex.RUnlock()
	return len(fake.onFailedArgsForCall)
}

func (fake *FakeHandler) OnFailedCalls(stub func(bool, *types.ICEConnectionInfo)) {
	fake.onFailedMutex.Lock()
	defer fake.onFailedMutex.Unlock()
	fake.OnFailedStub = stub
}

func (fake *FakeHandler) OnFailedArgsForCall(i int) (bool, *types.ICEConnectionInfo) {
	fake.onFailedMutex.RLock()
	defer fake.onFailedMutex.RUnlock()
	argsForCall := fake.onFailedArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeHandler) OnFullyEstablished() {
	fake.onFullyEstablishedMutex.Lock()
	fake.onFullyEstablishedArgsForCall = append(fake.onFullyEstablishedArgsForCall, struct {
	}{})
	stub := fake.OnFullyEstablishedStub
	fake.recordInvocation("OnFullyEstablished", []interface{}{})
	fake.onFullyEstablishedMutex.Unlock()
	if stub != nil {
		fake.OnFullyEstablishedStub()
	}
}

func (fake *FakeHandler) OnFullyEstablishedCallCount() int {
	fake.onFullyEstablishedMutex.RLock()
	defer fake.onFullyEstablishedMutex.RUnlock()
	return len(fake.onFullyEstablishedArgsForCall)
}

func (fake *FakeHandler) OnFullyEstablishedCalls(stub func()) {
	fake.onFullyEstablishedMutex.Lock()
	defer fake.onFullyEstablishedMutex.Unlock()
	fake.OnFullyEstablishedStub = stub
}

func (fake *FakeHandler) OnICECandidate(arg1 *webrtc.ICECandidate, arg2 livekit.SignalTarget) error {
	fake.onICECandidateMutex.Lock()
	ret, specificReturn := fake.onICECandidateReturnsOnCall[len(fake.onICECandidateArgsForCall)]
	fake.onICECandidateArgsForCall = append(fake.onICECandidateArgsForCall, struct {
		arg1 *webrtc.ICECandidate
		arg2 livekit.SignalTarget
	}{arg1, arg2})
	stub := fake.OnICECandidateStub
	fakeReturns := fake.onICECandidateReturns
	fake.recordInvocation("OnICECandidate", []interface{}{arg1, arg2})
	fake.onICECandidateMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeHandler) OnICECandidateCallCount() int {
	fake.onICECandidateMutex.RLock()
	defer fake.onICECandidateMutex.RUnlock()
	return len(fake.onICECandidateArgsForCall)
}

func (fake *FakeHandler) OnICECandidateCalls(stub func(*webrtc.ICECandidate, livekit.SignalTarget) error) {
	fake.onICECandidateMutex.Lock()
	defer fake.onICECandidateMutex.Unlock()
	fake.OnICECandidateStub = stub
}

func (fake *FakeHandler) OnICECandidateArgsForCall(i int) (*webrtc.ICECandidate, livekit.SignalTarget) {
	fake.onICECandidateMutex.RLock()
	defer fake.onICECandidateMutex.RUnlock()
	argsForCall := fake.onICECandidateArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeHandler) OnICECandidateReturns(result1 error) {
	fake.onICECandidateMutex.Lock()
	defer fake.onICECandidateMutex.Unlock()
	fake.OnICECandidateStub = nil
	fake.onICECandidateReturns = struct {
		result1 error
	}{result1}
}

func (fake *FakeHandler) OnICECandidateReturnsOnCall(i int, result1 error) {
	fake.onICECandidateMutex.Lock()
	defer fake.onICECandidateMutex.Unlock()
	fake.OnICECandidateStub = nil
	if fake.onICECandidateReturnsOnCall == nil {
		fake.onICECandidateReturnsOnCall = make(map[int]struct {
			result1 error
		})
	}
	fake.onICECandidateReturnsOnCall[i] = struct {
		result1 error
	}{result1}
}

func (fake *FakeHandler) OnInitialConnected() {
	fake.onInitialConnectedMutex.Lock()
	fake.onInitialConnectedArgsForCall = append(fake.onInitialConnectedArgsForCall, struct {
	}{})
	stub := fake.OnInitialConnectedStub
	fake.recordInvocation("OnInitialConnected", []interface{}{})
	fake.onInitialConnectedMutex.Unlock()
	if stub != nil {
		fake.OnInitialConnectedStub()
	}
}

func (fake *FakeHandler) OnInitialConnectedCallCount() int {
	fake.onInitialConnectedMutex.RLock()
	defer fake.onInitialConnectedMutex.RUnlock()
	return len(fake.onInitialConnectedArgsForCall)
}

func (fake *FakeHandler) OnInitialConnectedCalls(stub func()) {
	fake.onInitialConnectedMutex.Lock()
	defer fake.onInitialConnectedMutex.Unlock()
	fake.OnInitialConnectedStub = stub
}

func (fake *FakeHandler) OnNegotiationFailed() {
	fake.onNegotiationFailedMutex.Lock()
	fake.onNegotiationFailedArgsForCall = append(fake.onNegotiationFailedArgsForCall, struct {
	}{})
	stub := fake.OnNegotiationFailedStub
	fake.recordInvocation("OnNegotiationFailed", []interface{}{})
	fake.onNegotiationFailedMutex.Unlock()
	if stub != nil {
		fake.OnNegotiationFailedStub()
	}
}

func (fake *FakeHandler) OnNegotiationFailedCallCount() int {
	fake.onNegotiationFailedMutex.RLock()
	defer fake.onNegotiationFailedMutex.RUnlock()
	return len(fake.onNegotiationFailedArgsForCall)
}

func (fake *FakeHandler) OnNegotiationFailedCalls(stub func()) {
	fake.onNegotiationFailedMutex.Lock()
	defer fake.onNegotiationFailedMutex.Unlock()
	fake.OnNegotiationFailedStub = stub
}

func (fake *FakeHandler) OnNegotiationStateChanged(arg1 transport.NegotiationState) {
	fake.onNegotiationStateChangedMutex.Lock()
	fake.onNegotiationStateChangedArgsForCall = append(fake.onNegotiationStateChangedArgsForCall, struct {
		arg1 transport.NegotiationState
	}{arg1})
	stub := fake.OnNegotiationStateChangedStub
	fake.recordInvocation("OnNegotiationStateChanged", []interface{}{arg1})
	fake.onNegotiationStateChangedMutex.Unlock()
	if stub != nil {
		fake.OnNegotiationStateChangedStub(arg1)
	}
}

func (fake *FakeHandler) OnNegotiationStateChangedCallCount() int {
	fake.onNegotiationStateChangedMutex.RLock()
	defer fake.onNegotiationStateChangedMutex.RUnlock()
	return len(fake.onNegotiationStateChangedArgsForCall)
}

func (fake *FakeHandler) OnNegotiationStateChangedCalls(stub func(transport.NegotiationState)) {
	fake.onNegotiationStateChangedMutex.Lock()
	defer fake.onNegotiationStateChangedMutex.Unlock()
	fake.OnNegotiationStateChangedStub = stub
}

func (fake *FakeHandler) OnNegotiationStateChangedArgsForCall(i int) transport.NegotiationState {
	fake.onNegotiationStateChangedMutex.RLock()
	defer fake.onNegotiationStateChangedMutex.RUnlock()
	argsForCall := fake.onNegotiationStateChangedArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeHandler) OnOffer(arg1 webrtc.SessionDescription, arg2 uint32) error {
	fake.onOfferMutex.Lock()
	ret, specificReturn := fake.onOfferReturnsOnCall[len(fake.onOfferArgsForCall)]
	fake.onOfferArgsForCall = append(fake.onOfferArgsForCall, struct {
		arg1 webrtc.SessionDescription
		arg2 uint32
	}{arg1, arg2})
	stub := fake.OnOfferStub
	fakeReturns := fake.onOfferReturns
	fake.recordInvocation("OnOffer", []interface{}{arg1, arg2})
	fake.onOfferMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeHandler) OnOfferCallCount() int {
	fake.onOfferMutex.RLock()
	defer fake.onOfferMutex.RUnlock()
	return len(fake.onOfferArgsForCall)
}

func (fake *FakeHandler) OnOfferCalls(stub func(webrtc.SessionDescription, uint32) error) {
	fake.onOfferMutex.Lock()
	defer fake.onOfferMutex.Unlock()
	fake.OnOfferStub = stub
}

func (fake *FakeHandler) OnOfferArgsForCall(i int) (webrtc.SessionDescription, uint32) {
	fake.onOfferMutex.RLock()
	defer fake.onOfferMutex.RUnlock()
	argsForCall := fake.onOfferArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeHandler) OnOfferReturns(result1 error) {
	fake.onOfferMutex.Lock()
	defer fake.onOfferMutex.Unlock()
	fake.OnOfferStub = nil
	fake.onOfferReturns = struct {
		result1 error
	}{result1}
}

func (fake *FakeHandler) OnOfferReturnsOnCall(i int, result1 error) {
	fake.onOfferMutex.Lock()
	defer fake.onOfferMutex.Unlock()
	fake.OnOfferStub = nil
	if fake.onOfferReturnsOnCall == nil {
		fake.onOfferReturnsOnCall = make(map[int]struct {
			result1 error
		})
	}
	fake.onOfferReturnsOnCall[i] = struct {
		result1 error
	}{result1}
}

func (fake *FakeHandler) OnStreamStateChange(arg1 *streamallocator.StreamStateUpdate) error {
	fake.onStreamStateChangeMutex.Lock()
	ret, specificReturn := fake.onStreamStateChangeReturnsOnCall[len(fake.onStreamStateChangeArgsForCall)]
	fake.onStreamStateChangeArgsForCall = append(fake.onStreamStateChangeArgsForCall, struct {
		arg1 *streamallocator.StreamStateUpdate
	}{arg1})
	stub := fake.OnStreamStateChangeStub
	fakeReturns := fake.onStreamStateChangeReturns
	fake.recordInvocation("OnStreamStateChange", []interface{}{arg1})
	fake.onStreamStateChangeMutex.Unlock()
	if stub != nil {
		return stub(arg1)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeHandler) OnStreamStateChangeCallCount() int {
	fake.onStreamStateChangeMutex.RLock()
	defer fake.onStreamStateChangeMutex.RUnlock()
	return len(fake.onStreamStateChangeArgsForCall)
}

func (fake *FakeHandler) OnStreamStateChangeCalls(stub func(*streamallocator.StreamStateUpdate) error) {
	fake.onStreamStateChangeMutex.Lock()
	defer fake.onStreamStateChangeMutex.Unlock()
	fake.OnStreamStateChangeStub = stub
}

func (fake *FakeHandler) OnStreamStateChangeArgsForCall(i int) *streamallocator.StreamStateUpdate {
	fake.onStreamStateChangeMutex.RLock()
	defer fake.onStreamStateChangeMutex.RUnlock()
	argsForCall := fake.onStreamStateChangeArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeHandler) OnStreamStateChangeReturns(result1 error) {
	fake.onStreamStateChangeMutex.Lock()
	defer fake.onStreamStateChangeMutex.Unlock()
	fake.OnStreamStateChangeStub = nil
	fake.onStreamStateChangeReturns = struct {
		result1 error
	}{result1}
}

func (fake *FakeHandler) OnStreamStateChangeReturnsOnCall(i int, result1 error) {
	fake.onStreamStateChangeMutex.Lock()
	defer fake.onStreamStateChangeMutex.Unlock()
	fake.OnStreamStateChangeStub = nil
	if fake.onStreamStateChangeReturnsOnCall == nil {
		fake.onStreamStateChangeReturnsOnCall = make(map[int]struct {
			result1 error
		})
	}
	fake.onStreamStateChangeReturnsOnCall[i] = struct {
		result1 error
	}{result1}
}

func (fake *FakeHandler) OnTrack(arg1 *webrtc.TrackRemote, arg2 *webrtc.RTPReceiver) {
	fake.onTrackMutex.Lock()
	fake.onTrackArgsForCall = append(fake.onTrackArgsForCall, struct {
		arg1 *webrtc.TrackRemote
		arg2 *webrtc.RTPReceiver
	}{arg1, arg2})
	stub := fake.OnTrackStub
	fake.recordInvocation("OnTrack", []interface{}{arg1, arg2})
	fake.onTrackMutex.Unlock()
	if stub != nil {
		fake.OnTrackStub(arg1, arg2)
	}
}

func (fake *FakeHandler) OnTrackCallCount() int {
	fake.onTrackMutex.RLock()
	defer fake.onTrackMutex.RUnlock()
	return len(fake.onTrackArgsForCall)
}

func (fake *FakeHandler) OnTrackCalls(stub func(*webrtc.TrackRemote, *webrtc.RTPReceiver)) {
	fake.onTrackMutex.Lock()
	defer fake.onTrackMutex.Unlock()
	fake.OnTrackStub = stub
}

func (fake *FakeHandler) OnTrackArgsForCall(i int) (*webrtc.TrackRemote, *webrtc.RTPReceiver) {
	fake.onTrackMutex.RLock()
	defer fake.onTrackMutex.RUnlock()
	argsForCall := fake.onTrackArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeHandler) Invocations() map[string][][]interface{} {
	fake.invocationsMutex.RLock()
	defer fake.invocationsMutex.RUnlock()
	fake.onAnswerMutex.RLock()
	defer fake.onAnswerMutex.RUnlock()
	fake.onDataMessageMutex.RLock()
	defer fake.onDataMessageMutex.RUnlock()
	fake.onDataMessageUnlabeledMutex.RLock()
	defer fake.onDataMessageUnlabeledMutex.RUnlock()
	fake.onDataSendErrorMutex.RLock()
	defer fake.onDataSendErrorMutex.RUnlock()
	fake.onFailedMutex.RLock()
	defer fake.onFailedMutex.RUnlock()
	fake.onFullyEstablishedMutex.RLock()
	defer fake.onFullyEstablishedMutex.RUnlock()
	fake.onICECandidateMutex.RLock()
	defer fake.onICECandidateMutex.RUnlock()
	fake.onInitialConnectedMutex.RLock()
	defer fake.onInitialConnectedMutex.RUnlock()
	fake.onNegotiationFailedMutex.RLock()
	defer fake.onNegotiationFailedMutex.RUnlock()
	fake.onNegotiationStateChangedMutex.RLock()
	defer fake.onNegotiationStateChangedMutex.RUnlock()
	fake.onOfferMutex.RLock()
	defer fake.onOfferMutex.RUnlock()
	fake.onStreamStateChangeMutex.RLock()
	defer fake.onStreamStateChangeMutex.RUnlock()
	fake.onTrackMutex.RLock()
	defer fake.onTrackMutex.RUnlock()
	copiedInvocations := map[string][][]interface{}{}
	for key, value := range fake.invocations {
		copiedInvocations[key] = value
	}
	return copiedInvocations
}

func (fake *FakeHandler) recordInvocation(key string, args []interface{}) {
	fake.invocationsMutex.Lock()
	defer fake.invocationsMutex.Unlock()
	if fake.invocations == nil {
		fake.invocations = map[string][][]interface{}{}
	}
	if fake.invocations[key] == nil {
		fake.invocations[key] = [][]interface{}{}
	}
	fake.invocations[key] = append(fake.invocations[key], args)
}

var _ transport.Handler = new(FakeHandler)
