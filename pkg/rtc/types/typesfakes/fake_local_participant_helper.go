// Code generated by counterfeiter. DO NOT EDIT.
package typesfakes

import (
	"sync"

	"github.com/livekit/livekit-server/pkg/sfu"
	"github.com/livekit/protocol/rpc"

	"github.com/livekit/livekit-server/pkg/rtc/types"
	"github.com/livekit/protocol/livekit"
)

type FakeLocalParticipantHelper struct {
	GetCachedReliableDataMessageStub        func(map[livekit.ParticipantID]uint32) []*types.DataMessageCache
	getCachedReliableDataMessageMutex       sync.RWMutex
	getCachedReliableDataMessageArgsForCall []struct {
		arg1 map[livekit.ParticipantID]uint32
	}
	getCachedReliableDataMessageReturns struct {
		result1 []*types.DataMessageCache
	}
	getCachedReliableDataMessageReturnsOnCall map[int]struct {
		result1 []*types.DataMessageCache
	}
	GetParticipantInfoStub        func(livekit.ParticipantID) *livekit.ParticipantInfo
	getParticipantInfoMutex       sync.RWMutex
	getParticipantInfoArgsForCall []struct {
		arg1 livekit.ParticipantID
	}
	getParticipantInfoReturns struct {
		result1 *livekit.ParticipantInfo
	}
	getParticipantInfoReturnsOnCall map[int]struct {
		result1 *livekit.ParticipantInfo
	}
	GetRegionSettingsStub        func(string) *livekit.RegionSettings
	getRegionSettingsMutex       sync.RWMutex
	getRegionSettingsArgsForCall []struct {
		arg1 string
	}
	getRegionSettingsReturns struct {
		result1 *livekit.RegionSettings
	}
	getRegionSettingsReturnsOnCall map[int]struct {
		result1 *livekit.RegionSettings
	}
	GetSubscriberForwarderStateStub        func(types.LocalParticipant) (map[livekit.TrackID]*livekit.RTPForwarderState, error)
	getSubscriberForwarderStateMutex       sync.RWMutex
	getSubscriberForwarderStateArgsForCall []struct {
		arg1 types.LocalParticipant
	}
	getSubscriberForwarderStateReturns struct {
		result1 map[livekit.TrackID]*livekit.RTPForwarderState
		result2 error
	}
	getSubscriberForwarderStateReturnsOnCall map[int]struct {
		result1 map[livekit.TrackID]*livekit.RTPForwarderState
		result2 error
	}
	ResolveMediaTrackStub        func(types.LocalParticipant, livekit.TrackID) types.MediaResolverResult
	resolveMediaTrackMutex       sync.RWMutex
	resolveMediaTrackArgsForCall []struct {
		arg1 types.LocalParticipant
		arg2 livekit.TrackID
	}
	resolveMediaTrackReturns struct {
		result1 types.MediaResolverResult
	}
	resolveMediaTrackReturnsOnCall map[int]struct {
		result1 types.MediaResolverResult
	}
	ShouldRegressCodecStub        func() bool
	shouldRegressCodecMutex       sync.RWMutex
	shouldRegressCodecArgsForCall []struct {
	}
	shouldRegressCodecReturns struct {
		result1 bool
	}
	shouldRegressCodecReturnsOnCall map[int]struct {
		result1 bool
	}
	invocations      map[string][][]interface{}
	invocationsMutex sync.RWMutex
}

func (fake *FakeLocalParticipantHelper) GetCachedReliableDataMessage(arg1 map[livekit.ParticipantID]uint32) []*types.DataMessageCache {
	fake.getCachedReliableDataMessageMutex.Lock()
	ret, specificReturn := fake.getCachedReliableDataMessageReturnsOnCall[len(fake.getCachedReliableDataMessageArgsForCall)]
	fake.getCachedReliableDataMessageArgsForCall = append(fake.getCachedReliableDataMessageArgsForCall, struct {
		arg1 map[livekit.ParticipantID]uint32
	}{arg1})
	stub := fake.GetCachedReliableDataMessageStub
	fakeReturns := fake.getCachedReliableDataMessageReturns
	fake.recordInvocation("GetCachedReliableDataMessage", []interface{}{arg1})
	fake.getCachedReliableDataMessageMutex.Unlock()
	if stub != nil {
		return stub(arg1)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeLocalParticipantHelper) GetCachedReliableDataMessageCallCount() int {
	fake.getCachedReliableDataMessageMutex.RLock()
	defer fake.getCachedReliableDataMessageMutex.RUnlock()
	return len(fake.getCachedReliableDataMessageArgsForCall)
}

func (fake *FakeLocalParticipantHelper) GetCachedReliableDataMessageCalls(stub func(map[livekit.ParticipantID]uint32) []*types.DataMessageCache) {
	fake.getCachedReliableDataMessageMutex.Lock()
	defer fake.getCachedReliableDataMessageMutex.Unlock()
	fake.GetCachedReliableDataMessageStub = stub
}

func (fake *FakeLocalParticipantHelper) GetCachedReliableDataMessageArgsForCall(i int) map[livekit.ParticipantID]uint32 {
	fake.getCachedReliableDataMessageMutex.RLock()
	defer fake.getCachedReliableDataMessageMutex.RUnlock()
	argsForCall := fake.getCachedReliableDataMessageArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeLocalParticipantHelper) GetCachedReliableDataMessageReturns(result1 []*types.DataMessageCache) {
	fake.getCachedReliableDataMessageMutex.Lock()
	defer fake.getCachedReliableDataMessageMutex.Unlock()
	fake.GetCachedReliableDataMessageStub = nil
	fake.getCachedReliableDataMessageReturns = struct {
		result1 []*types.DataMessageCache
	}{result1}
}

func (fake *FakeLocalParticipantHelper) GetCachedReliableDataMessageReturnsOnCall(i int, result1 []*types.DataMessageCache) {
	fake.getCachedReliableDataMessageMutex.Lock()
	defer fake.getCachedReliableDataMessageMutex.Unlock()
	fake.GetCachedReliableDataMessageStub = nil
	if fake.getCachedReliableDataMessageReturnsOnCall == nil {
		fake.getCachedReliableDataMessageReturnsOnCall = make(map[int]struct {
			result1 []*types.DataMessageCache
		})
	}
	fake.getCachedReliableDataMessageReturnsOnCall[i] = struct {
		result1 []*types.DataMessageCache
	}{result1}
}

func (fake *FakeLocalParticipantHelper) GetParticipantInfo(arg1 livekit.ParticipantID) *livekit.ParticipantInfo {
	fake.getParticipantInfoMutex.Lock()
	ret, specificReturn := fake.getParticipantInfoReturnsOnCall[len(fake.getParticipantInfoArgsForCall)]
	fake.getParticipantInfoArgsForCall = append(fake.getParticipantInfoArgsForCall, struct {
		arg1 livekit.ParticipantID
	}{arg1})
	stub := fake.GetParticipantInfoStub
	fakeReturns := fake.getParticipantInfoReturns
	fake.recordInvocation("GetParticipantInfo", []interface{}{arg1})
	fake.getParticipantInfoMutex.Unlock()
	if stub != nil {
		return stub(arg1)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeLocalParticipantHelper) GetParticipantInfoCallCount() int {
	fake.getParticipantInfoMutex.RLock()
	defer fake.getParticipantInfoMutex.RUnlock()
	return len(fake.getParticipantInfoArgsForCall)
}

func (fake *FakeLocalParticipantHelper) GetParticipantInfoCalls(stub func(livekit.ParticipantID) *livekit.ParticipantInfo) {
	fake.getParticipantInfoMutex.Lock()
	defer fake.getParticipantInfoMutex.Unlock()
	fake.GetParticipantInfoStub = stub
}

func (fake *FakeLocalParticipantHelper) GetParticipantInfoArgsForCall(i int) livekit.ParticipantID {
	fake.getParticipantInfoMutex.RLock()
	defer fake.getParticipantInfoMutex.RUnlock()
	argsForCall := fake.getParticipantInfoArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeLocalParticipantHelper) GetParticipantInfoReturns(result1 *livekit.ParticipantInfo) {
	fake.getParticipantInfoMutex.Lock()
	defer fake.getParticipantInfoMutex.Unlock()
	fake.GetParticipantInfoStub = nil
	fake.getParticipantInfoReturns = struct {
		result1 *livekit.ParticipantInfo
	}{result1}
}

func (fake *FakeLocalParticipantHelper) GetParticipantInfoReturnsOnCall(i int, result1 *livekit.ParticipantInfo) {
	fake.getParticipantInfoMutex.Lock()
	defer fake.getParticipantInfoMutex.Unlock()
	fake.GetParticipantInfoStub = nil
	if fake.getParticipantInfoReturnsOnCall == nil {
		fake.getParticipantInfoReturnsOnCall = make(map[int]struct {
			result1 *livekit.ParticipantInfo
		})
	}
	fake.getParticipantInfoReturnsOnCall[i] = struct {
		result1 *livekit.ParticipantInfo
	}{result1}
}

func (fake *FakeLocalParticipantHelper) GetRegionSettings(arg1 string) *livekit.RegionSettings {
	fake.getRegionSettingsMutex.Lock()
	ret, specificReturn := fake.getRegionSettingsReturnsOnCall[len(fake.getRegionSettingsArgsForCall)]
	fake.getRegionSettingsArgsForCall = append(fake.getRegionSettingsArgsForCall, struct {
		arg1 string
	}{arg1})
	stub := fake.GetRegionSettingsStub
	fakeReturns := fake.getRegionSettingsReturns
	fake.recordInvocation("GetRegionSettings", []interface{}{arg1})
	fake.getRegionSettingsMutex.Unlock()
	if stub != nil {
		return stub(arg1)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeLocalParticipantHelper) GetRegionSettingsCallCount() int {
	fake.getRegionSettingsMutex.RLock()
	defer fake.getRegionSettingsMutex.RUnlock()
	return len(fake.getRegionSettingsArgsForCall)
}

func (fake *FakeLocalParticipantHelper) GetRegionSettingsCalls(stub func(string) *livekit.RegionSettings) {
	fake.getRegionSettingsMutex.Lock()
	defer fake.getRegionSettingsMutex.Unlock()
	fake.GetRegionSettingsStub = stub
}

func (fake *FakeLocalParticipantHelper) GetRegionSettingsArgsForCall(i int) string {
	fake.getRegionSettingsMutex.RLock()
	defer fake.getRegionSettingsMutex.RUnlock()
	argsForCall := fake.getRegionSettingsArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeLocalParticipantHelper) GetRegionSettingsReturns(result1 *livekit.RegionSettings) {
	fake.getRegionSettingsMutex.Lock()
	defer fake.getRegionSettingsMutex.Unlock()
	fake.GetRegionSettingsStub = nil
	fake.getRegionSettingsReturns = struct {
		result1 *livekit.RegionSettings
	}{result1}
}

func (fake *FakeLocalParticipantHelper) GetRegionSettingsReturnsOnCall(i int, result1 *livekit.RegionSettings) {
	fake.getRegionSettingsMutex.Lock()
	defer fake.getRegionSettingsMutex.Unlock()
	fake.GetRegionSettingsStub = nil
	if fake.getRegionSettingsReturnsOnCall == nil {
		fake.getRegionSettingsReturnsOnCall = make(map[int]struct {
			result1 *livekit.RegionSettings
		})
	}
	fake.getRegionSettingsReturnsOnCall[i] = struct {
		result1 *livekit.RegionSettings
	}{result1}
}

func (fake *FakeLocalParticipantHelper) GetSubscriberForwarderState(arg1 types.LocalParticipant) (map[livekit.TrackID]*livekit.RTPForwarderState, error) {
	fake.getSubscriberForwarderStateMutex.Lock()
	ret, specificReturn := fake.getSubscriberForwarderStateReturnsOnCall[len(fake.getSubscriberForwarderStateArgsForCall)]
	fake.getSubscriberForwarderStateArgsForCall = append(fake.getSubscriberForwarderStateArgsForCall, struct {
		arg1 types.LocalParticipant
	}{arg1})
	stub := fake.GetSubscriberForwarderStateStub
	fakeReturns := fake.getSubscriberForwarderStateReturns
	fake.recordInvocation("GetSubscriberForwarderState", []interface{}{arg1})
	fake.getSubscriberForwarderStateMutex.Unlock()
	if stub != nil {
		return stub(arg1)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeLocalParticipantHelper) GetSubscriberForwarderStateCallCount() int {
	fake.getSubscriberForwarderStateMutex.RLock()
	defer fake.getSubscriberForwarderStateMutex.RUnlock()
	return len(fake.getSubscriberForwarderStateArgsForCall)
}

func (fake *FakeLocalParticipantHelper) GetSubscriberForwarderStateCalls(stub func(types.LocalParticipant) (map[livekit.TrackID]*livekit.RTPForwarderState, error)) {
	fake.getSubscriberForwarderStateMutex.Lock()
	defer fake.getSubscriberForwarderStateMutex.Unlock()
	fake.GetSubscriberForwarderStateStub = stub
}

func (fake *FakeLocalParticipantHelper) GetSubscriberForwarderStateArgsForCall(i int) types.LocalParticipant {
	fake.getSubscriberForwarderStateMutex.RLock()
	defer fake.getSubscriberForwarderStateMutex.RUnlock()
	argsForCall := fake.getSubscriberForwarderStateArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeLocalParticipantHelper) GetSubscriberForwarderStateReturns(result1 map[livekit.TrackID]*livekit.RTPForwarderState, result2 error) {
	fake.getSubscriberForwarderStateMutex.Lock()
	defer fake.getSubscriberForwarderStateMutex.Unlock()
	fake.GetSubscriberForwarderStateStub = nil
	fake.getSubscriberForwarderStateReturns = struct {
		result1 map[livekit.TrackID]*livekit.RTPForwarderState
		result2 error
	}{result1, result2}
}

func (fake *FakeLocalParticipantHelper) GetSubscriberForwarderStateReturnsOnCall(i int, result1 map[livekit.TrackID]*livekit.RTPForwarderState, result2 error) {
	fake.getSubscriberForwarderStateMutex.Lock()
	defer fake.getSubscriberForwarderStateMutex.Unlock()
	fake.GetSubscriberForwarderStateStub = nil
	if fake.getSubscriberForwarderStateReturnsOnCall == nil {
		fake.getSubscriberForwarderStateReturnsOnCall = make(map[int]struct {
			result1 map[livekit.TrackID]*livekit.RTPForwarderState
			result2 error
		})
	}
	fake.getSubscriberForwarderStateReturnsOnCall[i] = struct {
		result1 map[livekit.TrackID]*livekit.RTPForwarderState
		result2 error
	}{result1, result2}
}

func (fake *FakeLocalParticipantHelper) ResolveMediaTrack(arg1 types.LocalParticipant, arg2 livekit.TrackID) types.MediaResolverResult {
	fake.resolveMediaTrackMutex.Lock()
	ret, specificReturn := fake.resolveMediaTrackReturnsOnCall[len(fake.resolveMediaTrackArgsForCall)]
	fake.resolveMediaTrackArgsForCall = append(fake.resolveMediaTrackArgsForCall, struct {
		arg1 types.LocalParticipant
		arg2 livekit.TrackID
	}{arg1, arg2})
	stub := fake.ResolveMediaTrackStub
	fakeReturns := fake.resolveMediaTrackReturns
	fake.recordInvocation("ResolveMediaTrack", []interface{}{arg1, arg2})
	fake.resolveMediaTrackMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeLocalParticipantHelper) ResolveMediaTrackCallCount() int {
	fake.resolveMediaTrackMutex.RLock()
	defer fake.resolveMediaTrackMutex.RUnlock()
	return len(fake.resolveMediaTrackArgsForCall)
}

func (fake *FakeLocalParticipantHelper) ResolveMediaTrackCalls(stub func(types.LocalParticipant, livekit.TrackID) types.MediaResolverResult) {
	fake.resolveMediaTrackMutex.Lock()
	defer fake.resolveMediaTrackMutex.Unlock()
	fake.ResolveMediaTrackStub = stub
}

func (fake *FakeLocalParticipantHelper) ResolveMediaTrackArgsForCall(i int) (types.LocalParticipant, livekit.TrackID) {
	fake.resolveMediaTrackMutex.RLock()
	defer fake.resolveMediaTrackMutex.RUnlock()
	argsForCall := fake.resolveMediaTrackArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeLocalParticipantHelper) ResolveMediaTrackReturns(result1 types.MediaResolverResult) {
	fake.resolveMediaTrackMutex.Lock()
	defer fake.resolveMediaTrackMutex.Unlock()
	fake.ResolveMediaTrackStub = nil
	fake.resolveMediaTrackReturns = struct {
		result1 types.MediaResolverResult
	}{result1}
}

func (fake *FakeLocalParticipantHelper) ResolveMediaTrackReturnsOnCall(i int, result1 types.MediaResolverResult) {
	fake.resolveMediaTrackMutex.Lock()
	defer fake.resolveMediaTrackMutex.Unlock()
	fake.ResolveMediaTrackStub = nil
	if fake.resolveMediaTrackReturnsOnCall == nil {
		fake.resolveMediaTrackReturnsOnCall = make(map[int]struct {
			result1 types.MediaResolverResult
		})
	}
	fake.resolveMediaTrackReturnsOnCall[i] = struct {
		result1 types.MediaResolverResult
	}{result1}
}

func (fake *FakeLocalParticipantHelper) ShouldRegressCodec() bool {
	fake.shouldRegressCodecMutex.Lock()
	ret, specificReturn := fake.shouldRegressCodecReturnsOnCall[len(fake.shouldRegressCodecArgsForCall)]
	fake.shouldRegressCodecArgsForCall = append(fake.shouldRegressCodecArgsForCall, struct {
	}{})
	stub := fake.ShouldRegressCodecStub
	fakeReturns := fake.shouldRegressCodecReturns
	fake.recordInvocation("ShouldRegressCodec", []interface{}{})
	fake.shouldRegressCodecMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeLocalParticipantHelper) ShouldRegressCodecCallCount() int {
	fake.shouldRegressCodecMutex.RLock()
	defer fake.shouldRegressCodecMutex.RUnlock()
	return len(fake.shouldRegressCodecArgsForCall)
}

func (fake *FakeLocalParticipantHelper) ShouldRegressCodecCalls(stub func() bool) {
	fake.shouldRegressCodecMutex.Lock()
	defer fake.shouldRegressCodecMutex.Unlock()
	fake.ShouldRegressCodecStub = stub
}

func (fake *FakeLocalParticipantHelper) ShouldRegressCodecReturns(result1 bool) {
	fake.shouldRegressCodecMutex.Lock()
	defer fake.shouldRegressCodecMutex.Unlock()
	fake.ShouldRegressCodecStub = nil
	fake.shouldRegressCodecReturns = struct {
		result1 bool
	}{result1}
}

func (fake *FakeLocalParticipantHelper) ShouldRegressCodecReturnsOnCall(i int, result1 bool) {
	fake.shouldRegressCodecMutex.Lock()
	defer fake.shouldRegressCodecMutex.Unlock()
	fake.ShouldRegressCodecStub = nil
	if fake.shouldRegressCodecReturnsOnCall == nil {
		fake.shouldRegressCodecReturnsOnCall = make(map[int]struct {
			result1 bool
		})
	}
	fake.shouldRegressCodecReturnsOnCall[i] = struct {
		result1 bool
	}{result1}
}

func (fake *FakeLocalParticipantHelper) Invocations() map[string][][]interface{} {
	fake.invocationsMutex.RLock()
	defer fake.invocationsMutex.RUnlock()
	fake.getCachedReliableDataMessageMutex.RLock()
	defer fake.getCachedReliableDataMessageMutex.RUnlock()
	fake.getParticipantInfoMutex.RLock()
	defer fake.getParticipantInfoMutex.RUnlock()
	fake.getRegionSettingsMutex.RLock()
	defer fake.getRegionSettingsMutex.RUnlock()
	fake.getSubscriberForwarderStateMutex.RLock()
	defer fake.getSubscriberForwarderStateMutex.RUnlock()
	fake.resolveMediaTrackMutex.RLock()
	defer fake.resolveMediaTrackMutex.RUnlock()
	fake.shouldRegressCodecMutex.RLock()
	defer fake.shouldRegressCodecMutex.RUnlock()
	copiedInvocations := map[string][][]interface{}{}
	for key, value := range fake.invocations {
		copiedInvocations[key] = value
	}
	return copiedInvocations
}

func (fake *FakeLocalParticipantHelper) recordInvocation(key string, args []interface{}) {
	fake.invocationsMutex.Lock()
	defer fake.invocationsMutex.Unlock()
	if fake.invocations == nil {
		fake.invocations = map[string][][]interface{}{}
	}
	if fake.invocations[key] == nil {
		fake.invocations[key] = [][]interface{}{}
	}
	fake.invocations[key] = append(fake.invocations[key], args)
}

func (fake *FakeLocalParticipantHelper) GetMediaRelayClient(nodeID livekit.NodeID) (rpc.TypedMediaRelayClient, error) {
	return nil, nil
}

func (h *FakeLocalParticipantHelper) GetMediaRelayChan(startRequest *rpc.StartRequest) (sfu.MessageSource, error) {
	return nil, nil
}

var _ types.LocalParticipantHelper = new(FakeLocalParticipantHelper)
