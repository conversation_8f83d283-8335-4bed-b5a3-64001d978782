// Code generated by counterfeiter. DO NOT EDIT.
package typesfakes

import (
	"sync"

	"github.com/livekit/livekit-server/pkg/rtc/types"
	"github.com/livekit/livekit-server/pkg/sfu"
	"github.com/livekit/livekit-server/pkg/sfu/mime"
	"github.com/livekit/protocol/livekit"
	"github.com/livekit/protocol/logger"
)

type FakeMediaTrack struct {
	AddOnCloseStub        func(func(isExpectedToResume bool))
	addOnCloseMutex       sync.RWMutex
	addOnCloseArgsForCall []struct {
		arg1 func(isExpectedToResume bool)
	}
	AddSubscriberStub        func(types.LocalParticipant) (types.SubscribedTrack, error)
	addSubscriberMutex       sync.RWMutex
	addSubscriberArgsForCall []struct {
		arg1 types.LocalParticipant
	}
	addSubscriberReturns struct {
		result1 types.SubscribedTrack
		result2 error
	}
	addSubscriberReturnsOnCall map[int]struct {
		result1 types.SubscribedTrack
		result2 error
	}
	ClearAllReceiversStub        func(bool)
	clearAllReceiversMutex       sync.RWMutex
	clearAllReceiversArgsForCall []struct {
		arg1 bool
	}
	CloseStub        func(bool)
	closeMutex       sync.RWMutex
	closeArgsForCall []struct {
		arg1 bool
	}
	GetAllSubscribersStub        func() []livekit.ParticipantID
	getAllSubscribersMutex       sync.RWMutex
	getAllSubscribersArgsForCall []struct {
	}
	getAllSubscribersReturns struct {
		result1 []livekit.ParticipantID
	}
	getAllSubscribersReturnsOnCall map[int]struct {
		result1 []livekit.ParticipantID
	}
	GetAudioLevelStub        func() (float64, bool)
	getAudioLevelMutex       sync.RWMutex
	getAudioLevelArgsForCall []struct {
	}
	getAudioLevelReturns struct {
		result1 float64
		result2 bool
	}
	getAudioLevelReturnsOnCall map[int]struct {
		result1 float64
		result2 bool
	}
	GetNumSubscribersStub        func() int
	getNumSubscribersMutex       sync.RWMutex
	getNumSubscribersArgsForCall []struct {
	}
	getNumSubscribersReturns struct {
		result1 int
	}
	getNumSubscribersReturnsOnCall map[int]struct {
		result1 int
	}
	GetQualityForDimensionStub        func(uint32, uint32) livekit.VideoQuality
	getQualityForDimensionMutex       sync.RWMutex
	getQualityForDimensionArgsForCall []struct {
		arg1 uint32
		arg2 uint32
	}
	getQualityForDimensionReturns struct {
		result1 livekit.VideoQuality
	}
	getQualityForDimensionReturnsOnCall map[int]struct {
		result1 livekit.VideoQuality
	}
	GetTemporalLayerForSpatialFpsStub        func(int32, uint32, mime.MimeType) int32
	getTemporalLayerForSpatialFpsMutex       sync.RWMutex
	getTemporalLayerForSpatialFpsArgsForCall []struct {
		arg1 int32
		arg2 uint32
		arg3 mime.MimeType
	}
	getTemporalLayerForSpatialFpsReturns struct {
		result1 int32
	}
	getTemporalLayerForSpatialFpsReturnsOnCall map[int]struct {
		result1 int32
	}
	IDStub        func() livekit.TrackID
	iDMutex       sync.RWMutex
	iDArgsForCall []struct {
	}
	iDReturns struct {
		result1 livekit.TrackID
	}
	iDReturnsOnCall map[int]struct {
		result1 livekit.TrackID
	}
	IsEncryptedStub        func() bool
	isEncryptedMutex       sync.RWMutex
	isEncryptedArgsForCall []struct {
	}
	isEncryptedReturns struct {
		result1 bool
	}
	isEncryptedReturnsOnCall map[int]struct {
		result1 bool
	}
	IsMutedStub        func() bool
	isMutedMutex       sync.RWMutex
	isMutedArgsForCall []struct {
	}
	isMutedReturns struct {
		result1 bool
	}
	isMutedReturnsOnCall map[int]struct {
		result1 bool
	}
	IsOpenStub        func() bool
	isOpenMutex       sync.RWMutex
	isOpenArgsForCall []struct {
	}
	isOpenReturns struct {
		result1 bool
	}
	isOpenReturnsOnCall map[int]struct {
		result1 bool
	}
	IsSimulcastStub        func() bool
	isSimulcastMutex       sync.RWMutex
	isSimulcastArgsForCall []struct {
	}
	isSimulcastReturns struct {
		result1 bool
	}
	isSimulcastReturnsOnCall map[int]struct {
		result1 bool
	}
	IsSubscriberStub        func(livekit.ParticipantID) bool
	isSubscriberMutex       sync.RWMutex
	isSubscriberArgsForCall []struct {
		arg1 livekit.ParticipantID
	}
	isSubscriberReturns struct {
		result1 bool
	}
	isSubscriberReturnsOnCall map[int]struct {
		result1 bool
	}
	KindStub        func() livekit.TrackType
	kindMutex       sync.RWMutex
	kindArgsForCall []struct {
	}
	kindReturns struct {
		result1 livekit.TrackType
	}
	kindReturnsOnCall map[int]struct {
		result1 livekit.TrackType
	}
	LoggerStub        func() logger.Logger
	loggerMutex       sync.RWMutex
	loggerArgsForCall []struct {
	}
	loggerReturns struct {
		result1 logger.Logger
	}
	loggerReturnsOnCall map[int]struct {
		result1 logger.Logger
	}
	NameStub        func() string
	nameMutex       sync.RWMutex
	nameArgsForCall []struct {
	}
	nameReturns struct {
		result1 string
	}
	nameReturnsOnCall map[int]struct {
		result1 string
	}
	OnTrackSubscribedStub        func()
	onTrackSubscribedMutex       sync.RWMutex
	onTrackSubscribedArgsForCall []struct {
	}
	PublisherIDStub        func() livekit.ParticipantID
	publisherIDMutex       sync.RWMutex
	publisherIDArgsForCall []struct {
	}
	publisherIDReturns struct {
		result1 livekit.ParticipantID
	}
	publisherIDReturnsOnCall map[int]struct {
		result1 livekit.ParticipantID
	}
	PublisherIdentityStub        func() livekit.ParticipantIdentity
	publisherIdentityMutex       sync.RWMutex
	publisherIdentityArgsForCall []struct {
	}
	publisherIdentityReturns struct {
		result1 livekit.ParticipantIdentity
	}
	publisherIdentityReturnsOnCall map[int]struct {
		result1 livekit.ParticipantIdentity
	}
	PublisherVersionStub        func() uint32
	publisherVersionMutex       sync.RWMutex
	publisherVersionArgsForCall []struct {
	}
	publisherVersionReturns struct {
		result1 uint32
	}
	publisherVersionReturnsOnCall map[int]struct {
		result1 uint32
	}
	ReceiversStub        func() []sfu.TrackReceiver
	receiversMutex       sync.RWMutex
	receiversArgsForCall []struct {
	}
	receiversReturns struct {
		result1 []sfu.TrackReceiver
	}
	receiversReturnsOnCall map[int]struct {
		result1 []sfu.TrackReceiver
	}
	RemoveSubscriberStub        func(livekit.ParticipantID, bool)
	removeSubscriberMutex       sync.RWMutex
	removeSubscriberArgsForCall []struct {
		arg1 livekit.ParticipantID
		arg2 bool
	}
	RevokeDisallowedSubscribersStub        func([]livekit.ParticipantIdentity) []livekit.ParticipantIdentity
	revokeDisallowedSubscribersMutex       sync.RWMutex
	revokeDisallowedSubscribersArgsForCall []struct {
		arg1 []livekit.ParticipantIdentity
	}
	revokeDisallowedSubscribersReturns struct {
		result1 []livekit.ParticipantIdentity
	}
	revokeDisallowedSubscribersReturnsOnCall map[int]struct {
		result1 []livekit.ParticipantIdentity
	}
	SetMutedStub        func(bool)
	setMutedMutex       sync.RWMutex
	setMutedArgsForCall []struct {
		arg1 bool
	}
	SourceStub        func() livekit.TrackSource
	sourceMutex       sync.RWMutex
	sourceArgsForCall []struct {
	}
	sourceReturns struct {
		result1 livekit.TrackSource
	}
	sourceReturnsOnCall map[int]struct {
		result1 livekit.TrackSource
	}
	StreamStub        func() string
	streamMutex       sync.RWMutex
	streamArgsForCall []struct {
	}
	streamReturns struct {
		result1 string
	}
	streamReturnsOnCall map[int]struct {
		result1 string
	}
	ToProtoStub        func() *livekit.TrackInfo
	toProtoMutex       sync.RWMutex
	toProtoArgsForCall []struct {
	}
	toProtoReturns struct {
		result1 *livekit.TrackInfo
	}
	toProtoReturnsOnCall map[int]struct {
		result1 *livekit.TrackInfo
	}
	UpdateAudioTrackStub        func(*livekit.UpdateLocalAudioTrack)
	updateAudioTrackMutex       sync.RWMutex
	updateAudioTrackArgsForCall []struct {
		arg1 *livekit.UpdateLocalAudioTrack
	}
	UpdateTrackInfoStub        func(*livekit.TrackInfo)
	updateTrackInfoMutex       sync.RWMutex
	updateTrackInfoArgsForCall []struct {
		arg1 *livekit.TrackInfo
	}
	UpdateVideoTrackStub        func(*livekit.UpdateLocalVideoTrack)
	updateVideoTrackMutex       sync.RWMutex
	updateVideoTrackArgsForCall []struct {
		arg1 *livekit.UpdateLocalVideoTrack
	}
	invocations      map[string][][]interface{}
	invocationsMutex sync.RWMutex
}

func (fake *FakeMediaTrack) AddOnClose(arg1 func(isExpectedToResume bool)) {
	fake.addOnCloseMutex.Lock()
	fake.addOnCloseArgsForCall = append(fake.addOnCloseArgsForCall, struct {
		arg1 func(isExpectedToResume bool)
	}{arg1})
	stub := fake.AddOnCloseStub
	fake.recordInvocation("AddOnClose", []interface{}{arg1})
	fake.addOnCloseMutex.Unlock()
	if stub != nil {
		fake.AddOnCloseStub(arg1)
	}
}

func (fake *FakeMediaTrack) AddOnCloseCallCount() int {
	fake.addOnCloseMutex.RLock()
	defer fake.addOnCloseMutex.RUnlock()
	return len(fake.addOnCloseArgsForCall)
}

func (fake *FakeMediaTrack) AddOnCloseCalls(stub func(func(isExpectedToResume bool))) {
	fake.addOnCloseMutex.Lock()
	defer fake.addOnCloseMutex.Unlock()
	fake.AddOnCloseStub = stub
}

func (fake *FakeMediaTrack) AddOnCloseArgsForCall(i int) func(isExpectedToResume bool) {
	fake.addOnCloseMutex.RLock()
	defer fake.addOnCloseMutex.RUnlock()
	argsForCall := fake.addOnCloseArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeMediaTrack) AddSubscriber(arg1 types.LocalParticipant) (types.SubscribedTrack, error) {
	fake.addSubscriberMutex.Lock()
	ret, specificReturn := fake.addSubscriberReturnsOnCall[len(fake.addSubscriberArgsForCall)]
	fake.addSubscriberArgsForCall = append(fake.addSubscriberArgsForCall, struct {
		arg1 types.LocalParticipant
	}{arg1})
	stub := fake.AddSubscriberStub
	fakeReturns := fake.addSubscriberReturns
	fake.recordInvocation("AddSubscriber", []interface{}{arg1})
	fake.addSubscriberMutex.Unlock()
	if stub != nil {
		return stub(arg1)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeMediaTrack) AddSubscriberCallCount() int {
	fake.addSubscriberMutex.RLock()
	defer fake.addSubscriberMutex.RUnlock()
	return len(fake.addSubscriberArgsForCall)
}

func (fake *FakeMediaTrack) AddSubscriberCalls(stub func(types.LocalParticipant) (types.SubscribedTrack, error)) {
	fake.addSubscriberMutex.Lock()
	defer fake.addSubscriberMutex.Unlock()
	fake.AddSubscriberStub = stub
}

func (fake *FakeMediaTrack) AddSubscriberArgsForCall(i int) types.LocalParticipant {
	fake.addSubscriberMutex.RLock()
	defer fake.addSubscriberMutex.RUnlock()
	argsForCall := fake.addSubscriberArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeMediaTrack) AddSubscriberReturns(result1 types.SubscribedTrack, result2 error) {
	fake.addSubscriberMutex.Lock()
	defer fake.addSubscriberMutex.Unlock()
	fake.AddSubscriberStub = nil
	fake.addSubscriberReturns = struct {
		result1 types.SubscribedTrack
		result2 error
	}{result1, result2}
}

func (fake *FakeMediaTrack) AddSubscriberReturnsOnCall(i int, result1 types.SubscribedTrack, result2 error) {
	fake.addSubscriberMutex.Lock()
	defer fake.addSubscriberMutex.Unlock()
	fake.AddSubscriberStub = nil
	if fake.addSubscriberReturnsOnCall == nil {
		fake.addSubscriberReturnsOnCall = make(map[int]struct {
			result1 types.SubscribedTrack
			result2 error
		})
	}
	fake.addSubscriberReturnsOnCall[i] = struct {
		result1 types.SubscribedTrack
		result2 error
	}{result1, result2}
}

func (fake *FakeMediaTrack) ClearAllReceivers(arg1 bool) {
	fake.clearAllReceiversMutex.Lock()
	fake.clearAllReceiversArgsForCall = append(fake.clearAllReceiversArgsForCall, struct {
		arg1 bool
	}{arg1})
	stub := fake.ClearAllReceiversStub
	fake.recordInvocation("ClearAllReceivers", []interface{}{arg1})
	fake.clearAllReceiversMutex.Unlock()
	if stub != nil {
		fake.ClearAllReceiversStub(arg1)
	}
}

func (fake *FakeMediaTrack) ClearAllReceiversCallCount() int {
	fake.clearAllReceiversMutex.RLock()
	defer fake.clearAllReceiversMutex.RUnlock()
	return len(fake.clearAllReceiversArgsForCall)
}

func (fake *FakeMediaTrack) ClearAllReceiversCalls(stub func(bool)) {
	fake.clearAllReceiversMutex.Lock()
	defer fake.clearAllReceiversMutex.Unlock()
	fake.ClearAllReceiversStub = stub
}

func (fake *FakeMediaTrack) ClearAllReceiversArgsForCall(i int) bool {
	fake.clearAllReceiversMutex.RLock()
	defer fake.clearAllReceiversMutex.RUnlock()
	argsForCall := fake.clearAllReceiversArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeMediaTrack) Close(arg1 bool) {
	fake.closeMutex.Lock()
	fake.closeArgsForCall = append(fake.closeArgsForCall, struct {
		arg1 bool
	}{arg1})
	stub := fake.CloseStub
	fake.recordInvocation("Close", []interface{}{arg1})
	fake.closeMutex.Unlock()
	if stub != nil {
		fake.CloseStub(arg1)
	}
}

func (fake *FakeMediaTrack) CloseCallCount() int {
	fake.closeMutex.RLock()
	defer fake.closeMutex.RUnlock()
	return len(fake.closeArgsForCall)
}

func (fake *FakeMediaTrack) CloseCalls(stub func(bool)) {
	fake.closeMutex.Lock()
	defer fake.closeMutex.Unlock()
	fake.CloseStub = stub
}

func (fake *FakeMediaTrack) CloseArgsForCall(i int) bool {
	fake.closeMutex.RLock()
	defer fake.closeMutex.RUnlock()
	argsForCall := fake.closeArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeMediaTrack) GetAllSubscribers() []livekit.ParticipantID {
	fake.getAllSubscribersMutex.Lock()
	ret, specificReturn := fake.getAllSubscribersReturnsOnCall[len(fake.getAllSubscribersArgsForCall)]
	fake.getAllSubscribersArgsForCall = append(fake.getAllSubscribersArgsForCall, struct {
	}{})
	stub := fake.GetAllSubscribersStub
	fakeReturns := fake.getAllSubscribersReturns
	fake.recordInvocation("GetAllSubscribers", []interface{}{})
	fake.getAllSubscribersMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeMediaTrack) GetAllSubscribersCallCount() int {
	fake.getAllSubscribersMutex.RLock()
	defer fake.getAllSubscribersMutex.RUnlock()
	return len(fake.getAllSubscribersArgsForCall)
}

func (fake *FakeMediaTrack) GetAllSubscribersCalls(stub func() []livekit.ParticipantID) {
	fake.getAllSubscribersMutex.Lock()
	defer fake.getAllSubscribersMutex.Unlock()
	fake.GetAllSubscribersStub = stub
}

func (fake *FakeMediaTrack) GetAllSubscribersReturns(result1 []livekit.ParticipantID) {
	fake.getAllSubscribersMutex.Lock()
	defer fake.getAllSubscribersMutex.Unlock()
	fake.GetAllSubscribersStub = nil
	fake.getAllSubscribersReturns = struct {
		result1 []livekit.ParticipantID
	}{result1}
}

func (fake *FakeMediaTrack) GetAllSubscribersReturnsOnCall(i int, result1 []livekit.ParticipantID) {
	fake.getAllSubscribersMutex.Lock()
	defer fake.getAllSubscribersMutex.Unlock()
	fake.GetAllSubscribersStub = nil
	if fake.getAllSubscribersReturnsOnCall == nil {
		fake.getAllSubscribersReturnsOnCall = make(map[int]struct {
			result1 []livekit.ParticipantID
		})
	}
	fake.getAllSubscribersReturnsOnCall[i] = struct {
		result1 []livekit.ParticipantID
	}{result1}
}

func (fake *FakeMediaTrack) GetAudioLevel() (float64, bool) {
	fake.getAudioLevelMutex.Lock()
	ret, specificReturn := fake.getAudioLevelReturnsOnCall[len(fake.getAudioLevelArgsForCall)]
	fake.getAudioLevelArgsForCall = append(fake.getAudioLevelArgsForCall, struct {
	}{})
	stub := fake.GetAudioLevelStub
	fakeReturns := fake.getAudioLevelReturns
	fake.recordInvocation("GetAudioLevel", []interface{}{})
	fake.getAudioLevelMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeMediaTrack) GetAudioLevelCallCount() int {
	fake.getAudioLevelMutex.RLock()
	defer fake.getAudioLevelMutex.RUnlock()
	return len(fake.getAudioLevelArgsForCall)
}

func (fake *FakeMediaTrack) GetAudioLevelCalls(stub func() (float64, bool)) {
	fake.getAudioLevelMutex.Lock()
	defer fake.getAudioLevelMutex.Unlock()
	fake.GetAudioLevelStub = stub
}

func (fake *FakeMediaTrack) GetAudioLevelReturns(result1 float64, result2 bool) {
	fake.getAudioLevelMutex.Lock()
	defer fake.getAudioLevelMutex.Unlock()
	fake.GetAudioLevelStub = nil
	fake.getAudioLevelReturns = struct {
		result1 float64
		result2 bool
	}{result1, result2}
}

func (fake *FakeMediaTrack) GetAudioLevelReturnsOnCall(i int, result1 float64, result2 bool) {
	fake.getAudioLevelMutex.Lock()
	defer fake.getAudioLevelMutex.Unlock()
	fake.GetAudioLevelStub = nil
	if fake.getAudioLevelReturnsOnCall == nil {
		fake.getAudioLevelReturnsOnCall = make(map[int]struct {
			result1 float64
			result2 bool
		})
	}
	fake.getAudioLevelReturnsOnCall[i] = struct {
		result1 float64
		result2 bool
	}{result1, result2}
}

func (fake *FakeMediaTrack) GetNumSubscribers() int {
	fake.getNumSubscribersMutex.Lock()
	ret, specificReturn := fake.getNumSubscribersReturnsOnCall[len(fake.getNumSubscribersArgsForCall)]
	fake.getNumSubscribersArgsForCall = append(fake.getNumSubscribersArgsForCall, struct {
	}{})
	stub := fake.GetNumSubscribersStub
	fakeReturns := fake.getNumSubscribersReturns
	fake.recordInvocation("GetNumSubscribers", []interface{}{})
	fake.getNumSubscribersMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeMediaTrack) GetNumSubscribersCallCount() int {
	fake.getNumSubscribersMutex.RLock()
	defer fake.getNumSubscribersMutex.RUnlock()
	return len(fake.getNumSubscribersArgsForCall)
}

func (fake *FakeMediaTrack) GetNumSubscribersCalls(stub func() int) {
	fake.getNumSubscribersMutex.Lock()
	defer fake.getNumSubscribersMutex.Unlock()
	fake.GetNumSubscribersStub = stub
}

func (fake *FakeMediaTrack) GetNumSubscribersReturns(result1 int) {
	fake.getNumSubscribersMutex.Lock()
	defer fake.getNumSubscribersMutex.Unlock()
	fake.GetNumSubscribersStub = nil
	fake.getNumSubscribersReturns = struct {
		result1 int
	}{result1}
}

func (fake *FakeMediaTrack) GetNumSubscribersReturnsOnCall(i int, result1 int) {
	fake.getNumSubscribersMutex.Lock()
	defer fake.getNumSubscribersMutex.Unlock()
	fake.GetNumSubscribersStub = nil
	if fake.getNumSubscribersReturnsOnCall == nil {
		fake.getNumSubscribersReturnsOnCall = make(map[int]struct {
			result1 int
		})
	}
	fake.getNumSubscribersReturnsOnCall[i] = struct {
		result1 int
	}{result1}
}

func (fake *FakeMediaTrack) GetQualityForDimension(arg1 uint32, arg2 uint32) livekit.VideoQuality {
	fake.getQualityForDimensionMutex.Lock()
	ret, specificReturn := fake.getQualityForDimensionReturnsOnCall[len(fake.getQualityForDimensionArgsForCall)]
	fake.getQualityForDimensionArgsForCall = append(fake.getQualityForDimensionArgsForCall, struct {
		arg1 uint32
		arg2 uint32
	}{arg1, arg2})
	stub := fake.GetQualityForDimensionStub
	fakeReturns := fake.getQualityForDimensionReturns
	fake.recordInvocation("GetQualityForDimension", []interface{}{arg1, arg2})
	fake.getQualityForDimensionMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeMediaTrack) GetQualityForDimensionCallCount() int {
	fake.getQualityForDimensionMutex.RLock()
	defer fake.getQualityForDimensionMutex.RUnlock()
	return len(fake.getQualityForDimensionArgsForCall)
}

func (fake *FakeMediaTrack) GetQualityForDimensionCalls(stub func(uint32, uint32) livekit.VideoQuality) {
	fake.getQualityForDimensionMutex.Lock()
	defer fake.getQualityForDimensionMutex.Unlock()
	fake.GetQualityForDimensionStub = stub
}

func (fake *FakeMediaTrack) GetQualityForDimensionArgsForCall(i int) (uint32, uint32) {
	fake.getQualityForDimensionMutex.RLock()
	defer fake.getQualityForDimensionMutex.RUnlock()
	argsForCall := fake.getQualityForDimensionArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeMediaTrack) GetQualityForDimensionReturns(result1 livekit.VideoQuality) {
	fake.getQualityForDimensionMutex.Lock()
	defer fake.getQualityForDimensionMutex.Unlock()
	fake.GetQualityForDimensionStub = nil
	fake.getQualityForDimensionReturns = struct {
		result1 livekit.VideoQuality
	}{result1}
}

func (fake *FakeMediaTrack) GetQualityForDimensionReturnsOnCall(i int, result1 livekit.VideoQuality) {
	fake.getQualityForDimensionMutex.Lock()
	defer fake.getQualityForDimensionMutex.Unlock()
	fake.GetQualityForDimensionStub = nil
	if fake.getQualityForDimensionReturnsOnCall == nil {
		fake.getQualityForDimensionReturnsOnCall = make(map[int]struct {
			result1 livekit.VideoQuality
		})
	}
	fake.getQualityForDimensionReturnsOnCall[i] = struct {
		result1 livekit.VideoQuality
	}{result1}
}

func (fake *FakeMediaTrack) GetTemporalLayerForSpatialFps(arg1 int32, arg2 uint32, arg3 mime.MimeType) int32 {
	fake.getTemporalLayerForSpatialFpsMutex.Lock()
	ret, specificReturn := fake.getTemporalLayerForSpatialFpsReturnsOnCall[len(fake.getTemporalLayerForSpatialFpsArgsForCall)]
	fake.getTemporalLayerForSpatialFpsArgsForCall = append(fake.getTemporalLayerForSpatialFpsArgsForCall, struct {
		arg1 int32
		arg2 uint32
		arg3 mime.MimeType
	}{arg1, arg2, arg3})
	stub := fake.GetTemporalLayerForSpatialFpsStub
	fakeReturns := fake.getTemporalLayerForSpatialFpsReturns
	fake.recordInvocation("GetTemporalLayerForSpatialFps", []interface{}{arg1, arg2, arg3})
	fake.getTemporalLayerForSpatialFpsMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeMediaTrack) GetTemporalLayerForSpatialFpsCallCount() int {
	fake.getTemporalLayerForSpatialFpsMutex.RLock()
	defer fake.getTemporalLayerForSpatialFpsMutex.RUnlock()
	return len(fake.getTemporalLayerForSpatialFpsArgsForCall)
}

func (fake *FakeMediaTrack) GetTemporalLayerForSpatialFpsCalls(stub func(int32, uint32, mime.MimeType) int32) {
	fake.getTemporalLayerForSpatialFpsMutex.Lock()
	defer fake.getTemporalLayerForSpatialFpsMutex.Unlock()
	fake.GetTemporalLayerForSpatialFpsStub = stub
}

func (fake *FakeMediaTrack) GetTemporalLayerForSpatialFpsArgsForCall(i int) (int32, uint32, mime.MimeType) {
	fake.getTemporalLayerForSpatialFpsMutex.RLock()
	defer fake.getTemporalLayerForSpatialFpsMutex.RUnlock()
	argsForCall := fake.getTemporalLayerForSpatialFpsArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeMediaTrack) GetTemporalLayerForSpatialFpsReturns(result1 int32) {
	fake.getTemporalLayerForSpatialFpsMutex.Lock()
	defer fake.getTemporalLayerForSpatialFpsMutex.Unlock()
	fake.GetTemporalLayerForSpatialFpsStub = nil
	fake.getTemporalLayerForSpatialFpsReturns = struct {
		result1 int32
	}{result1}
}

func (fake *FakeMediaTrack) GetTemporalLayerForSpatialFpsReturnsOnCall(i int, result1 int32) {
	fake.getTemporalLayerForSpatialFpsMutex.Lock()
	defer fake.getTemporalLayerForSpatialFpsMutex.Unlock()
	fake.GetTemporalLayerForSpatialFpsStub = nil
	if fake.getTemporalLayerForSpatialFpsReturnsOnCall == nil {
		fake.getTemporalLayerForSpatialFpsReturnsOnCall = make(map[int]struct {
			result1 int32
		})
	}
	fake.getTemporalLayerForSpatialFpsReturnsOnCall[i] = struct {
		result1 int32
	}{result1}
}

func (fake *FakeMediaTrack) ID() livekit.TrackID {
	fake.iDMutex.Lock()
	ret, specificReturn := fake.iDReturnsOnCall[len(fake.iDArgsForCall)]
	fake.iDArgsForCall = append(fake.iDArgsForCall, struct {
	}{})
	stub := fake.IDStub
	fakeReturns := fake.iDReturns
	fake.recordInvocation("ID", []interface{}{})
	fake.iDMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeMediaTrack) IDCallCount() int {
	fake.iDMutex.RLock()
	defer fake.iDMutex.RUnlock()
	return len(fake.iDArgsForCall)
}

func (fake *FakeMediaTrack) IDCalls(stub func() livekit.TrackID) {
	fake.iDMutex.Lock()
	defer fake.iDMutex.Unlock()
	fake.IDStub = stub
}

func (fake *FakeMediaTrack) IDReturns(result1 livekit.TrackID) {
	fake.iDMutex.Lock()
	defer fake.iDMutex.Unlock()
	fake.IDStub = nil
	fake.iDReturns = struct {
		result1 livekit.TrackID
	}{result1}
}

func (fake *FakeMediaTrack) IDReturnsOnCall(i int, result1 livekit.TrackID) {
	fake.iDMutex.Lock()
	defer fake.iDMutex.Unlock()
	fake.IDStub = nil
	if fake.iDReturnsOnCall == nil {
		fake.iDReturnsOnCall = make(map[int]struct {
			result1 livekit.TrackID
		})
	}
	fake.iDReturnsOnCall[i] = struct {
		result1 livekit.TrackID
	}{result1}
}

func (fake *FakeMediaTrack) IsEncrypted() bool {
	fake.isEncryptedMutex.Lock()
	ret, specificReturn := fake.isEncryptedReturnsOnCall[len(fake.isEncryptedArgsForCall)]
	fake.isEncryptedArgsForCall = append(fake.isEncryptedArgsForCall, struct {
	}{})
	stub := fake.IsEncryptedStub
	fakeReturns := fake.isEncryptedReturns
	fake.recordInvocation("IsEncrypted", []interface{}{})
	fake.isEncryptedMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeMediaTrack) IsEncryptedCallCount() int {
	fake.isEncryptedMutex.RLock()
	defer fake.isEncryptedMutex.RUnlock()
	return len(fake.isEncryptedArgsForCall)
}

func (fake *FakeMediaTrack) IsEncryptedCalls(stub func() bool) {
	fake.isEncryptedMutex.Lock()
	defer fake.isEncryptedMutex.Unlock()
	fake.IsEncryptedStub = stub
}

func (fake *FakeMediaTrack) IsEncryptedReturns(result1 bool) {
	fake.isEncryptedMutex.Lock()
	defer fake.isEncryptedMutex.Unlock()
	fake.IsEncryptedStub = nil
	fake.isEncryptedReturns = struct {
		result1 bool
	}{result1}
}

func (fake *FakeMediaTrack) IsEncryptedReturnsOnCall(i int, result1 bool) {
	fake.isEncryptedMutex.Lock()
	defer fake.isEncryptedMutex.Unlock()
	fake.IsEncryptedStub = nil
	if fake.isEncryptedReturnsOnCall == nil {
		fake.isEncryptedReturnsOnCall = make(map[int]struct {
			result1 bool
		})
	}
	fake.isEncryptedReturnsOnCall[i] = struct {
		result1 bool
	}{result1}
}

func (fake *FakeMediaTrack) IsMuted() bool {
	fake.isMutedMutex.Lock()
	ret, specificReturn := fake.isMutedReturnsOnCall[len(fake.isMutedArgsForCall)]
	fake.isMutedArgsForCall = append(fake.isMutedArgsForCall, struct {
	}{})
	stub := fake.IsMutedStub
	fakeReturns := fake.isMutedReturns
	fake.recordInvocation("IsMuted", []interface{}{})
	fake.isMutedMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeMediaTrack) IsMutedCallCount() int {
	fake.isMutedMutex.RLock()
	defer fake.isMutedMutex.RUnlock()
	return len(fake.isMutedArgsForCall)
}

func (fake *FakeMediaTrack) IsMutedCalls(stub func() bool) {
	fake.isMutedMutex.Lock()
	defer fake.isMutedMutex.Unlock()
	fake.IsMutedStub = stub
}

func (fake *FakeMediaTrack) IsMutedReturns(result1 bool) {
	fake.isMutedMutex.Lock()
	defer fake.isMutedMutex.Unlock()
	fake.IsMutedStub = nil
	fake.isMutedReturns = struct {
		result1 bool
	}{result1}
}

func (fake *FakeMediaTrack) IsMutedReturnsOnCall(i int, result1 bool) {
	fake.isMutedMutex.Lock()
	defer fake.isMutedMutex.Unlock()
	fake.IsMutedStub = nil
	if fake.isMutedReturnsOnCall == nil {
		fake.isMutedReturnsOnCall = make(map[int]struct {
			result1 bool
		})
	}
	fake.isMutedReturnsOnCall[i] = struct {
		result1 bool
	}{result1}
}

func (fake *FakeMediaTrack) IsOpen() bool {
	fake.isOpenMutex.Lock()
	ret, specificReturn := fake.isOpenReturnsOnCall[len(fake.isOpenArgsForCall)]
	fake.isOpenArgsForCall = append(fake.isOpenArgsForCall, struct {
	}{})
	stub := fake.IsOpenStub
	fakeReturns := fake.isOpenReturns
	fake.recordInvocation("IsOpen", []interface{}{})
	fake.isOpenMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeMediaTrack) IsOpenCallCount() int {
	fake.isOpenMutex.RLock()
	defer fake.isOpenMutex.RUnlock()
	return len(fake.isOpenArgsForCall)
}

func (fake *FakeMediaTrack) IsOpenCalls(stub func() bool) {
	fake.isOpenMutex.Lock()
	defer fake.isOpenMutex.Unlock()
	fake.IsOpenStub = stub
}

func (fake *FakeMediaTrack) IsOpenReturns(result1 bool) {
	fake.isOpenMutex.Lock()
	defer fake.isOpenMutex.Unlock()
	fake.IsOpenStub = nil
	fake.isOpenReturns = struct {
		result1 bool
	}{result1}
}

func (fake *FakeMediaTrack) IsOpenReturnsOnCall(i int, result1 bool) {
	fake.isOpenMutex.Lock()
	defer fake.isOpenMutex.Unlock()
	fake.IsOpenStub = nil
	if fake.isOpenReturnsOnCall == nil {
		fake.isOpenReturnsOnCall = make(map[int]struct {
			result1 bool
		})
	}
	fake.isOpenReturnsOnCall[i] = struct {
		result1 bool
	}{result1}
}

func (fake *FakeMediaTrack) IsSimulcast() bool {
	fake.isSimulcastMutex.Lock()
	ret, specificReturn := fake.isSimulcastReturnsOnCall[len(fake.isSimulcastArgsForCall)]
	fake.isSimulcastArgsForCall = append(fake.isSimulcastArgsForCall, struct {
	}{})
	stub := fake.IsSimulcastStub
	fakeReturns := fake.isSimulcastReturns
	fake.recordInvocation("IsSimulcast", []interface{}{})
	fake.isSimulcastMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeMediaTrack) IsSimulcastCallCount() int {
	fake.isSimulcastMutex.RLock()
	defer fake.isSimulcastMutex.RUnlock()
	return len(fake.isSimulcastArgsForCall)
}

func (fake *FakeMediaTrack) IsSimulcastCalls(stub func() bool) {
	fake.isSimulcastMutex.Lock()
	defer fake.isSimulcastMutex.Unlock()
	fake.IsSimulcastStub = stub
}

func (fake *FakeMediaTrack) IsSimulcastReturns(result1 bool) {
	fake.isSimulcastMutex.Lock()
	defer fake.isSimulcastMutex.Unlock()
	fake.IsSimulcastStub = nil
	fake.isSimulcastReturns = struct {
		result1 bool
	}{result1}
}

func (fake *FakeMediaTrack) IsSimulcastReturnsOnCall(i int, result1 bool) {
	fake.isSimulcastMutex.Lock()
	defer fake.isSimulcastMutex.Unlock()
	fake.IsSimulcastStub = nil
	if fake.isSimulcastReturnsOnCall == nil {
		fake.isSimulcastReturnsOnCall = make(map[int]struct {
			result1 bool
		})
	}
	fake.isSimulcastReturnsOnCall[i] = struct {
		result1 bool
	}{result1}
}

func (fake *FakeMediaTrack) IsSubscriber(arg1 livekit.ParticipantID) bool {
	fake.isSubscriberMutex.Lock()
	ret, specificReturn := fake.isSubscriberReturnsOnCall[len(fake.isSubscriberArgsForCall)]
	fake.isSubscriberArgsForCall = append(fake.isSubscriberArgsForCall, struct {
		arg1 livekit.ParticipantID
	}{arg1})
	stub := fake.IsSubscriberStub
	fakeReturns := fake.isSubscriberReturns
	fake.recordInvocation("IsSubscriber", []interface{}{arg1})
	fake.isSubscriberMutex.Unlock()
	if stub != nil {
		return stub(arg1)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeMediaTrack) IsSubscriberCallCount() int {
	fake.isSubscriberMutex.RLock()
	defer fake.isSubscriberMutex.RUnlock()
	return len(fake.isSubscriberArgsForCall)
}

func (fake *FakeMediaTrack) IsSubscriberCalls(stub func(livekit.ParticipantID) bool) {
	fake.isSubscriberMutex.Lock()
	defer fake.isSubscriberMutex.Unlock()
	fake.IsSubscriberStub = stub
}

func (fake *FakeMediaTrack) IsSubscriberArgsForCall(i int) livekit.ParticipantID {
	fake.isSubscriberMutex.RLock()
	defer fake.isSubscriberMutex.RUnlock()
	argsForCall := fake.isSubscriberArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeMediaTrack) IsSubscriberReturns(result1 bool) {
	fake.isSubscriberMutex.Lock()
	defer fake.isSubscriberMutex.Unlock()
	fake.IsSubscriberStub = nil
	fake.isSubscriberReturns = struct {
		result1 bool
	}{result1}
}

func (fake *FakeMediaTrack) IsSubscriberReturnsOnCall(i int, result1 bool) {
	fake.isSubscriberMutex.Lock()
	defer fake.isSubscriberMutex.Unlock()
	fake.IsSubscriberStub = nil
	if fake.isSubscriberReturnsOnCall == nil {
		fake.isSubscriberReturnsOnCall = make(map[int]struct {
			result1 bool
		})
	}
	fake.isSubscriberReturnsOnCall[i] = struct {
		result1 bool
	}{result1}
}

func (fake *FakeMediaTrack) Kind() livekit.TrackType {
	fake.kindMutex.Lock()
	ret, specificReturn := fake.kindReturnsOnCall[len(fake.kindArgsForCall)]
	fake.kindArgsForCall = append(fake.kindArgsForCall, struct {
	}{})
	stub := fake.KindStub
	fakeReturns := fake.kindReturns
	fake.recordInvocation("Kind", []interface{}{})
	fake.kindMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeMediaTrack) KindCallCount() int {
	fake.kindMutex.RLock()
	defer fake.kindMutex.RUnlock()
	return len(fake.kindArgsForCall)
}

func (fake *FakeMediaTrack) KindCalls(stub func() livekit.TrackType) {
	fake.kindMutex.Lock()
	defer fake.kindMutex.Unlock()
	fake.KindStub = stub
}

func (fake *FakeMediaTrack) KindReturns(result1 livekit.TrackType) {
	fake.kindMutex.Lock()
	defer fake.kindMutex.Unlock()
	fake.KindStub = nil
	fake.kindReturns = struct {
		result1 livekit.TrackType
	}{result1}
}

func (fake *FakeMediaTrack) KindReturnsOnCall(i int, result1 livekit.TrackType) {
	fake.kindMutex.Lock()
	defer fake.kindMutex.Unlock()
	fake.KindStub = nil
	if fake.kindReturnsOnCall == nil {
		fake.kindReturnsOnCall = make(map[int]struct {
			result1 livekit.TrackType
		})
	}
	fake.kindReturnsOnCall[i] = struct {
		result1 livekit.TrackType
	}{result1}
}

func (fake *FakeMediaTrack) Logger() logger.Logger {
	fake.loggerMutex.Lock()
	ret, specificReturn := fake.loggerReturnsOnCall[len(fake.loggerArgsForCall)]
	fake.loggerArgsForCall = append(fake.loggerArgsForCall, struct {
	}{})
	stub := fake.LoggerStub
	fakeReturns := fake.loggerReturns
	fake.recordInvocation("Logger", []interface{}{})
	fake.loggerMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeMediaTrack) LoggerCallCount() int {
	fake.loggerMutex.RLock()
	defer fake.loggerMutex.RUnlock()
	return len(fake.loggerArgsForCall)
}

func (fake *FakeMediaTrack) LoggerCalls(stub func() logger.Logger) {
	fake.loggerMutex.Lock()
	defer fake.loggerMutex.Unlock()
	fake.LoggerStub = stub
}

func (fake *FakeMediaTrack) LoggerReturns(result1 logger.Logger) {
	fake.loggerMutex.Lock()
	defer fake.loggerMutex.Unlock()
	fake.LoggerStub = nil
	fake.loggerReturns = struct {
		result1 logger.Logger
	}{result1}
}

func (fake *FakeMediaTrack) LoggerReturnsOnCall(i int, result1 logger.Logger) {
	fake.loggerMutex.Lock()
	defer fake.loggerMutex.Unlock()
	fake.LoggerStub = nil
	if fake.loggerReturnsOnCall == nil {
		fake.loggerReturnsOnCall = make(map[int]struct {
			result1 logger.Logger
		})
	}
	fake.loggerReturnsOnCall[i] = struct {
		result1 logger.Logger
	}{result1}
}

func (fake *FakeMediaTrack) Name() string {
	fake.nameMutex.Lock()
	ret, specificReturn := fake.nameReturnsOnCall[len(fake.nameArgsForCall)]
	fake.nameArgsForCall = append(fake.nameArgsForCall, struct {
	}{})
	stub := fake.NameStub
	fakeReturns := fake.nameReturns
	fake.recordInvocation("Name", []interface{}{})
	fake.nameMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeMediaTrack) NameCallCount() int {
	fake.nameMutex.RLock()
	defer fake.nameMutex.RUnlock()
	return len(fake.nameArgsForCall)
}

func (fake *FakeMediaTrack) NameCalls(stub func() string) {
	fake.nameMutex.Lock()
	defer fake.nameMutex.Unlock()
	fake.NameStub = stub
}

func (fake *FakeMediaTrack) NameReturns(result1 string) {
	fake.nameMutex.Lock()
	defer fake.nameMutex.Unlock()
	fake.NameStub = nil
	fake.nameReturns = struct {
		result1 string
	}{result1}
}

func (fake *FakeMediaTrack) NameReturnsOnCall(i int, result1 string) {
	fake.nameMutex.Lock()
	defer fake.nameMutex.Unlock()
	fake.NameStub = nil
	if fake.nameReturnsOnCall == nil {
		fake.nameReturnsOnCall = make(map[int]struct {
			result1 string
		})
	}
	fake.nameReturnsOnCall[i] = struct {
		result1 string
	}{result1}
}

func (fake *FakeMediaTrack) OnTrackSubscribed() {
	fake.onTrackSubscribedMutex.Lock()
	fake.onTrackSubscribedArgsForCall = append(fake.onTrackSubscribedArgsForCall, struct {
	}{})
	stub := fake.OnTrackSubscribedStub
	fake.recordInvocation("OnTrackSubscribed", []interface{}{})
	fake.onTrackSubscribedMutex.Unlock()
	if stub != nil {
		fake.OnTrackSubscribedStub()
	}
}

func (fake *FakeMediaTrack) OnTrackSubscribedCallCount() int {
	fake.onTrackSubscribedMutex.RLock()
	defer fake.onTrackSubscribedMutex.RUnlock()
	return len(fake.onTrackSubscribedArgsForCall)
}

func (fake *FakeMediaTrack) OnTrackSubscribedCalls(stub func()) {
	fake.onTrackSubscribedMutex.Lock()
	defer fake.onTrackSubscribedMutex.Unlock()
	fake.OnTrackSubscribedStub = stub
}

func (fake *FakeMediaTrack) PublisherID() livekit.ParticipantID {
	fake.publisherIDMutex.Lock()
	ret, specificReturn := fake.publisherIDReturnsOnCall[len(fake.publisherIDArgsForCall)]
	fake.publisherIDArgsForCall = append(fake.publisherIDArgsForCall, struct {
	}{})
	stub := fake.PublisherIDStub
	fakeReturns := fake.publisherIDReturns
	fake.recordInvocation("PublisherID", []interface{}{})
	fake.publisherIDMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeMediaTrack) PublisherIDCallCount() int {
	fake.publisherIDMutex.RLock()
	defer fake.publisherIDMutex.RUnlock()
	return len(fake.publisherIDArgsForCall)
}

func (fake *FakeMediaTrack) PublisherIDCalls(stub func() livekit.ParticipantID) {
	fake.publisherIDMutex.Lock()
	defer fake.publisherIDMutex.Unlock()
	fake.PublisherIDStub = stub
}

func (fake *FakeMediaTrack) PublisherIDReturns(result1 livekit.ParticipantID) {
	fake.publisherIDMutex.Lock()
	defer fake.publisherIDMutex.Unlock()
	fake.PublisherIDStub = nil
	fake.publisherIDReturns = struct {
		result1 livekit.ParticipantID
	}{result1}
}

func (fake *FakeMediaTrack) PublisherIDReturnsOnCall(i int, result1 livekit.ParticipantID) {
	fake.publisherIDMutex.Lock()
	defer fake.publisherIDMutex.Unlock()
	fake.PublisherIDStub = nil
	if fake.publisherIDReturnsOnCall == nil {
		fake.publisherIDReturnsOnCall = make(map[int]struct {
			result1 livekit.ParticipantID
		})
	}
	fake.publisherIDReturnsOnCall[i] = struct {
		result1 livekit.ParticipantID
	}{result1}
}

func (fake *FakeMediaTrack) PublisherIdentity() livekit.ParticipantIdentity {
	fake.publisherIdentityMutex.Lock()
	ret, specificReturn := fake.publisherIdentityReturnsOnCall[len(fake.publisherIdentityArgsForCall)]
	fake.publisherIdentityArgsForCall = append(fake.publisherIdentityArgsForCall, struct {
	}{})
	stub := fake.PublisherIdentityStub
	fakeReturns := fake.publisherIdentityReturns
	fake.recordInvocation("PublisherIdentity", []interface{}{})
	fake.publisherIdentityMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeMediaTrack) PublisherIdentityCallCount() int {
	fake.publisherIdentityMutex.RLock()
	defer fake.publisherIdentityMutex.RUnlock()
	return len(fake.publisherIdentityArgsForCall)
}

func (fake *FakeMediaTrack) PublisherIdentityCalls(stub func() livekit.ParticipantIdentity) {
	fake.publisherIdentityMutex.Lock()
	defer fake.publisherIdentityMutex.Unlock()
	fake.PublisherIdentityStub = stub
}

func (fake *FakeMediaTrack) PublisherIdentityReturns(result1 livekit.ParticipantIdentity) {
	fake.publisherIdentityMutex.Lock()
	defer fake.publisherIdentityMutex.Unlock()
	fake.PublisherIdentityStub = nil
	fake.publisherIdentityReturns = struct {
		result1 livekit.ParticipantIdentity
	}{result1}
}

func (fake *FakeMediaTrack) PublisherIdentityReturnsOnCall(i int, result1 livekit.ParticipantIdentity) {
	fake.publisherIdentityMutex.Lock()
	defer fake.publisherIdentityMutex.Unlock()
	fake.PublisherIdentityStub = nil
	if fake.publisherIdentityReturnsOnCall == nil {
		fake.publisherIdentityReturnsOnCall = make(map[int]struct {
			result1 livekit.ParticipantIdentity
		})
	}
	fake.publisherIdentityReturnsOnCall[i] = struct {
		result1 livekit.ParticipantIdentity
	}{result1}
}

func (fake *FakeMediaTrack) PublisherVersion() uint32 {
	fake.publisherVersionMutex.Lock()
	ret, specificReturn := fake.publisherVersionReturnsOnCall[len(fake.publisherVersionArgsForCall)]
	fake.publisherVersionArgsForCall = append(fake.publisherVersionArgsForCall, struct {
	}{})
	stub := fake.PublisherVersionStub
	fakeReturns := fake.publisherVersionReturns
	fake.recordInvocation("PublisherVersion", []interface{}{})
	fake.publisherVersionMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeMediaTrack) PublisherVersionCallCount() int {
	fake.publisherVersionMutex.RLock()
	defer fake.publisherVersionMutex.RUnlock()
	return len(fake.publisherVersionArgsForCall)
}

func (fake *FakeMediaTrack) PublisherVersionCalls(stub func() uint32) {
	fake.publisherVersionMutex.Lock()
	defer fake.publisherVersionMutex.Unlock()
	fake.PublisherVersionStub = stub
}

func (fake *FakeMediaTrack) PublisherVersionReturns(result1 uint32) {
	fake.publisherVersionMutex.Lock()
	defer fake.publisherVersionMutex.Unlock()
	fake.PublisherVersionStub = nil
	fake.publisherVersionReturns = struct {
		result1 uint32
	}{result1}
}

func (fake *FakeMediaTrack) PublisherVersionReturnsOnCall(i int, result1 uint32) {
	fake.publisherVersionMutex.Lock()
	defer fake.publisherVersionMutex.Unlock()
	fake.PublisherVersionStub = nil
	if fake.publisherVersionReturnsOnCall == nil {
		fake.publisherVersionReturnsOnCall = make(map[int]struct {
			result1 uint32
		})
	}
	fake.publisherVersionReturnsOnCall[i] = struct {
		result1 uint32
	}{result1}
}

func (fake *FakeMediaTrack) Receivers() []sfu.TrackReceiver {
	fake.receiversMutex.Lock()
	ret, specificReturn := fake.receiversReturnsOnCall[len(fake.receiversArgsForCall)]
	fake.receiversArgsForCall = append(fake.receiversArgsForCall, struct {
	}{})
	stub := fake.ReceiversStub
	fakeReturns := fake.receiversReturns
	fake.recordInvocation("Receivers", []interface{}{})
	fake.receiversMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeMediaTrack) ReceiversCallCount() int {
	fake.receiversMutex.RLock()
	defer fake.receiversMutex.RUnlock()
	return len(fake.receiversArgsForCall)
}

func (fake *FakeMediaTrack) ReceiversCalls(stub func() []sfu.TrackReceiver) {
	fake.receiversMutex.Lock()
	defer fake.receiversMutex.Unlock()
	fake.ReceiversStub = stub
}

func (fake *FakeMediaTrack) ReceiversReturns(result1 []sfu.TrackReceiver) {
	fake.receiversMutex.Lock()
	defer fake.receiversMutex.Unlock()
	fake.ReceiversStub = nil
	fake.receiversReturns = struct {
		result1 []sfu.TrackReceiver
	}{result1}
}

func (fake *FakeMediaTrack) ReceiversReturnsOnCall(i int, result1 []sfu.TrackReceiver) {
	fake.receiversMutex.Lock()
	defer fake.receiversMutex.Unlock()
	fake.ReceiversStub = nil
	if fake.receiversReturnsOnCall == nil {
		fake.receiversReturnsOnCall = make(map[int]struct {
			result1 []sfu.TrackReceiver
		})
	}
	fake.receiversReturnsOnCall[i] = struct {
		result1 []sfu.TrackReceiver
	}{result1}
}

func (fake *FakeMediaTrack) RemoveSubscriber(arg1 livekit.ParticipantID, arg2 bool) {
	fake.removeSubscriberMutex.Lock()
	fake.removeSubscriberArgsForCall = append(fake.removeSubscriberArgsForCall, struct {
		arg1 livekit.ParticipantID
		arg2 bool
	}{arg1, arg2})
	stub := fake.RemoveSubscriberStub
	fake.recordInvocation("RemoveSubscriber", []interface{}{arg1, arg2})
	fake.removeSubscriberMutex.Unlock()
	if stub != nil {
		fake.RemoveSubscriberStub(arg1, arg2)
	}
}

func (fake *FakeMediaTrack) RemoveSubscriberCallCount() int {
	fake.removeSubscriberMutex.RLock()
	defer fake.removeSubscriberMutex.RUnlock()
	return len(fake.removeSubscriberArgsForCall)
}

func (fake *FakeMediaTrack) RemoveSubscriberCalls(stub func(livekit.ParticipantID, bool)) {
	fake.removeSubscriberMutex.Lock()
	defer fake.removeSubscriberMutex.Unlock()
	fake.RemoveSubscriberStub = stub
}

func (fake *FakeMediaTrack) RemoveSubscriberArgsForCall(i int) (livekit.ParticipantID, bool) {
	fake.removeSubscriberMutex.RLock()
	defer fake.removeSubscriberMutex.RUnlock()
	argsForCall := fake.removeSubscriberArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeMediaTrack) RevokeDisallowedSubscribers(arg1 []livekit.ParticipantIdentity) []livekit.ParticipantIdentity {
	var arg1Copy []livekit.ParticipantIdentity
	if arg1 != nil {
		arg1Copy = make([]livekit.ParticipantIdentity, len(arg1))
		copy(arg1Copy, arg1)
	}
	fake.revokeDisallowedSubscribersMutex.Lock()
	ret, specificReturn := fake.revokeDisallowedSubscribersReturnsOnCall[len(fake.revokeDisallowedSubscribersArgsForCall)]
	fake.revokeDisallowedSubscribersArgsForCall = append(fake.revokeDisallowedSubscribersArgsForCall, struct {
		arg1 []livekit.ParticipantIdentity
	}{arg1Copy})
	stub := fake.RevokeDisallowedSubscribersStub
	fakeReturns := fake.revokeDisallowedSubscribersReturns
	fake.recordInvocation("RevokeDisallowedSubscribers", []interface{}{arg1Copy})
	fake.revokeDisallowedSubscribersMutex.Unlock()
	if stub != nil {
		return stub(arg1)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeMediaTrack) RevokeDisallowedSubscribersCallCount() int {
	fake.revokeDisallowedSubscribersMutex.RLock()
	defer fake.revokeDisallowedSubscribersMutex.RUnlock()
	return len(fake.revokeDisallowedSubscribersArgsForCall)
}

func (fake *FakeMediaTrack) RevokeDisallowedSubscribersCalls(stub func([]livekit.ParticipantIdentity) []livekit.ParticipantIdentity) {
	fake.revokeDisallowedSubscribersMutex.Lock()
	defer fake.revokeDisallowedSubscribersMutex.Unlock()
	fake.RevokeDisallowedSubscribersStub = stub
}

func (fake *FakeMediaTrack) RevokeDisallowedSubscribersArgsForCall(i int) []livekit.ParticipantIdentity {
	fake.revokeDisallowedSubscribersMutex.RLock()
	defer fake.revokeDisallowedSubscribersMutex.RUnlock()
	argsForCall := fake.revokeDisallowedSubscribersArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeMediaTrack) RevokeDisallowedSubscribersReturns(result1 []livekit.ParticipantIdentity) {
	fake.revokeDisallowedSubscribersMutex.Lock()
	defer fake.revokeDisallowedSubscribersMutex.Unlock()
	fake.RevokeDisallowedSubscribersStub = nil
	fake.revokeDisallowedSubscribersReturns = struct {
		result1 []livekit.ParticipantIdentity
	}{result1}
}

func (fake *FakeMediaTrack) RevokeDisallowedSubscribersReturnsOnCall(i int, result1 []livekit.ParticipantIdentity) {
	fake.revokeDisallowedSubscribersMutex.Lock()
	defer fake.revokeDisallowedSubscribersMutex.Unlock()
	fake.RevokeDisallowedSubscribersStub = nil
	if fake.revokeDisallowedSubscribersReturnsOnCall == nil {
		fake.revokeDisallowedSubscribersReturnsOnCall = make(map[int]struct {
			result1 []livekit.ParticipantIdentity
		})
	}
	fake.revokeDisallowedSubscribersReturnsOnCall[i] = struct {
		result1 []livekit.ParticipantIdentity
	}{result1}
}

func (fake *FakeMediaTrack) SetMuted(arg1 bool) {
	fake.setMutedMutex.Lock()
	fake.setMutedArgsForCall = append(fake.setMutedArgsForCall, struct {
		arg1 bool
	}{arg1})
	stub := fake.SetMutedStub
	fake.recordInvocation("SetMuted", []interface{}{arg1})
	fake.setMutedMutex.Unlock()
	if stub != nil {
		fake.SetMutedStub(arg1)
	}
}

func (fake *FakeMediaTrack) SetMutedCallCount() int {
	fake.setMutedMutex.RLock()
	defer fake.setMutedMutex.RUnlock()
	return len(fake.setMutedArgsForCall)
}

func (fake *FakeMediaTrack) SetMutedCalls(stub func(bool)) {
	fake.setMutedMutex.Lock()
	defer fake.setMutedMutex.Unlock()
	fake.SetMutedStub = stub
}

func (fake *FakeMediaTrack) SetMutedArgsForCall(i int) bool {
	fake.setMutedMutex.RLock()
	defer fake.setMutedMutex.RUnlock()
	argsForCall := fake.setMutedArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeMediaTrack) Source() livekit.TrackSource {
	fake.sourceMutex.Lock()
	ret, specificReturn := fake.sourceReturnsOnCall[len(fake.sourceArgsForCall)]
	fake.sourceArgsForCall = append(fake.sourceArgsForCall, struct {
	}{})
	stub := fake.SourceStub
	fakeReturns := fake.sourceReturns
	fake.recordInvocation("Source", []interface{}{})
	fake.sourceMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeMediaTrack) SourceCallCount() int {
	fake.sourceMutex.RLock()
	defer fake.sourceMutex.RUnlock()
	return len(fake.sourceArgsForCall)
}

func (fake *FakeMediaTrack) SourceCalls(stub func() livekit.TrackSource) {
	fake.sourceMutex.Lock()
	defer fake.sourceMutex.Unlock()
	fake.SourceStub = stub
}

func (fake *FakeMediaTrack) SourceReturns(result1 livekit.TrackSource) {
	fake.sourceMutex.Lock()
	defer fake.sourceMutex.Unlock()
	fake.SourceStub = nil
	fake.sourceReturns = struct {
		result1 livekit.TrackSource
	}{result1}
}

func (fake *FakeMediaTrack) SourceReturnsOnCall(i int, result1 livekit.TrackSource) {
	fake.sourceMutex.Lock()
	defer fake.sourceMutex.Unlock()
	fake.SourceStub = nil
	if fake.sourceReturnsOnCall == nil {
		fake.sourceReturnsOnCall = make(map[int]struct {
			result1 livekit.TrackSource
		})
	}
	fake.sourceReturnsOnCall[i] = struct {
		result1 livekit.TrackSource
	}{result1}
}

func (fake *FakeMediaTrack) Stream() string {
	fake.streamMutex.Lock()
	ret, specificReturn := fake.streamReturnsOnCall[len(fake.streamArgsForCall)]
	fake.streamArgsForCall = append(fake.streamArgsForCall, struct {
	}{})
	stub := fake.StreamStub
	fakeReturns := fake.streamReturns
	fake.recordInvocation("Stream", []interface{}{})
	fake.streamMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeMediaTrack) StreamCallCount() int {
	fake.streamMutex.RLock()
	defer fake.streamMutex.RUnlock()
	return len(fake.streamArgsForCall)
}

func (fake *FakeMediaTrack) StreamCalls(stub func() string) {
	fake.streamMutex.Lock()
	defer fake.streamMutex.Unlock()
	fake.StreamStub = stub
}

func (fake *FakeMediaTrack) StreamReturns(result1 string) {
	fake.streamMutex.Lock()
	defer fake.streamMutex.Unlock()
	fake.StreamStub = nil
	fake.streamReturns = struct {
		result1 string
	}{result1}
}

func (fake *FakeMediaTrack) StreamReturnsOnCall(i int, result1 string) {
	fake.streamMutex.Lock()
	defer fake.streamMutex.Unlock()
	fake.StreamStub = nil
	if fake.streamReturnsOnCall == nil {
		fake.streamReturnsOnCall = make(map[int]struct {
			result1 string
		})
	}
	fake.streamReturnsOnCall[i] = struct {
		result1 string
	}{result1}
}

func (fake *FakeMediaTrack) ToProto() *livekit.TrackInfo {
	fake.toProtoMutex.Lock()
	ret, specificReturn := fake.toProtoReturnsOnCall[len(fake.toProtoArgsForCall)]
	fake.toProtoArgsForCall = append(fake.toProtoArgsForCall, struct {
	}{})
	stub := fake.ToProtoStub
	fakeReturns := fake.toProtoReturns
	fake.recordInvocation("ToProto", []interface{}{})
	fake.toProtoMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeMediaTrack) ToProtoCallCount() int {
	fake.toProtoMutex.RLock()
	defer fake.toProtoMutex.RUnlock()
	return len(fake.toProtoArgsForCall)
}

func (fake *FakeMediaTrack) ToProtoCalls(stub func() *livekit.TrackInfo) {
	fake.toProtoMutex.Lock()
	defer fake.toProtoMutex.Unlock()
	fake.ToProtoStub = stub
}

func (fake *FakeMediaTrack) ToProtoReturns(result1 *livekit.TrackInfo) {
	fake.toProtoMutex.Lock()
	defer fake.toProtoMutex.Unlock()
	fake.ToProtoStub = nil
	fake.toProtoReturns = struct {
		result1 *livekit.TrackInfo
	}{result1}
}

func (fake *FakeMediaTrack) ToProtoReturnsOnCall(i int, result1 *livekit.TrackInfo) {
	fake.toProtoMutex.Lock()
	defer fake.toProtoMutex.Unlock()
	fake.ToProtoStub = nil
	if fake.toProtoReturnsOnCall == nil {
		fake.toProtoReturnsOnCall = make(map[int]struct {
			result1 *livekit.TrackInfo
		})
	}
	fake.toProtoReturnsOnCall[i] = struct {
		result1 *livekit.TrackInfo
	}{result1}
}

func (fake *FakeMediaTrack) UpdateAudioTrack(arg1 *livekit.UpdateLocalAudioTrack) {
	fake.updateAudioTrackMutex.Lock()
	fake.updateAudioTrackArgsForCall = append(fake.updateAudioTrackArgsForCall, struct {
		arg1 *livekit.UpdateLocalAudioTrack
	}{arg1})
	stub := fake.UpdateAudioTrackStub
	fake.recordInvocation("UpdateAudioTrack", []interface{}{arg1})
	fake.updateAudioTrackMutex.Unlock()
	if stub != nil {
		fake.UpdateAudioTrackStub(arg1)
	}
}

func (fake *FakeMediaTrack) UpdateAudioTrackCallCount() int {
	fake.updateAudioTrackMutex.RLock()
	defer fake.updateAudioTrackMutex.RUnlock()
	return len(fake.updateAudioTrackArgsForCall)
}

func (fake *FakeMediaTrack) UpdateAudioTrackCalls(stub func(*livekit.UpdateLocalAudioTrack)) {
	fake.updateAudioTrackMutex.Lock()
	defer fake.updateAudioTrackMutex.Unlock()
	fake.UpdateAudioTrackStub = stub
}

func (fake *FakeMediaTrack) UpdateAudioTrackArgsForCall(i int) *livekit.UpdateLocalAudioTrack {
	fake.updateAudioTrackMutex.RLock()
	defer fake.updateAudioTrackMutex.RUnlock()
	argsForCall := fake.updateAudioTrackArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeMediaTrack) UpdateTrackInfo(arg1 *livekit.TrackInfo) {
	fake.updateTrackInfoMutex.Lock()
	fake.updateTrackInfoArgsForCall = append(fake.updateTrackInfoArgsForCall, struct {
		arg1 *livekit.TrackInfo
	}{arg1})
	stub := fake.UpdateTrackInfoStub
	fake.recordInvocation("UpdateTrackInfo", []interface{}{arg1})
	fake.updateTrackInfoMutex.Unlock()
	if stub != nil {
		fake.UpdateTrackInfoStub(arg1)
	}
}

func (fake *FakeMediaTrack) UpdateTrackInfoCallCount() int {
	fake.updateTrackInfoMutex.RLock()
	defer fake.updateTrackInfoMutex.RUnlock()
	return len(fake.updateTrackInfoArgsForCall)
}

func (fake *FakeMediaTrack) UpdateTrackInfoCalls(stub func(*livekit.TrackInfo)) {
	fake.updateTrackInfoMutex.Lock()
	defer fake.updateTrackInfoMutex.Unlock()
	fake.UpdateTrackInfoStub = stub
}

func (fake *FakeMediaTrack) UpdateTrackInfoArgsForCall(i int) *livekit.TrackInfo {
	fake.updateTrackInfoMutex.RLock()
	defer fake.updateTrackInfoMutex.RUnlock()
	argsForCall := fake.updateTrackInfoArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeMediaTrack) UpdateVideoTrack(arg1 *livekit.UpdateLocalVideoTrack) {
	fake.updateVideoTrackMutex.Lock()
	fake.updateVideoTrackArgsForCall = append(fake.updateVideoTrackArgsForCall, struct {
		arg1 *livekit.UpdateLocalVideoTrack
	}{arg1})
	stub := fake.UpdateVideoTrackStub
	fake.recordInvocation("UpdateVideoTrack", []interface{}{arg1})
	fake.updateVideoTrackMutex.Unlock()
	if stub != nil {
		fake.UpdateVideoTrackStub(arg1)
	}
}

func (fake *FakeMediaTrack) UpdateVideoTrackCallCount() int {
	fake.updateVideoTrackMutex.RLock()
	defer fake.updateVideoTrackMutex.RUnlock()
	return len(fake.updateVideoTrackArgsForCall)
}

func (fake *FakeMediaTrack) UpdateVideoTrackCalls(stub func(*livekit.UpdateLocalVideoTrack)) {
	fake.updateVideoTrackMutex.Lock()
	defer fake.updateVideoTrackMutex.Unlock()
	fake.UpdateVideoTrackStub = stub
}

func (fake *FakeMediaTrack) UpdateVideoTrackArgsForCall(i int) *livekit.UpdateLocalVideoTrack {
	fake.updateVideoTrackMutex.RLock()
	defer fake.updateVideoTrackMutex.RUnlock()
	argsForCall := fake.updateVideoTrackArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeMediaTrack) Invocations() map[string][][]interface{} {
	fake.invocationsMutex.RLock()
	defer fake.invocationsMutex.RUnlock()
	fake.addOnCloseMutex.RLock()
	defer fake.addOnCloseMutex.RUnlock()
	fake.addSubscriberMutex.RLock()
	defer fake.addSubscriberMutex.RUnlock()
	fake.clearAllReceiversMutex.RLock()
	defer fake.clearAllReceiversMutex.RUnlock()
	fake.closeMutex.RLock()
	defer fake.closeMutex.RUnlock()
	fake.getAllSubscribersMutex.RLock()
	defer fake.getAllSubscribersMutex.RUnlock()
	fake.getAudioLevelMutex.RLock()
	defer fake.getAudioLevelMutex.RUnlock()
	fake.getNumSubscribersMutex.RLock()
	defer fake.getNumSubscribersMutex.RUnlock()
	fake.getQualityForDimensionMutex.RLock()
	defer fake.getQualityForDimensionMutex.RUnlock()
	fake.getTemporalLayerForSpatialFpsMutex.RLock()
	defer fake.getTemporalLayerForSpatialFpsMutex.RUnlock()
	fake.iDMutex.RLock()
	defer fake.iDMutex.RUnlock()
	fake.isEncryptedMutex.RLock()
	defer fake.isEncryptedMutex.RUnlock()
	fake.isMutedMutex.RLock()
	defer fake.isMutedMutex.RUnlock()
	fake.isOpenMutex.RLock()
	defer fake.isOpenMutex.RUnlock()
	fake.isSimulcastMutex.RLock()
	defer fake.isSimulcastMutex.RUnlock()
	fake.isSubscriberMutex.RLock()
	defer fake.isSubscriberMutex.RUnlock()
	fake.kindMutex.RLock()
	defer fake.kindMutex.RUnlock()
	fake.loggerMutex.RLock()
	defer fake.loggerMutex.RUnlock()
	fake.nameMutex.RLock()
	defer fake.nameMutex.RUnlock()
	fake.onTrackSubscribedMutex.RLock()
	defer fake.onTrackSubscribedMutex.RUnlock()
	fake.publisherIDMutex.RLock()
	defer fake.publisherIDMutex.RUnlock()
	fake.publisherIdentityMutex.RLock()
	defer fake.publisherIdentityMutex.RUnlock()
	fake.publisherVersionMutex.RLock()
	defer fake.publisherVersionMutex.RUnlock()
	fake.receiversMutex.RLock()
	defer fake.receiversMutex.RUnlock()
	fake.removeSubscriberMutex.RLock()
	defer fake.removeSubscriberMutex.RUnlock()
	fake.revokeDisallowedSubscribersMutex.RLock()
	defer fake.revokeDisallowedSubscribersMutex.RUnlock()
	fake.setMutedMutex.RLock()
	defer fake.setMutedMutex.RUnlock()
	fake.sourceMutex.RLock()
	defer fake.sourceMutex.RUnlock()
	fake.streamMutex.RLock()
	defer fake.streamMutex.RUnlock()
	fake.toProtoMutex.RLock()
	defer fake.toProtoMutex.RUnlock()
	fake.updateAudioTrackMutex.RLock()
	defer fake.updateAudioTrackMutex.RUnlock()
	fake.updateTrackInfoMutex.RLock()
	defer fake.updateTrackInfoMutex.RUnlock()
	fake.updateVideoTrackMutex.RLock()
	defer fake.updateVideoTrackMutex.RUnlock()
	copiedInvocations := map[string][][]interface{}{}
	for key, value := range fake.invocations {
		copiedInvocations[key] = value
	}
	return copiedInvocations
}

func (fake *FakeMediaTrack) recordInvocation(key string, args []interface{}) {
	fake.invocationsMutex.Lock()
	defer fake.invocationsMutex.Unlock()
	if fake.invocations == nil {
		fake.invocations = map[string][][]interface{}{}
	}
	if fake.invocations[key] == nil {
		fake.invocations[key] = [][]interface{}{}
	}
	fake.invocations[key] = append(fake.invocations[key], args)
}

func (fake *FakeMediaTrack) IsRelayed() bool {
	return false
}

var _ types.MediaTrack = new(FakeMediaTrack)
