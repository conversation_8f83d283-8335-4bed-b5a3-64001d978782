package rtc

import (
	"sync"
	"time"

	"github.com/livekit/livekit-server/pkg/config"
	"github.com/livekit/livekit-server/pkg/rtc/types"
	"github.com/livekit/livekit-server/pkg/sfu"
	"github.com/livekit/livekit-server/pkg/telemetry"
	"github.com/livekit/protocol/livekit"
	"github.com/livekit/protocol/logger"
	"github.com/livekit/protocol/rpc"
	"github.com/livekit/protocol/utils"
	"github.com/pkg/errors"
	"go.uber.org/atomic"
	"golang.org/x/exp/maps"
)

type RemoteParticipantParams struct {
	CurrentNodeID livekit.NodeID
	CurrentNodeIP string

	NodeID        livekit.NodeID
	NodeIP        string
	NodeRelayPort int
	RoomName      livekit.RoomName
	Config        *WebRTCConfig
	AudioConfig   sfu.AudioConfig
	VideoConfig   config.VideoConfig
	RTCConfig     config.RTCConfig
	Telemetry     telemetry.TelemetryService

	ParticipantHelper types.LocalParticipantHelper
	Logger            logger.Logger
}

type RemoteParticipant struct {
	params          RemoteParticipantParams
	participantInfo atomic.Pointer[livekit.ParticipantInfo]

	lock            sync.RWMutex
	publishedTracks map[livekit.TrackID]types.MediaTrack

	isClosed    atomic.Bool
	closeReason atomic.Value // types.ParticipantCloseReason
}

func NewRemoteParticipant(params RemoteParticipantParams, pi *livekit.ParticipantInfo) *RemoteParticipant {
	rp := &RemoteParticipant{
		params:          params,
		publishedTracks: make(map[livekit.TrackID]types.MediaTrack),
	}
	rp.participantInfo.Store(utils.CloneProto(pi))

	// 构造远程媒体轨道
	for _, trackInfo := range pi.Tracks {
		rp.addRemoteMediaTrackLocked(trackInfo)
	}
	return rp
}

func (p *RemoteParticipant) addRemoteMediaTrackLocked(trackInfo *livekit.TrackInfo) (types.MediaTrack, bool) {
	trackID := livekit.TrackID(trackInfo.Sid)

	p.lock.RLock()
	publishedTrack := p.publishedTracks[trackID]
	p.lock.RUnlock()

	if publishedTrack != nil {
		return publishedTrack, false
	}

	p.lock.Lock()
	defer p.lock.Unlock()
	publishedTrack = p.publishedTracks[trackID]
	if publishedTrack != nil {
		return publishedTrack, false
	}

	track := NewRemoteMediaTrack(RemoteMediaTrackParams{
		CurrentNodeID:       p.params.CurrentNodeID,
		CurrentNodeIP:       p.params.CurrentNodeIP,
		NodeID:              p.params.NodeID,
		NodeIP:              p.params.NodeIP,
		NodeRelayPort:       p.params.NodeRelayPort,
		RoomName:            p.params.RoomName,
		ParticipantID:       p.ID,
		ParticipantIdentity: p.Identity(),
		ParticipantVersion:  p.RemoteParticipant().Version,
		ReceiverConfig:      p.params.Config.Receiver,
		SubscriberConfig:    p.params.Config.Subscriber,
		AudioConfig:         p.params.AudioConfig,
		VideoConfig:         p.params.VideoConfig,
		RTCConfig:           p.params.RTCConfig,
		Telemetry:           p.params.Telemetry,
		GetMediaRelayClient: func(nodeID livekit.NodeID) (rpc.TypedMediaRelayClient, error) {
			return p.params.ParticipantHelper.GetMediaRelayClient(nodeID)
		},
		GetMediaRelayChan: func(startRequest *rpc.StartRequest) (sfu.MessageSource, error) {
			return p.params.ParticipantHelper.GetMediaRelayChan(startRequest)
		},
		Logger: LoggerWithTrack(p.params.Logger, trackID, true),
	}, trackInfo)
	p.publishedTracks[trackID] = track
	return track, true
}

func (p *RemoteParticipant) UpdateParticipantInfo(
	pi *livekit.ParticipantInfo,
	onTrackAdded func(types.MediaTrack),
	onTrackRemoved func(types.MediaTrack),
) {
	p.participantInfo.Store(utils.CloneProto(pi))

	p.lock.RLock()
	publishedTracks := p.publishedTracks
	p.lock.RUnlock()

	var forAdd []*livekit.TrackInfo
	var forUpdate = make(map[types.MediaTrack]*livekit.TrackInfo)
	var forRemove []types.MediaTrack

	// 差异对比，遍历出需要添加和更新的
	for _, trackInfo := range pi.Tracks {
		var publishedTrack types.MediaTrack
		for _, track := range publishedTracks {
			if track.ID() == livekit.TrackID(trackInfo.Sid) {
				publishedTrack = track
				break
			}
		}
		if publishedTrack != nil {
			forUpdate[publishedTrack] = trackInfo
		} else {
			forAdd = append(forAdd, trackInfo)
		}
	}
	// 差异对比，遍历出需要移除的
	for _, track := range publishedTracks {
		var found bool
		for _, trackInfo := range pi.Tracks {
			if track.ID() == livekit.TrackID(trackInfo.Sid) {
				found = true
				break
			}
		}
		if !found {
			forRemove = append(forRemove, track)
		}
	}

	// 添加
	for _, trackInfo := range forAdd {
		track, isNew := p.addRemoteMediaTrackLocked(trackInfo)
		if isNew && onTrackAdded != nil {
			onTrackAdded(track)
		}
	}
	// 更新
	for publishedTrack, trackInfo := range forUpdate {
		publishedTrack.UpdateTrackInfo(trackInfo)
	}
	// 删除
	for _, publishedTrack := range forRemove {
		publishedTrack.Close(false)
		p.lock.Lock()
		delete(publishedTracks, publishedTrack.ID())
		p.lock.Unlock()
		if onTrackRemoved != nil {
			onTrackRemoved(publishedTrack)
		}
	}
}

func (p *RemoteParticipant) RemoteParticipant() *livekit.ParticipantInfo {
	return p.participantInfo.Load()
}

func (p *RemoteParticipant) ID() livekit.ParticipantID {
	return livekit.ParticipantID(p.RemoteParticipant().Sid)
}

func (p *RemoteParticipant) Identity() livekit.ParticipantIdentity {
	return livekit.ParticipantIdentity(p.RemoteParticipant().Identity)
}

func (p *RemoteParticipant) State() livekit.ParticipantInfo_State {
	return p.RemoteParticipant().State
}

func (p *RemoteParticipant) ConnectedAt() time.Time {
	return time.UnixMilli(p.RemoteParticipant().JoinedAtMs)
}

func (p *RemoteParticipant) CloseReason() types.ParticipantCloseReason {
	return p.closeReason.Load().(types.ParticipantCloseReason)
}

func (p *RemoteParticipant) Kind() livekit.ParticipantInfo_Kind {
	return p.RemoteParticipant().Kind
}

func (p *RemoteParticipant) IsRecorder() bool {
	return p.RemoteParticipant().Kind == livekit.ParticipantInfo_EGRESS
}

func (p *RemoteParticipant) IsDependent() bool {
	return p.RemoteParticipant().Kind == livekit.ParticipantInfo_AGENT || p.RemoteParticipant().Kind == livekit.ParticipantInfo_EGRESS
}

func (p *RemoteParticipant) IsAgent() bool {
	return p.RemoteParticipant().Kind == livekit.ParticipantInfo_AGENT
}

func (p *RemoteParticipant) CanSkipBroadcast() bool {
	return !p.RemoteParticipant().IsPublisher
}

func (p *RemoteParticipant) Version() utils.TimedVersion {
	// 返回一个零值对象
	var t utils.TimedVersion
	return t
}

func (p *RemoteParticipant) ToProto() *livekit.ParticipantInfo {
	return utils.CloneProto(p.RemoteParticipant())
}

func (p *RemoteParticipant) IsPublisher() bool {
	return p.RemoteParticipant().IsPublisher
}

func (p *RemoteParticipant) GetPublishedTrack(_ livekit.TrackID) types.MediaTrack {
	// 跟踪调用链发现这个方法应该是不会被调用到的（仅针对远程参与者而言）
	err := errors.New("意料之外的方法调用，可能存在逻辑不严谨的隐患，请检查代码")
	p.params.Logger.Errorw("调用远程参与者 GetPublishedTrack 方法", err)
	return nil
}

func (p *RemoteParticipant) GetPublishedTracks() []types.MediaTrack {
	p.lock.RLock()
	defer p.lock.RUnlock()

	return maps.Values(p.publishedTracks)
}

func (p *RemoteParticipant) RemovePublishedTrack(track types.MediaTrack, isExpectedToResume bool, shouldClose bool) {
	// TODO-zjx 目前只有本地参与者的 RemovePublishedTrack 才会被调用，因此这里先不实现
}

func (p *RemoteParticipant) GetAudioLevel() (smoothedLevel float64, active bool) {
	// 获取音频音量的功能应该从本地参与者获取，然后广播到房间的所有参与者（包括远程参与者）

	// 跟踪调用链发现这个方法应该是不会被调用到的（仅针对远程参与者而言）
	err := errors.New("意料之外的方法调用，可能存在逻辑不严谨的隐患，请检查代码")
	p.params.Logger.Errorw("调用远程参与者 GetAudioLevel 方法", err)
	return 0, false
}

func (p *RemoteParticipant) HasPermission(trackID livekit.TrackID, subIdentity livekit.ParticipantIdentity) bool {
	// TODO-zjx 待后续再补充轨道订阅相关的逻辑，目前先直接返回 true
	return true
}

func (p *RemoteParticipant) Hidden() bool {
	return p.RemoteParticipant().Permission.Hidden
}

func (p *RemoteParticipant) Close(sendLeave bool, reason types.ParticipantCloseReason, isExpectedToResume bool) error {
	if p.isClosed.Swap(true) {
		// already closed
		return nil
	}
	p.closeReason.Store(reason)

	p.lock.Lock()
	publishedTracks := p.publishedTracks
	p.publishedTracks = make(map[livekit.TrackID]types.MediaTrack)
	for _, t := range publishedTracks {
		t.Close(isExpectedToResume)
	}
	p.lock.Unlock()

	return nil
}

func (p *RemoteParticipant) SubscriptionPermission() (*livekit.SubscriptionPermission, utils.TimedVersion) {
	// 跟踪调用链发现这个方法应该是不会被调用到的
	err := errors.New("意料之外的方法调用，可能存在逻辑不严谨的隐患，请检查代码")
	p.params.Logger.Errorw("调用远程参与者 SubscriptionPermission 方法", err)
	return nil, utils.TimedVersion(0)
}

func (p *RemoteParticipant) UpdateSubscriptionPermission(subscriptionPermission *livekit.SubscriptionPermission, timedVersion utils.TimedVersion, resolverBySid func(participantID livekit.ParticipantID) types.LocalParticipant) error {
	// TODO-zjx 待后续再补充轨道订阅相关的逻辑
	return nil
}

func (p *RemoteParticipant) DebugInfo() map[string]interface{} {
	info := map[string]interface{}{
		"ID":       p.ID(),
		"State":    p.State().String(),
		"IsRemote": true,
	}

	var trackInfos []string
	for _, ti := range p.RemoteParticipant().Tracks {
		trackInfos = append(trackInfos, ti.String())
	}
	info["TrackInfos"] = trackInfos

	return info
}

func (p *RemoteParticipant) OnMetrics(callback func(types.Participant, *livekit.DataPacket)) {
	// do nothing
}
