package rtc

import (
	"github.com/livekit/protocol/livekit"
	"github.com/livekit/protocol/rpc"
)

// 发送信令广播到所有房间所有人
func (r *Room) SendBroadcastUpdates(updates []*ParticipantUpdate) {
	remoteParticipantUpdate := &livekit.RemoteParticipantUpdate{
		Participants: ParticipantUpdateToRemote(updates, string(r.currentNode.NodeID()), r.currentNode.NodeIP(), r.currentNode.RelayPort()),
	}

	broadcastUpdate := &rpc.BroadcastUpdate{
		Update: &rpc.BroadcastUpdateMessage{
			Message: &rpc.BroadcastUpdateMessage_RemoteParticipantUpdate{
				RemoteParticipantUpdate: remoteParticipantUpdate,
			},
		},
	}
	err := r.broadcastSignalPubSub.PublishUpdate(r.broadcastSignalCtx, rpc.FormatRoomTopic(r.Name()), broadcastUpdate)
	if err != nil {
		r.logger.Errorw("信令广播异常", err)
	}
}

// 发送信令广播到所有房间指定人
func (r *Room) SendBroadcastUpdatesForReceive(updates []*ParticipantUpdate, receiveIdentities []livekit.ParticipantIdentity) {
	remoteParticipantUpdate := &livekit.RemoteParticipantUpdate{
		Participants: ParticipantUpdateToRemote(updates, string(r.currentNode.NodeID()), r.currentNode.NodeIP(), r.currentNode.RelayPort()),
	}

	broadcastUpdate := &rpc.BroadcastUpdate{
		ReceiveIdentities: livekit.IDsAsStrings[livekit.ParticipantIdentity](receiveIdentities),
		Update: &rpc.BroadcastUpdateMessage{
			Message: &rpc.BroadcastUpdateMessage_RemoteParticipantUpdate{
				RemoteParticipantUpdate: remoteParticipantUpdate,
			},
		},
	}
	err := r.broadcastSignalPubSub.PublishUpdate(r.broadcastSignalCtx, rpc.FormatRoomTopic(r.Name()), broadcastUpdate)
	if err != nil {
		r.logger.Errorw("信令广播异常", err)
	}
}
