package rtc

import (
	"context"
	"encoding/json"
	"sync"

	"github.com/livekit/livekit-server/pkg/config"
	"github.com/livekit/livekit-server/pkg/sfu"
	"github.com/livekit/livekit-server/pkg/sfu/mime"
	"github.com/livekit/livekit-server/pkg/telemetry"
	"github.com/livekit/protocol/livekit"
	"github.com/livekit/protocol/logger"
	"github.com/livekit/protocol/rpc"
	"github.com/livekit/protocol/utils"
	"github.com/pion/webrtc/v4"
	"go.uber.org/atomic"
)

type RemoteMediaTrackParams struct {
	CurrentNodeID livekit.NodeID
	CurrentNodeIP string

	NodeID              livekit.NodeID
	NodeIP              string
	NodeRelayPort       int
	RoomName            livekit.RoomName
	ParticipantID       func() livekit.ParticipantID
	ParticipantIdentity livekit.ParticipantIdentity
	ParticipantVersion  uint32

	ReceiverConfig   ReceiverConfig
	SubscriberConfig DirectionConfig
	AudioConfig      sfu.AudioConfig
	VideoConfig      config.VideoConfig
	RTCConfig        config.RTCConfig
	Telemetry        telemetry.TelemetryService

	GetMediaRelayClient func(nodeID livekit.NodeID) (rpc.TypedMediaRelayClient, error)
	GetMediaRelayChan   func(startRequest *rpc.StartRequest) (sfu.MessageSource, error)
	Logger              logger.Logger
}

type RemoteMediaTrack struct {
	params    RemoteMediaTrackParams
	trackInfo atomic.Pointer[livekit.TrackInfo]

	ctx    context.Context
	cancel context.CancelFunc

	mediaRelayClient rpc.TypedMediaRelayClient
	remoteReceiver   *sfu.RemoteReceiver
	lock             sync.RWMutex

	*MediaTrackReceiver

	backupCodecPolicy     livekit.BackupCodecPolicy
	regressionTargetCodec mime.MimeType
}

func NewRemoteMediaTrack(params RemoteMediaTrackParams, ti *livekit.TrackInfo) *RemoteMediaTrack {
	ctx, cancelFunc := context.WithCancel(context.Background())
	t := &RemoteMediaTrack{
		params:            params,
		ctx:               ctx,
		cancel:            cancelFunc,
		backupCodecPolicy: ti.BackupCodecPolicy,
	}
	t.trackInfo.Store(utils.CloneProto(ti))
	if t.backupCodecPolicy != livekit.BackupCodecPolicy_SIMULCAST && len(ti.Codecs) > 1 {
		t.regressionTargetCodec = mime.NormalizeMimeType(ti.Codecs[1].MimeType)
		t.params.Logger.Debugw("track enabled codec regression", "regressionCodec", t.regressionTargetCodec)
	}

	t.MediaTrackReceiver = NewMediaTrackReceiver(MediaTrackReceiverParams{
		MediaTrack:            t,
		IsRelayed:             true,
		ParticipantID:         params.ParticipantID,
		ParticipantIdentity:   params.ParticipantIdentity,
		ParticipantVersion:    params.ParticipantVersion,
		ReceiverConfig:        params.ReceiverConfig,
		SubscriberConfig:      params.SubscriberConfig,
		AudioConfig:           params.AudioConfig,
		Telemetry:             params.Telemetry,
		Logger:                params.Logger,
		RegressionTargetCodec: t.regressionTargetCodec,
	}, ti)

	if ti.Type == livekit.TrackType_AUDIO {
		// TODO-zjx 可能需要补充 Audio 的一些特性处理
	}

	if ti.Type == livekit.TrackType_VIDEO {
		t.MediaTrackReceiver.OnSetupReceiver(func(mime mime.MimeType) {
			t.sendOnSetupReceiver(mime)
		})
		t.MediaTrackReceiver.OnSubscriberMaxQualityChange(func(subscriberID livekit.ParticipantID, mimeType mime.MimeType, layer int32) {
			t.sendOnSubscriberMaxQualityChange(subscriberID, mimeType, layer)
		})
		t.MediaTrackReceiver.OnCodecRegression(func(old, new webrtc.RTPCodecParameters) {
			t.sendOnCodecRegression(old, new)
		})
	}

	return t
}

func (t *RemoteMediaTrack) ToProto() *livekit.TrackInfo {
	return utils.CloneProto(t.TrackInfo())
}

func (t *RemoteMediaTrack) Logger() logger.Logger {
	return t.params.Logger
}

func (t *RemoteMediaTrack) OnTrackSubscribed() {
	t.sendOnTrackSubscribed()
}

func (t *RemoteMediaTrack) CreateRemoteReceiverIfNecessary(subscriberIdentity livekit.ParticipantIdentity) error {
	t.lock.RLock()
	rr := t.remoteReceiver
	t.lock.RUnlock()
	if rr != nil {
		return nil
	}

	t.lock.Lock()
	if t.remoteReceiver != nil {
		t.lock.Unlock()
		return nil
	}
	client, err := t.params.GetMediaRelayClient(t.params.NodeID)
	if err != nil {
		return err
	}

	source, err := t.newSource(subscriberIdentity)
	if err != nil {
		t.lock.Unlock()
		return err
	}

	// 创建远程接收器
	mimeType := mime.NormalizeMimeType(t.TrackInfo().MimeType)
	rr = sfu.NewRemoteReceiver(sfu.RemoteReceiverParams{
		Logger:                     LoggerWithCodecMime(t.params.Logger, mimeType),
		NodeID:                     t.params.NodeID,
		RoomName:                   t.params.RoomName,
		ParticipantID:              t.params.ParticipantID,
		ParticipantIdentity:        t.params.ParticipantIdentity,
		Ctx:                        t.ctx,
		PacketBufferSizeVideo:      t.params.RTCConfig.PacketBufferSizeVideo,
		PacketBufferSizeAudio:      t.params.RTCConfig.PacketBufferSizeAudio,
		StreamTrackerManagerConfig: t.params.VideoConfig.StreamTrackerManager,
	}, client, source, t.TrackInfo())

	// 设置关闭回调
	rr.OnCloseHandler(func() {
		t.lock.Lock()
		// RemoteReceiver 关闭时不能直接关闭 MediaTrackReceiver,
		//t.MediaTrackReceiver.SetClosing(false)
		t.MediaTrackReceiver.ClearReceiver(mimeType, false)
		//if t.MediaTrackReceiver.TryClose() {
		//	// 本地媒体轨道的逻辑是调用 t.dynacastManager.Close() 关闭质量动态广播管
		//	// 理器（用于动态调整如何处理发布者的视频质量），但这里是远程媒体轨道，属于订阅者所以无需处理
		//}
		t.closeSource(source)
		t.mediaRelayClient = nil
		t.remoteReceiver = nil
		t.lock.Unlock()
	})

	// 设置最大层变化回调
	rr.OnMaxLayerChange(func(maxLayer int32) {
		t.MediaTrackReceiver.NotifyMaxLayerChange(maxLayer)
	})

	t.mediaRelayClient = client
	t.remoteReceiver = rr
	t.lock.Unlock()

	// 添加到媒体 MediaTrackReceiver 对象中
	t.MediaTrackReceiver.SetupReceiver(rr, 0, t.TrackInfo().Mid)

	return nil
}

func (t *RemoteMediaTrack) Close(isExpectedToResume bool) {
	t.MediaTrackReceiver.SetClosing(isExpectedToResume)

	t.MediaTrackReceiver.ClearAllReceivers(isExpectedToResume)
	t.MediaTrackReceiver.Close(isExpectedToResume)
}

func (t *RemoteMediaTrack) newSource(subscriberIdentity livekit.ParticipantIdentity) (sfu.MessageSource, error) {
	req := &rpc.StartRequest{
		RoomName:               string(t.params.RoomName),
		SubscriberNodeId:       string(t.params.CurrentNodeID),
		SubscriberNodeIp:       t.params.CurrentNodeIP,
		SubscriberIdentity:     string(subscriberIdentity),
		PublisherNodeId:        string(t.params.NodeID),
		PublisherNodeIp:        t.params.NodeIP,
		PublisherNodeRelayPort: int32(t.params.NodeRelayPort),
		PublisherIdentity:      string(t.params.ParticipantIdentity),
		TrackId:                string(t.ID()),
	}
	source, err := t.params.GetMediaRelayChan(req)
	return source, err
}

func (t *RemoteMediaTrack) closeSource(source sfu.MessageSource) {
	source.Close()
}

func (t *RemoteMediaTrack) isRelayReady() bool {
	t.lock.RLock()
	defer t.lock.RUnlock()
	return t.mediaRelayClient != nil && t.remoteReceiver != nil
}

func (t *RemoteMediaTrack) sendOnSetupReceiver(mime mime.MimeType) {
	if !t.isRelayReady() {
		return
	}
	request := t.remoteReceiver.GetRelayTrackReceiverBaseRequest()
	request.Request.Message = &rpc.TrackReceiverRequest_OnSetupReceiverRequest{
		OnSetupReceiverRequest: &rpc.OnSetupReceiver{
			MimeType: int32(mime),
		},
	}
	_, err := t.mediaRelayClient.RelayTrackReceiver(t.ctx, t.params.NodeID, request)
	if err != nil {
		t.params.Logger.Errorw("轨道中继RPC发送 OnSetupReceiver 失败", err)
	}
}

func (t *RemoteMediaTrack) sendOnSubscriberMaxQualityChange(subscriberID livekit.ParticipantID, mimeType mime.MimeType, layer int32) {
	if !t.isRelayReady() {
		return
	}
	request := t.remoteReceiver.GetRelayTrackReceiverBaseRequest()
	request.Request.Message = &rpc.TrackReceiverRequest_OnSubscriberMaxQualityChangeRequest{
		OnSubscriberMaxQualityChangeRequest: &rpc.OnSubscriberMaxQualityChange{
			SubscriberId: string(subscriberID),
			MimeType:     int32(mimeType),
			Layer:        layer,
		},
	}
	_, err := t.mediaRelayClient.RelayTrackReceiver(t.ctx, t.params.NodeID, request)
	if err != nil {
		t.params.Logger.Errorw("轨道中继RPC发送 OnSubscriberMaxQualityChange 失败", err)
	}
}

func (t *RemoteMediaTrack) sendOnCodecRegression(old, new webrtc.RTPCodecParameters) {
	if !t.isRelayReady() {
		return
	}
	oldBytes, err := json.Marshal(old)
	if err != nil {
		t.params.Logger.Errorw("轨道中继RPC发送 OnCodecRegression 失败-序列化old", err)
	}
	newBytes, err := json.Marshal(new)
	if err != nil {
		t.params.Logger.Errorw("轨道中继RPC发送 OnCodecRegression 失败-序列化new", err)
	}
	request := t.remoteReceiver.GetRelayTrackReceiverBaseRequest()
	request.Request.Message = &rpc.TrackReceiverRequest_OnCodecRegressionRequest{
		OnCodecRegressionRequest: &rpc.OnCodecRegression{
			Old: oldBytes,
			New: newBytes,
		},
	}
	_, err = t.mediaRelayClient.RelayTrackReceiver(t.ctx, t.params.NodeID, request)
	if err != nil {
		t.params.Logger.Errorw("轨道中继RPC发送 OnCodecRegression 失败", err)
	}
}

func (t *RemoteMediaTrack) sendOnTrackSubscribed() {
	if !t.isRelayReady() {
		return
	}
	request := t.remoteReceiver.GetRelayTrackReceiverBaseRequest()
	request.Request.Message = &rpc.TrackReceiverRequest_OnTrackSubscribedRequest{
		OnTrackSubscribedRequest: &rpc.OnTrackSubscribed{},
	}
	_, err := t.mediaRelayClient.RelayTrackReceiver(t.ctx, t.params.NodeID, request)
	if err != nil {
		t.params.Logger.Errorw("轨道中继RPC发送 OnTrackSubscribed 失败", err)
	}
}
