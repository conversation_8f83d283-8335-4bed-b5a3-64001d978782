// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package rtc

import (
	"context"
	"math"
	"sync"
	"time"

	"github.com/pion/rtcp"
	"github.com/pion/webrtc/v4"
	"go.uber.org/atomic"

	"github.com/livekit/protocol/livekit"
	"github.com/livekit/protocol/logger"
	"github.com/livekit/protocol/observability/roomobs"

	"github.com/livekit/livekit-server/pkg/config"
	"github.com/livekit/livekit-server/pkg/rtc/dynacast"
	"github.com/livekit/livekit-server/pkg/rtc/types"
	"github.com/livekit/livekit-server/pkg/sfu"
	"github.com/livekit/livekit-server/pkg/sfu/buffer"
	"github.com/livekit/livekit-server/pkg/sfu/connectionquality"
	"github.com/livekit/livekit-server/pkg/sfu/mime"
	"github.com/livekit/livekit-server/pkg/telemetry"
	util "github.com/livekit/mediatransportutil"
)

// MediaTrack represents a WebRTC track that needs to be forwarded
// Implements MediaTrack and PublishedTrack interface
type MediaTrack struct {
	params         MediaTrackParams
	numUpTracks    atomic.Uint32
	buffer         *buffer.Buffer
	everSubscribed atomic.Bool

	*MediaTrackReceiver
	*MediaLossProxy

	dynacastManager *dynacast.DynacastManager

	lock sync.RWMutex

	rttFromXR atomic.Bool

	backupCodecPolicy             livekit.BackupCodecPolicy
	regressionTargetCodec         mime.MimeType
	regressionTargetCodecReceived bool
}

type MediaTrackParams struct {
	SignalCid             string
	SdpCid                string
	ParticipantID         func() livekit.ParticipantID
	ParticipantIdentity   livekit.ParticipantIdentity
	ParticipantVersion    uint32
	BufferFactory         *buffer.Factory
	ReceiverConfig        ReceiverConfig
	SubscriberConfig      DirectionConfig
	PLIThrottleConfig     sfu.PLIThrottleConfig
	AudioConfig           sfu.AudioConfig
	VideoConfig           config.VideoConfig
	Telemetry             telemetry.TelemetryService
	Logger                logger.Logger
	Reporter              roomobs.TrackReporter
	SimTracks             map[uint32]SimulcastTrackInfo
	OnRTCP                func([]rtcp.Packet)
	ForwardStats          *sfu.ForwardStats
	OnTrackEverSubscribed func(livekit.TrackID)
	ShouldRegressCodec    func() bool
}

func NewMediaTrack(params MediaTrackParams, ti *livekit.TrackInfo) *MediaTrack {
	t := &MediaTrack{
		params:            params,
		backupCodecPolicy: ti.BackupCodecPolicy,
	}

	if t.backupCodecPolicy != livekit.BackupCodecPolicy_SIMULCAST && len(ti.Codecs) > 1 {
		t.regressionTargetCodec = mime.NormalizeMimeType(ti.Codecs[1].MimeType)
		t.params.Logger.Debugw("track enabled codec regression", "regressionCodec", t.regressionTargetCodec)
	}

	t.MediaTrackReceiver = NewMediaTrackReceiver(MediaTrackReceiverParams{
		MediaTrack:            t,
		IsRelayed:             false,
		ParticipantID:         params.ParticipantID,
		ParticipantIdentity:   params.ParticipantIdentity,
		ParticipantVersion:    params.ParticipantVersion,
		ReceiverConfig:        params.ReceiverConfig,
		SubscriberConfig:      params.SubscriberConfig,
		AudioConfig:           params.AudioConfig,
		Telemetry:             params.Telemetry,
		Logger:                params.Logger,
		RegressionTargetCodec: t.regressionTargetCodec,
	}, ti)

	if ti.Type == livekit.TrackType_AUDIO {
		t.MediaLossProxy = NewMediaLossProxy(MediaLossProxyParams{
			Logger: params.Logger,
		})
		t.MediaLossProxy.OnMediaLossUpdate(func(fractionalLoss uint8) {
			if t.buffer != nil {
				t.buffer.SetLastFractionLostReport(fractionalLoss)
			}
		})
		t.MediaTrackReceiver.OnMediaLossFeedback(t.MediaLossProxy.HandleMaxLossFeedback)
	}

	if ti.Type == livekit.TrackType_VIDEO {
		t.dynacastManager = dynacast.NewDynacastManager(dynacast.DynacastManagerParams{
			DynacastPauseDelay: params.VideoConfig.DynacastPauseDelay,
			Logger:             params.Logger,
		})
		t.MediaTrackReceiver.OnSetupReceiver(func(mime mime.MimeType) {
			t.dynacastManager.AddCodec(mime)
		})
		t.MediaTrackReceiver.OnSubscriberMaxQualityChange(
			func(subscriberID livekit.ParticipantID, mimeType mime.MimeType, layer int32) {
				t.dynacastManager.NotifySubscriberMaxQuality(
					subscriberID,
					mimeType,
					buffer.GetVideoQualityForSpatialLayer(layer, t.MediaTrackReceiver.TrackInfo()),
				)
			},
		)
		t.MediaTrackReceiver.OnCodecRegression(func(old, new webrtc.RTPCodecParameters) {
			t.dynacastManager.HandleCodecRegression(mime.NormalizeMimeType(old.MimeType), mime.NormalizeMimeType(new.MimeType))
		})
	}

	return t
}

func (t *MediaTrack) OnSubscribedMaxQualityChange(
	f func(
		trackID livekit.TrackID,
		trackInfo *livekit.TrackInfo,
		subscribedQualities []*livekit.SubscribedCodec,
		maxSubscribedQualities []types.SubscribedCodecQuality,
	) error,
) {
	if t.dynacastManager == nil {
		return
	}

	handler := func(subscribedQualities []*livekit.SubscribedCodec, maxSubscribedQualities []types.SubscribedCodecQuality) {
		if f != nil && !t.IsMuted() {
			_ = f(t.ID(), t.ToProto(), subscribedQualities, maxSubscribedQualities)
		}

		for _, q := range maxSubscribedQualities {
			receiver := t.Receiver(q.CodecMime)
			if receiver != nil {
				receiver.SetMaxExpectedSpatialLayer(buffer.GetSpatialLayerForVideoQuality(q.Quality, t.MediaTrackReceiver.TrackInfo()))
			}
		}
	}

	t.dynacastManager.OnSubscribedMaxQualityChange(handler)
}

func (t *MediaTrack) NotifySubscriberNodeMaxQuality(nodeID livekit.NodeID, qualities []types.SubscribedCodecQuality) {
	if t.dynacastManager != nil {
		t.dynacastManager.NotifySubscriberNodeMaxQuality(nodeID, qualities)
	}
}

func (t *MediaTrack) ClearSubscriberNodesMaxQuality() {
	if t.dynacastManager != nil {
		t.dynacastManager.ClearSubscriberNodesMaxQuality()
	}
}

func (t *MediaTrack) SignalCid() string {
	return t.params.SignalCid
}

func (t *MediaTrack) HasSdpCid(cid string) bool {
	if t.params.SdpCid == cid {
		return true
	}

	ti := t.MediaTrackReceiver.TrackInfoClone()
	for _, c := range ti.Codecs {
		if c.Cid == cid {
			return true
		}
	}
	return false
}

func (t *MediaTrack) ToProto() *livekit.TrackInfo {
	return t.MediaTrackReceiver.TrackInfoClone()
}

func (t *MediaTrack) UpdateCodecCid(codecs []*livekit.SimulcastCodec) {
	t.MediaTrackReceiver.UpdateCodecCid(codecs)
}

// AddReceiver adds a new RTP receiver to the track, returns true when receiver represents a new codec
func (t *MediaTrack) AddReceiver(receiver *webrtc.RTPReceiver, track sfu.TrackRemote, mid string) bool {
	var newCodec bool

	// 获取同步源标识 SSRC，不同 layer 拥有不同的 SSRC，对于客户端而言就是不同的轨道
	ssrc := uint32(track.SSRC())

	// 从缓冲工厂获取 RTP 缓冲区和 RTCP 读取器
	buff, rtcpReader := t.params.BufferFactory.GetBufferPair(ssrc)
	if buff == nil || rtcpReader == nil {
		t.params.Logger.Errorw("could not retrieve buffer pair", nil)
		return newCodec
	}

	// 用于避免重复计算 RTT
	var lastRR uint32

	// 注册 RTCP 数据包事件处理函数
	rtcpReader.OnPacket(func(bytes []byte) {
		pkts, err := rtcp.Unmarshal(bytes)
		if err != nil {
			t.params.Logger.Errorw("could not unmarshal RTCP", err)
			return
		}

		for _, pkt := range pkts {
			switch pkt := pkt.(type) {
			case *rtcp.SourceDescription:
			case *rtcp.SenderReport:
				if pkt.SSRC == uint32(track.SSRC()) {
					buff.SetSenderReportData(pkt.RTPTime, pkt.NTPTime, pkt.PacketCount, pkt.OctetCount)
				}
			case *rtcp.ExtendedReport:
			rttFromXR:
				for _, report := range pkt.Reports {
					if rr, ok := report.(*rtcp.DLRRReportBlock); ok {
						for _, dlrrReport := range rr.Reports {
							if dlrrReport.LastRR <= lastRR {
								continue
							}
							nowNTP := util.ToNtpTime(time.Now())
							nowNTP32 := uint32(nowNTP >> 16)
							ntpDiff := nowNTP32 - dlrrReport.LastRR - dlrrReport.DLRR
							rtt := uint32(math.Ceil(float64(ntpDiff) * 1000.0 / 65536.0))
							buff.SetRTT(rtt)
							t.rttFromXR.Store(true)
							lastRR = dlrrReport.LastRR
							break rttFromXR
						}
					}
				}
			}
		}
	})

	ti := t.MediaTrackReceiver.TrackInfoClone()
	t.lock.Lock()
	var regressCodec bool
	mimeType := mime.NormalizeMimeType(track.Codec().MimeType)
	layer := buffer.GetSpatialLayerForRid(track.RID(), ti)
	t.params.Logger.Debugw(
		"AddReceiver",
		"rid", track.RID(),
		"layer", layer,
		"ssrc", track.SSRC(),
		"codec", track.Codec(),
	)
	logger.Infow(
		"AddReceiver",
		"rid", track.RID(),
		"layer", layer,
		"ssrc", track.SSRC(),
		"codec", track.Codec(),
		"trackInfo", logger.Proto(ti),
	) // REMOVE

	// 获取已存在的轨道接收器（WebRTCReceiver）
	// 相同 mimeType 但不同 layer 复用同一个轨道接收器，
	// WebRTCReceiver 中会维护不同层对应的 buffer 和 webrtc.TrackRemote
	wr := t.MediaTrackReceiver.Receiver(mimeType)

	// 如果相应媒体类型的接收器为空进入创建流程
	if wr == nil {
		// 设置优先级，以 Codec.MimeType 的索引号座位优先级
		priority := -1
		// 只有视频才有 TrackInfo.Codec
		for idx, c := range ti.Codecs {
			if mime.IsMimeTypeStringEqual(track.Codec().MimeType, c.MimeType) {
				priority = idx
				break
			}
		}
		// 音频、旧版本客户端或者未开启 simulcast 的情况，设置为 0
		if priority < 0 {
			switch len(ti.Codecs) {
			case 0:
				// audio track
				priority = 0
			case 1:
				// older clients or non simulcast-codec, mime type only set later
				if ti.Codecs[0].MimeType == "" {
					priority = 0
				}
			}
		}
		if priority < 0 {
			t.params.Logger.Warnw("could not find codec for webrtc receiver", nil, "webrtcCodec", mimeType, "track", logger.Proto(ti))
			t.lock.Unlock()
			return false
		}

		// 创建 WebRTCReceiver 并设置回调
		newWR := sfu.NewWebRTCReceiver(
			receiver,
			track,
			ti,
			LoggerWithCodecMime(t.params.Logger, mimeType),
			t.params.OnRTCP,
			t.params.VideoConfig.StreamTrackerManager,
			sfu.WithPliThrottleConfig(t.params.PLIThrottleConfig),
			sfu.WithAudioConfig(t.params.AudioConfig),
			sfu.WithLoadBalanceThreshold(20),
			sfu.WithStreamTrackers(),
			sfu.WithForwardStats(t.params.ForwardStats),
		)

		// 设置关闭回调
		newWR.OnCloseHandler(func() {
			t.MediaTrackReceiver.SetClosing(false)
			t.MediaTrackReceiver.ClearReceiver(mimeType, false)
			if t.MediaTrackReceiver.TryClose() {
				if t.dynacastManager != nil {
					t.dynacastManager.Close()
				}
			}
		})

		// 统计回调
		// SIMULCAST-CODEC-TODO: these need to be receiver/mime aware, setting it up only for primary now
		newWR.OnStatsUpdate(func(_ *sfu.WebRTCReceiver, stat *livekit.AnalyticsStat) {
			// send for only one codec, either primary (priority == 0) OR regressed codec
			t.lock.RLock()
			regressionTargetCodecReceived := t.regressionTargetCodecReceived
			t.lock.RUnlock()
			if priority == 0 || regressionTargetCodecReceived {
				key := telemetry.StatsKeyForTrack(livekit.StreamType_UPSTREAM, t.PublisherID(), t.ID(), ti.Source, ti.Type)
				t.params.Telemetry.TrackStats(key, stat)

				if cs, ok := telemetry.CondenseStat(stat); ok {
					t.params.Reporter.Tx(func(tx roomobs.TrackTx) {
						tx.ReportName(ti.Name)
						tx.ReportKind(roomobs.TrackKindPub)
						tx.ReportType(roomobs.TrackTypeFromProto(ti.Type))
						tx.ReportSource(roomobs.TrackSourceFromProto(ti.Source))
						tx.ReportMime(mime.NormalizeMimeType(ti.MimeType).ReporterType())
						tx.ReportLayer(roomobs.PackTrackLayer(ti.Height, ti.Width))
						tx.ReportDuration(uint16(cs.EndTime.Sub(cs.StartTime).Milliseconds()))
						tx.ReportFrames(uint16(cs.Frames))
						tx.ReportRecvBytes(uint32(cs.Bytes))
						tx.ReportRecvPackets(cs.Packets)
						tx.ReportPacketsLost(cs.PacketsLost)
						tx.ReportScore(stat.Score)
					})
				}
			}
		})

		// 设置最大层变化回调
		newWR.OnMaxLayerChange(func(maxLayer int32) {
			// send for only one codec, either primary (priority == 0) OR regressed codec
			t.lock.RLock()
			regressionTargetCodecReceived := t.regressionTargetCodecReceived
			t.lock.RUnlock()
			if priority == 0 || regressionTargetCodecReceived {
				t.MediaTrackReceiver.NotifyMaxLayerChange(maxLayer)
			}
		})
		// SIMULCAST-CODEC-TODO END: these need to be receiver/mime aware, setting it up only for primary now

		// 主要的编解码器已发布，设置潜在的编解码器
		if t.PrimaryReceiver() == nil {
			// primary codec published, set potential codecs
			potentialCodecs := make([]webrtc.RTPCodecParameters, 0, len(ti.Codecs))
			parameters := receiver.GetParameters()
			for _, c := range ti.Codecs {
				for _, nc := range parameters.Codecs {
					if mime.IsMimeTypeStringEqual(nc.MimeType, c.MimeType) {
						potentialCodecs = append(potentialCodecs, nc)
						break
					}
				}
			}

			if len(potentialCodecs) > 0 {
				t.params.Logger.Debugw("primary codec published, set potential codecs", "potential", potentialCodecs)
				// 设置潜在编解码器的接收器，该方法会向 MediaTrackReceiver.receivers 中追加 simulcastReceiver{DummyReceiver}
				t.MediaTrackReceiver.SetPotentialCodecs(potentialCodecs, parameters.HeaderExtensions)
			}
		}

		t.buffer = buff

		// 启动接收器（newWR WebRTCReceiver）
		//  如果已存在接收器且类型为 simulcastReceiver{DummyReceiver} 则设置 WebRTCReceiver 为真实接收器，即升级
		//  如果不存在则利用 simulcastReceiver 接收器包装 WebRTCReceiver
		t.MediaTrackReceiver.SetupReceiver(newWR, priority, mid)

		// 这段逻辑似乎不会进来，t.params.SimTracks 来自 ParticipantParams.SimTracks，但我没有找到这个字段被赋值的代码
		for ssrc, info := range t.params.SimTracks {
			if info.Mid == mid {
				t.MediaTrackReceiver.SetLayerSsrc(mimeType, info.Rid, ssrc)
			}
		}
		wr = newWR
		newCodec = true

		newWR.AddOnCodecStateChange(func(codec webrtc.RTPCodecParameters, state sfu.ReceiverCodecState) {
			t.MediaTrackReceiver.HandleReceiverCodecChange(newWR, codec, state)
		})
	}

	if newCodec && t.enableRegression() {
		if mimeType == t.regressionTargetCodec {
			t.params.Logger.Infow("regression target codec received", "codec", mimeType)
			t.regressionTargetCodecReceived = true
			regressCodec = true
		} else if t.regressionTargetCodecReceived {
			regressCodec = true
		}
	}
	t.lock.Unlock()

	// 添加上行轨道
	//  设置 WebRTCReceiver 中 layer 对应的的 TrackRemote 和 buff
	//  启动 layer 对应的RTP转发 w.forwardRTP(layer, buff)
	if err := wr.(*sfu.WebRTCReceiver).AddUpTrack(track, buff); err != nil {
		t.params.Logger.Warnw(
			"adding up track failed", err,
			"rid", track.RID(),
			"layer", layer,
			"ssrc", track.SSRC(),
			"newCodec", newCodec,
		)
		buff.Close()
		return false
	}

	// LK-TODO: can remove this completely when VideoLayers protocol becomes the default as it has info from client or if we decide to use TrackInfo.Simulcast
	if t.numUpTracks.Inc() > 1 || track.RID() != "" {
		// cannot only rely on numUpTracks since we fire metadata events immediately after the first layer
		t.SetSimulcast(true)
	}

	var bitrates int
	if len(ti.Layers) > int(layer) {
		bitrates = int(ti.Layers[layer].GetBitrate())
	}

	// 设置 layer 到 SSRC 的映射
	t.MediaTrackReceiver.SetLayerSsrc(mimeType, track.RID(), uint32(track.SSRC()))

	if regressCodec {
		for _, c := range ti.Codecs {
			if mime.NormalizeMimeType(c.MimeType) == t.regressionTargetCodec {
				continue
			}

			t.params.Logger.Debugw("suspending codec for codec regression", "codec", c.MimeType)
			if r := t.MediaTrackReceiver.Receiver(mime.NormalizeMimeType(c.MimeType)); r != nil {
				if rtcreceiver, ok := r.(*sfu.WebRTCReceiver); ok {
					rtcreceiver.SetCodecState(sfu.ReceiverCodecStateSuspended)
				}
			}
		}
	}

	buff.Bind(receiver.GetParameters(), track.Codec().RTPCodecCapability, bitrates)

	// if subscriber request fps before fps calculated, update them after fps updated.
	buff.OnFpsChanged(func() {
		t.MediaTrackReceiver.MediaTrackSubscriptions.UpdateVideoLayers()
	})

	// 统计
	buff.OnFinalRtpStats(func(stats *livekit.RTPStats) {
		t.params.Telemetry.TrackPublishRTPStats(
			context.Background(),
			t.params.ParticipantID(),
			t.ID(),
			mimeType,
			int(layer),
			stats,
		)
	})
	return newCodec
}

func (t *MediaTrack) GetConnectionScoreAndQuality() (float32, livekit.ConnectionQuality) {
	receiver := t.PrimaryReceiver()
	if rtcReceiver, ok := receiver.(*sfu.WebRTCReceiver); ok {
		return rtcReceiver.GetConnectionScoreAndQuality()
	}

	return connectionquality.MaxMOS, livekit.ConnectionQuality_EXCELLENT
}

func (t *MediaTrack) SetRTT(rtt uint32) {
	if !t.rttFromXR.Load() {
		t.MediaTrackReceiver.SetRTT(rtt)
	}
}

func (t *MediaTrack) HasPendingCodec() bool {
	return t.MediaTrackReceiver.PrimaryReceiver() == nil
}

func (t *MediaTrack) Restart() {
	t.MediaTrackReceiver.Restart()

	if t.dynacastManager != nil {
		t.dynacastManager.Restart()
	}
}

func (t *MediaTrack) Close(isExpectedToResume bool) {
	t.MediaTrackReceiver.SetClosing(isExpectedToResume)
	if t.dynacastManager != nil {
		t.dynacastManager.Close()
	}
	t.MediaTrackReceiver.ClearAllReceivers(isExpectedToResume)
	t.MediaTrackReceiver.Close(isExpectedToResume)
}

func (t *MediaTrack) SetMuted(muted bool) {
	// update quality based on subscription if unmuting.
	// This will queue up the current state, but subscriber
	// driven changes could update it.
	if !muted && t.dynacastManager != nil {
		t.dynacastManager.ForceUpdate()
	}

	t.MediaTrackReceiver.SetMuted(muted)
}

// OnTrackSubscribed is called when the track is subscribed by a non-hidden subscriber
// this allows the publisher to know when they should start sending data
func (t *MediaTrack) OnTrackSubscribed() {
	if !t.everSubscribed.Swap(true) && t.params.OnTrackEverSubscribed != nil {
		go t.params.OnTrackEverSubscribed(t.ID())
	}
}

func (t *MediaTrack) enableRegression() bool {
	return t.backupCodecPolicy == livekit.BackupCodecPolicy_REGRESSION ||
		(t.backupCodecPolicy == livekit.BackupCodecPolicy_PREFER_REGRESSION && t.params.ShouldRegressCodec())
}

func (t *MediaTrack) Logger() logger.Logger {
	return t.params.Logger
}
