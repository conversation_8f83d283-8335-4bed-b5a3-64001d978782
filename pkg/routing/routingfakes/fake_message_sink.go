// Code generated by counterfeiter. DO NOT EDIT.
package routingfakes

import (
	"sync"

	"github.com/livekit/livekit-server/pkg/routing"
	"github.com/livekit/protocol/livekit"
	"google.golang.org/protobuf/proto"
)

type FakeMessageSink struct {
	CloseStub        func()
	closeMutex       sync.RWMutex
	closeArgsForCall []struct {
	}
	ConnectionIDStub        func() livekit.ConnectionID
	connectionIDMutex       sync.RWMutex
	connectionIDArgsForCall []struct {
	}
	connectionIDReturns struct {
		result1 livekit.ConnectionID
	}
	connectionIDReturnsOnCall map[int]struct {
		result1 livekit.ConnectionID
	}
	IsClosedStub        func() bool
	isClosedMutex       sync.RWMutex
	isClosedArgsForCall []struct {
	}
	isClosedReturns struct {
		result1 bool
	}
	isClosedReturnsOnCall map[int]struct {
		result1 bool
	}
	WriteMessageStub        func(proto.Message) error
	writeMessageMutex       sync.RWMutex
	writeMessageArgsForCall []struct {
		arg1 proto.Message
	}
	writeMessageReturns struct {
		result1 error
	}
	writeMessageReturnsOnCall map[int]struct {
		result1 error
	}
	invocations      map[string][][]interface{}
	invocationsMutex sync.RWMutex
}

func (fake *FakeMessageSink) Close() {
	fake.closeMutex.Lock()
	fake.closeArgsForCall = append(fake.closeArgsForCall, struct {
	}{})
	stub := fake.CloseStub
	fake.recordInvocation("Close", []interface{}{})
	fake.closeMutex.Unlock()
	if stub != nil {
		fake.CloseStub()
	}
}

func (fake *FakeMessageSink) CloseCallCount() int {
	fake.closeMutex.RLock()
	defer fake.closeMutex.RUnlock()
	return len(fake.closeArgsForCall)
}

func (fake *FakeMessageSink) CloseCalls(stub func()) {
	fake.closeMutex.Lock()
	defer fake.closeMutex.Unlock()
	fake.CloseStub = stub
}

func (fake *FakeMessageSink) ConnectionID() livekit.ConnectionID {
	fake.connectionIDMutex.Lock()
	ret, specificReturn := fake.connectionIDReturnsOnCall[len(fake.connectionIDArgsForCall)]
	fake.connectionIDArgsForCall = append(fake.connectionIDArgsForCall, struct {
	}{})
	stub := fake.ConnectionIDStub
	fakeReturns := fake.connectionIDReturns
	fake.recordInvocation("ConnectionID", []interface{}{})
	fake.connectionIDMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeMessageSink) ConnectionIDCallCount() int {
	fake.connectionIDMutex.RLock()
	defer fake.connectionIDMutex.RUnlock()
	return len(fake.connectionIDArgsForCall)
}

func (fake *FakeMessageSink) ConnectionIDCalls(stub func() livekit.ConnectionID) {
	fake.connectionIDMutex.Lock()
	defer fake.connectionIDMutex.Unlock()
	fake.ConnectionIDStub = stub
}

func (fake *FakeMessageSink) ConnectionIDReturns(result1 livekit.ConnectionID) {
	fake.connectionIDMutex.Lock()
	defer fake.connectionIDMutex.Unlock()
	fake.ConnectionIDStub = nil
	fake.connectionIDReturns = struct {
		result1 livekit.ConnectionID
	}{result1}
}

func (fake *FakeMessageSink) ConnectionIDReturnsOnCall(i int, result1 livekit.ConnectionID) {
	fake.connectionIDMutex.Lock()
	defer fake.connectionIDMutex.Unlock()
	fake.ConnectionIDStub = nil
	if fake.connectionIDReturnsOnCall == nil {
		fake.connectionIDReturnsOnCall = make(map[int]struct {
			result1 livekit.ConnectionID
		})
	}
	fake.connectionIDReturnsOnCall[i] = struct {
		result1 livekit.ConnectionID
	}{result1}
}

func (fake *FakeMessageSink) IsClosed() bool {
	fake.isClosedMutex.Lock()
	ret, specificReturn := fake.isClosedReturnsOnCall[len(fake.isClosedArgsForCall)]
	fake.isClosedArgsForCall = append(fake.isClosedArgsForCall, struct {
	}{})
	stub := fake.IsClosedStub
	fakeReturns := fake.isClosedReturns
	fake.recordInvocation("IsClosed", []interface{}{})
	fake.isClosedMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeMessageSink) IsClosedCallCount() int {
	fake.isClosedMutex.RLock()
	defer fake.isClosedMutex.RUnlock()
	return len(fake.isClosedArgsForCall)
}

func (fake *FakeMessageSink) IsClosedCalls(stub func() bool) {
	fake.isClosedMutex.Lock()
	defer fake.isClosedMutex.Unlock()
	fake.IsClosedStub = stub
}

func (fake *FakeMessageSink) IsClosedReturns(result1 bool) {
	fake.isClosedMutex.Lock()
	defer fake.isClosedMutex.Unlock()
	fake.IsClosedStub = nil
	fake.isClosedReturns = struct {
		result1 bool
	}{result1}
}

func (fake *FakeMessageSink) IsClosedReturnsOnCall(i int, result1 bool) {
	fake.isClosedMutex.Lock()
	defer fake.isClosedMutex.Unlock()
	fake.IsClosedStub = nil
	if fake.isClosedReturnsOnCall == nil {
		fake.isClosedReturnsOnCall = make(map[int]struct {
			result1 bool
		})
	}
	fake.isClosedReturnsOnCall[i] = struct {
		result1 bool
	}{result1}
}

func (fake *FakeMessageSink) WriteMessage(arg1 proto.Message) error {
	fake.writeMessageMutex.Lock()
	ret, specificReturn := fake.writeMessageReturnsOnCall[len(fake.writeMessageArgsForCall)]
	fake.writeMessageArgsForCall = append(fake.writeMessageArgsForCall, struct {
		arg1 proto.Message
	}{arg1})
	stub := fake.WriteMessageStub
	fakeReturns := fake.writeMessageReturns
	fake.recordInvocation("WriteMessage", []interface{}{arg1})
	fake.writeMessageMutex.Unlock()
	if stub != nil {
		return stub(arg1)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeMessageSink) WriteMessageCallCount() int {
	fake.writeMessageMutex.RLock()
	defer fake.writeMessageMutex.RUnlock()
	return len(fake.writeMessageArgsForCall)
}

func (fake *FakeMessageSink) WriteMessageCalls(stub func(proto.Message) error) {
	fake.writeMessageMutex.Lock()
	defer fake.writeMessageMutex.Unlock()
	fake.WriteMessageStub = stub
}

func (fake *FakeMessageSink) WriteMessageArgsForCall(i int) proto.Message {
	fake.writeMessageMutex.RLock()
	defer fake.writeMessageMutex.RUnlock()
	argsForCall := fake.writeMessageArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeMessageSink) WriteMessageReturns(result1 error) {
	fake.writeMessageMutex.Lock()
	defer fake.writeMessageMutex.Unlock()
	fake.WriteMessageStub = nil
	fake.writeMessageReturns = struct {
		result1 error
	}{result1}
}

func (fake *FakeMessageSink) WriteMessageReturnsOnCall(i int, result1 error) {
	fake.writeMessageMutex.Lock()
	defer fake.writeMessageMutex.Unlock()
	fake.WriteMessageStub = nil
	if fake.writeMessageReturnsOnCall == nil {
		fake.writeMessageReturnsOnCall = make(map[int]struct {
			result1 error
		})
	}
	fake.writeMessageReturnsOnCall[i] = struct {
		result1 error
	}{result1}
}

func (fake *FakeMessageSink) Invocations() map[string][][]interface{} {
	fake.invocationsMutex.RLock()
	defer fake.invocationsMutex.RUnlock()
	fake.closeMutex.RLock()
	defer fake.closeMutex.RUnlock()
	fake.connectionIDMutex.RLock()
	defer fake.connectionIDMutex.RUnlock()
	fake.isClosedMutex.RLock()
	defer fake.isClosedMutex.RUnlock()
	fake.writeMessageMutex.RLock()
	defer fake.writeMessageMutex.RUnlock()
	copiedInvocations := map[string][][]interface{}{}
	for key, value := range fake.invocations {
		copiedInvocations[key] = value
	}
	return copiedInvocations
}

func (fake *FakeMessageSink) recordInvocation(key string, args []interface{}) {
	fake.invocationsMutex.Lock()
	defer fake.invocationsMutex.Unlock()
	if fake.invocations == nil {
		fake.invocations = map[string][][]interface{}{}
	}
	if fake.invocations[key] == nil {
		fake.invocations[key] = [][]interface{}{}
	}
	fake.invocations[key] = append(fake.invocations[key], args)
}

var _ routing.MessageSink = new(FakeMessageSink)
