// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package ccutils

import "fmt"

// ------------------------------------------------

type ProbeSignal int

const (
	ProbeSignalInconclusive ProbeSignal = iota
	ProbeSignalCongesting
	ProbeSignalNotCongesting
)

func (p ProbeSignal) String() string {
	switch p {
	case ProbeSignalInconclusive:
		return "INCONCLUSIVE"
	case ProbeSignalCongesting:
		return "CONGESTING"
	case ProbeSignalNotCongesting:
		return "NOT_CONGESTING"
	default:
		return fmt.Sprintf("%d", int(p))
	}
}
