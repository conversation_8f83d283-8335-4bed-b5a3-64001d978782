// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package utils

import (
	"errors"
	"fmt"
	"math"
	"unsafe"

	"go.uber.org/zap/zapcore"
)

const (
	minRanges = 1
)

var (
	errReversedOrder = errors.New("end <= start")
	errKeyNotFound   = errors.New("key not found")
	errKeyTooOld     = errors.New("key too old")
	errKeyExcluded   = errors.New("key excluded")
)

type rangeType interface {
	uint32 | uint64
}

type valueType interface {
	uint32 | uint64
}

// ---------------------------------------------------

type rangeVal[RT rangeType, VT valueType] struct {
	start RT
	end   RT
	value VT
}

func (r rangeVal[RT, VT]) MarshalLogObject(e zapcore.ObjectEncoder) error {
	e.AddUint64("start", uint64(r.start))
	e.AddUint64("end", uint64(r.end))
	e.AddUint64("value", uint64(r.value))
	return nil
}

// ---------------------------------------------------

type RangeMap[RT rangeType, VT valueType] struct {
	halfRange RT

	size   int
	ranges []rangeVal[RT, VT]
}

func NewRangeMap[RT rangeType, VT valueType](size int) *RangeMap[RT, VT] {
	var t RT
	r := &RangeMap[RT, VT]{
		halfRange: 1 << ((unsafe.Sizeof(t) * 8) - 1),
		size:      int(math.Max(float64(size), float64(minRanges))),
	}
	r.initRanges(0, 0)
	return r
}

func (r *RangeMap[RT, VT]) MarshalLogObject(e zapcore.ObjectEncoder) error {
	e.AddInt("numRanges", len(r.ranges))

	// just the last 10 ranges max
	startIdx := len(r.ranges) - 10
	if startIdx < 0 {
		startIdx = 0
	}
	for i := startIdx; i < len(r.ranges); i++ {
		e.AddObject(fmt.Sprintf("range[%d]", i), r.ranges[i])
	}

	return nil
}

func (r *RangeMap[RT, VT]) ClearAndResetValue(start RT, val VT) {
	r.initRanges(start, val)
}

func (r *RangeMap[RT, VT]) DecValue(end RT, dec VT) {
	// 获取 ranges 切片中最后一个范围的指针。这个是当前的“开放范围”
	lr := &r.ranges[len(r.ranges)-1]

	// 检查：如果开放范围的起始点（lr.start）大于传入的结束点（end），
	// 说明这个“递减”操作是针对一个仍在处理中的范围，而不是要关闭的范围。
	if lr.start > end {
		// modify existing value if end is in open range
		lr.value -= dec
		return
	}

	// 如果 lr.start <= end，说明我们要关闭当前的开放范围。
	// 将开放范围的结束点设置为传入的 end。
	// close open range
	lr.end = end

	// 创建一个新的“开放范围”。
	// 新范围的起始点是旧范围的结束点（end）的下一个序列号。
	// 新的偏移量是旧偏移量减去 dec
	// start a new open one with decremented value
	r.ranges = append(r.ranges, rangeVal[RT, VT]{
		start: end + 1,
		end:   0,
		value: lr.value - dec,
	})

	// 对 ranges 切片进行修剪，以防止其无限制增长
	r.prune()
}

func (r *RangeMap[RT, VT]) initRanges(start RT, val VT) {
	r.ranges = []rangeVal[RT, VT]{
		{
			start: start,
			end:   0,
			value: val,
		},
	}
}

func (r *RangeMap[RT, VT]) ExcludeRange(startInclusive RT, endExclusive RT) error {
	// 验证：检查要排除的范围是否有效，如果 endExclusive 小于或等于 startInclusive，或者范围过大，则返回错误。
	if endExclusive == startInclusive || endExclusive-startInclusive > r.halfRange {
		return fmt.Errorf("%w, start %d, end %d", errReversedOrder, startInclusive, endExclusive)
	}

	// 获取当前“开放范围”的指针
	lr := &r.ranges[len(r.ranges)-1]

	// 验证：检查要排除的范围是否在开放范围之前。
	// 如果要排除的范围起始点（startInclusive）早于当前开放范围的起始点（lr.start），
	// 说明逻辑有误，因为这可能意味着一个已处理的范围被排除，这不应该发生。
	if lr.start > startInclusive {
		// start of open range is after start of exclusion range, cannot close the open range
		return fmt.Errorf("%w, existingStart %d, newStart %d", errReversedOrder, lr.start, startInclusive)
	}

	// 增加偏移量，以补偿被排除的序列号
	newValue := lr.value + VT(endExclusive-startInclusive)

	// 检查：如果要排除的范围的起始点，正好等于开放范围的起始点，直接将开放范围的起始点向后移动到排除范围的结束点
	// if start of exclusion range matches start of open range, move the open range
	if lr.start == startInclusive {
		lr.start = endExclusive
		lr.value = newValue
		return nil
	}

	// 将当前的开放范围的结束点设置为要排除的范围的起始点的前一个序列号，这样就关闭了旧的开放范围
	// close previous range
	lr.end = startInclusive - 1

	// 创建一个新的“开放范围”。新的偏移量为旧偏移量加上被排除的序列号数量
	// start new open one after given exclusion range
	r.ranges = append(r.ranges, rangeVal[RT, VT]{
		start: endExclusive,
		end:   0,
		value: newValue,
	})

	r.prune()
	return nil
}

func (r *RangeMap[RT, VT]) GetValue(key RT) (VT, error) {
	numRanges := len(r.ranges)
	if numRanges != 0 {
		// 检查：如果 key 在“开放范围”内（最后一个范围的 start 之后，是 RangeMap 正在处理的最新序列号范围）则直接返回该偏移量
		if key >= r.ranges[numRanges-1].start {
			// in the open range
			return r.ranges[numRanges-1].value, nil
		}

		// 检查：如果 key 小于最早的序列号范围的 start，说明它太旧，已经被修剪掉了
		if key < r.ranges[0].start {
			// too old
			return 0, errKeyTooOld
		}
	}

	// 遍历所有已关闭的范围，从最新到最旧
	for idx := numRanges - 1; idx >= 0; idx-- {
		rv := &r.ranges[idx]
		// 跳过开放范围，因为它已经在上面的 if 语句中检查过了
		if idx != numRanges-1 {
			// open range checked above
			// 检查 key 是否在当前遍历的范围 [rv.start, rv.end] 内。
			// 使用 r.halfRange 进行检查是为了处理序列号回绕（wrap around）的情况。
			// key-rv.start < r.halfRange 检查 key 是否在 rv.start 之后且未回绕。
			// rv.end-key < r.halfRange 检查 key 是否在 rv.end 之前且未回绕。
			if key-rv.start < r.halfRange && rv.end-key < r.halfRange {
				return rv.value, nil
			}
		}

		// 检查 key 是否位于两个相邻的范围之间，如果是，这表示 key 属于一个已被排除的序列号范围（例如，一个被丢弃的包）
		if idx > 0 {
			rvPrev := &r.ranges[idx-1]
			// 检查 key 是否在 rvPrev.end 和 rv.start 之间
			beforeDiff := key - rvPrev.end
			afterDiff := rv.start - key
			// 同样使用 r.halfRange 检查回绕
			if beforeDiff > 0 && beforeDiff < r.halfRange && afterDiff > 0 && afterDiff < r.halfRange {
				// in excluded range
				return 0, errKeyExcluded
			}
		}
	}

	// 如果所有检查都失败，表示找不到 key
	return 0, errKeyNotFound
}

func (r *RangeMap[RT, VT]) prune() {
	if len(r.ranges) > r.size+1 { // +1 to accommodate the open range
		r.ranges = r.ranges[len(r.ranges)-r.size-1:]
	}
}
