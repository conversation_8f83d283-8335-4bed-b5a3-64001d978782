package sfu

import (
	"encoding/json"
	"fmt"
	"reflect"
	"sync"
	"time"

	"github.com/livekit/livekit-server/pkg/sfu/buffer"
	"github.com/livekit/protocol/livekit"
	"github.com/livekit/protocol/logger"
	"github.com/livekit/protocol/rpc"
	"github.com/pion/webrtc/v4"
	"go.uber.org/atomic"
	"google.golang.org/protobuf/proto"
)

type RemoteDownTrackParams struct {
	RoomName           livekit.RoomName
	SubscriberIdentity livekit.ParticipantIdentity
	PublisherIdentity  livekit.ParticipantIdentity
	TrackId            livekit.TrackID
	Receiver           TrackReceiver
	Logger             logger.Logger
}

// 实现 TrackSender 接口用于向远程订阅者发送媒体数据，
// 事实上 RemoteDownTrack 是远程订阅者对应本地 DownTrack 的远程替身，
// 因此需要将下行轨道的相关事件透传到下游的 DownTrack 对象
type RemoteDownTrack struct {
	params       RemoteDownTrackParams
	connectionID livekit.ConnectionID
	sink         MessageSink
	createdAt    int64

	receiverLock sync.RWMutex
	receiver     TrackReceiver

	sendErrCount atomic.Uint32
	isClosed     atomic.Bool
}

func NewRemoteDownTrack(params RemoteDownTrackParams, sink MessageSink) *RemoteDownTrack {
	dt := &RemoteDownTrack{
		params:       params,
		connectionID: livekit.GetMediaRelayConnectionID(string(params.RoomName), string(params.TrackId)),
		sink:         sink,
		createdAt:    time.Now().UnixNano(),
		receiver:     params.Receiver,
	}
	return dt
}

func (d *RemoteDownTrack) UpTrackLayersChange() {
	// 下游 RemoteReceiver 自行维护 StreamTrackerManager，因此这里不需要透传给下游
}

func (d *RemoteDownTrack) UpTrackBitrateAvailabilityChange() {
	// 下游 RemoteReceiver 自行维护 StreamTrackerManager，因此这里不需要透传给下游
}

func (d *RemoteDownTrack) UpTrackMaxPublishedLayerChange(maxPublishedLayer int32) {
	// 下游 RemoteReceiver 自行维护 StreamTrackerManager，因此这里不需要透传给下游
}

func (d *RemoteDownTrack) UpTrackMaxTemporalLayerSeenChange(maxTemporalLayerSeen int32) {
	// 下游 RemoteReceiver 自行维护 StreamTrackerManager，因此这里不需要透传给下游
}

func (d *RemoteDownTrack) UpTrackBitrateReport(availableLayers []int32, bitrates Bitrates) {
	// 下游 RemoteReceiver 自行维护 StreamTrackerManager，因此这里不需要透传给下游
}

func (d *RemoteDownTrack) WriteRTP(ep *buffer.ExtPacket, layer int32) error {
	if d.IsClosed() {
		return nil
	}

	pp, err := ep.ToProto()
	if err != nil {
		d.params.Logger.Errorw("RTP包序列化失败", err)
		return err
	}

	// DEBUG-zjx RTP发送
	//jsonBytes, _ := protojson.Marshal(pp)
	//logger.Debugw("测试UDP日志-发送 " + string(jsonBytes))

	return d.send(&rpc.RelayMediaResponse{
		Message: &rpc.MediaResponse{
			Message: &rpc.MediaResponse_PacketResponse{
				PacketResponse: &rpc.PacketResponse{
					ExtPacket: pp,
					Layer:     layer,
				},
			},
		},
	})
}

func (d *RemoteDownTrack) Close() {
	if d.isClosed.Swap(true) {
		return
	}
	d.params.Receiver.DeleteDownTrack(d.SubscriberID())

	// 关闭RPC流
	d.sink.Close()
}

func (d *RemoteDownTrack) IsClosed() bool {
	return d.isClosed.Load()
}

func (d *RemoteDownTrack) ID() string {
	return string(d.params.TrackId)
}

func (d *RemoteDownTrack) SubscriberID() livekit.ParticipantID {
	// 与本地下行轨道 DownTrack 一样，添加 createdAt 是为了避免当同一订阅者重复订阅同一轨道时，导致订阅 ID 冲突
	return livekit.ParticipantID(fmt.Sprintf("%s:%d", d.params.SubscriberIdentity, d.createdAt))
}

func (d *RemoteDownTrack) HandleRTCPSenderReportData(payloadType webrtc.PayloadType, isSVC bool, layer int32, publisherSRData *livekit.RTCPSenderReportState) error {
	publisherSRDataBytes, _ := json.Marshal(publisherSRData)
	return d.send(&rpc.RelayMediaResponse{
		Message: &rpc.MediaResponse{
			Message: &rpc.MediaResponse_HandleRtcpSenderReportData{
				HandleRtcpSenderReportData: &rpc.HandleRTCPSenderReportData{
					PayloadType:     int32(payloadType),
					IsSvc:           isSVC,
					Layer:           layer,
					PublisherSRData: publisherSRDataBytes,
				},
			},
		},
	})
}

func (d *RemoteDownTrack) Resync() {
	_ = d.send(&rpc.RelayMediaResponse{
		Message: &rpc.MediaResponse{
			Message: &rpc.MediaResponse_Resync{
				Resync: &rpc.Resync{},
			},
		},
	})
}

func (d *RemoteDownTrack) SetReceiver(r TrackReceiver) {
	// TODO-zjx 该实现是DownTrack.SetReceiver的简化版，可能存在隐患

	d.receiverLock.Lock()
	old := d.receiver
	d.receiver = r
	d.receiverLock.Unlock()

	old.DeleteDownTrack(d.SubscriberID())
	if err := r.AddDownTrack(d); err != nil {
		d.params.Logger.Warnw("failed to add downtrack to receiver", err)
	}
}

func (d *RemoteDownTrack) send(msg proto.Message) error {
	if d.IsClosed() {
		return ErrRemoteDownTrackClosed
	}

	m := msg.(*rpc.RelayMediaResponse)
	m.ConnectionId = string(d.connectionID)
	err := d.sink.WriteMessage(m)
	if err != nil {
		t := reflect.TypeOf(m.Message).String()
		d.params.Logger.Debugw("媒体中继发送失败", "type", t)
		//d.params.Logger.Errorw("媒体中继发送失败", err, "type", t)
		// 发送失败次数 > 10 就直接关闭，避免因为没收到关闭指令导致连接无法释放
		if d.sendErrCount.Inc() > 10 {
			d.Close()
		}
	}
	return err
}
