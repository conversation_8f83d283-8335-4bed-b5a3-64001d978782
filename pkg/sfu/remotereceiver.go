package sfu

import (
	"context"
	"encoding/json"
	dd "github.com/livekit/livekit-server/pkg/sfu/rtpextension/dependencydescriptor"
	"github.com/livekit/livekit-server/pkg/sfu/streamtracker"
	"io"
	"sync"

	"github.com/livekit/livekit-server/pkg/sfu/buffer"
	"github.com/livekit/livekit-server/pkg/sfu/mime"
	"github.com/livekit/mediatransportutil/pkg/bucket"
	"github.com/livekit/protocol/livekit"
	"github.com/livekit/protocol/logger"
	"github.com/livekit/protocol/rpc"
	"github.com/livekit/protocol/utils"
	"github.com/pion/webrtc/v4"
	"github.com/pkg/errors"
	"go.uber.org/atomic"
)

var (
	ErrBucketNotFound = errors.New("bucket not found")
)

type RemoteReceiverParams struct {
	Logger                     logger.Logger
	NodeID                     livekit.NodeID
	RoomName                   livekit.RoomName
	ParticipantID              func() livekit.ParticipantID
	ParticipantIdentity        livekit.ParticipantIdentity
	Ctx                        context.Context
	PacketBufferSizeVideo      int
	PacketBufferSizeAudio      int
	StreamTrackerManagerConfig StreamTrackerManagerConfig
}

type RemoteReceiver struct {
	params RemoteReceiverParams

	client    rpc.TypedMediaRelayClient
	source    MessageSource
	trackInfo atomic.Pointer[livekit.TrackInfo]

	lbThreshold          int
	streamTrackerManager *StreamTrackerManager
	spatialTrackers      [buffer.DefaultMaxLayerSpatial + 1]streamtracker.StreamTrackerWorker
	downTrackSpreader    *DownTrackSpreader

	bufferMu       sync.RWMutex
	buckets        [buffer.DefaultMaxLayerSpatial + 1]*bucket.Bucket[uint64]
	isSVC          bool
	isRED          bool
	redTransformer atomic.Value // redTransformer interface

	onMaxLayerChange func(maxLayer int32)

	onCloseHandler func()
	mu             sync.Mutex
	closeOnce      sync.Once
	closed         atomic.Bool
}

func NewRemoteReceiver(
	params RemoteReceiverParams,
	client rpc.TypedMediaRelayClient,
	source MessageSource,
	trackInfo *livekit.TrackInfo,
) *RemoteReceiver {
	r := &RemoteReceiver{
		params:      params,
		client:      client,
		source:      source,
		lbThreshold: 20,
		isSVC:       mime.IsMimeTypeStringSVC(trackInfo.MimeType),
		isRED:       mime.IsMimeTypeStringRED(trackInfo.MimeType),
	}
	r.trackInfo.Store(utils.CloneProto(trackInfo))
	r.downTrackSpreader = NewDownTrackSpreader(DownTrackSpreaderParams{
		Threshold: r.lbThreshold,
		Logger:    params.Logger,
	})

	r.streamTrackerManager = NewStreamTrackerManager(params.Logger, trackInfo, r.isSVC, r.Codec().ClockRate, params.StreamTrackerManagerConfig)
	r.streamTrackerManager.SetListener(r)
	// SVC-TODO: Handle DD for non-SVC cases???
	if r.isSVC {
		headerExtensions := r.HeaderExtensions()
		for _, ext := range headerExtensions {
			if ext.URI == dd.ExtensionURI {
				r.streamTrackerManager.AddDependencyDescriptorTrackers()
				break
			}
		}
	}

	if mime.IsMimeTypeStringVideo(trackInfo.MimeType) && !r.isSVC {
		for _, layer := range trackInfo.Layers {
			spatialLayer := layer.SpatialLayer
			r.spatialTrackers[spatialLayer] = r.streamTrackerManager.AddTracker(spatialLayer)
		}
	}

	// 启动媒体中继
	go r.relayRTP()
	return r
}

func (r *RemoteReceiver) TrackID() livekit.TrackID {
	return livekit.TrackID(r.TrackInfo().Sid)
}

func (r *RemoteReceiver) StreamID() string {
	return r.TrackInfo().Stream
}

func (r *RemoteReceiver) GetRelayTrackReceiverBaseRequest() *rpc.RelayTrackReceiverRequest {
	return &rpc.RelayTrackReceiverRequest{
		NodeId:              string(r.params.NodeID),
		RoomName:            string(r.params.RoomName),
		ParticipantIdentity: string(r.params.ParticipantIdentity),
		TrackId:             string(r.TrackID()),
		Request:             &rpc.TrackReceiverRequest{},
	}
}

func (r *RemoteReceiver) Codec() webrtc.RTPCodecParameters {
	request := r.GetRelayTrackReceiverBaseRequest()
	request.Request.Message = &rpc.TrackReceiverRequest_CodecRequest{
		CodecRequest: &rpc.Codec{},
	}
	response, err := r.client.RelayTrackReceiver(r.params.Ctx, r.params.NodeID, request)
	if err != nil {
		r.params.Logger.Errorw("轨道中继RPC发送 Codec 失败", err)
	}

	codec := webrtc.RTPCodecParameters{}
	_ = json.Unmarshal(response.GetCodecResponse().Bytes, &codec)
	r.params.Logger.Debugw("轨道中继RPC发送 Codec 响应", "data", codec)
	return codec
}

func (r *RemoteReceiver) Mime() mime.MimeType {
	return mime.NormalizeMimeType(r.TrackInfo().MimeType)
}

func (r *RemoteReceiver) HeaderExtensions() []webrtc.RTPHeaderExtensionParameter {
	request := r.GetRelayTrackReceiverBaseRequest()
	request.Request.Message = &rpc.TrackReceiverRequest_HeaderExtensionsRequest{
		HeaderExtensionsRequest: &rpc.HeaderExtensions{},
	}
	response, err := r.client.RelayTrackReceiver(r.params.Ctx, r.params.NodeID, request)
	if err != nil {
		r.params.Logger.Errorw("轨道中继RPC发送 HeaderExtensions 失败", err)
	}

	var headerExtensionParameter []webrtc.RTPHeaderExtensionParameter
	_ = json.Unmarshal(response.GetHeaderExtensionsResponse().Bytes, &headerExtensionParameter)
	r.params.Logger.Debugw("轨道中继RPC发送 HeaderExtensions 响应", "data", headerExtensionParameter)
	return headerExtensionParameter
}

func (r *RemoteReceiver) IsClosed() bool {
	return r.closed.Load()
}

func (r *RemoteReceiver) ReadRTP(buf []byte, layer uint8, esn uint64) (int, error) {
	b := r.getBucket(int32(layer))
	if b == nil {
		return 0, ErrBucketNotFound
	}

	if r.closed.Load() {
		return 0, io.EOF
	}
	return b.GetPacket(buf, esn)
}

var getLayeredBitrateCount = 0

func (r *RemoteReceiver) GetLayeredBitrate() ([]int32, Bitrates) {
	return r.streamTrackerManager.GetLayeredBitrate()
}

func (r *RemoteReceiver) GetAudioLevel() (float64, bool) {
	request := r.GetRelayTrackReceiverBaseRequest()
	request.Request.Message = &rpc.TrackReceiverRequest_GetAudioLevelRequest{
		GetAudioLevelRequest: &rpc.GetAudioLevel{},
	}
	response, err := r.client.RelayTrackReceiver(r.params.Ctx, r.params.NodeID, request)
	if err != nil {
		r.params.Logger.Errorw("轨道中继RPC发送 GetAudioLevel 失败", err)
	}

	getAudioLevelResponse := response.GetGetAudioLevelResponse()
	level := getAudioLevelResponse.Level
	active := getAudioLevelResponse.Active
	r.params.Logger.Debugw("轨道中继RPC发送 GetAudioLevel 响应", "data.level", level, "data.active", active)
	return level, active
}

func (r *RemoteReceiver) SendPLI(layer int32, force bool) {
	request := r.GetRelayTrackReceiverBaseRequest()
	request.Request.Message = &rpc.TrackReceiverRequest_SendPliRequest{
		SendPliRequest: &rpc.SendPli{
			Layer: layer,
			Force: force,
		},
	}
	_, err := r.client.RelayTrackReceiver(r.params.Ctx, r.params.NodeID, request)
	if err != nil {
		r.params.Logger.Errorw("轨道中继RPC发送 SendPli 失败", err)
	}
	r.params.Logger.Debugw("轨道中继RPC发送 SendPli 响应")
}

func (r *RemoteReceiver) SetUpTrackPaused(paused bool) {
	r.streamTrackerManager.SetPaused(paused)

	request := r.GetRelayTrackReceiverBaseRequest()
	request.Request.Message = &rpc.TrackReceiverRequest_SetUpTrackPausedRequest{
		SetUpTrackPausedRequest: &rpc.SetUpTrackPaused{
			Paused: paused,
		},
	}
	_, err := r.client.RelayTrackReceiver(r.params.Ctx, r.params.NodeID, request)
	if err != nil {
		r.params.Logger.Errorw("轨道中继RPC发送 SetUpTrackPaused 失败", err)
	}
	r.params.Logger.Debugw("轨道中继RPC发送 SetUpTrackPaused 响应")
}

func (r *RemoteReceiver) SetMaxExpectedSpatialLayer(layer int32) {
	r.streamTrackerManager.SetMaxExpectedSpatialLayer(layer)

	request := r.GetRelayTrackReceiverBaseRequest()
	request.Request.Message = &rpc.TrackReceiverRequest_SetMaxExpectedSpatialLayerRequest{
		SetMaxExpectedSpatialLayerRequest: &rpc.SetMaxExpectedSpatialLayer{
			Layer: layer,
		},
	}
	_, err := r.client.RelayTrackReceiver(r.params.Ctx, r.params.NodeID, request)
	if err != nil {
		r.params.Logger.Errorw("轨道中继RPC发送 SetMaxExpectedSpatialLayer 失败", err)
	}
	r.params.Logger.Debugw("轨道中继RPC发送 SetMaxExpectedSpatialLayer 响应")
}

func (r *RemoteReceiver) close() {
	r.closeOnce.Do(func() {
		r.params.Logger.Debugw("媒体中继流关闭")
		r.closed.Store(true)
		r.closeTracks()
		if rt := r.redTransformer.Load(); rt != nil {
			rt.(REDTransformer).Close()
		}
	})

	for _, layer := range r.TrackInfo().Layers {
		r.streamTrackerManager.RemoveTracker(layer.SpatialLayer)
	}
	if r.isSVC {
		r.streamTrackerManager.RemoveAllTrackers()
	}
}

func (r *RemoteReceiver) getBucket(layer int32) *bucket.Bucket[uint64] {
	r.bufferMu.RLock()
	defer r.bufferMu.RUnlock()

	return r.buckets[layer]
}

func (r *RemoteReceiver) getOrNewBucket(layer int32) *bucket.Bucket[uint64] {
	r.bufferMu.RLock()
	b := r.buckets[layer]
	r.bufferMu.RUnlock()
	if b != nil {
		return b
	}

	r.bufferMu.Lock()
	defer r.bufferMu.Unlock()
	if b := r.buckets[layer]; b != nil {
		return b
	}

	bucketCapacity := 200
	if mime.IsMimeTypeAudio(r.Mime()) {
		bucketCapacity = r.params.PacketBufferSizeAudio
	} else if mime.IsMimeTypeVideo(r.Mime()) {
		bucketCapacity = r.params.PacketBufferSizeVideo
	}

	b = bucket.NewBucket[uint64](bucketCapacity)
	r.buckets[layer] = b
	return b
}

func (r *RemoteReceiver) unpackExtPacket(pr *rpc.PacketResponse) (*buffer.ExtPacket, int32) {
	layer := pr.Layer
	ep := &buffer.ExtPacket{}
	if err := ep.FromProto(pr.GetExtPacket()); err != nil {
		r.params.Logger.Errorw("媒体中继RTP报文反序列化失败", err)
		return nil, layer
	}

	// 缓存 RTP 包用于重传
	b := r.getOrNewBucket(layer)
	_, err := b.AddPacketWithSequenceNumber(ep.RawPacket, ep.ExtSequenceNumber)
	if err != nil {
		r.params.Logger.Errorw("媒体中继RTP报文缓存失败", err)
	}

	return ep, layer
}

// 接收远程发布者的 RTP 数据包和 UpTrack 事件，转发给本地 DownTrack
func (r *RemoteReceiver) relayRTP() {
	defer func() {
		r.close()
	}()

	for read := range r.source.ReadChan() {
		response := read.(*rpc.RelayMediaResponse)
		switch m := response.Message.Message.(type) {

		// 广播 RTP
		case *rpc.MediaResponse_PacketResponse:
			packetResponse := m.PacketResponse
			pkt, spatialLayer := r.unpackExtPacket(packetResponse)
			if pkt == nil {
				continue
			}

			// DEBUG-zjx RTP接收
			//jsonBytes, _ := protojson.Marshal(packetResponse.GetExtPacket())
			//logger.Debugw("测试UDP日志-接收 " + string(jsonBytes))

			r.downTrackSpreader.Broadcast(func(dt TrackSender) {
				_ = dt.WriteRTP(pkt, spatialLayer)
			})

			if rt := r.redTransformer.Load(); rt != nil {
				_ = rt.(REDTransformer).ForwardRTP(pkt, spatialLayer)
			}

			// track video layers

			if mime.IsMimeTypeStringVideo(r.TrackInfo().MimeType) {
				if r.spatialTrackers[spatialLayer] == nil {
					r.spatialTrackers[spatialLayer] = r.streamTrackerManager.GetTracker(spatialLayer)
					if r.spatialTrackers[spatialLayer] == nil {
						r.spatialTrackers[spatialLayer] = r.streamTrackerManager.AddTracker(spatialLayer)
					}
				}
				if r.spatialTrackers[spatialLayer] != nil {
					r.spatialTrackers[spatialLayer].Observe(
						pkt.Temporal,
						len(pkt.RawPacket),
						len(pkt.Packet.Payload),
						pkt.Packet.Marker,
						pkt.Packet.Timestamp,
						pkt.DependencyDescriptor,
					)
				}
			}
		// 发布者关闭媒体中继
		case *rpc.MediaResponse_Closed:
			return
		// 广播 UpTrack 事件
		case *rpc.MediaResponse_HandleRtcpSenderReportData:
			handleRtcpSenderReportData := m.HandleRtcpSenderReportData
			publisherSRData := &livekit.RTCPSenderReportState{}
			_ = json.Unmarshal(handleRtcpSenderReportData.PublisherSRData, publisherSRData)
			r.downTrackSpreader.Broadcast(func(dt TrackSender) {
				_ = dt.HandleRTCPSenderReportData(
					webrtc.PayloadType(handleRtcpSenderReportData.PayloadType),
					handleRtcpSenderReportData.IsSvc,
					handleRtcpSenderReportData.Layer,
					publisherSRData,
				)
			})
		case *rpc.MediaResponse_Resync:
			r.downTrackSpreader.Broadcast(func(dt TrackSender) {
				dt.Resync()
			})
		}
	}
}

func (r *RemoteReceiver) AddDownTrack(track TrackSender) error {
	if r.closed.Load() {
		return ErrReceiverClosed
	}

	if r.downTrackSpreader.HasDownTrack(track.SubscriberID()) {
		r.params.Logger.Infow("订阅者ID已存在，替换下行轨道", "subscriberID", track.SubscriberID())
	}

	track.UpTrackMaxPublishedLayerChange(r.streamTrackerManager.GetMaxPublishedLayer())
	track.UpTrackMaxTemporalLayerSeenChange(r.streamTrackerManager.GetMaxTemporalLayerSeen())

	r.mu.Lock()
	r.downTrackSpreader.Store(track)
	r.mu.Unlock()

	r.params.Logger.Debugw("下行轨道已添加", "subscriberID", track.SubscriberID())
	return nil
}

func (r *RemoteReceiver) DeleteDownTrack(subscriberID livekit.ParticipantID) {
	if r.closed.Load() {
		return
	}

	r.downTrackSpreader.Free(subscriberID)
	r.params.Logger.Debugw("删除下行轨道", "subscriberID", subscriberID)

	// 下行轨道数为0，则释放连接
	r.mu.Lock()
	defer r.mu.Unlock()
	if r.downTrackSpreader.DownTrackCount() == 0 {
		r.close()
	}
}

func (r *RemoteReceiver) GetDownTracks() []TrackSender {
	return r.downTrackSpreader.GetDownTracks()
}

func (r *RemoteReceiver) DebugInfo() map[string]interface{} {
	return map[string]interface{}{
		"note": "远程接收器不存储上行轨道信息",
	}
}

func (r *RemoteReceiver) TrackInfo() *livekit.TrackInfo {
	return r.trackInfo.Load()
}

func (r *RemoteReceiver) UpdateTrackInfo(ti *livekit.TrackInfo) {
	r.trackInfo.Store(utils.CloneProto(ti))
	r.streamTrackerManager.UpdateTrackInfo(ti)
}

func (r *RemoteReceiver) GetPrimaryReceiverForRed() TrackReceiver {
	r.bufferMu.Lock()
	defer r.bufferMu.Unlock()

	if !r.isRED || r.closed.Load() {
		return r
	}

	rt := r.redTransformer.Load()
	if rt == nil {
		pr := NewRedPrimaryReceiver(r, DownTrackSpreaderParams{
			Threshold: r.lbThreshold,
			Logger:    r.params.Logger,
		})
		r.redTransformer.Store(pr)
		return pr
	} else {
		if pr, ok := rt.(*RedPrimaryReceiver); ok {
			return pr
		}
	}
	return nil
}

func (r *RemoteReceiver) GetRedReceiver() TrackReceiver {
	r.bufferMu.Lock()
	defer r.bufferMu.Unlock()

	if r.isRED || r.closed.Load() {
		return r
	}

	rt := r.redTransformer.Load()
	if rt == nil {
		pr := NewRedReceiver(r, DownTrackSpreaderParams{
			Threshold: r.lbThreshold,
			Logger:    r.params.Logger,
		})
		r.redTransformer.Store(pr)
		return pr
	} else {
		if pr, ok := rt.(*RedReceiver); ok {
			return pr
		}
	}
	return nil
}

func (r *RemoteReceiver) GetTemporalLayerFpsForSpatial(layer int32) []float32 {
	request := r.GetRelayTrackReceiverBaseRequest()
	request.Request.Message = &rpc.TrackReceiverRequest_GetTemporalLayerFpsForSpatialRequest{
		GetTemporalLayerFpsForSpatialRequest: &rpc.GetTemporalLayerFpsForSpatial{
			Spatial: layer,
		},
	}
	response, err := r.client.RelayTrackReceiver(r.params.Ctx, r.params.NodeID, request)
	if err != nil {
		r.params.Logger.Errorw("轨道中继RPC发送 GetTemporalLayerFpsForSpatial 失败", err)
	}

	fps := response.GetGetTemporalLayerFpsForSpatialResponse().Fps
	r.params.Logger.Debugw("轨道中继RPC发送 GetTemporalLayerFpsForSpatial 响应", "data", fps)
	return fps
}

func (r *RemoteReceiver) GetTrackStats() *livekit.RTPStats {
	// 跟踪调用链发现这个方法应该是不会被调用到的
	err := errors.New("意料之外的方法调用，可能存在逻辑不严谨的隐患，请检查代码")
	r.params.Logger.Errorw("远程接收器调用 GetTrackStats 方法", err)
	return nil
}

func (r *RemoteReceiver) AddOnReady(f func()) {
	f()
}

func (r *RemoteReceiver) AddOnCodecStateChange(_ func(webrtc.RTPCodecParameters, ReceiverCodecState)) {
	// 跟踪调用链发现这个方法应该是不会被调用到的（仅针对远程接收器而言）
	err := errors.New("意料之外的方法调用，可能存在逻辑不严谨的隐患，请检查代码")
	r.params.Logger.Errorw("调用远程接收器 AddOnCodecStateChange 方法", err)
}

func (r *RemoteReceiver) CodecState() ReceiverCodecState {
	// 跟踪调用链发现这个方法应该是不会被调用到的（仅针对远程接收器而言）
	err := errors.New("意料之外的方法调用，可能存在逻辑不严谨的隐患，请检查代码")
	r.params.Logger.Errorw("调用远程接收器 CodecState 方法", err)
	return ReceiverCodecStateNormal
}

// OnCloseHandler method to be called on remote tracked removed
func (r *RemoteReceiver) OnCloseHandler(fn func()) {
	r.onCloseHandler = fn
}

// closeTracks close all tracks from Receiver
func (r *RemoteReceiver) closeTracks() {
	r.streamTrackerManager.Close()

	wg := sync.WaitGroup{}
	for _, dt := range r.GetDownTracks() {
		dt := dt
		wg.Add(1)
		go func() {
			defer wg.Done()
			dt.Close()
		}()
	}
	wg.Wait()

	if r.onCloseHandler != nil {
		r.onCloseHandler()
	}
}

func (r *RemoteReceiver) OnAvailableLayersChanged() {
	r.downTrackSpreader.Broadcast(func(dt TrackSender) {
		dt.UpTrackLayersChange()
	})
}

func (r *RemoteReceiver) OnBitrateAvailabilityChanged() {
	r.downTrackSpreader.Broadcast(func(dt TrackSender) {
		dt.UpTrackBitrateAvailabilityChange()
	})
}

func (r *RemoteReceiver) OnMaxPublishedLayerChanged(maxPublishedLayer int32) {
	r.downTrackSpreader.Broadcast(func(dt TrackSender) {
		dt.UpTrackMaxPublishedLayerChange(maxPublishedLayer)
	})
}

func (r *RemoteReceiver) OnMaxTemporalLayerSeenChanged(maxTemporalLayerSeen int32) {
	r.downTrackSpreader.Broadcast(func(dt TrackSender) {
		dt.UpTrackMaxTemporalLayerSeenChange(maxTemporalLayerSeen)
	})
}

func (r *RemoteReceiver) OnMaxAvailableLayerChanged(maxAvailableLayer int32) {
	if onMaxLayerChange := r.getOnMaxLayerChange(); onMaxLayerChange != nil {
		onMaxLayerChange(maxAvailableLayer)
	}
}

func (r *RemoteReceiver) OnBitrateReport(availableLayers []int32, bitrates Bitrates) {
	r.downTrackSpreader.Broadcast(func(dt TrackSender) {
		dt.UpTrackBitrateReport(availableLayers, bitrates)
	})
}

func (r *RemoteReceiver) OnMaxLayerChange(fn func(maxLayer int32)) {
	r.bufferMu.Lock()
	r.onMaxLayerChange = fn
	r.bufferMu.Unlock()
}

func (r *RemoteReceiver) getOnMaxLayerChange() func(maxLayer int32) {
	r.bufferMu.RLock()
	defer r.bufferMu.RUnlock()
	return r.onMaxLayerChange
}
