// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package dependencydescriptor

import (
	"fmt"
	"github.com/livekit/protocol/livekit"
	"math"
	"strconv"
)

// DependencyDescriptorExtension is a extension payload format in
// https://aomediacodec.github.io/av1-rtp-spec/#dependency-descriptor-rtp-header-extension

func formatBitmask(b *uint32) string {
	if b == nil {
		return "-"
	}
	return strconv.FormatInt(int64(*b), 2)
}

// ------------------------------------------------------------------------------

type DependencyDescriptorExtension struct {
	Descriptor *DependencyDescriptor
	Structure  *FrameDependencyStructure
}

func (d *DependencyDescriptorExtension) Marshal() ([]byte, error) {
	return d.MarshalWithActiveChains(^uint32(0))
}

func (d *DependencyDescriptorExtension) MarshalWithActiveChains(activeChains uint32) ([]byte, error) {
	writer, err := NewDependencyDescriptorWriter(nil, d.Structure, activeChains, d.Descriptor)
	if err != nil {
		return nil, err
	}
	buf := make([]byte, int(math.Ceil(float64(writer.ValueSizeBits())/8)))
	writer.ResetBuf(buf)
	if err = writer.Write(); err != nil {
		return nil, err
	}
	return buf, nil
}

func (d *DependencyDescriptorExtension) Unmarshal(buf []byte) (int, error) {
	reader := NewDependencyDescriptorReader(buf, d.Structure, d.Descriptor)
	return reader.Parse()
}

// ------------------------------------------------------------------------------

const (
	MaxSpatialIds    = 4
	MaxTemporalIds   = 8
	MaxDecodeTargets = 32
	MaxTemplates     = 64

	AllChainsAreActive = uint32(0)

	ExtensionURI = "https://aomediacodec.github.io/av1-rtp-spec/#dependency-descriptor-rtp-header-extension"
)

// ------------------------------------------------------------------------------

type DependencyDescriptor struct {
	FirstPacketInFrame         bool
	LastPacketInFrame          bool
	FrameNumber                uint16
	FrameDependencies          *FrameDependencyTemplate
	Resolution                 *RenderResolution
	ActiveDecodeTargetsBitmask *uint32
	AttachedStructure          *FrameDependencyStructure
}

func (d *DependencyDescriptor) MarshalSize() (int, error) {
	return d.MarshalSizeWithActiveChains(^uint32(0))
}

func (d *DependencyDescriptor) MarshalSizeWithActiveChains(activeChains uint32) (int, error) {
	writer, err := NewDependencyDescriptorWriter(nil, d.AttachedStructure, activeChains, d)
	if err != nil {
		return 0, err
	}
	return int(math.Ceil(float64(writer.ValueSizeBits()) / 8)), nil
}

func (d *DependencyDescriptor) String() string {
	resolution, dependencies := "-", "-"
	if d.Resolution != nil {
		resolution = fmt.Sprintf("%+v", *d.Resolution)
	}
	if d.FrameDependencies != nil {
		dependencies = fmt.Sprintf("%+v", *d.FrameDependencies)
	}
	return fmt.Sprintf("DependencyDescriptor{FirstPacketInFrame: %v, LastPacketInFrame: %v, FrameNumber: %v, FrameDependencies: %s, Resolution: %s, ActiveDecodeTargetsBitmask: %v, AttachedStructure: %v}",
		d.FirstPacketInFrame, d.LastPacketInFrame, d.FrameNumber, dependencies, resolution, formatBitmask(d.ActiveDecodeTargetsBitmask), d.AttachedStructure)
}

func (d *DependencyDescriptor) ToProto() *livekit.DependencyDescriptor {
	proto := &livekit.DependencyDescriptor{
		FirstPacketInFrame: d.FirstPacketInFrame,
		LastPacketInFrame:  d.LastPacketInFrame,
		FrameNumber:        uint32(d.FrameNumber),
	}

	if d.FrameDependencies != nil {
		proto.FrameDependencies = d.FrameDependencies.ToProto()
	}

	if d.Resolution != nil {
		proto.Resolution = d.Resolution.ToProto()
	}

	if d.ActiveDecodeTargetsBitmask != nil {
		bitmask := *d.ActiveDecodeTargetsBitmask
		proto.ActiveDecodeTargetsBitmask = &bitmask
	}

	if d.AttachedStructure != nil {
		proto.AttachedStructure = d.AttachedStructure.ToProto()
	}

	return proto
}

func (d *DependencyDescriptor) FromProto(proto *livekit.DependencyDescriptor) {
	d.FirstPacketInFrame = proto.FirstPacketInFrame
	d.LastPacketInFrame = proto.LastPacketInFrame
	d.FrameNumber = uint16(proto.FrameNumber)

	if proto.FrameDependencies != nil {
		fdt := &FrameDependencyTemplate{}
		fdt.FromProto(proto.FrameDependencies)
		d.FrameDependencies = fdt
	}

	if proto.Resolution != nil {
		rr := &RenderResolution{}
		rr.FromProto(proto.Resolution)
		d.Resolution = rr
	}

	if proto.ActiveDecodeTargetsBitmask != nil {
		bitmask := *proto.ActiveDecodeTargetsBitmask
		d.ActiveDecodeTargetsBitmask = &bitmask
	}

	if proto.AttachedStructure != nil {
		fds := &FrameDependencyStructure{}
		fds.FromProto(proto.AttachedStructure)
		d.AttachedStructure = fds
	}
}

// ------------------------------------------------------------------------------

// Relationship of a frame to a Decode target.
type DecodeTargetIndication int

const (
	DecodeTargetNotPresent  DecodeTargetIndication = iota // DecodeTargetInfo symbol '-'
	DecodeTargetDiscardable                               // DecodeTargetInfo symbol 'D'
	DecodeTargetSwitch                                    // DecodeTargetInfo symbol 'S'
	DecodeTargetRequired                                  // DecodeTargetInfo symbol 'R'
)

func (i DecodeTargetIndication) String() string {
	switch i {
	case DecodeTargetNotPresent:
		return "-"
	case DecodeTargetDiscardable:
		return "D"
	case DecodeTargetSwitch:
		return "S"
	case DecodeTargetRequired:
		return "R"
	default:
		return "Unknown"
	}
}

// ------------------------------------------------------------------------------

type FrameDependencyTemplate struct {
	SpatialId               int
	TemporalId              int
	DecodeTargetIndications []DecodeTargetIndication
	FrameDiffs              []int
	ChainDiffs              []int
}

func (t *FrameDependencyTemplate) Clone() *FrameDependencyTemplate {
	t2 := &FrameDependencyTemplate{
		SpatialId:  t.SpatialId,
		TemporalId: t.TemporalId,
	}

	t2.DecodeTargetIndications = make([]DecodeTargetIndication, len(t.DecodeTargetIndications))
	copy(t2.DecodeTargetIndications, t.DecodeTargetIndications)

	t2.FrameDiffs = make([]int, len(t.FrameDiffs))
	copy(t2.FrameDiffs, t.FrameDiffs)

	t2.ChainDiffs = make([]int, len(t.ChainDiffs))
	copy(t2.ChainDiffs, t.ChainDiffs)

	return t2
}

func (t *FrameDependencyTemplate) ToProto() *livekit.FrameDependencyTemplate {
	proto := &livekit.FrameDependencyTemplate{
		SpatialId:  int32(t.SpatialId),
		TemporalId: int32(t.TemporalId),
	}

	proto.DecodeTargetIndications = make([]int32, 0, len(t.DecodeTargetIndications))
	for _, i := range t.DecodeTargetIndications {
		proto.DecodeTargetIndications = append(proto.DecodeTargetIndications, int32(i))
	}

	proto.FrameDiffs = make([]int32, 0, len(t.FrameDiffs))
	for _, i := range t.FrameDiffs {
		proto.FrameDiffs = append(proto.FrameDiffs, int32(i))
	}

	proto.ChainDiffs = make([]int32, 0, len(t.ChainDiffs))
	for _, i := range t.ChainDiffs {
		proto.ChainDiffs = append(proto.ChainDiffs, int32(i))
	}

	return proto
}

func (t *FrameDependencyTemplate) FromProto(proto *livekit.FrameDependencyTemplate) {
	t.SpatialId = int(proto.SpatialId)
	t.TemporalId = int(proto.TemporalId)

	t.DecodeTargetIndications = make([]DecodeTargetIndication, 0, len(proto.DecodeTargetIndications))
	for _, i := range proto.DecodeTargetIndications {
		t.DecodeTargetIndications = append(t.DecodeTargetIndications, DecodeTargetIndication(i))
	}

	t.FrameDiffs = make([]int, 0, len(proto.FrameDiffs))
	for _, i := range proto.FrameDiffs {
		t.FrameDiffs = append(t.FrameDiffs, int(i))
	}

	t.ChainDiffs = make([]int, 0, len(proto.ChainDiffs))
	for _, i := range proto.ChainDiffs {
		t.ChainDiffs = append(t.ChainDiffs, int(i))
	}

	t.DecodeTargetIndications = make([]DecodeTargetIndication, 0, len(proto.DecodeTargetIndications))
	for _, i := range proto.DecodeTargetIndications {
		t.DecodeTargetIndications = append(t.DecodeTargetIndications, DecodeTargetIndication(i))
	}
}

// ------------------------------------------------------------------------------

type FrameDependencyStructure struct {
	StructureId      int
	NumDecodeTargets int
	NumChains        int
	// If chains are used (num_chains > 0), maps decode target index into index of
	// the chain protecting that target.
	DecodeTargetProtectedByChain []int
	Resolutions                  []RenderResolution
	Templates                    []*FrameDependencyTemplate
}

func (f *FrameDependencyStructure) String() string {
	str := fmt.Sprintf("FrameDependencyStructure{StructureId: %v, NumDecodeTargets: %v, NumChains: %v, DecodeTargetProtectedByChain: %v, Resolutions: %+v, Templates: [",
		f.StructureId, f.NumDecodeTargets, f.NumChains, f.DecodeTargetProtectedByChain, f.Resolutions)

	// templates
	for _, t := range f.Templates {
		str += fmt.Sprintf("%+v, ", t)
	}
	str += "]}"

	return str
}

func (f *FrameDependencyStructure) ToProto() *livekit.FrameDependencyStructure {
	proto := &livekit.FrameDependencyStructure{
		StructureId:      int32(f.StructureId),
		NumDecodeTargets: int32(f.NumDecodeTargets),
		NumChains:        int32(f.NumChains),
	}

	proto.DecodeTargetProtectedByChain = make([]int32, 0, len(f.DecodeTargetProtectedByChain))
	for _, i := range f.DecodeTargetProtectedByChain {
		proto.DecodeTargetProtectedByChain = append(proto.DecodeTargetProtectedByChain, int32(i))
	}

	proto.Resolutions = make([]*livekit.RenderResolution, 0, len(f.Resolutions))
	for _, i := range f.Resolutions {
		proto.Resolutions = append(proto.Resolutions, i.ToProto())
	}

	proto.Templates = make([]*livekit.FrameDependencyTemplate, 0, len(f.Templates))
	for _, i := range f.Templates {
		proto.Templates = append(proto.Templates, i.ToProto())
	}

	return proto
}

func (f *FrameDependencyStructure) FromProto(proto *livekit.FrameDependencyStructure) {
	f.StructureId = int(proto.StructureId)
	f.NumDecodeTargets = int(proto.NumDecodeTargets)
	f.NumChains = int(proto.NumChains)

	f.DecodeTargetProtectedByChain = make([]int, 0, len(proto.DecodeTargetProtectedByChain))
	for _, i := range proto.DecodeTargetProtectedByChain {
		f.DecodeTargetProtectedByChain = append(f.DecodeTargetProtectedByChain, int(i))
	}

	f.Resolutions = make([]RenderResolution, 0, len(proto.Resolutions))
	for _, i := range proto.Resolutions {
		rr := RenderResolution{}
		rr.FromProto(i)
		f.Resolutions = append(f.Resolutions, rr)
	}

	f.Templates = make([]*FrameDependencyTemplate, 0, len(proto.Templates))
	for _, i := range proto.Templates {
		fdt := &FrameDependencyTemplate{}
		fdt.FromProto(i)
		f.Templates = append(f.Templates, fdt)
	}
}

// ------------------------------------------------------------------------------

type RenderResolution struct {
	Width  int
	Height int
}

func (r *RenderResolution) ToProto() *livekit.RenderResolution {
	return &livekit.RenderResolution{
		Width:  int32(r.Width),
		Height: int32(r.Height),
	}
}

func (r *RenderResolution) FromProto(proto *livekit.RenderResolution) {
	r.Width = int(proto.Width)
	r.Height = int(proto.Height)
}

// ------------------------------------------------------------------------------
