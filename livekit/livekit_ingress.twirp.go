// Code generated by protoc-gen-twirp v8.1.3, DO NOT EDIT.
// source: livekit_ingress.proto

package livekit

import context "context"
import fmt "fmt"
import http "net/http"
import io "io"
import json "encoding/json"
import strconv "strconv"
import strings "strings"

import protojson "google.golang.org/protobuf/encoding/protojson"
import proto "google.golang.org/protobuf/proto"
import twirp "github.com/twitchtv/twirp"
import ctxsetters "github.com/twitchtv/twirp/ctxsetters"

// Version compatibility assertion.
// If the constant is not defined in the package, that likely means
// the package needs to be updated to work with this generated code.
// See https://twitchtv.github.io/twirp/docs/version_matrix.html
const _ = twirp.TwirpPackageMinVersion_8_1_0

// =================
// Ingress Interface
// =================

type Ingress interface {
	// Create a new Ingress
	CreateIngress(context.Context, *CreateIngressRequest) (*IngressInfo, error)

	// Update an existing Ingress. Ingress can only be updated when it's in ENDPOINT_WAITING state.
	UpdateIngress(context.Context, *UpdateIngressRequest) (*IngressInfo, error)

	ListIngress(context.Context, *ListIngressRequest) (*ListIngressResponse, error)

	DeleteIngress(context.Context, *DeleteIngressRequest) (*IngressInfo, error)
}

// =======================
// Ingress Protobuf Client
// =======================

type ingressProtobufClient struct {
	client      HTTPClient
	urls        [4]string
	interceptor twirp.Interceptor
	opts        twirp.ClientOptions
}

// NewIngressProtobufClient creates a Protobuf client that implements the Ingress interface.
// It communicates using Protobuf and can be configured with a custom HTTPClient.
func NewIngressProtobufClient(baseURL string, client HTTPClient, opts ...twirp.ClientOption) Ingress {
	if c, ok := client.(*http.Client); ok {
		client = withoutRedirects(c)
	}

	clientOpts := twirp.ClientOptions{}
	for _, o := range opts {
		o(&clientOpts)
	}

	// Using ReadOpt allows backwards and forwards compatibility with new options in the future
	literalURLs := false
	_ = clientOpts.ReadOpt("literalURLs", &literalURLs)
	var pathPrefix string
	if ok := clientOpts.ReadOpt("pathPrefix", &pathPrefix); !ok {
		pathPrefix = "/twirp" // default prefix
	}

	// Build method URLs: <baseURL>[<prefix>]/<package>.<Service>/<Method>
	serviceURL := sanitizeBaseURL(baseURL)
	serviceURL += baseServicePath(pathPrefix, "livekit", "Ingress")
	urls := [4]string{
		serviceURL + "CreateIngress",
		serviceURL + "UpdateIngress",
		serviceURL + "ListIngress",
		serviceURL + "DeleteIngress",
	}

	return &ingressProtobufClient{
		client:      client,
		urls:        urls,
		interceptor: twirp.ChainInterceptors(clientOpts.Interceptors...),
		opts:        clientOpts,
	}
}

func (c *ingressProtobufClient) CreateIngress(ctx context.Context, in *CreateIngressRequest) (*IngressInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Ingress")
	ctx = ctxsetters.WithMethodName(ctx, "CreateIngress")
	caller := c.callCreateIngress
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *CreateIngressRequest) (*IngressInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateIngressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateIngressRequest) when calling interceptor")
					}
					return c.callCreateIngress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*IngressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*IngressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *ingressProtobufClient) callCreateIngress(ctx context.Context, in *CreateIngressRequest) (*IngressInfo, error) {
	out := new(IngressInfo)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[0], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *ingressProtobufClient) UpdateIngress(ctx context.Context, in *UpdateIngressRequest) (*IngressInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Ingress")
	ctx = ctxsetters.WithMethodName(ctx, "UpdateIngress")
	caller := c.callUpdateIngress
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *UpdateIngressRequest) (*IngressInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateIngressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateIngressRequest) when calling interceptor")
					}
					return c.callUpdateIngress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*IngressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*IngressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *ingressProtobufClient) callUpdateIngress(ctx context.Context, in *UpdateIngressRequest) (*IngressInfo, error) {
	out := new(IngressInfo)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[1], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *ingressProtobufClient) ListIngress(ctx context.Context, in *ListIngressRequest) (*ListIngressResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Ingress")
	ctx = ctxsetters.WithMethodName(ctx, "ListIngress")
	caller := c.callListIngress
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ListIngressRequest) (*ListIngressResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListIngressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListIngressRequest) when calling interceptor")
					}
					return c.callListIngress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListIngressResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListIngressResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *ingressProtobufClient) callListIngress(ctx context.Context, in *ListIngressRequest) (*ListIngressResponse, error) {
	out := new(ListIngressResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[2], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *ingressProtobufClient) DeleteIngress(ctx context.Context, in *DeleteIngressRequest) (*IngressInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Ingress")
	ctx = ctxsetters.WithMethodName(ctx, "DeleteIngress")
	caller := c.callDeleteIngress
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *DeleteIngressRequest) (*IngressInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*DeleteIngressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*DeleteIngressRequest) when calling interceptor")
					}
					return c.callDeleteIngress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*IngressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*IngressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *ingressProtobufClient) callDeleteIngress(ctx context.Context, in *DeleteIngressRequest) (*IngressInfo, error) {
	out := new(IngressInfo)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[3], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

// ===================
// Ingress JSON Client
// ===================

type ingressJSONClient struct {
	client      HTTPClient
	urls        [4]string
	interceptor twirp.Interceptor
	opts        twirp.ClientOptions
}

// NewIngressJSONClient creates a JSON client that implements the Ingress interface.
// It communicates using JSON and can be configured with a custom HTTPClient.
func NewIngressJSONClient(baseURL string, client HTTPClient, opts ...twirp.ClientOption) Ingress {
	if c, ok := client.(*http.Client); ok {
		client = withoutRedirects(c)
	}

	clientOpts := twirp.ClientOptions{}
	for _, o := range opts {
		o(&clientOpts)
	}

	// Using ReadOpt allows backwards and forwards compatibility with new options in the future
	literalURLs := false
	_ = clientOpts.ReadOpt("literalURLs", &literalURLs)
	var pathPrefix string
	if ok := clientOpts.ReadOpt("pathPrefix", &pathPrefix); !ok {
		pathPrefix = "/twirp" // default prefix
	}

	// Build method URLs: <baseURL>[<prefix>]/<package>.<Service>/<Method>
	serviceURL := sanitizeBaseURL(baseURL)
	serviceURL += baseServicePath(pathPrefix, "livekit", "Ingress")
	urls := [4]string{
		serviceURL + "CreateIngress",
		serviceURL + "UpdateIngress",
		serviceURL + "ListIngress",
		serviceURL + "DeleteIngress",
	}

	return &ingressJSONClient{
		client:      client,
		urls:        urls,
		interceptor: twirp.ChainInterceptors(clientOpts.Interceptors...),
		opts:        clientOpts,
	}
}

func (c *ingressJSONClient) CreateIngress(ctx context.Context, in *CreateIngressRequest) (*IngressInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Ingress")
	ctx = ctxsetters.WithMethodName(ctx, "CreateIngress")
	caller := c.callCreateIngress
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *CreateIngressRequest) (*IngressInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateIngressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateIngressRequest) when calling interceptor")
					}
					return c.callCreateIngress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*IngressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*IngressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *ingressJSONClient) callCreateIngress(ctx context.Context, in *CreateIngressRequest) (*IngressInfo, error) {
	out := new(IngressInfo)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[0], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *ingressJSONClient) UpdateIngress(ctx context.Context, in *UpdateIngressRequest) (*IngressInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Ingress")
	ctx = ctxsetters.WithMethodName(ctx, "UpdateIngress")
	caller := c.callUpdateIngress
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *UpdateIngressRequest) (*IngressInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateIngressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateIngressRequest) when calling interceptor")
					}
					return c.callUpdateIngress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*IngressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*IngressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *ingressJSONClient) callUpdateIngress(ctx context.Context, in *UpdateIngressRequest) (*IngressInfo, error) {
	out := new(IngressInfo)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[1], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *ingressJSONClient) ListIngress(ctx context.Context, in *ListIngressRequest) (*ListIngressResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Ingress")
	ctx = ctxsetters.WithMethodName(ctx, "ListIngress")
	caller := c.callListIngress
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ListIngressRequest) (*ListIngressResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListIngressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListIngressRequest) when calling interceptor")
					}
					return c.callListIngress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListIngressResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListIngressResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *ingressJSONClient) callListIngress(ctx context.Context, in *ListIngressRequest) (*ListIngressResponse, error) {
	out := new(ListIngressResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[2], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *ingressJSONClient) DeleteIngress(ctx context.Context, in *DeleteIngressRequest) (*IngressInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Ingress")
	ctx = ctxsetters.WithMethodName(ctx, "DeleteIngress")
	caller := c.callDeleteIngress
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *DeleteIngressRequest) (*IngressInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*DeleteIngressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*DeleteIngressRequest) when calling interceptor")
					}
					return c.callDeleteIngress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*IngressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*IngressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *ingressJSONClient) callDeleteIngress(ctx context.Context, in *DeleteIngressRequest) (*IngressInfo, error) {
	out := new(IngressInfo)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[3], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

// ======================
// Ingress Server Handler
// ======================

type ingressServer struct {
	Ingress
	interceptor      twirp.Interceptor
	hooks            *twirp.ServerHooks
	pathPrefix       string // prefix for routing
	jsonSkipDefaults bool   // do not include unpopulated fields (default values) in the response
	jsonCamelCase    bool   // JSON fields are serialized as lowerCamelCase rather than keeping the original proto names
}

// NewIngressServer builds a TwirpServer that can be used as an http.Handler to handle
// HTTP requests that are routed to the right method in the provided svc implementation.
// The opts are twirp.ServerOption modifiers, for example twirp.WithServerHooks(hooks).
func NewIngressServer(svc Ingress, opts ...interface{}) TwirpServer {
	serverOpts := newServerOpts(opts)

	// Using ReadOpt allows backwards and forwards compatibility with new options in the future
	jsonSkipDefaults := false
	_ = serverOpts.ReadOpt("jsonSkipDefaults", &jsonSkipDefaults)
	jsonCamelCase := false
	_ = serverOpts.ReadOpt("jsonCamelCase", &jsonCamelCase)
	var pathPrefix string
	if ok := serverOpts.ReadOpt("pathPrefix", &pathPrefix); !ok {
		pathPrefix = "/twirp" // default prefix
	}

	return &ingressServer{
		Ingress:          svc,
		hooks:            serverOpts.Hooks,
		interceptor:      twirp.ChainInterceptors(serverOpts.Interceptors...),
		pathPrefix:       pathPrefix,
		jsonSkipDefaults: jsonSkipDefaults,
		jsonCamelCase:    jsonCamelCase,
	}
}

// writeError writes an HTTP response with a valid Twirp error format, and triggers hooks.
// If err is not a twirp.Error, it will get wrapped with twirp.InternalErrorWith(err)
func (s *ingressServer) writeError(ctx context.Context, resp http.ResponseWriter, err error) {
	writeError(ctx, resp, err, s.hooks)
}

// handleRequestBodyError is used to handle error when the twirp server cannot read request
func (s *ingressServer) handleRequestBodyError(ctx context.Context, resp http.ResponseWriter, msg string, err error) {
	if context.Canceled == ctx.Err() {
		s.writeError(ctx, resp, twirp.NewError(twirp.Canceled, "failed to read request: context canceled"))
		return
	}
	if context.DeadlineExceeded == ctx.Err() {
		s.writeError(ctx, resp, twirp.NewError(twirp.DeadlineExceeded, "failed to read request: deadline exceeded"))
		return
	}
	s.writeError(ctx, resp, twirp.WrapError(malformedRequestError(msg), err))
}

// IngressPathPrefix is a convenience constant that may identify URL paths.
// Should be used with caution, it only matches routes generated by Twirp Go clients,
// with the default "/twirp" prefix and default CamelCase service and method names.
// More info: https://twitchtv.github.io/twirp/docs/routing.html
const IngressPathPrefix = "/twirp/livekit.Ingress/"

func (s *ingressServer) ServeHTTP(resp http.ResponseWriter, req *http.Request) {
	ctx := req.Context()
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Ingress")
	ctx = ctxsetters.WithResponseWriter(ctx, resp)

	var err error
	ctx, err = callRequestReceived(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	if req.Method != "POST" {
		msg := fmt.Sprintf("unsupported method %q (only POST is allowed)", req.Method)
		s.writeError(ctx, resp, badRouteError(msg, req.Method, req.URL.Path))
		return
	}

	// Verify path format: [<prefix>]/<package>.<Service>/<Method>
	prefix, pkgService, method := parseTwirpPath(req.URL.Path)
	if pkgService != "livekit.Ingress" {
		msg := fmt.Sprintf("no handler for path %q", req.URL.Path)
		s.writeError(ctx, resp, badRouteError(msg, req.Method, req.URL.Path))
		return
	}
	if prefix != s.pathPrefix {
		msg := fmt.Sprintf("invalid path prefix %q, expected %q, on path %q", prefix, s.pathPrefix, req.URL.Path)
		s.writeError(ctx, resp, badRouteError(msg, req.Method, req.URL.Path))
		return
	}

	switch method {
	case "CreateIngress":
		s.serveCreateIngress(ctx, resp, req)
		return
	case "UpdateIngress":
		s.serveUpdateIngress(ctx, resp, req)
		return
	case "ListIngress":
		s.serveListIngress(ctx, resp, req)
		return
	case "DeleteIngress":
		s.serveDeleteIngress(ctx, resp, req)
		return
	default:
		msg := fmt.Sprintf("no handler for path %q", req.URL.Path)
		s.writeError(ctx, resp, badRouteError(msg, req.Method, req.URL.Path))
		return
	}
}

func (s *ingressServer) serveCreateIngress(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveCreateIngressJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveCreateIngressProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *ingressServer) serveCreateIngressJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "CreateIngress")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(CreateIngressRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.Ingress.CreateIngress
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *CreateIngressRequest) (*IngressInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateIngressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateIngressRequest) when calling interceptor")
					}
					return s.Ingress.CreateIngress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*IngressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*IngressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *IngressInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *IngressInfo and nil error while calling CreateIngress. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *ingressServer) serveCreateIngressProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "CreateIngress")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(CreateIngressRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.Ingress.CreateIngress
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *CreateIngressRequest) (*IngressInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateIngressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateIngressRequest) when calling interceptor")
					}
					return s.Ingress.CreateIngress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*IngressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*IngressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *IngressInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *IngressInfo and nil error while calling CreateIngress. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *ingressServer) serveUpdateIngress(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveUpdateIngressJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveUpdateIngressProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *ingressServer) serveUpdateIngressJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "UpdateIngress")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(UpdateIngressRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.Ingress.UpdateIngress
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *UpdateIngressRequest) (*IngressInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateIngressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateIngressRequest) when calling interceptor")
					}
					return s.Ingress.UpdateIngress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*IngressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*IngressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *IngressInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *IngressInfo and nil error while calling UpdateIngress. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *ingressServer) serveUpdateIngressProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "UpdateIngress")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(UpdateIngressRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.Ingress.UpdateIngress
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *UpdateIngressRequest) (*IngressInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateIngressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateIngressRequest) when calling interceptor")
					}
					return s.Ingress.UpdateIngress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*IngressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*IngressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *IngressInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *IngressInfo and nil error while calling UpdateIngress. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *ingressServer) serveListIngress(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveListIngressJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveListIngressProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *ingressServer) serveListIngressJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "ListIngress")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(ListIngressRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.Ingress.ListIngress
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ListIngressRequest) (*ListIngressResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListIngressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListIngressRequest) when calling interceptor")
					}
					return s.Ingress.ListIngress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListIngressResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListIngressResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ListIngressResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ListIngressResponse and nil error while calling ListIngress. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *ingressServer) serveListIngressProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "ListIngress")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(ListIngressRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.Ingress.ListIngress
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ListIngressRequest) (*ListIngressResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListIngressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListIngressRequest) when calling interceptor")
					}
					return s.Ingress.ListIngress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListIngressResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListIngressResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ListIngressResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ListIngressResponse and nil error while calling ListIngress. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *ingressServer) serveDeleteIngress(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveDeleteIngressJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveDeleteIngressProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *ingressServer) serveDeleteIngressJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "DeleteIngress")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(DeleteIngressRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.Ingress.DeleteIngress
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *DeleteIngressRequest) (*IngressInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*DeleteIngressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*DeleteIngressRequest) when calling interceptor")
					}
					return s.Ingress.DeleteIngress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*IngressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*IngressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *IngressInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *IngressInfo and nil error while calling DeleteIngress. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *ingressServer) serveDeleteIngressProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "DeleteIngress")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(DeleteIngressRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.Ingress.DeleteIngress
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *DeleteIngressRequest) (*IngressInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*DeleteIngressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*DeleteIngressRequest) when calling interceptor")
					}
					return s.Ingress.DeleteIngress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*IngressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*IngressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *IngressInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *IngressInfo and nil error while calling DeleteIngress. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *ingressServer) ServiceDescriptor() ([]byte, int) {
	return twirpFileDescriptor2, 0
}

func (s *ingressServer) ProtocGenTwirpVersion() string {
	return "v8.1.3"
}

// PathPrefix returns the base service path, in the form: "/<prefix>/<package>.<Service>/"
// that is everything in a Twirp route except for the <Method>. This can be used for routing,
// for example to identify the requests that are targeted to this service in a mux.
func (s *ingressServer) PathPrefix() string {
	return baseServicePath(s.pathPrefix, "livekit", "Ingress")
}

var twirpFileDescriptor2 = []byte{
	// 1485 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x58, 0x4d, 0x6f, 0xdb, 0x46,
	0x13, 0x36, 0xf5, 0xad, 0x51, 0x24, 0x2b, 0x6b, 0x39, 0x61, 0xfc, 0x81, 0xd7, 0x50, 0xf2, 0x22,
	0x8e, 0xf3, 0x42, 0xb1, 0x15, 0xc7, 0x6f, 0x1b, 0x20, 0x40, 0x2d, 0x5b, 0x8e, 0x08, 0xdb, 0x92,
	0xb0, 0x92, 0x53, 0xb4, 0x17, 0x82, 0x16, 0x37, 0x36, 0x11, 0x89, 0x54, 0xc9, 0x95, 0x1b, 0xa3,
	0xd7, 0x1e, 0xf2, 0x03, 0x7a, 0xef, 0xa1, 0xed, 0x25, 0x97, 0xa2, 0x7f, 0xab, 0xe8, 0xb9, 0xe7,
	0x62, 0x3f, 0x44, 0x93, 0x12, 0x15, 0xd8, 0x69, 0x51, 0xe4, 0xa6, 0x9d, 0x67, 0x66, 0xf8, 0xec,
	0xce, 0xec, 0x33, 0x0b, 0xc1, 0x62, 0xdf, 0xba, 0x20, 0x6f, 0x2c, 0xaa, 0x5b, 0xf6, 0x99, 0x4b,
	0x3c, 0xaf, 0x32, 0x74, 0x1d, 0xea, 0xa0, 0xb4, 0x34, 0x2f, 0x95, 0xc6, 0xf8, 0xc0, 0x31, 0x49,
	0x5f, 0xc2, 0xe5, 0xdf, 0x12, 0x50, 0xda, 0x73, 0x89, 0x41, 0x89, 0x26, 0xc2, 0x30, 0xf9, 0x66,
	0x44, 0x3c, 0x8a, 0xb6, 0x01, 0x2c, 0x7b, 0x38, 0xa2, 0x3a, 0xbd, 0x1c, 0x12, 0x55, 0x59, 0x53,
	0xd6, 0x0b, 0xd5, 0xc5, 0x8a, 0xcc, 0x51, 0x91, 0xce, 0x1a, 0xf3, 0xc0, 0x59, 0xee, 0xd8, 0xbd,
	0x1c, 0x12, 0x54, 0x84, 0xf8, 0xc8, 0xed, 0xab, 0xd9, 0x35, 0x65, 0x3d, 0x8b, 0xd9, 0x4f, 0x84,
	0x20, 0x61, 0x1b, 0x03, 0xa2, 0xc6, 0xb8, 0x89, 0xff, 0x46, 0xcb, 0x90, 0x75, 0x1d, 0x67, 0xa0,
	0x73, 0x20, 0xce, 0x81, 0x0c, 0x33, 0x34, 0x19, 0xb8, 0x05, 0xa5, 0xa1, 0xe1, 0x52, 0xab, 0x67,
	0x0d, 0x0d, 0x9b, 0xea, 0x96, 0x49, 0x6c, 0x6a, 0xd1, 0x4b, 0x35, 0xc1, 0xfd, 0x16, 0x02, 0x98,
	0x26, 0x21, 0xf4, 0x08, 0x8a, 0xc1, 0x10, 0x9e, 0x36, 0xc9, 0xdd, 0xe7, 0x03, 0xf6, 0xa8, 0xec,
	0x03, 0x42, 0x0d, 0xd3, 0xa0, 0x86, 0x0a, 0x53, 0xd9, 0x8f, 0x25, 0x84, 0xb6, 0x00, 0x9d, 0x5e,
	0x0e, 0x0d, 0xcf, 0xd3, 0xa9, 0x6b, 0xd8, 0x5e, 0xcf, 0x31, 0x2d, 0xfb, 0x4c, 0xcd, 0xac, 0x29,
	0xeb, 0x99, 0x5a, 0x4c, 0x55, 0xf0, 0x6d, 0x81, 0x76, 0xaf, 0x40, 0x54, 0x05, 0x44, 0x6c, 0xe3,
	0xb4, 0x4f, 0x42, 0x21, 0x39, 0x16, 0xd2, 0x98, 0xc3, 0xb7, 0x05, 0x16, 0x08, 0x78, 0xa7, 0x28,
	0xa8, 0x0a, 0x49, 0x63, 0x64, 0x5a, 0x8e, 0x9a, 0x5a, 0x53, 0xd6, 0x73, 0xd5, 0x95, 0xc9, 0xb3,
	0xde, 0x65, 0x60, 0x6b, 0x48, 0x2d, 0xc7, 0xf6, 0xb0, 0x70, 0x65, 0x31, 0x17, 0x96, 0x49, 0x1c,
	0x35, 0x1d, 0x1d, 0xf3, 0x8a, 0x81, 0x7e, 0x0c, 0x77, 0x45, 0xab, 0x90, 0x16, 0xdf, 0x37, 0xd5,
	0x5b, 0x9c, 0x90, 0x82, 0xc7, 0x86, 0x77, 0x8a, 0x52, 0x5b, 0x84, 0x05, 0x7d, 0x9a, 0x7b, 0x0d,
	0x20, 0x23, 0xcd, 0x66, 0xf9, 0x0f, 0x05, 0x16, 0x22, 0x48, 0xf9, 0xa5, 0x56, 0x02, 0xa5, 0xfe,
	0x1f, 0xa4, 0x3c, 0x67, 0xe4, 0xf6, 0x44, 0x03, 0x14, 0xaa, 0x25, 0x9f, 0x62, 0xd7, 0x35, 0x7a,
	0x6f, 0x3a, 0x1c, 0xc3, 0xd2, 0x07, 0xbd, 0x80, 0xd4, 0xd0, 0x25, 0x1e, 0xa1, 0xbc, 0x2b, 0x0a,
	0xd5, 0xfb, 0x91, 0x87, 0x50, 0xb7, 0x05, 0xa9, 0x36, 0x77, 0x6d, 0xcc, 0x61, 0x19, 0x84, 0xbe,
	0x80, 0xb4, 0x23, 0xb8, 0xf0, 0x6e, 0xc9, 0x55, 0x1f, 0x7c, 0x30, 0x5e, 0xf2, 0x6e, 0xcc, 0xe1,
	0x71, 0x58, 0x0d, 0x41, 0x91, 0x48, 0x54, 0x97, 0xb6, 0xe0, 0x76, 0x83, 0xe7, 0xf9, 0x6f, 0x6c,
	0x97, 0x7f, 0xef, 0x6f, 0x6c, 0x37, 0x14, 0x7f, 0xcd, 0xed, 0xbe, 0x57, 0x60, 0xf9, 0x03, 0xa7,
	0x85, 0xb6, 0x21, 0xc7, 0x9b, 0x4f, 0xef, 0x39, 0x26, 0xe9, 0x49, 0x65, 0x58, 0xf0, 0xbf, 0xcc,
	0x63, 0xf6, 0x18, 0x84, 0xc1, 0xf0, 0x7f, 0x23, 0x15, 0xd2, 0xa7, 0x16, 0x75, 0x0d, 0x2a, 0x4e,
	0x26, 0x8f, 0xc7, 0x4b, 0xf4, 0x1f, 0xc8, 0x99, 0x96, 0xc7, 0x1b, 0xce, 0xa4, 0x6f, 0xf9, 0x49,
	0x64, 0x30, 0x48, 0xd3, 0x3e, 0x7d, 0x8b, 0x96, 0x20, 0xd3, 0x3b, 0x37, 0x6c, 0x9b, 0xf4, 0xc5,
	0x3e, 0xf3, 0xd8, 0x5f, 0x97, 0x7f, 0xbc, 0x22, 0x1b, 0xb5, 0x57, 0x46, 0x96, 0x77, 0xfd, 0x0c,
	0xb2, 0x3c, 0x46, 0x92, 0xbd, 0xf0, 0x7f, 0xa3, 0x55, 0x80, 0xd7, 0xae, 0x31, 0x20, 0xba, 0xcf,
	0x57, 0xc1, 0x59, 0x6e, 0xc1, 0x8c, 0xf1, 0x63, 0x48, 0xf5, 0x8d, 0x4b, 0xe2, 0x7a, 0x6a, 0x7c,
	0x2d, 0xbe, 0x9e, 0x9b, 0xcc, 0x77, 0xc4, 0x30, 0x2c, 0x5d, 0xca, 0xbf, 0x26, 0x21, 0xe7, 0xab,
	0xe5, 0x6b, 0x76, 0xfd, 0x40, 0x0a, 0xb4, 0x6e, 0x99, 0xb2, 0x77, 0xb2, 0xd2, 0xa2, 0x99, 0x91,
	0x72, 0xb9, 0x0a, 0xe0, 0x51, 0x97, 0x18, 0x03, 0xfd, 0x0d, 0xb9, 0x94, 0x7a, 0x99, 0x15, 0x96,
	0x43, 0x72, 0x39, 0xd6, 0xdc, 0xc4, 0x95, 0xe6, 0x86, 0xb5, 0x3b, 0x79, 0x4d, 0xed, 0x8e, 0xd6,
	0xb9, 0xfc, 0xcd, 0x75, 0x6e, 0xfe, 0x93, 0xd0, 0xb9, 0xd0, 0x90, 0xc9, 0x5c, 0x73, 0xc8, 0x64,
	0x6f, 0x36, 0x64, 0xe0, 0x66, 0x43, 0xa6, 0x30, 0x7b, 0xc8, 0x2c, 0x41, 0xc6, 0x25, 0x23, 0xde,
	0xf3, 0x62, 0x4e, 0x60, 0x7f, 0x8d, 0x1e, 0x43, 0xd2, 0xa3, 0xac, 0x13, 0x6f, 0xf1, 0xdd, 0x4f,
	0x55, 0xb2, 0xc3, 0x40, 0x2c, 0x7c, 0x82, 0xf2, 0x5e, 0xfc, 0x38, 0x79, 0xff, 0x33, 0x0e, 0xb7,
	0x82, 0x99, 0xd1, 0x36, 0xa4, 0x58, 0xee, 0x91, 0x27, 0xef, 0xcf, 0x4a, 0x24, 0x81, 0x4a, 0x87,
	0xfb, 0x60, 0xe9, 0x8b, 0x4a, 0x90, 0x24, 0xae, 0xeb, 0xb8, 0xb2, 0x95, 0xc5, 0x02, 0x55, 0xc6,
	0x95, 0x8c, 0xf3, 0xbd, 0xa8, 0x81, 0x54, 0xc3, 0x11, 0xe5, 0x75, 0x94, 0xdb, 0x11, 0x55, 0xac,
	0x8c, 0xbb, 0x25, 0x11, 0xe5, 0xcf, 0x7b, 0x45, 0xfa, 0x8b, 0x4e, 0xb9, 0x0b, 0x69, 0x5e, 0x75,
	0xcb, 0x94, 0x2f, 0x80, 0x14, 0x5b, 0x6a, 0xa6, 0xb8, 0x44, 0x86, 0x4b, 0x89, 0xa9, 0x1b, 0x94,
	0xf7, 0x51, 0x9c, 0x5d, 0x22, 0x6e, 0xd9, 0xa5, 0xe8, 0x1e, 0x64, 0x88, 0x6d, 0x0a, 0x30, 0xc3,
	0xc1, 0x34, 0x5f, 0xef, 0x52, 0x16, 0x39, 0x1a, 0x9a, 0x86, 0x8c, 0x04, 0x11, 0x29, 0x2d, 0xbb,
	0x94, 0xe9, 0x97, 0x4b, 0x84, 0xa0, 0xb3, 0xaf, 0x8a, 0x0e, 0x82, 0xb1, 0x49, 0x33, 0xd1, 0x06,
	0xa4, 0x28, 0x13, 0x7f, 0x4f, 0x4d, 0x71, 0xb9, 0x40, 0xe1, 0x99, 0xc0, 0x54, 0x01, 0x4b, 0x8f,
	0xf2, 0x77, 0x90, 0x12, 0xc7, 0x88, 0x16, 0xe1, 0x76, 0xbd, 0xb9, 0xdf, 0x6e, 0x69, 0xcd, 0xae,
	0xae, 0x35, 0x77, 0xf7, 0xba, 0xda, 0xab, 0x7a, 0x71, 0x0e, 0xdd, 0x01, 0xe4, 0x9b, 0x6b, 0x27,
	0x07, 0x07, 0x75, 0xac, 0x35, 0x5f, 0x16, 0x15, 0x74, 0x17, 0x16, 0x7c, 0x7b, 0xfb, 0xa4, 0x76,
	0xa4, 0x75, 0x1a, 0x0c, 0x88, 0x21, 0x04, 0x05, 0x1f, 0xa8, 0x63, 0xdc, 0xc2, 0xc5, 0x78, 0x28,
	0xf7, 0x5e, 0xeb, 0xb8, 0x7d, 0x54, 0xef, 0xd6, 0x8b, 0x89, 0xf2, 0x4f, 0x0a, 0xcc, 0x4f, 0x94,
	0x81, 0xdd, 0xa2, 0x81, 0x35, 0x20, 0x57, 0xaf, 0xc0, 0x2c, 0xce, 0x30, 0x03, 0x57, 0x8c, 0x87,
	0x30, 0x6f, 0x5c, 0x10, 0xd7, 0x38, 0x23, 0x7a, 0x58, 0xdc, 0x0b, 0xd2, 0x5c, 0x93, 0x1a, 0x5f,
	0x82, 0xe4, 0xb7, 0x96, 0x49, 0xcf, 0x79, 0xd5, 0xf3, 0x58, 0x2c, 0xd0, 0x1d, 0x48, 0x9d, 0x13,
	0xeb, 0xec, 0x9c, 0x4a, 0x59, 0x97, 0x2b, 0xb4, 0x02, 0x42, 0x6c, 0x79, 0xc2, 0x64, 0x40, 0x7d,
	0x99, 0xa1, 0xfc, 0xc3, 0x98, 0xe5, 0x55, 0xf1, 0xff, 0x21, 0x96, 0xc1, 0x41, 0x13, 0x0f, 0x0f,
	0x1a, 0x56, 0x65, 0xcf, 0x18, 0x0c, 0xfb, 0x72, 0x26, 0x08, 0xc2, 0x20, 0x4c, 0x6c, 0x28, 0x94,
	0x7f, 0x4e, 0x40, 0xe9, 0x84, 0x37, 0xc5, 0xc4, 0x43, 0xfa, 0x23, 0x04, 0xff, 0x13, 0x7c, 0x1f,
	0x67, 0x67, 0x4b, 0xd7, 0xce, 0x87, 0xdf, 0xc7, 0x6c, 0x10, 0x4c, 0x4d, 0x0e, 0x31, 0x08, 0xa2,
	0x86, 0x07, 0x48, 0xd1, 0xfa, 0x64, 0x1e, 0xc9, 0xe2, 0xd5, 0x1e, 0x9b, 0x52, 0xd1, 0xe9, 0x3d,
	0x5f, 0x47, 0x5c, 0xdb, 0x80, 0x8e, 0x2c, 0x8f, 0x4e, 0xf4, 0x48, 0xa8, 0xe0, 0xca, 0x44, 0xc1,
	0xc3, 0x0d, 0x14, 0x9b, 0x68, 0xa0, 0xf2, 0x2e, 0x2c, 0x84, 0x32, 0x7a, 0x43, 0xc7, 0xf6, 0x08,
	0xda, 0x80, 0xa4, 0x45, 0xc9, 0x80, 0x69, 0x36, 0x13, 0x9d, 0xd2, 0xf4, 0xf8, 0x7f, 0xed, 0x60,
	0xe1, 0x52, 0x7e, 0x06, 0xa5, 0x7d, 0xd2, 0x27, 0x37, 0x6c, 0xdd, 0x8d, 0x17, 0xfe, 0x9c, 0xe0,
	0xf7, 0x11, 0x15, 0x00, 0x70, 0xf7, 0xb8, 0xad, 0x6b, 0xcd, 0xf6, 0x49, 0xb7, 0x38, 0xc7, 0xd6,
	0x5f, 0x36, 0xb4, 0xf1, 0x5a, 0x41, 0x79, 0xc8, 0x9e, 0xe0, 0x23, 0xb9, 0x8c, 0x6d, 0x68, 0xb0,
	0x34, 0xfb, 0x55, 0xcf, 0x84, 0xae, 0xd5, 0x3e, 0xe9, 0xe8, 0x9d, 0x6e, 0x1d, 0xd7, 0x5b, 0xfa,
	0xe7, 0x3b, 0x87, 0xb5, 0x76, 0xa7, 0x38, 0x87, 0x16, 0x60, 0x9e, 0xdb, 0x8f, 0x5b, 0xcd, 0x96,
	0xbe, 0xb3, 0x7d, 0x58, 0xeb, 0x14, 0x95, 0x8d, 0xef, 0xe3, 0x7e, 0xae, 0x88, 0x27, 0x33, 0x5a,
	0x01, 0xb5, 0x51, 0xdd, 0xd9, 0xd6, 0xff, 0x5f, 0xdd, 0x6c, 0xeb, 0x4f, 0x37, 0x0f, 0xda, 0x1d,
	0xfd, 0xa9, 0x7e, 0xb4, 0xfb, 0x55, 0x1d, 0xb3, 0x8c, 0xab, 0x70, 0x8f, 0xa3, 0x5b, 0x9b, 0x9f,
	0x4d, 0xc3, 0x8a, 0x1f, 0xfc, 0x6c, 0x7b, 0xb3, 0xad, 0x57, 0x9f, 0x31, 0xb4, 0x3a, 0x46, 0x63,
	0x68, 0x19, 0xee, 0x4e, 0xa6, 0xde, 0x12, 0x68, 0x31, 0xee, 0x87, 0x06, 0x33, 0x8f, 0xd1, 0x04,
	0x5a, 0x87, 0x07, 0xb3, 0x58, 0xe9, 0x0d, 0xed, 0x65, 0x43, 0x3f, 0x6e, 0x75, 0xb5, 0x56, 0xb3,
	0x98, 0x44, 0x8f, 0xe0, 0xbf, 0x33, 0x19, 0x86, 0x5c, 0x53, 0x7e, 0xd2, 0x08, 0xb6, 0x21, 0xcf,
	0x34, 0x7a, 0x08, 0xf7, 0x67, 0x30, 0x0f, 0x39, 0x66, 0xfc, 0x94, 0x11, 0xbb, 0x08, 0x79, 0x66,
	0xab, 0xbf, 0xc4, 0x20, 0x2d, 0xcb, 0x80, 0xf6, 0x21, 0x1f, 0xfa, 0x5f, 0x01, 0xad, 0xfa, 0x1d,
	0x18, 0xf5, 0x7f, 0xc3, 0x52, 0x64, 0x83, 0xb2, 0x2c, 0x21, 0x51, 0x0d, 0x64, 0x89, 0x12, 0xdb,
	0x19, 0x59, 0x1a, 0x90, 0x0b, 0x5c, 0x11, 0xb4, 0xec, 0x3b, 0x4d, 0x5f, 0xc5, 0xa5, 0x95, 0x68,
	0x50, 0xde, 0xaa, 0x7d, 0xc8, 0x87, 0x6e, 0x4a, 0x80, 0x4f, 0xd4, 0x0d, 0x8a, 0xe6, 0x53, 0x3b,
	0xf8, 0xfa, 0xfe, 0x99, 0x45, 0xcf, 0x47, 0xa7, 0x95, 0x9e, 0x33, 0x78, 0x22, 0x3d, 0x9e, 0xf0,
	0x3f, 0x64, 0x7a, 0x4e, 0x7f, 0x6c, 0x78, 0x1f, 0xcb, 0x1f, 0x59, 0x17, 0xe4, 0xd0, 0xa2, 0x95,
	0x36, 0x83, 0x7e, 0x8f, 0x15, 0xe4, 0xfa, 0xf9, 0x73, 0x6e, 0x38, 0x4d, 0xf1, 0x90, 0xa7, 0x7f,
	0x05, 0x00, 0x00, 0xff, 0xff, 0x22, 0x5c, 0x1b, 0x0e, 0xfb, 0x11, 0x00, 0x00,
}
