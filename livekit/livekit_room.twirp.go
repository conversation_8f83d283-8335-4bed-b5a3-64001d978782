// Code generated by protoc-gen-twirp v8.1.3, DO NOT EDIT.
// source: livekit_room.proto

package livekit

import context "context"
import fmt "fmt"
import http "net/http"
import io "io"
import json "encoding/json"
import strconv "strconv"
import strings "strings"

import protojson "google.golang.org/protobuf/encoding/protojson"
import proto "google.golang.org/protobuf/proto"
import twirp "github.com/twitchtv/twirp"
import ctxsetters "github.com/twitchtv/twirp/ctxsetters"

// Version compatibility assertion.
// If the constant is not defined in the package, that likely means
// the package needs to be updated to work with this generated code.
// See https://twitchtv.github.io/twirp/docs/version_matrix.html
const _ = twirp.TwirpPackageMinVersion_8_1_0

// =====================
// RoomService Interface
// =====================

// Room service that can be performed on any node
// they are Twirp-based HTTP req/responses
type RoomService interface {
	// Creates a room with settings. Requires `roomCreate` permission.
	// This method is optional; rooms are automatically created when clients connect to them for the first time.
	CreateRoom(context.Context, *CreateRoomRequest) (*Room, error)

	// List rooms that are active on the server. Requires `roomList` permission.
	ListRooms(context.Context, *ListRoomsRequest) (*ListRoomsResponse, error)

	// Deletes an existing room by name or id. Requires `roomCreate` permission.
	// DeleteRoom will disconnect all participants that are currently in the room.
	DeleteRoom(context.Context, *DeleteRoomRequest) (*DeleteRoomResponse, error)

	// Lists participants in a room, Requires `roomAdmin`
	ListParticipants(context.Context, *ListParticipantsRequest) (*ListParticipantsResponse, error)

	// Get information on a specific participant, Requires `roomAdmin`
	GetParticipant(context.Context, *RoomParticipantIdentity) (*ParticipantInfo, error)

	// Removes a participant from room. Requires `roomAdmin`
	RemoveParticipant(context.Context, *RoomParticipantIdentity) (*RemoveParticipantResponse, error)

	// Mute/unmute a participant's track, Requires `roomAdmin`
	MutePublishedTrack(context.Context, *MuteRoomTrackRequest) (*MuteRoomTrackResponse, error)

	// Update participant metadata, will cause updates to be broadcasted to everyone in the room. Requires `roomAdmin`
	UpdateParticipant(context.Context, *UpdateParticipantRequest) (*ParticipantInfo, error)

	// Subscribes or unsubscribe a participant from tracks. Requires `roomAdmin`
	UpdateSubscriptions(context.Context, *UpdateSubscriptionsRequest) (*UpdateSubscriptionsResponse, error)

	// Send data over data channel to participants in a room, Requires `roomAdmin`
	SendData(context.Context, *SendDataRequest) (*SendDataResponse, error)

	// Update room metadata, will cause updates to be broadcasted to everyone in the room, Requires `roomAdmin`
	UpdateRoomMetadata(context.Context, *UpdateRoomMetadataRequest) (*Room, error)

	// Cloud-only
	// a connected participant's track(s) to another room. Requires `roomAdmin` and `destinationRoom`. The forwarding will
	// stop when the participant leaves the room or `RemoveParticipant` has been called in the destination room.
	// A participant can be forwarded to multiple rooms. The destination room will be created if it does not exist.
	ForwardParticipant(context.Context, *ForwardParticipantRequest) (*ForwardParticipantResponse, error)

	// Cloud-only
	// Move a connected participant to a different room. Requires `roomAdmin` and `destinationRoom`.
	// The participant will be removed from the current room and added to the destination room.
	// From the other observers' perspective, the participant would've disconnected from the previous room and joined the new one.
	MoveParticipant(context.Context, *MoveParticipantRequest) (*MoveParticipantResponse, error)
}

// ===========================
// RoomService Protobuf Client
// ===========================

type roomServiceProtobufClient struct {
	client      HTTPClient
	urls        [13]string
	interceptor twirp.Interceptor
	opts        twirp.ClientOptions
}

// NewRoomServiceProtobufClient creates a Protobuf client that implements the RoomService interface.
// It communicates using Protobuf and can be configured with a custom HTTPClient.
func NewRoomServiceProtobufClient(baseURL string, client HTTPClient, opts ...twirp.ClientOption) RoomService {
	if c, ok := client.(*http.Client); ok {
		client = withoutRedirects(c)
	}

	clientOpts := twirp.ClientOptions{}
	for _, o := range opts {
		o(&clientOpts)
	}

	// Using ReadOpt allows backwards and forwards compatibility with new options in the future
	literalURLs := false
	_ = clientOpts.ReadOpt("literalURLs", &literalURLs)
	var pathPrefix string
	if ok := clientOpts.ReadOpt("pathPrefix", &pathPrefix); !ok {
		pathPrefix = "/twirp" // default prefix
	}

	// Build method URLs: <baseURL>[<prefix>]/<package>.<Service>/<Method>
	serviceURL := sanitizeBaseURL(baseURL)
	serviceURL += baseServicePath(pathPrefix, "livekit", "RoomService")
	urls := [13]string{
		serviceURL + "CreateRoom",
		serviceURL + "ListRooms",
		serviceURL + "DeleteRoom",
		serviceURL + "ListParticipants",
		serviceURL + "GetParticipant",
		serviceURL + "RemoveParticipant",
		serviceURL + "MutePublishedTrack",
		serviceURL + "UpdateParticipant",
		serviceURL + "UpdateSubscriptions",
		serviceURL + "SendData",
		serviceURL + "UpdateRoomMetadata",
		serviceURL + "ForwardParticipant",
		serviceURL + "MoveParticipant",
	}

	return &roomServiceProtobufClient{
		client:      client,
		urls:        urls,
		interceptor: twirp.ChainInterceptors(clientOpts.Interceptors...),
		opts:        clientOpts,
	}
}

func (c *roomServiceProtobufClient) CreateRoom(ctx context.Context, in *CreateRoomRequest) (*Room, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithMethodName(ctx, "CreateRoom")
	caller := c.callCreateRoom
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *CreateRoomRequest) (*Room, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateRoomRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateRoomRequest) when calling interceptor")
					}
					return c.callCreateRoom(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*Room)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*Room) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *roomServiceProtobufClient) callCreateRoom(ctx context.Context, in *CreateRoomRequest) (*Room, error) {
	out := new(Room)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[0], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *roomServiceProtobufClient) ListRooms(ctx context.Context, in *ListRoomsRequest) (*ListRoomsResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithMethodName(ctx, "ListRooms")
	caller := c.callListRooms
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ListRoomsRequest) (*ListRoomsResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListRoomsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListRoomsRequest) when calling interceptor")
					}
					return c.callListRooms(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListRoomsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListRoomsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *roomServiceProtobufClient) callListRooms(ctx context.Context, in *ListRoomsRequest) (*ListRoomsResponse, error) {
	out := new(ListRoomsResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[1], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *roomServiceProtobufClient) DeleteRoom(ctx context.Context, in *DeleteRoomRequest) (*DeleteRoomResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithMethodName(ctx, "DeleteRoom")
	caller := c.callDeleteRoom
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *DeleteRoomRequest) (*DeleteRoomResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*DeleteRoomRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*DeleteRoomRequest) when calling interceptor")
					}
					return c.callDeleteRoom(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*DeleteRoomResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*DeleteRoomResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *roomServiceProtobufClient) callDeleteRoom(ctx context.Context, in *DeleteRoomRequest) (*DeleteRoomResponse, error) {
	out := new(DeleteRoomResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[2], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *roomServiceProtobufClient) ListParticipants(ctx context.Context, in *ListParticipantsRequest) (*ListParticipantsResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithMethodName(ctx, "ListParticipants")
	caller := c.callListParticipants
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ListParticipantsRequest) (*ListParticipantsResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListParticipantsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListParticipantsRequest) when calling interceptor")
					}
					return c.callListParticipants(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListParticipantsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListParticipantsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *roomServiceProtobufClient) callListParticipants(ctx context.Context, in *ListParticipantsRequest) (*ListParticipantsResponse, error) {
	out := new(ListParticipantsResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[3], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *roomServiceProtobufClient) GetParticipant(ctx context.Context, in *RoomParticipantIdentity) (*ParticipantInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithMethodName(ctx, "GetParticipant")
	caller := c.callGetParticipant
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *RoomParticipantIdentity) (*ParticipantInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*RoomParticipantIdentity)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*RoomParticipantIdentity) when calling interceptor")
					}
					return c.callGetParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ParticipantInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ParticipantInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *roomServiceProtobufClient) callGetParticipant(ctx context.Context, in *RoomParticipantIdentity) (*ParticipantInfo, error) {
	out := new(ParticipantInfo)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[4], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *roomServiceProtobufClient) RemoveParticipant(ctx context.Context, in *RoomParticipantIdentity) (*RemoveParticipantResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithMethodName(ctx, "RemoveParticipant")
	caller := c.callRemoveParticipant
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *RoomParticipantIdentity) (*RemoveParticipantResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*RoomParticipantIdentity)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*RoomParticipantIdentity) when calling interceptor")
					}
					return c.callRemoveParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*RemoveParticipantResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*RemoveParticipantResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *roomServiceProtobufClient) callRemoveParticipant(ctx context.Context, in *RoomParticipantIdentity) (*RemoveParticipantResponse, error) {
	out := new(RemoveParticipantResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[5], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *roomServiceProtobufClient) MutePublishedTrack(ctx context.Context, in *MuteRoomTrackRequest) (*MuteRoomTrackResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithMethodName(ctx, "MutePublishedTrack")
	caller := c.callMutePublishedTrack
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *MuteRoomTrackRequest) (*MuteRoomTrackResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*MuteRoomTrackRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*MuteRoomTrackRequest) when calling interceptor")
					}
					return c.callMutePublishedTrack(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*MuteRoomTrackResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*MuteRoomTrackResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *roomServiceProtobufClient) callMutePublishedTrack(ctx context.Context, in *MuteRoomTrackRequest) (*MuteRoomTrackResponse, error) {
	out := new(MuteRoomTrackResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[6], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *roomServiceProtobufClient) UpdateParticipant(ctx context.Context, in *UpdateParticipantRequest) (*ParticipantInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithMethodName(ctx, "UpdateParticipant")
	caller := c.callUpdateParticipant
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *UpdateParticipantRequest) (*ParticipantInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateParticipantRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateParticipantRequest) when calling interceptor")
					}
					return c.callUpdateParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ParticipantInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ParticipantInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *roomServiceProtobufClient) callUpdateParticipant(ctx context.Context, in *UpdateParticipantRequest) (*ParticipantInfo, error) {
	out := new(ParticipantInfo)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[7], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *roomServiceProtobufClient) UpdateSubscriptions(ctx context.Context, in *UpdateSubscriptionsRequest) (*UpdateSubscriptionsResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithMethodName(ctx, "UpdateSubscriptions")
	caller := c.callUpdateSubscriptions
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *UpdateSubscriptionsRequest) (*UpdateSubscriptionsResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateSubscriptionsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateSubscriptionsRequest) when calling interceptor")
					}
					return c.callUpdateSubscriptions(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*UpdateSubscriptionsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*UpdateSubscriptionsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *roomServiceProtobufClient) callUpdateSubscriptions(ctx context.Context, in *UpdateSubscriptionsRequest) (*UpdateSubscriptionsResponse, error) {
	out := new(UpdateSubscriptionsResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[8], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *roomServiceProtobufClient) SendData(ctx context.Context, in *SendDataRequest) (*SendDataResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithMethodName(ctx, "SendData")
	caller := c.callSendData
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *SendDataRequest) (*SendDataResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*SendDataRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*SendDataRequest) when calling interceptor")
					}
					return c.callSendData(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SendDataResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SendDataResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *roomServiceProtobufClient) callSendData(ctx context.Context, in *SendDataRequest) (*SendDataResponse, error) {
	out := new(SendDataResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[9], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *roomServiceProtobufClient) UpdateRoomMetadata(ctx context.Context, in *UpdateRoomMetadataRequest) (*Room, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithMethodName(ctx, "UpdateRoomMetadata")
	caller := c.callUpdateRoomMetadata
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *UpdateRoomMetadataRequest) (*Room, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateRoomMetadataRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateRoomMetadataRequest) when calling interceptor")
					}
					return c.callUpdateRoomMetadata(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*Room)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*Room) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *roomServiceProtobufClient) callUpdateRoomMetadata(ctx context.Context, in *UpdateRoomMetadataRequest) (*Room, error) {
	out := new(Room)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[10], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *roomServiceProtobufClient) ForwardParticipant(ctx context.Context, in *ForwardParticipantRequest) (*ForwardParticipantResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithMethodName(ctx, "ForwardParticipant")
	caller := c.callForwardParticipant
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ForwardParticipantRequest) (*ForwardParticipantResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ForwardParticipantRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ForwardParticipantRequest) when calling interceptor")
					}
					return c.callForwardParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ForwardParticipantResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ForwardParticipantResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *roomServiceProtobufClient) callForwardParticipant(ctx context.Context, in *ForwardParticipantRequest) (*ForwardParticipantResponse, error) {
	out := new(ForwardParticipantResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[11], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *roomServiceProtobufClient) MoveParticipant(ctx context.Context, in *MoveParticipantRequest) (*MoveParticipantResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithMethodName(ctx, "MoveParticipant")
	caller := c.callMoveParticipant
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *MoveParticipantRequest) (*MoveParticipantResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*MoveParticipantRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*MoveParticipantRequest) when calling interceptor")
					}
					return c.callMoveParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*MoveParticipantResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*MoveParticipantResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *roomServiceProtobufClient) callMoveParticipant(ctx context.Context, in *MoveParticipantRequest) (*MoveParticipantResponse, error) {
	out := new(MoveParticipantResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[12], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

// =======================
// RoomService JSON Client
// =======================

type roomServiceJSONClient struct {
	client      HTTPClient
	urls        [13]string
	interceptor twirp.Interceptor
	opts        twirp.ClientOptions
}

// NewRoomServiceJSONClient creates a JSON client that implements the RoomService interface.
// It communicates using JSON and can be configured with a custom HTTPClient.
func NewRoomServiceJSONClient(baseURL string, client HTTPClient, opts ...twirp.ClientOption) RoomService {
	if c, ok := client.(*http.Client); ok {
		client = withoutRedirects(c)
	}

	clientOpts := twirp.ClientOptions{}
	for _, o := range opts {
		o(&clientOpts)
	}

	// Using ReadOpt allows backwards and forwards compatibility with new options in the future
	literalURLs := false
	_ = clientOpts.ReadOpt("literalURLs", &literalURLs)
	var pathPrefix string
	if ok := clientOpts.ReadOpt("pathPrefix", &pathPrefix); !ok {
		pathPrefix = "/twirp" // default prefix
	}

	// Build method URLs: <baseURL>[<prefix>]/<package>.<Service>/<Method>
	serviceURL := sanitizeBaseURL(baseURL)
	serviceURL += baseServicePath(pathPrefix, "livekit", "RoomService")
	urls := [13]string{
		serviceURL + "CreateRoom",
		serviceURL + "ListRooms",
		serviceURL + "DeleteRoom",
		serviceURL + "ListParticipants",
		serviceURL + "GetParticipant",
		serviceURL + "RemoveParticipant",
		serviceURL + "MutePublishedTrack",
		serviceURL + "UpdateParticipant",
		serviceURL + "UpdateSubscriptions",
		serviceURL + "SendData",
		serviceURL + "UpdateRoomMetadata",
		serviceURL + "ForwardParticipant",
		serviceURL + "MoveParticipant",
	}

	return &roomServiceJSONClient{
		client:      client,
		urls:        urls,
		interceptor: twirp.ChainInterceptors(clientOpts.Interceptors...),
		opts:        clientOpts,
	}
}

func (c *roomServiceJSONClient) CreateRoom(ctx context.Context, in *CreateRoomRequest) (*Room, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithMethodName(ctx, "CreateRoom")
	caller := c.callCreateRoom
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *CreateRoomRequest) (*Room, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateRoomRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateRoomRequest) when calling interceptor")
					}
					return c.callCreateRoom(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*Room)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*Room) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *roomServiceJSONClient) callCreateRoom(ctx context.Context, in *CreateRoomRequest) (*Room, error) {
	out := new(Room)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[0], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *roomServiceJSONClient) ListRooms(ctx context.Context, in *ListRoomsRequest) (*ListRoomsResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithMethodName(ctx, "ListRooms")
	caller := c.callListRooms
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ListRoomsRequest) (*ListRoomsResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListRoomsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListRoomsRequest) when calling interceptor")
					}
					return c.callListRooms(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListRoomsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListRoomsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *roomServiceJSONClient) callListRooms(ctx context.Context, in *ListRoomsRequest) (*ListRoomsResponse, error) {
	out := new(ListRoomsResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[1], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *roomServiceJSONClient) DeleteRoom(ctx context.Context, in *DeleteRoomRequest) (*DeleteRoomResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithMethodName(ctx, "DeleteRoom")
	caller := c.callDeleteRoom
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *DeleteRoomRequest) (*DeleteRoomResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*DeleteRoomRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*DeleteRoomRequest) when calling interceptor")
					}
					return c.callDeleteRoom(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*DeleteRoomResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*DeleteRoomResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *roomServiceJSONClient) callDeleteRoom(ctx context.Context, in *DeleteRoomRequest) (*DeleteRoomResponse, error) {
	out := new(DeleteRoomResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[2], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *roomServiceJSONClient) ListParticipants(ctx context.Context, in *ListParticipantsRequest) (*ListParticipantsResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithMethodName(ctx, "ListParticipants")
	caller := c.callListParticipants
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ListParticipantsRequest) (*ListParticipantsResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListParticipantsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListParticipantsRequest) when calling interceptor")
					}
					return c.callListParticipants(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListParticipantsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListParticipantsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *roomServiceJSONClient) callListParticipants(ctx context.Context, in *ListParticipantsRequest) (*ListParticipantsResponse, error) {
	out := new(ListParticipantsResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[3], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *roomServiceJSONClient) GetParticipant(ctx context.Context, in *RoomParticipantIdentity) (*ParticipantInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithMethodName(ctx, "GetParticipant")
	caller := c.callGetParticipant
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *RoomParticipantIdentity) (*ParticipantInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*RoomParticipantIdentity)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*RoomParticipantIdentity) when calling interceptor")
					}
					return c.callGetParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ParticipantInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ParticipantInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *roomServiceJSONClient) callGetParticipant(ctx context.Context, in *RoomParticipantIdentity) (*ParticipantInfo, error) {
	out := new(ParticipantInfo)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[4], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *roomServiceJSONClient) RemoveParticipant(ctx context.Context, in *RoomParticipantIdentity) (*RemoveParticipantResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithMethodName(ctx, "RemoveParticipant")
	caller := c.callRemoveParticipant
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *RoomParticipantIdentity) (*RemoveParticipantResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*RoomParticipantIdentity)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*RoomParticipantIdentity) when calling interceptor")
					}
					return c.callRemoveParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*RemoveParticipantResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*RemoveParticipantResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *roomServiceJSONClient) callRemoveParticipant(ctx context.Context, in *RoomParticipantIdentity) (*RemoveParticipantResponse, error) {
	out := new(RemoveParticipantResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[5], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *roomServiceJSONClient) MutePublishedTrack(ctx context.Context, in *MuteRoomTrackRequest) (*MuteRoomTrackResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithMethodName(ctx, "MutePublishedTrack")
	caller := c.callMutePublishedTrack
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *MuteRoomTrackRequest) (*MuteRoomTrackResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*MuteRoomTrackRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*MuteRoomTrackRequest) when calling interceptor")
					}
					return c.callMutePublishedTrack(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*MuteRoomTrackResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*MuteRoomTrackResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *roomServiceJSONClient) callMutePublishedTrack(ctx context.Context, in *MuteRoomTrackRequest) (*MuteRoomTrackResponse, error) {
	out := new(MuteRoomTrackResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[6], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *roomServiceJSONClient) UpdateParticipant(ctx context.Context, in *UpdateParticipantRequest) (*ParticipantInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithMethodName(ctx, "UpdateParticipant")
	caller := c.callUpdateParticipant
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *UpdateParticipantRequest) (*ParticipantInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateParticipantRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateParticipantRequest) when calling interceptor")
					}
					return c.callUpdateParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ParticipantInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ParticipantInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *roomServiceJSONClient) callUpdateParticipant(ctx context.Context, in *UpdateParticipantRequest) (*ParticipantInfo, error) {
	out := new(ParticipantInfo)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[7], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *roomServiceJSONClient) UpdateSubscriptions(ctx context.Context, in *UpdateSubscriptionsRequest) (*UpdateSubscriptionsResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithMethodName(ctx, "UpdateSubscriptions")
	caller := c.callUpdateSubscriptions
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *UpdateSubscriptionsRequest) (*UpdateSubscriptionsResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateSubscriptionsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateSubscriptionsRequest) when calling interceptor")
					}
					return c.callUpdateSubscriptions(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*UpdateSubscriptionsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*UpdateSubscriptionsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *roomServiceJSONClient) callUpdateSubscriptions(ctx context.Context, in *UpdateSubscriptionsRequest) (*UpdateSubscriptionsResponse, error) {
	out := new(UpdateSubscriptionsResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[8], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *roomServiceJSONClient) SendData(ctx context.Context, in *SendDataRequest) (*SendDataResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithMethodName(ctx, "SendData")
	caller := c.callSendData
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *SendDataRequest) (*SendDataResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*SendDataRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*SendDataRequest) when calling interceptor")
					}
					return c.callSendData(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SendDataResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SendDataResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *roomServiceJSONClient) callSendData(ctx context.Context, in *SendDataRequest) (*SendDataResponse, error) {
	out := new(SendDataResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[9], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *roomServiceJSONClient) UpdateRoomMetadata(ctx context.Context, in *UpdateRoomMetadataRequest) (*Room, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithMethodName(ctx, "UpdateRoomMetadata")
	caller := c.callUpdateRoomMetadata
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *UpdateRoomMetadataRequest) (*Room, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateRoomMetadataRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateRoomMetadataRequest) when calling interceptor")
					}
					return c.callUpdateRoomMetadata(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*Room)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*Room) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *roomServiceJSONClient) callUpdateRoomMetadata(ctx context.Context, in *UpdateRoomMetadataRequest) (*Room, error) {
	out := new(Room)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[10], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *roomServiceJSONClient) ForwardParticipant(ctx context.Context, in *ForwardParticipantRequest) (*ForwardParticipantResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithMethodName(ctx, "ForwardParticipant")
	caller := c.callForwardParticipant
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ForwardParticipantRequest) (*ForwardParticipantResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ForwardParticipantRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ForwardParticipantRequest) when calling interceptor")
					}
					return c.callForwardParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ForwardParticipantResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ForwardParticipantResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *roomServiceJSONClient) callForwardParticipant(ctx context.Context, in *ForwardParticipantRequest) (*ForwardParticipantResponse, error) {
	out := new(ForwardParticipantResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[11], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *roomServiceJSONClient) MoveParticipant(ctx context.Context, in *MoveParticipantRequest) (*MoveParticipantResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithMethodName(ctx, "MoveParticipant")
	caller := c.callMoveParticipant
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *MoveParticipantRequest) (*MoveParticipantResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*MoveParticipantRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*MoveParticipantRequest) when calling interceptor")
					}
					return c.callMoveParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*MoveParticipantResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*MoveParticipantResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *roomServiceJSONClient) callMoveParticipant(ctx context.Context, in *MoveParticipantRequest) (*MoveParticipantResponse, error) {
	out := new(MoveParticipantResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[12], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

// ==========================
// RoomService Server Handler
// ==========================

type roomServiceServer struct {
	RoomService
	interceptor      twirp.Interceptor
	hooks            *twirp.ServerHooks
	pathPrefix       string // prefix for routing
	jsonSkipDefaults bool   // do not include unpopulated fields (default values) in the response
	jsonCamelCase    bool   // JSON fields are serialized as lowerCamelCase rather than keeping the original proto names
}

// NewRoomServiceServer builds a TwirpServer that can be used as an http.Handler to handle
// HTTP requests that are routed to the right method in the provided svc implementation.
// The opts are twirp.ServerOption modifiers, for example twirp.WithServerHooks(hooks).
func NewRoomServiceServer(svc RoomService, opts ...interface{}) TwirpServer {
	serverOpts := newServerOpts(opts)

	// Using ReadOpt allows backwards and forwards compatibility with new options in the future
	jsonSkipDefaults := false
	_ = serverOpts.ReadOpt("jsonSkipDefaults", &jsonSkipDefaults)
	jsonCamelCase := false
	_ = serverOpts.ReadOpt("jsonCamelCase", &jsonCamelCase)
	var pathPrefix string
	if ok := serverOpts.ReadOpt("pathPrefix", &pathPrefix); !ok {
		pathPrefix = "/twirp" // default prefix
	}

	return &roomServiceServer{
		RoomService:      svc,
		hooks:            serverOpts.Hooks,
		interceptor:      twirp.ChainInterceptors(serverOpts.Interceptors...),
		pathPrefix:       pathPrefix,
		jsonSkipDefaults: jsonSkipDefaults,
		jsonCamelCase:    jsonCamelCase,
	}
}

// writeError writes an HTTP response with a valid Twirp error format, and triggers hooks.
// If err is not a twirp.Error, it will get wrapped with twirp.InternalErrorWith(err)
func (s *roomServiceServer) writeError(ctx context.Context, resp http.ResponseWriter, err error) {
	writeError(ctx, resp, err, s.hooks)
}

// handleRequestBodyError is used to handle error when the twirp server cannot read request
func (s *roomServiceServer) handleRequestBodyError(ctx context.Context, resp http.ResponseWriter, msg string, err error) {
	if context.Canceled == ctx.Err() {
		s.writeError(ctx, resp, twirp.NewError(twirp.Canceled, "failed to read request: context canceled"))
		return
	}
	if context.DeadlineExceeded == ctx.Err() {
		s.writeError(ctx, resp, twirp.NewError(twirp.DeadlineExceeded, "failed to read request: deadline exceeded"))
		return
	}
	s.writeError(ctx, resp, twirp.WrapError(malformedRequestError(msg), err))
}

// RoomServicePathPrefix is a convenience constant that may identify URL paths.
// Should be used with caution, it only matches routes generated by Twirp Go clients,
// with the default "/twirp" prefix and default CamelCase service and method names.
// More info: https://twitchtv.github.io/twirp/docs/routing.html
const RoomServicePathPrefix = "/twirp/livekit.RoomService/"

func (s *roomServiceServer) ServeHTTP(resp http.ResponseWriter, req *http.Request) {
	ctx := req.Context()
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "RoomService")
	ctx = ctxsetters.WithResponseWriter(ctx, resp)

	var err error
	ctx, err = callRequestReceived(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	if req.Method != "POST" {
		msg := fmt.Sprintf("unsupported method %q (only POST is allowed)", req.Method)
		s.writeError(ctx, resp, badRouteError(msg, req.Method, req.URL.Path))
		return
	}

	// Verify path format: [<prefix>]/<package>.<Service>/<Method>
	prefix, pkgService, method := parseTwirpPath(req.URL.Path)
	if pkgService != "livekit.RoomService" {
		msg := fmt.Sprintf("no handler for path %q", req.URL.Path)
		s.writeError(ctx, resp, badRouteError(msg, req.Method, req.URL.Path))
		return
	}
	if prefix != s.pathPrefix {
		msg := fmt.Sprintf("invalid path prefix %q, expected %q, on path %q", prefix, s.pathPrefix, req.URL.Path)
		s.writeError(ctx, resp, badRouteError(msg, req.Method, req.URL.Path))
		return
	}

	switch method {
	case "CreateRoom":
		s.serveCreateRoom(ctx, resp, req)
		return
	case "ListRooms":
		s.serveListRooms(ctx, resp, req)
		return
	case "DeleteRoom":
		s.serveDeleteRoom(ctx, resp, req)
		return
	case "ListParticipants":
		s.serveListParticipants(ctx, resp, req)
		return
	case "GetParticipant":
		s.serveGetParticipant(ctx, resp, req)
		return
	case "RemoveParticipant":
		s.serveRemoveParticipant(ctx, resp, req)
		return
	case "MutePublishedTrack":
		s.serveMutePublishedTrack(ctx, resp, req)
		return
	case "UpdateParticipant":
		s.serveUpdateParticipant(ctx, resp, req)
		return
	case "UpdateSubscriptions":
		s.serveUpdateSubscriptions(ctx, resp, req)
		return
	case "SendData":
		s.serveSendData(ctx, resp, req)
		return
	case "UpdateRoomMetadata":
		s.serveUpdateRoomMetadata(ctx, resp, req)
		return
	case "ForwardParticipant":
		s.serveForwardParticipant(ctx, resp, req)
		return
	case "MoveParticipant":
		s.serveMoveParticipant(ctx, resp, req)
		return
	default:
		msg := fmt.Sprintf("no handler for path %q", req.URL.Path)
		s.writeError(ctx, resp, badRouteError(msg, req.Method, req.URL.Path))
		return
	}
}

func (s *roomServiceServer) serveCreateRoom(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveCreateRoomJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveCreateRoomProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *roomServiceServer) serveCreateRoomJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "CreateRoom")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(CreateRoomRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.RoomService.CreateRoom
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *CreateRoomRequest) (*Room, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateRoomRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateRoomRequest) when calling interceptor")
					}
					return s.RoomService.CreateRoom(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*Room)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*Room) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *Room
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *Room and nil error while calling CreateRoom. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *roomServiceServer) serveCreateRoomProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "CreateRoom")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(CreateRoomRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.RoomService.CreateRoom
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *CreateRoomRequest) (*Room, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateRoomRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateRoomRequest) when calling interceptor")
					}
					return s.RoomService.CreateRoom(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*Room)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*Room) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *Room
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *Room and nil error while calling CreateRoom. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *roomServiceServer) serveListRooms(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveListRoomsJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveListRoomsProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *roomServiceServer) serveListRoomsJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "ListRooms")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(ListRoomsRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.RoomService.ListRooms
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ListRoomsRequest) (*ListRoomsResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListRoomsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListRoomsRequest) when calling interceptor")
					}
					return s.RoomService.ListRooms(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListRoomsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListRoomsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ListRoomsResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ListRoomsResponse and nil error while calling ListRooms. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *roomServiceServer) serveListRoomsProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "ListRooms")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(ListRoomsRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.RoomService.ListRooms
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ListRoomsRequest) (*ListRoomsResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListRoomsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListRoomsRequest) when calling interceptor")
					}
					return s.RoomService.ListRooms(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListRoomsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListRoomsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ListRoomsResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ListRoomsResponse and nil error while calling ListRooms. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *roomServiceServer) serveDeleteRoom(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveDeleteRoomJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveDeleteRoomProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *roomServiceServer) serveDeleteRoomJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "DeleteRoom")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(DeleteRoomRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.RoomService.DeleteRoom
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *DeleteRoomRequest) (*DeleteRoomResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*DeleteRoomRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*DeleteRoomRequest) when calling interceptor")
					}
					return s.RoomService.DeleteRoom(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*DeleteRoomResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*DeleteRoomResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *DeleteRoomResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *DeleteRoomResponse and nil error while calling DeleteRoom. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *roomServiceServer) serveDeleteRoomProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "DeleteRoom")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(DeleteRoomRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.RoomService.DeleteRoom
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *DeleteRoomRequest) (*DeleteRoomResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*DeleteRoomRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*DeleteRoomRequest) when calling interceptor")
					}
					return s.RoomService.DeleteRoom(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*DeleteRoomResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*DeleteRoomResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *DeleteRoomResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *DeleteRoomResponse and nil error while calling DeleteRoom. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *roomServiceServer) serveListParticipants(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveListParticipantsJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveListParticipantsProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *roomServiceServer) serveListParticipantsJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "ListParticipants")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(ListParticipantsRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.RoomService.ListParticipants
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ListParticipantsRequest) (*ListParticipantsResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListParticipantsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListParticipantsRequest) when calling interceptor")
					}
					return s.RoomService.ListParticipants(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListParticipantsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListParticipantsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ListParticipantsResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ListParticipantsResponse and nil error while calling ListParticipants. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *roomServiceServer) serveListParticipantsProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "ListParticipants")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(ListParticipantsRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.RoomService.ListParticipants
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ListParticipantsRequest) (*ListParticipantsResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListParticipantsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListParticipantsRequest) when calling interceptor")
					}
					return s.RoomService.ListParticipants(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListParticipantsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListParticipantsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ListParticipantsResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ListParticipantsResponse and nil error while calling ListParticipants. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *roomServiceServer) serveGetParticipant(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveGetParticipantJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveGetParticipantProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *roomServiceServer) serveGetParticipantJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "GetParticipant")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(RoomParticipantIdentity)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.RoomService.GetParticipant
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *RoomParticipantIdentity) (*ParticipantInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*RoomParticipantIdentity)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*RoomParticipantIdentity) when calling interceptor")
					}
					return s.RoomService.GetParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ParticipantInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ParticipantInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ParticipantInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ParticipantInfo and nil error while calling GetParticipant. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *roomServiceServer) serveGetParticipantProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "GetParticipant")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(RoomParticipantIdentity)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.RoomService.GetParticipant
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *RoomParticipantIdentity) (*ParticipantInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*RoomParticipantIdentity)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*RoomParticipantIdentity) when calling interceptor")
					}
					return s.RoomService.GetParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ParticipantInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ParticipantInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ParticipantInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ParticipantInfo and nil error while calling GetParticipant. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *roomServiceServer) serveRemoveParticipant(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveRemoveParticipantJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveRemoveParticipantProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *roomServiceServer) serveRemoveParticipantJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "RemoveParticipant")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(RoomParticipantIdentity)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.RoomService.RemoveParticipant
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *RoomParticipantIdentity) (*RemoveParticipantResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*RoomParticipantIdentity)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*RoomParticipantIdentity) when calling interceptor")
					}
					return s.RoomService.RemoveParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*RemoveParticipantResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*RemoveParticipantResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *RemoveParticipantResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *RemoveParticipantResponse and nil error while calling RemoveParticipant. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *roomServiceServer) serveRemoveParticipantProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "RemoveParticipant")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(RoomParticipantIdentity)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.RoomService.RemoveParticipant
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *RoomParticipantIdentity) (*RemoveParticipantResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*RoomParticipantIdentity)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*RoomParticipantIdentity) when calling interceptor")
					}
					return s.RoomService.RemoveParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*RemoveParticipantResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*RemoveParticipantResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *RemoveParticipantResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *RemoveParticipantResponse and nil error while calling RemoveParticipant. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *roomServiceServer) serveMutePublishedTrack(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveMutePublishedTrackJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveMutePublishedTrackProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *roomServiceServer) serveMutePublishedTrackJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "MutePublishedTrack")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(MuteRoomTrackRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.RoomService.MutePublishedTrack
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *MuteRoomTrackRequest) (*MuteRoomTrackResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*MuteRoomTrackRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*MuteRoomTrackRequest) when calling interceptor")
					}
					return s.RoomService.MutePublishedTrack(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*MuteRoomTrackResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*MuteRoomTrackResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *MuteRoomTrackResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *MuteRoomTrackResponse and nil error while calling MutePublishedTrack. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *roomServiceServer) serveMutePublishedTrackProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "MutePublishedTrack")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(MuteRoomTrackRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.RoomService.MutePublishedTrack
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *MuteRoomTrackRequest) (*MuteRoomTrackResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*MuteRoomTrackRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*MuteRoomTrackRequest) when calling interceptor")
					}
					return s.RoomService.MutePublishedTrack(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*MuteRoomTrackResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*MuteRoomTrackResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *MuteRoomTrackResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *MuteRoomTrackResponse and nil error while calling MutePublishedTrack. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *roomServiceServer) serveUpdateParticipant(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveUpdateParticipantJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveUpdateParticipantProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *roomServiceServer) serveUpdateParticipantJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "UpdateParticipant")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(UpdateParticipantRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.RoomService.UpdateParticipant
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *UpdateParticipantRequest) (*ParticipantInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateParticipantRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateParticipantRequest) when calling interceptor")
					}
					return s.RoomService.UpdateParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ParticipantInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ParticipantInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ParticipantInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ParticipantInfo and nil error while calling UpdateParticipant. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *roomServiceServer) serveUpdateParticipantProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "UpdateParticipant")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(UpdateParticipantRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.RoomService.UpdateParticipant
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *UpdateParticipantRequest) (*ParticipantInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateParticipantRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateParticipantRequest) when calling interceptor")
					}
					return s.RoomService.UpdateParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ParticipantInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ParticipantInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ParticipantInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ParticipantInfo and nil error while calling UpdateParticipant. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *roomServiceServer) serveUpdateSubscriptions(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveUpdateSubscriptionsJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveUpdateSubscriptionsProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *roomServiceServer) serveUpdateSubscriptionsJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "UpdateSubscriptions")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(UpdateSubscriptionsRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.RoomService.UpdateSubscriptions
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *UpdateSubscriptionsRequest) (*UpdateSubscriptionsResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateSubscriptionsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateSubscriptionsRequest) when calling interceptor")
					}
					return s.RoomService.UpdateSubscriptions(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*UpdateSubscriptionsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*UpdateSubscriptionsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *UpdateSubscriptionsResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *UpdateSubscriptionsResponse and nil error while calling UpdateSubscriptions. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *roomServiceServer) serveUpdateSubscriptionsProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "UpdateSubscriptions")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(UpdateSubscriptionsRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.RoomService.UpdateSubscriptions
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *UpdateSubscriptionsRequest) (*UpdateSubscriptionsResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateSubscriptionsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateSubscriptionsRequest) when calling interceptor")
					}
					return s.RoomService.UpdateSubscriptions(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*UpdateSubscriptionsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*UpdateSubscriptionsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *UpdateSubscriptionsResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *UpdateSubscriptionsResponse and nil error while calling UpdateSubscriptions. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *roomServiceServer) serveSendData(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveSendDataJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveSendDataProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *roomServiceServer) serveSendDataJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "SendData")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(SendDataRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.RoomService.SendData
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *SendDataRequest) (*SendDataResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*SendDataRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*SendDataRequest) when calling interceptor")
					}
					return s.RoomService.SendData(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SendDataResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SendDataResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *SendDataResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *SendDataResponse and nil error while calling SendData. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *roomServiceServer) serveSendDataProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "SendData")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(SendDataRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.RoomService.SendData
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *SendDataRequest) (*SendDataResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*SendDataRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*SendDataRequest) when calling interceptor")
					}
					return s.RoomService.SendData(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SendDataResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SendDataResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *SendDataResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *SendDataResponse and nil error while calling SendData. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *roomServiceServer) serveUpdateRoomMetadata(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveUpdateRoomMetadataJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveUpdateRoomMetadataProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *roomServiceServer) serveUpdateRoomMetadataJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "UpdateRoomMetadata")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(UpdateRoomMetadataRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.RoomService.UpdateRoomMetadata
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *UpdateRoomMetadataRequest) (*Room, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateRoomMetadataRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateRoomMetadataRequest) when calling interceptor")
					}
					return s.RoomService.UpdateRoomMetadata(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*Room)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*Room) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *Room
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *Room and nil error while calling UpdateRoomMetadata. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *roomServiceServer) serveUpdateRoomMetadataProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "UpdateRoomMetadata")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(UpdateRoomMetadataRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.RoomService.UpdateRoomMetadata
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *UpdateRoomMetadataRequest) (*Room, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateRoomMetadataRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateRoomMetadataRequest) when calling interceptor")
					}
					return s.RoomService.UpdateRoomMetadata(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*Room)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*Room) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *Room
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *Room and nil error while calling UpdateRoomMetadata. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *roomServiceServer) serveForwardParticipant(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveForwardParticipantJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveForwardParticipantProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *roomServiceServer) serveForwardParticipantJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "ForwardParticipant")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(ForwardParticipantRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.RoomService.ForwardParticipant
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ForwardParticipantRequest) (*ForwardParticipantResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ForwardParticipantRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ForwardParticipantRequest) when calling interceptor")
					}
					return s.RoomService.ForwardParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ForwardParticipantResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ForwardParticipantResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ForwardParticipantResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ForwardParticipantResponse and nil error while calling ForwardParticipant. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *roomServiceServer) serveForwardParticipantProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "ForwardParticipant")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(ForwardParticipantRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.RoomService.ForwardParticipant
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ForwardParticipantRequest) (*ForwardParticipantResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ForwardParticipantRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ForwardParticipantRequest) when calling interceptor")
					}
					return s.RoomService.ForwardParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ForwardParticipantResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ForwardParticipantResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ForwardParticipantResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ForwardParticipantResponse and nil error while calling ForwardParticipant. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *roomServiceServer) serveMoveParticipant(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveMoveParticipantJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveMoveParticipantProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *roomServiceServer) serveMoveParticipantJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "MoveParticipant")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(MoveParticipantRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.RoomService.MoveParticipant
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *MoveParticipantRequest) (*MoveParticipantResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*MoveParticipantRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*MoveParticipantRequest) when calling interceptor")
					}
					return s.RoomService.MoveParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*MoveParticipantResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*MoveParticipantResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *MoveParticipantResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *MoveParticipantResponse and nil error while calling MoveParticipant. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *roomServiceServer) serveMoveParticipantProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "MoveParticipant")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(MoveParticipantRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.RoomService.MoveParticipant
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *MoveParticipantRequest) (*MoveParticipantResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*MoveParticipantRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*MoveParticipantRequest) when calling interceptor")
					}
					return s.RoomService.MoveParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*MoveParticipantResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*MoveParticipantResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *MoveParticipantResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *MoveParticipantResponse and nil error while calling MoveParticipant. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *roomServiceServer) ServiceDescriptor() ([]byte, int) {
	return twirpFileDescriptor3, 0
}

func (s *roomServiceServer) ProtocGenTwirpVersion() string {
	return "v8.1.3"
}

// PathPrefix returns the base service path, in the form: "/<prefix>/<package>.<Service>/"
// that is everything in a Twirp route except for the <Method>. This can be used for routing,
// for example to identify the requests that are targeted to this service in a mux.
func (s *roomServiceServer) PathPrefix() string {
	return baseServicePath(s.pathPrefix, "livekit", "RoomService")
}

var twirpFileDescriptor3 = []byte{
	// 1375 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x57, 0xe9, 0x6e, 0xdb, 0xc6,
	0x16, 0xbe, 0x5a, 0x23, 0x1d, 0x79, 0xd3, 0xc4, 0x89, 0x29, 0x3a, 0x8b, 0x42, 0xdf, 0x8b, 0xab,
	0xdc, 0xdc, 0x38, 0xad, 0x8a, 0x22, 0x81, 0xd1, 0xcd, 0x8e, 0x9d, 0xd4, 0x48, 0x0c, 0xa8, 0x74,
	0x8a, 0x2e, 0x40, 0xc1, 0x52, 0xe2, 0xc4, 0x19, 0x58, 0x5c, 0xca, 0x19, 0xba, 0xd1, 0xbf, 0xfe,
	0xcc, 0x3b, 0xf4, 0x0d, 0xfa, 0x06, 0x7d, 0x87, 0xfe, 0xec, 0x53, 0xf4, 0x1d, 0x0a, 0x14, 0xb3,
	0x88, 0x1c, 0x4a, 0x94, 0x92, 0x06, 0x2d, 0xd0, 0x7f, 0xe2, 0x39, 0xdf, 0x9c, 0x39, 0xf3, 0x9d,
	0x55, 0x80, 0xc6, 0xe4, 0x02, 0x9f, 0x13, 0xe6, 0xc4, 0x61, 0xe8, 0xef, 0x46, 0x71, 0xc8, 0x42,
	0x74, 0x49, 0xc9, 0xcc, 0xcd, 0xa9, 0xd2, 0x0f, 0x3d, 0x3c, 0xa6, 0x52, 0x9d, 0x49, 0xf1, 0x59,
	0x8c, 0xe9, 0x54, 0x7a, 0x6d, 0x2a, 0x75, 0xcf, 0x70, 0xc0, 0x1c, 0x8f, 0xd0, 0xc8, 0x65, 0xa3,
	0x17, 0x52, 0x6b, 0xfd, 0x5e, 0x81, 0xf6, 0xc3, 0x18, 0xbb, 0x0c, 0xdb, 0x61, 0xe8, 0xdb, 0xf8,
	0xbb, 0x04, 0x53, 0x86, 0x10, 0x54, 0x03, 0xd7, 0xc7, 0x46, 0xa9, 0x5b, 0xea, 0x35, 0x6d, 0xf1,
	0x1b, 0xdd, 0x84, 0x16, 0x77, 0xc5, 0x89, 0x62, 0x4c, 0x31, 0x33, 0x56, 0x84, 0x0a, 0xb8, 0x68,
	0x20, 0x24, 0x68, 0x07, 0x56, 0xb1, 0x1f, 0xb1, 0x89, 0xc3, 0x88, 0x8f, 0xc3, 0x84, 0x19, 0xe5,
	0x6e, 0xa9, 0xb7, 0x6a, 0xaf, 0x08, 0xe1, 0x33, 0x29, 0x43, 0x77, 0xa0, 0xed, 0xe1, 0xc8, 0x8d,
	0x59, 0x12, 0xe3, 0x14, 0x08, 0x02, 0xb8, 0x91, 0x2a, 0xa6, 0xe0, 0xdb, 0xb0, 0xe1, 0xbb, 0x2f,
	0x1d, 0x2e, 0x25, 0x23, 0x12, 0xb9, 0x01, 0xa3, 0x46, 0x45, 0x60, 0xd7, 0x7d, 0xf7, 0xe5, 0x40,
	0x13, 0xa3, 0x2d, 0xb8, 0x14, 0x84, 0x1e, 0x76, 0x88, 0x67, 0x54, 0x85, 0x67, 0x75, 0xfe, 0x79,
	0xec, 0x21, 0x13, 0x1a, 0x3e, 0x66, 0xae, 0xe7, 0x32, 0xd7, 0xa8, 0x09, 0x4d, 0xfa, 0x8d, 0xee,
	0x40, 0x5d, 0x52, 0x65, 0xd4, 0xbb, 0xa5, 0x5e, 0xab, 0x7f, 0x79, 0x57, 0x71, 0xb5, 0xcb, 0xc9,
	0x38, 0x12, 0x2a, 0x5b, 0x41, 0xd0, 0xff, 0xa0, 0xed, 0x93, 0xc0, 0x89, 0xc6, 0xee, 0x24, 0x4c,
	0x98, 0xe3, 0xe1, 0xb1, 0x3b, 0x31, 0x2e, 0x29, 0x6f, 0x48, 0x30, 0x90, 0xf2, 0x43, 0x2e, 0x16,
	0x58, 0xee, 0x78, 0x0e, 0xdb, 0xc8, 0x3c, 0xd7, 0xb1, 0xb7, 0x60, 0x85, 0x4e, 0x82, 0x91, 0x43,
	0x59, 0x8c, 0x5d, 0x9f, 0x1a, 0xcd, 0x6e, 0xa9, 0xd7, 0xb0, 0x5b, 0x5c, 0x76, 0x2a, 0x45, 0xe8,
	0x3f, 0xb0, 0x16, 0x63, 0x6e, 0xcc, 0xc1, 0x81, 0x3b, 0x1c, 0x63, 0xcf, 0x58, 0x15, 0xa0, 0x55,
	0x29, 0x3d, 0x92, 0x42, 0xd4, 0x87, 0xba, 0x88, 0x31, 0x35, 0xd6, 0xba, 0x95, 0x5e, 0xab, 0x6f,
	0xe6, 0x9e, 0xb3, 0xcf, 0x55, 0x87, 0x2a, 0xfa, 0xb6, 0x42, 0x5a, 0x3f, 0x97, 0x00, 0xb2, 0xc7,
	0xa2, 0xfb, 0x50, 0xe5, 0x11, 0x15, 0x81, 0x6f, 0xf5, 0x77, 0x72, 0x06, 0x1e, 0x86, 0x7e, 0x14,
	0x52, 0xc2, 0xb0, 0x22, 0x46, 0xe6, 0x8a, 0x2d, 0x0e, 0xa0, 0x4f, 0xa0, 0xa5, 0x85, 0x49, 0x44,
	0xa9, 0xd5, 0xbf, 0x91, 0x9e, 0xdf, 0x4f, 0x58, 0xa8, 0xc5, 0x4b, 0x59, 0xd0, 0x8f, 0xa0, 0x77,
	0xa0, 0xce, 0x62, 0x77, 0x74, 0x4e, 0x45, 0xde, 0xb4, 0xfa, 0x46, 0xee, 0xf0, 0x33, 0xae, 0x9a,
	0x46, 0x44, 0xe2, 0xac, 0xc7, 0xd0, 0x4c, 0x1f, 0x86, 0xf6, 0x00, 0xa6, 0xa9, 0x8d, 0xa9, 0x51,
	0x7a, 0x2d, 0x01, 0x1a, 0xda, 0xea, 0xc1, 0xc6, 0x53, 0x42, 0x19, 0x07, 0x4d, 0x9f, 0x85, 0x36,
	0xa1, 0xc6, 0xd3, 0x5e, 0x9a, 0x6a, 0xda, 0xf2, 0xc3, 0x7a, 0x00, 0x6d, 0x0d, 0x49, 0xa3, 0x30,
	0xa0, 0x18, 0xed, 0x40, 0x8d, 0x73, 0x30, 0xbd, 0x75, 0x35, 0x77, 0xab, 0x2d, 0x75, 0xd6, 0x7f,
	0xa1, 0x7d, 0x88, 0xc7, 0x78, 0xae, 0xce, 0x52, 0xba, 0x9b, 0x92, 0x49, 0x6b, 0x13, 0x90, 0x0e,
	0x94, 0x77, 0x58, 0x77, 0x61, 0x8b, 0x5f, 0xac, 0xe7, 0xfc, 0x32, 0x23, 0x5f, 0x82, 0x31, 0x0f,
	0x57, 0xee, 0x7e, 0x00, 0x2b, 0xb9, 0x8a, 0x92, 0x5e, 0x67, 0x74, 0x6b, 0x87, 0x8e, 0x83, 0xe7,
	0xa1, 0x9d, 0x43, 0x5b, 0xc7, 0xb0, 0xc5, 0x1d, 0xd3, 0x41, 0x1e, 0x0e, 0x18, 0x61, 0x93, 0x22,
	0x47, 0x78, 0xf9, 0x11, 0xa5, 0x17, 0x71, 0x6d, 0xda, 0xe9, 0xb7, 0xb5, 0x0d, 0x1d, 0x1b, 0xfb,
	0xe1, 0x05, 0xd6, 0x8c, 0xa5, 0x0f, 0x9e, 0xc0, 0xe6, 0x49, 0x22, 0x49, 0x10, 0xb1, 0x5f, 0xf2,
	0xda, 0x65, 0x97, 0xa0, 0x6d, 0x68, 0x8a, 0x74, 0x71, 0x28, 0xf1, 0x44, 0x5a, 0x36, 0xed, 0x86,
	0x10, 0x9c, 0x12, 0x8f, 0x07, 0xd9, 0x4f, 0x18, 0x96, 0x3d, 0xa3, 0x61, 0xcb, 0x0f, 0x6b, 0x1f,
	0xae, 0xcc, 0x5c, 0xad, 0x98, 0xeb, 0x41, 0x4d, 0x1c, 0x55, 0xe5, 0x81, 0x52, 0xca, 0x04, 0x4c,
	0x90, 0x25, 0x01, 0xd6, 0x2f, 0x65, 0x30, 0x3e, 0x8f, 0x3c, 0x97, 0xe5, 0xdf, 0xf6, 0x76, 0x4f,
	0xd0, 0x5b, 0x58, 0x65, 0xa6, 0x85, 0x7d, 0x04, 0x10, 0xe1, 0xd8, 0x27, 0x94, 0x92, 0x30, 0x10,
	0xcf, 0xd0, 0xcb, 0x4e, 0xbb, 0x7c, 0x90, 0xa2, 0x6c, 0xed, 0x44, 0xda, 0xe9, 0x6b, 0x5a, 0xa7,
	0xff, 0x0c, 0xc0, 0x65, 0x2c, 0x26, 0xc3, 0x84, 0x61, 0xde, 0x1a, 0x79, 0x7a, 0xbc, 0x9b, 0xda,
	0x5c, 0xf4, 0xac, 0xdd, 0xfd, 0xf4, 0xcc, 0x51, 0xc0, 0xe2, 0x89, 0xad, 0x19, 0x31, 0x3f, 0x84,
	0xf5, 0x19, 0x35, 0xda, 0x80, 0xca, 0x39, 0x9e, 0x28, 0x12, 0xf8, 0x4f, 0x1e, 0x8d, 0x0b, 0x77,
	0x9c, 0x60, 0x45, 0x80, 0xfc, 0xd8, 0x2b, 0x3f, 0x28, 0x59, 0xbf, 0x96, 0xc0, 0x94, 0xf7, 0x9e,
	0x26, 0x43, 0x3a, 0x8a, 0x49, 0xc4, 0x48, 0x18, 0xd0, 0xb7, 0x25, 0xf4, 0x3a, 0x40, 0x9a, 0x13,
	0x7c, 0xa2, 0xf0, 0x02, 0x6f, 0x4e, 0x93, 0x82, 0xa2, 0x6b, 0xd0, 0xa4, 0xf2, 0x9a, 0x21, 0x56,
	0x99, 0x91, 0x09, 0xd0, 0x31, 0x20, 0xad, 0x20, 0x1c, 0xd5, 0xb3, 0x6a, 0x33, 0x0d, 0x47, 0xe3,
	0x47, 0x24, 0x07, 0xb5, 0xdb, 0xd1, 0xac, 0xc8, 0xba, 0x0e, 0xdb, 0x85, 0xaf, 0x52, 0x25, 0xf0,
	0xaa, 0x0c, 0xeb, 0xa7, 0x38, 0xf0, 0x0e, 0x5d, 0xe6, 0x2e, 0x7b, 0x2a, 0x82, 0xaa, 0xc8, 0x0d,
	0xfe, 0xcc, 0x15, 0x5b, 0xfc, 0x46, 0xff, 0x87, 0xea, 0x39, 0x09, 0x64, 0xc6, 0xaf, 0x69, 0xc5,
	0xcd, 0x6d, 0x0d, 0xdc, 0xd1, 0x39, 0x66, 0xbb, 0x4f, 0x48, 0xe0, 0xd9, 0x02, 0x85, 0xee, 0xc2,
	0x86, 0x87, 0x29, 0x23, 0x81, 0xcb, 0x3d, 0x90, 0xb4, 0x54, 0x39, 0x2d, 0x07, 0x65, 0xa3, 0x64,
	0xaf, 0x6b, 0x3a, 0x41, 0xd0, 0xfb, 0x70, 0x55, 0x87, 0x2b, 0x5e, 0x89, 0x4a, 0x96, 0xa6, 0x7d,
	0x45, 0xd3, 0x1e, 0xa7, 0x4a, 0xd4, 0x81, 0x1a, 0x0b, 0x23, 0x32, 0x92, 0xc9, 0xf6, 0xe9, 0xbf,
	0x6c, 0xf9, 0xf9, 0xaa, 0x54, 0x12, 0xdd, 0x36, 0x0c, 0x46, 0x58, 0x0c, 0xd4, 0x15, 0x5b, 0x7e,
	0x1c, 0x34, 0xa0, 0xee, 0x08, 0x88, 0x85, 0x60, 0x23, 0x63, 0x42, 0xd1, 0xf3, 0x04, 0x3a, 0x92,
	0x3d, 0x5e, 0xa8, 0x27, 0xaa, 0x20, 0x5e, 0x93, 0x12, 0x69, 0x1d, 0x95, 0xf3, 0x75, 0x64, 0xfd,
	0x50, 0x81, 0xb6, 0x1c, 0x72, 0xc1, 0x73, 0x72, 0x96, 0xc4, 0xc2, 0xf7, 0xc2, 0x3d, 0xe8, 0xed,
	0xd7, 0x9c, 0xca, 0x9f, 0x58, 0x73, 0xaa, 0xc5, 0x6b, 0x4e, 0xb6, 0xb1, 0xd4, 0xfe, 0xd1, 0x1b,
	0x4b, 0xb6, 0x8a, 0xc0, 0x1b, 0xaf, 0x22, 0x17, 0xd0, 0x79, 0x14, 0xc6, 0xdf, 0xbb, 0xb1, 0xf7,
	0x17, 0xf4, 0xcc, 0xdb, 0xf9, 0x8c, 0x16, 0x67, 0x65, 0xef, 0xd4, 0xb3, 0x99, 0x3b, 0x63, 0x5d,
	0x03, 0xb3, 0xe8, 0x5e, 0x95, 0x65, 0x14, 0xae, 0x9e, 0xcc, 0x8e, 0xa8, 0xbf, 0xdd, 0xa5, 0x0e,
	0x6c, 0x9d, 0x14, 0xcf, 0xc5, 0xfe, 0x8f, 0x0d, 0x68, 0x71, 0xcc, 0x29, 0x8e, 0x2f, 0xc8, 0x08,
	0xa3, 0xfb, 0x00, 0xd9, 0xfe, 0x8e, 0x32, 0x9e, 0xe7, 0x96, 0x7a, 0x33, 0xbf, 0x97, 0xa0, 0x03,
	0x68, 0xa6, 0xab, 0x0c, 0xea, 0xa4, 0xba, 0xd9, 0x45, 0xc8, 0x34, 0x8b, 0x54, 0x6a, 0x20, 0x1e,
	0x01, 0x64, 0xbb, 0x8a, 0x76, 0xf9, 0xdc, 0xa6, 0x63, 0x6e, 0x17, 0xea, 0x94, 0x99, 0x2f, 0xe4,
	0xfe, 0x95, 0xcb, 0xf4, 0x6e, 0xee, 0xda, 0x82, 0xbd, 0xc7, 0xbc, 0xb5, 0x04, 0xa1, 0x0c, 0x3f,
	0x85, 0xb5, 0xc7, 0x58, 0x57, 0x69, 0x66, 0x17, 0x6c, 0x31, 0xe6, 0xc2, 0x45, 0x08, 0x7d, 0x05,
	0xed, 0xb9, 0x7d, 0xe5, 0x0d, 0x0c, 0x5a, 0x19, 0x62, 0xd1, 0xb6, 0x83, 0x4e, 0x01, 0xf1, 0x95,
	0x63, 0x90, 0x0c, 0xc7, 0x84, 0xbe, 0xc0, 0x9e, 0x18, 0x10, 0xe8, 0x7a, 0x7a, 0xb2, 0x68, 0x15,
	0x32, 0x6f, 0x2c, 0x52, 0x2b, 0xa3, 0x03, 0x68, 0xcf, 0x0d, 0x6b, 0x74, 0xeb, 0xb5, 0x83, 0x7c,
	0x09, 0x03, 0xdf, 0xc2, 0xe5, 0x82, 0x81, 0x85, 0x76, 0x66, 0x6c, 0x16, 0x0d, 0x69, 0xf3, 0xdf,
	0xcb, 0x41, 0xca, 0xe7, 0x8f, 0xa1, 0x31, 0x6d, 0xf4, 0x28, 0xf3, 0x63, 0x66, 0x0a, 0x9a, 0x9d,
	0x02, 0x8d, 0x32, 0xf0, 0x18, 0xd0, 0xfc, 0x54, 0x40, 0xd6, 0xcc, 0xe5, 0x05, 0x23, 0x63, 0xb6,
	0x3e, 0xbe, 0x01, 0x34, 0xdf, 0x16, 0x34, 0x43, 0x0b, 0x7b, 0x95, 0xb9, 0xb3, 0x14, 0xa3, 0xfc,
	0x7c, 0x06, 0xeb, 0x33, 0x25, 0x8e, 0x6e, 0x66, 0xf1, 0x2c, 0xec, 0x38, 0x66, 0x77, 0x31, 0x40,
	0x5a, 0x3d, 0x78, 0xf4, 0xf5, 0xce, 0x19, 0x61, 0x2f, 0x92, 0xe1, 0xee, 0x28, 0xf4, 0xef, 0x29,
	0xf4, 0x3d, 0xf1, 0x57, 0x7f, 0x14, 0x8e, 0xa7, 0x82, 0x9f, 0xca, 0xab, 0x4f, 0xc9, 0x05, 0x7e,
	0xc2, 0x23, 0xcc, 0x55, 0xbf, 0x95, 0xd7, 0xd4, 0xf7, 0xde, 0x9e, 0x10, 0x0c, 0xeb, 0xe2, 0xc8,
	0x7b, 0x7f, 0x04, 0x00, 0x00, 0xff, 0xff, 0xf7, 0x1c, 0x24, 0x10, 0x86, 0x10, 0x00, 0x00,
}
