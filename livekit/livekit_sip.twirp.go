// Code generated by protoc-gen-twirp v8.1.3, DO NOT EDIT.
// source: livekit_sip.proto

package livekit

import context "context"
import fmt "fmt"
import http "net/http"
import io "io"
import json "encoding/json"
import strconv "strconv"
import strings "strings"

import protojson "google.golang.org/protobuf/encoding/protojson"
import proto "google.golang.org/protobuf/proto"
import twirp "github.com/twitchtv/twirp"
import ctxsetters "github.com/twitchtv/twirp/ctxsetters"

import google_protobuf2 "google.golang.org/protobuf/types/known/emptypb"

// Version compatibility assertion.
// If the constant is not defined in the package, that likely means
// the package needs to be updated to work with this generated code.
// See https://twitchtv.github.io/twirp/docs/version_matrix.html
const _ = twirp.TwirpPackageMinVersion_8_1_0

// =============
// SIP Interface
// =============

type SIP interface {
	ListSIPTrunk(context.Context, *ListSIPTrunkRequest) (*ListSIPTrunkResponse, error)

	CreateSIPInboundTrunk(context.Context, *CreateSIPInboundTrunkRequest) (*SIPInboundTrunkInfo, error)

	CreateSIPOutboundTrunk(context.Context, *CreateSIPOutboundTrunkRequest) (*SIPOutboundTrunkInfo, error)

	UpdateSIPInboundTrunk(context.Context, *UpdateSIPInboundTrunkRequest) (*SIPInboundTrunkInfo, error)

	UpdateSIPOutboundTrunk(context.Context, *UpdateSIPOutboundTrunkRequest) (*SIPOutboundTrunkInfo, error)

	GetSIPInboundTrunk(context.Context, *GetSIPInboundTrunkRequest) (*GetSIPInboundTrunkResponse, error)

	GetSIPOutboundTrunk(context.Context, *GetSIPOutboundTrunkRequest) (*GetSIPOutboundTrunkResponse, error)

	ListSIPInboundTrunk(context.Context, *ListSIPInboundTrunkRequest) (*ListSIPInboundTrunkResponse, error)

	ListSIPOutboundTrunk(context.Context, *ListSIPOutboundTrunkRequest) (*ListSIPOutboundTrunkResponse, error)

	DeleteSIPTrunk(context.Context, *DeleteSIPTrunkRequest) (*SIPTrunkInfo, error)

	CreateSIPDispatchRule(context.Context, *CreateSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error)

	UpdateSIPDispatchRule(context.Context, *UpdateSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error)

	ListSIPDispatchRule(context.Context, *ListSIPDispatchRuleRequest) (*ListSIPDispatchRuleResponse, error)

	DeleteSIPDispatchRule(context.Context, *DeleteSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error)

	CreateSIPParticipant(context.Context, *CreateSIPParticipantRequest) (*SIPParticipantInfo, error)

	TransferSIPParticipant(context.Context, *TransferSIPParticipantRequest) (*google_protobuf2.Empty, error)
}

// ===================
// SIP Protobuf Client
// ===================

type sIPProtobufClient struct {
	client      HTTPClient
	urls        [16]string
	interceptor twirp.Interceptor
	opts        twirp.ClientOptions
}

// NewSIPProtobufClient creates a Protobuf client that implements the SIP interface.
// It communicates using Protobuf and can be configured with a custom HTTPClient.
func NewSIPProtobufClient(baseURL string, client HTTPClient, opts ...twirp.ClientOption) SIP {
	if c, ok := client.(*http.Client); ok {
		client = withoutRedirects(c)
	}

	clientOpts := twirp.ClientOptions{}
	for _, o := range opts {
		o(&clientOpts)
	}

	// Using ReadOpt allows backwards and forwards compatibility with new options in the future
	literalURLs := false
	_ = clientOpts.ReadOpt("literalURLs", &literalURLs)
	var pathPrefix string
	if ok := clientOpts.ReadOpt("pathPrefix", &pathPrefix); !ok {
		pathPrefix = "/twirp" // default prefix
	}

	// Build method URLs: <baseURL>[<prefix>]/<package>.<Service>/<Method>
	serviceURL := sanitizeBaseURL(baseURL)
	serviceURL += baseServicePath(pathPrefix, "livekit", "SIP")
	urls := [16]string{
		serviceURL + "ListSIPTrunk",
		serviceURL + "CreateSIPInboundTrunk",
		serviceURL + "CreateSIPOutboundTrunk",
		serviceURL + "UpdateSIPInboundTrunk",
		serviceURL + "UpdateSIPOutboundTrunk",
		serviceURL + "GetSIPInboundTrunk",
		serviceURL + "GetSIPOutboundTrunk",
		serviceURL + "ListSIPInboundTrunk",
		serviceURL + "ListSIPOutboundTrunk",
		serviceURL + "DeleteSIPTrunk",
		serviceURL + "CreateSIPDispatchRule",
		serviceURL + "UpdateSIPDispatchRule",
		serviceURL + "ListSIPDispatchRule",
		serviceURL + "DeleteSIPDispatchRule",
		serviceURL + "CreateSIPParticipant",
		serviceURL + "TransferSIPParticipant",
	}

	return &sIPProtobufClient{
		client:      client,
		urls:        urls,
		interceptor: twirp.ChainInterceptors(clientOpts.Interceptors...),
		opts:        clientOpts,
	}
}

func (c *sIPProtobufClient) ListSIPTrunk(ctx context.Context, in *ListSIPTrunkRequest) (*ListSIPTrunkResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "ListSIPTrunk")
	caller := c.callListSIPTrunk
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ListSIPTrunkRequest) (*ListSIPTrunkResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListSIPTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListSIPTrunkRequest) when calling interceptor")
					}
					return c.callListSIPTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListSIPTrunkResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListSIPTrunkResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPProtobufClient) callListSIPTrunk(ctx context.Context, in *ListSIPTrunkRequest) (*ListSIPTrunkResponse, error) {
	out := new(ListSIPTrunkResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[0], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPProtobufClient) CreateSIPInboundTrunk(ctx context.Context, in *CreateSIPInboundTrunkRequest) (*SIPInboundTrunkInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "CreateSIPInboundTrunk")
	caller := c.callCreateSIPInboundTrunk
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *CreateSIPInboundTrunkRequest) (*SIPInboundTrunkInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateSIPInboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateSIPInboundTrunkRequest) when calling interceptor")
					}
					return c.callCreateSIPInboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPInboundTrunkInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPInboundTrunkInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPProtobufClient) callCreateSIPInboundTrunk(ctx context.Context, in *CreateSIPInboundTrunkRequest) (*SIPInboundTrunkInfo, error) {
	out := new(SIPInboundTrunkInfo)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[1], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPProtobufClient) CreateSIPOutboundTrunk(ctx context.Context, in *CreateSIPOutboundTrunkRequest) (*SIPOutboundTrunkInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "CreateSIPOutboundTrunk")
	caller := c.callCreateSIPOutboundTrunk
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *CreateSIPOutboundTrunkRequest) (*SIPOutboundTrunkInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateSIPOutboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateSIPOutboundTrunkRequest) when calling interceptor")
					}
					return c.callCreateSIPOutboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPOutboundTrunkInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPOutboundTrunkInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPProtobufClient) callCreateSIPOutboundTrunk(ctx context.Context, in *CreateSIPOutboundTrunkRequest) (*SIPOutboundTrunkInfo, error) {
	out := new(SIPOutboundTrunkInfo)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[2], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPProtobufClient) UpdateSIPInboundTrunk(ctx context.Context, in *UpdateSIPInboundTrunkRequest) (*SIPInboundTrunkInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "UpdateSIPInboundTrunk")
	caller := c.callUpdateSIPInboundTrunk
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *UpdateSIPInboundTrunkRequest) (*SIPInboundTrunkInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateSIPInboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateSIPInboundTrunkRequest) when calling interceptor")
					}
					return c.callUpdateSIPInboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPInboundTrunkInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPInboundTrunkInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPProtobufClient) callUpdateSIPInboundTrunk(ctx context.Context, in *UpdateSIPInboundTrunkRequest) (*SIPInboundTrunkInfo, error) {
	out := new(SIPInboundTrunkInfo)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[3], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPProtobufClient) UpdateSIPOutboundTrunk(ctx context.Context, in *UpdateSIPOutboundTrunkRequest) (*SIPOutboundTrunkInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "UpdateSIPOutboundTrunk")
	caller := c.callUpdateSIPOutboundTrunk
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *UpdateSIPOutboundTrunkRequest) (*SIPOutboundTrunkInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateSIPOutboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateSIPOutboundTrunkRequest) when calling interceptor")
					}
					return c.callUpdateSIPOutboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPOutboundTrunkInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPOutboundTrunkInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPProtobufClient) callUpdateSIPOutboundTrunk(ctx context.Context, in *UpdateSIPOutboundTrunkRequest) (*SIPOutboundTrunkInfo, error) {
	out := new(SIPOutboundTrunkInfo)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[4], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPProtobufClient) GetSIPInboundTrunk(ctx context.Context, in *GetSIPInboundTrunkRequest) (*GetSIPInboundTrunkResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "GetSIPInboundTrunk")
	caller := c.callGetSIPInboundTrunk
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *GetSIPInboundTrunkRequest) (*GetSIPInboundTrunkResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*GetSIPInboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*GetSIPInboundTrunkRequest) when calling interceptor")
					}
					return c.callGetSIPInboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*GetSIPInboundTrunkResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*GetSIPInboundTrunkResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPProtobufClient) callGetSIPInboundTrunk(ctx context.Context, in *GetSIPInboundTrunkRequest) (*GetSIPInboundTrunkResponse, error) {
	out := new(GetSIPInboundTrunkResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[5], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPProtobufClient) GetSIPOutboundTrunk(ctx context.Context, in *GetSIPOutboundTrunkRequest) (*GetSIPOutboundTrunkResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "GetSIPOutboundTrunk")
	caller := c.callGetSIPOutboundTrunk
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *GetSIPOutboundTrunkRequest) (*GetSIPOutboundTrunkResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*GetSIPOutboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*GetSIPOutboundTrunkRequest) when calling interceptor")
					}
					return c.callGetSIPOutboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*GetSIPOutboundTrunkResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*GetSIPOutboundTrunkResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPProtobufClient) callGetSIPOutboundTrunk(ctx context.Context, in *GetSIPOutboundTrunkRequest) (*GetSIPOutboundTrunkResponse, error) {
	out := new(GetSIPOutboundTrunkResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[6], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPProtobufClient) ListSIPInboundTrunk(ctx context.Context, in *ListSIPInboundTrunkRequest) (*ListSIPInboundTrunkResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "ListSIPInboundTrunk")
	caller := c.callListSIPInboundTrunk
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ListSIPInboundTrunkRequest) (*ListSIPInboundTrunkResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListSIPInboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListSIPInboundTrunkRequest) when calling interceptor")
					}
					return c.callListSIPInboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListSIPInboundTrunkResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListSIPInboundTrunkResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPProtobufClient) callListSIPInboundTrunk(ctx context.Context, in *ListSIPInboundTrunkRequest) (*ListSIPInboundTrunkResponse, error) {
	out := new(ListSIPInboundTrunkResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[7], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPProtobufClient) ListSIPOutboundTrunk(ctx context.Context, in *ListSIPOutboundTrunkRequest) (*ListSIPOutboundTrunkResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "ListSIPOutboundTrunk")
	caller := c.callListSIPOutboundTrunk
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ListSIPOutboundTrunkRequest) (*ListSIPOutboundTrunkResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListSIPOutboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListSIPOutboundTrunkRequest) when calling interceptor")
					}
					return c.callListSIPOutboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListSIPOutboundTrunkResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListSIPOutboundTrunkResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPProtobufClient) callListSIPOutboundTrunk(ctx context.Context, in *ListSIPOutboundTrunkRequest) (*ListSIPOutboundTrunkResponse, error) {
	out := new(ListSIPOutboundTrunkResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[8], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPProtobufClient) DeleteSIPTrunk(ctx context.Context, in *DeleteSIPTrunkRequest) (*SIPTrunkInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "DeleteSIPTrunk")
	caller := c.callDeleteSIPTrunk
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *DeleteSIPTrunkRequest) (*SIPTrunkInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*DeleteSIPTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*DeleteSIPTrunkRequest) when calling interceptor")
					}
					return c.callDeleteSIPTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPTrunkInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPTrunkInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPProtobufClient) callDeleteSIPTrunk(ctx context.Context, in *DeleteSIPTrunkRequest) (*SIPTrunkInfo, error) {
	out := new(SIPTrunkInfo)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[9], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPProtobufClient) CreateSIPDispatchRule(ctx context.Context, in *CreateSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "CreateSIPDispatchRule")
	caller := c.callCreateSIPDispatchRule
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *CreateSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateSIPDispatchRuleRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateSIPDispatchRuleRequest) when calling interceptor")
					}
					return c.callCreateSIPDispatchRule(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPDispatchRuleInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPDispatchRuleInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPProtobufClient) callCreateSIPDispatchRule(ctx context.Context, in *CreateSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error) {
	out := new(SIPDispatchRuleInfo)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[10], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPProtobufClient) UpdateSIPDispatchRule(ctx context.Context, in *UpdateSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "UpdateSIPDispatchRule")
	caller := c.callUpdateSIPDispatchRule
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *UpdateSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateSIPDispatchRuleRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateSIPDispatchRuleRequest) when calling interceptor")
					}
					return c.callUpdateSIPDispatchRule(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPDispatchRuleInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPDispatchRuleInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPProtobufClient) callUpdateSIPDispatchRule(ctx context.Context, in *UpdateSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error) {
	out := new(SIPDispatchRuleInfo)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[11], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPProtobufClient) ListSIPDispatchRule(ctx context.Context, in *ListSIPDispatchRuleRequest) (*ListSIPDispatchRuleResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "ListSIPDispatchRule")
	caller := c.callListSIPDispatchRule
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ListSIPDispatchRuleRequest) (*ListSIPDispatchRuleResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListSIPDispatchRuleRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListSIPDispatchRuleRequest) when calling interceptor")
					}
					return c.callListSIPDispatchRule(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListSIPDispatchRuleResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListSIPDispatchRuleResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPProtobufClient) callListSIPDispatchRule(ctx context.Context, in *ListSIPDispatchRuleRequest) (*ListSIPDispatchRuleResponse, error) {
	out := new(ListSIPDispatchRuleResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[12], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPProtobufClient) DeleteSIPDispatchRule(ctx context.Context, in *DeleteSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "DeleteSIPDispatchRule")
	caller := c.callDeleteSIPDispatchRule
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *DeleteSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*DeleteSIPDispatchRuleRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*DeleteSIPDispatchRuleRequest) when calling interceptor")
					}
					return c.callDeleteSIPDispatchRule(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPDispatchRuleInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPDispatchRuleInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPProtobufClient) callDeleteSIPDispatchRule(ctx context.Context, in *DeleteSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error) {
	out := new(SIPDispatchRuleInfo)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[13], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPProtobufClient) CreateSIPParticipant(ctx context.Context, in *CreateSIPParticipantRequest) (*SIPParticipantInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "CreateSIPParticipant")
	caller := c.callCreateSIPParticipant
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *CreateSIPParticipantRequest) (*SIPParticipantInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateSIPParticipantRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateSIPParticipantRequest) when calling interceptor")
					}
					return c.callCreateSIPParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPParticipantInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPParticipantInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPProtobufClient) callCreateSIPParticipant(ctx context.Context, in *CreateSIPParticipantRequest) (*SIPParticipantInfo, error) {
	out := new(SIPParticipantInfo)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[14], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPProtobufClient) TransferSIPParticipant(ctx context.Context, in *TransferSIPParticipantRequest) (*google_protobuf2.Empty, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "TransferSIPParticipant")
	caller := c.callTransferSIPParticipant
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *TransferSIPParticipantRequest) (*google_protobuf2.Empty, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*TransferSIPParticipantRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*TransferSIPParticipantRequest) when calling interceptor")
					}
					return c.callTransferSIPParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*google_protobuf2.Empty)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*google_protobuf2.Empty) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPProtobufClient) callTransferSIPParticipant(ctx context.Context, in *TransferSIPParticipantRequest) (*google_protobuf2.Empty, error) {
	out := new(google_protobuf2.Empty)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[15], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

// ===============
// SIP JSON Client
// ===============

type sIPJSONClient struct {
	client      HTTPClient
	urls        [16]string
	interceptor twirp.Interceptor
	opts        twirp.ClientOptions
}

// NewSIPJSONClient creates a JSON client that implements the SIP interface.
// It communicates using JSON and can be configured with a custom HTTPClient.
func NewSIPJSONClient(baseURL string, client HTTPClient, opts ...twirp.ClientOption) SIP {
	if c, ok := client.(*http.Client); ok {
		client = withoutRedirects(c)
	}

	clientOpts := twirp.ClientOptions{}
	for _, o := range opts {
		o(&clientOpts)
	}

	// Using ReadOpt allows backwards and forwards compatibility with new options in the future
	literalURLs := false
	_ = clientOpts.ReadOpt("literalURLs", &literalURLs)
	var pathPrefix string
	if ok := clientOpts.ReadOpt("pathPrefix", &pathPrefix); !ok {
		pathPrefix = "/twirp" // default prefix
	}

	// Build method URLs: <baseURL>[<prefix>]/<package>.<Service>/<Method>
	serviceURL := sanitizeBaseURL(baseURL)
	serviceURL += baseServicePath(pathPrefix, "livekit", "SIP")
	urls := [16]string{
		serviceURL + "ListSIPTrunk",
		serviceURL + "CreateSIPInboundTrunk",
		serviceURL + "CreateSIPOutboundTrunk",
		serviceURL + "UpdateSIPInboundTrunk",
		serviceURL + "UpdateSIPOutboundTrunk",
		serviceURL + "GetSIPInboundTrunk",
		serviceURL + "GetSIPOutboundTrunk",
		serviceURL + "ListSIPInboundTrunk",
		serviceURL + "ListSIPOutboundTrunk",
		serviceURL + "DeleteSIPTrunk",
		serviceURL + "CreateSIPDispatchRule",
		serviceURL + "UpdateSIPDispatchRule",
		serviceURL + "ListSIPDispatchRule",
		serviceURL + "DeleteSIPDispatchRule",
		serviceURL + "CreateSIPParticipant",
		serviceURL + "TransferSIPParticipant",
	}

	return &sIPJSONClient{
		client:      client,
		urls:        urls,
		interceptor: twirp.ChainInterceptors(clientOpts.Interceptors...),
		opts:        clientOpts,
	}
}

func (c *sIPJSONClient) ListSIPTrunk(ctx context.Context, in *ListSIPTrunkRequest) (*ListSIPTrunkResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "ListSIPTrunk")
	caller := c.callListSIPTrunk
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ListSIPTrunkRequest) (*ListSIPTrunkResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListSIPTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListSIPTrunkRequest) when calling interceptor")
					}
					return c.callListSIPTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListSIPTrunkResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListSIPTrunkResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPJSONClient) callListSIPTrunk(ctx context.Context, in *ListSIPTrunkRequest) (*ListSIPTrunkResponse, error) {
	out := new(ListSIPTrunkResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[0], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPJSONClient) CreateSIPInboundTrunk(ctx context.Context, in *CreateSIPInboundTrunkRequest) (*SIPInboundTrunkInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "CreateSIPInboundTrunk")
	caller := c.callCreateSIPInboundTrunk
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *CreateSIPInboundTrunkRequest) (*SIPInboundTrunkInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateSIPInboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateSIPInboundTrunkRequest) when calling interceptor")
					}
					return c.callCreateSIPInboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPInboundTrunkInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPInboundTrunkInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPJSONClient) callCreateSIPInboundTrunk(ctx context.Context, in *CreateSIPInboundTrunkRequest) (*SIPInboundTrunkInfo, error) {
	out := new(SIPInboundTrunkInfo)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[1], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPJSONClient) CreateSIPOutboundTrunk(ctx context.Context, in *CreateSIPOutboundTrunkRequest) (*SIPOutboundTrunkInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "CreateSIPOutboundTrunk")
	caller := c.callCreateSIPOutboundTrunk
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *CreateSIPOutboundTrunkRequest) (*SIPOutboundTrunkInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateSIPOutboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateSIPOutboundTrunkRequest) when calling interceptor")
					}
					return c.callCreateSIPOutboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPOutboundTrunkInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPOutboundTrunkInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPJSONClient) callCreateSIPOutboundTrunk(ctx context.Context, in *CreateSIPOutboundTrunkRequest) (*SIPOutboundTrunkInfo, error) {
	out := new(SIPOutboundTrunkInfo)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[2], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPJSONClient) UpdateSIPInboundTrunk(ctx context.Context, in *UpdateSIPInboundTrunkRequest) (*SIPInboundTrunkInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "UpdateSIPInboundTrunk")
	caller := c.callUpdateSIPInboundTrunk
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *UpdateSIPInboundTrunkRequest) (*SIPInboundTrunkInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateSIPInboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateSIPInboundTrunkRequest) when calling interceptor")
					}
					return c.callUpdateSIPInboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPInboundTrunkInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPInboundTrunkInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPJSONClient) callUpdateSIPInboundTrunk(ctx context.Context, in *UpdateSIPInboundTrunkRequest) (*SIPInboundTrunkInfo, error) {
	out := new(SIPInboundTrunkInfo)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[3], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPJSONClient) UpdateSIPOutboundTrunk(ctx context.Context, in *UpdateSIPOutboundTrunkRequest) (*SIPOutboundTrunkInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "UpdateSIPOutboundTrunk")
	caller := c.callUpdateSIPOutboundTrunk
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *UpdateSIPOutboundTrunkRequest) (*SIPOutboundTrunkInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateSIPOutboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateSIPOutboundTrunkRequest) when calling interceptor")
					}
					return c.callUpdateSIPOutboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPOutboundTrunkInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPOutboundTrunkInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPJSONClient) callUpdateSIPOutboundTrunk(ctx context.Context, in *UpdateSIPOutboundTrunkRequest) (*SIPOutboundTrunkInfo, error) {
	out := new(SIPOutboundTrunkInfo)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[4], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPJSONClient) GetSIPInboundTrunk(ctx context.Context, in *GetSIPInboundTrunkRequest) (*GetSIPInboundTrunkResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "GetSIPInboundTrunk")
	caller := c.callGetSIPInboundTrunk
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *GetSIPInboundTrunkRequest) (*GetSIPInboundTrunkResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*GetSIPInboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*GetSIPInboundTrunkRequest) when calling interceptor")
					}
					return c.callGetSIPInboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*GetSIPInboundTrunkResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*GetSIPInboundTrunkResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPJSONClient) callGetSIPInboundTrunk(ctx context.Context, in *GetSIPInboundTrunkRequest) (*GetSIPInboundTrunkResponse, error) {
	out := new(GetSIPInboundTrunkResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[5], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPJSONClient) GetSIPOutboundTrunk(ctx context.Context, in *GetSIPOutboundTrunkRequest) (*GetSIPOutboundTrunkResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "GetSIPOutboundTrunk")
	caller := c.callGetSIPOutboundTrunk
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *GetSIPOutboundTrunkRequest) (*GetSIPOutboundTrunkResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*GetSIPOutboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*GetSIPOutboundTrunkRequest) when calling interceptor")
					}
					return c.callGetSIPOutboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*GetSIPOutboundTrunkResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*GetSIPOutboundTrunkResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPJSONClient) callGetSIPOutboundTrunk(ctx context.Context, in *GetSIPOutboundTrunkRequest) (*GetSIPOutboundTrunkResponse, error) {
	out := new(GetSIPOutboundTrunkResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[6], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPJSONClient) ListSIPInboundTrunk(ctx context.Context, in *ListSIPInboundTrunkRequest) (*ListSIPInboundTrunkResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "ListSIPInboundTrunk")
	caller := c.callListSIPInboundTrunk
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ListSIPInboundTrunkRequest) (*ListSIPInboundTrunkResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListSIPInboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListSIPInboundTrunkRequest) when calling interceptor")
					}
					return c.callListSIPInboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListSIPInboundTrunkResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListSIPInboundTrunkResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPJSONClient) callListSIPInboundTrunk(ctx context.Context, in *ListSIPInboundTrunkRequest) (*ListSIPInboundTrunkResponse, error) {
	out := new(ListSIPInboundTrunkResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[7], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPJSONClient) ListSIPOutboundTrunk(ctx context.Context, in *ListSIPOutboundTrunkRequest) (*ListSIPOutboundTrunkResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "ListSIPOutboundTrunk")
	caller := c.callListSIPOutboundTrunk
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ListSIPOutboundTrunkRequest) (*ListSIPOutboundTrunkResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListSIPOutboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListSIPOutboundTrunkRequest) when calling interceptor")
					}
					return c.callListSIPOutboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListSIPOutboundTrunkResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListSIPOutboundTrunkResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPJSONClient) callListSIPOutboundTrunk(ctx context.Context, in *ListSIPOutboundTrunkRequest) (*ListSIPOutboundTrunkResponse, error) {
	out := new(ListSIPOutboundTrunkResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[8], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPJSONClient) DeleteSIPTrunk(ctx context.Context, in *DeleteSIPTrunkRequest) (*SIPTrunkInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "DeleteSIPTrunk")
	caller := c.callDeleteSIPTrunk
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *DeleteSIPTrunkRequest) (*SIPTrunkInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*DeleteSIPTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*DeleteSIPTrunkRequest) when calling interceptor")
					}
					return c.callDeleteSIPTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPTrunkInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPTrunkInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPJSONClient) callDeleteSIPTrunk(ctx context.Context, in *DeleteSIPTrunkRequest) (*SIPTrunkInfo, error) {
	out := new(SIPTrunkInfo)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[9], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPJSONClient) CreateSIPDispatchRule(ctx context.Context, in *CreateSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "CreateSIPDispatchRule")
	caller := c.callCreateSIPDispatchRule
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *CreateSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateSIPDispatchRuleRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateSIPDispatchRuleRequest) when calling interceptor")
					}
					return c.callCreateSIPDispatchRule(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPDispatchRuleInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPDispatchRuleInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPJSONClient) callCreateSIPDispatchRule(ctx context.Context, in *CreateSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error) {
	out := new(SIPDispatchRuleInfo)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[10], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPJSONClient) UpdateSIPDispatchRule(ctx context.Context, in *UpdateSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "UpdateSIPDispatchRule")
	caller := c.callUpdateSIPDispatchRule
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *UpdateSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateSIPDispatchRuleRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateSIPDispatchRuleRequest) when calling interceptor")
					}
					return c.callUpdateSIPDispatchRule(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPDispatchRuleInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPDispatchRuleInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPJSONClient) callUpdateSIPDispatchRule(ctx context.Context, in *UpdateSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error) {
	out := new(SIPDispatchRuleInfo)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[11], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPJSONClient) ListSIPDispatchRule(ctx context.Context, in *ListSIPDispatchRuleRequest) (*ListSIPDispatchRuleResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "ListSIPDispatchRule")
	caller := c.callListSIPDispatchRule
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ListSIPDispatchRuleRequest) (*ListSIPDispatchRuleResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListSIPDispatchRuleRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListSIPDispatchRuleRequest) when calling interceptor")
					}
					return c.callListSIPDispatchRule(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListSIPDispatchRuleResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListSIPDispatchRuleResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPJSONClient) callListSIPDispatchRule(ctx context.Context, in *ListSIPDispatchRuleRequest) (*ListSIPDispatchRuleResponse, error) {
	out := new(ListSIPDispatchRuleResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[12], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPJSONClient) DeleteSIPDispatchRule(ctx context.Context, in *DeleteSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "DeleteSIPDispatchRule")
	caller := c.callDeleteSIPDispatchRule
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *DeleteSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*DeleteSIPDispatchRuleRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*DeleteSIPDispatchRuleRequest) when calling interceptor")
					}
					return c.callDeleteSIPDispatchRule(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPDispatchRuleInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPDispatchRuleInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPJSONClient) callDeleteSIPDispatchRule(ctx context.Context, in *DeleteSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error) {
	out := new(SIPDispatchRuleInfo)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[13], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPJSONClient) CreateSIPParticipant(ctx context.Context, in *CreateSIPParticipantRequest) (*SIPParticipantInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "CreateSIPParticipant")
	caller := c.callCreateSIPParticipant
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *CreateSIPParticipantRequest) (*SIPParticipantInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateSIPParticipantRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateSIPParticipantRequest) when calling interceptor")
					}
					return c.callCreateSIPParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPParticipantInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPParticipantInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPJSONClient) callCreateSIPParticipant(ctx context.Context, in *CreateSIPParticipantRequest) (*SIPParticipantInfo, error) {
	out := new(SIPParticipantInfo)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[14], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *sIPJSONClient) TransferSIPParticipant(ctx context.Context, in *TransferSIPParticipantRequest) (*google_protobuf2.Empty, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithMethodName(ctx, "TransferSIPParticipant")
	caller := c.callTransferSIPParticipant
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *TransferSIPParticipantRequest) (*google_protobuf2.Empty, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*TransferSIPParticipantRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*TransferSIPParticipantRequest) when calling interceptor")
					}
					return c.callTransferSIPParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*google_protobuf2.Empty)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*google_protobuf2.Empty) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *sIPJSONClient) callTransferSIPParticipant(ctx context.Context, in *TransferSIPParticipantRequest) (*google_protobuf2.Empty, error) {
	out := new(google_protobuf2.Empty)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[15], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

// ==================
// SIP Server Handler
// ==================

type sIPServer struct {
	SIP
	interceptor      twirp.Interceptor
	hooks            *twirp.ServerHooks
	pathPrefix       string // prefix for routing
	jsonSkipDefaults bool   // do not include unpopulated fields (default values) in the response
	jsonCamelCase    bool   // JSON fields are serialized as lowerCamelCase rather than keeping the original proto names
}

// NewSIPServer builds a TwirpServer that can be used as an http.Handler to handle
// HTTP requests that are routed to the right method in the provided svc implementation.
// The opts are twirp.ServerOption modifiers, for example twirp.WithServerHooks(hooks).
func NewSIPServer(svc SIP, opts ...interface{}) TwirpServer {
	serverOpts := newServerOpts(opts)

	// Using ReadOpt allows backwards and forwards compatibility with new options in the future
	jsonSkipDefaults := false
	_ = serverOpts.ReadOpt("jsonSkipDefaults", &jsonSkipDefaults)
	jsonCamelCase := false
	_ = serverOpts.ReadOpt("jsonCamelCase", &jsonCamelCase)
	var pathPrefix string
	if ok := serverOpts.ReadOpt("pathPrefix", &pathPrefix); !ok {
		pathPrefix = "/twirp" // default prefix
	}

	return &sIPServer{
		SIP:              svc,
		hooks:            serverOpts.Hooks,
		interceptor:      twirp.ChainInterceptors(serverOpts.Interceptors...),
		pathPrefix:       pathPrefix,
		jsonSkipDefaults: jsonSkipDefaults,
		jsonCamelCase:    jsonCamelCase,
	}
}

// writeError writes an HTTP response with a valid Twirp error format, and triggers hooks.
// If err is not a twirp.Error, it will get wrapped with twirp.InternalErrorWith(err)
func (s *sIPServer) writeError(ctx context.Context, resp http.ResponseWriter, err error) {
	writeError(ctx, resp, err, s.hooks)
}

// handleRequestBodyError is used to handle error when the twirp server cannot read request
func (s *sIPServer) handleRequestBodyError(ctx context.Context, resp http.ResponseWriter, msg string, err error) {
	if context.Canceled == ctx.Err() {
		s.writeError(ctx, resp, twirp.NewError(twirp.Canceled, "failed to read request: context canceled"))
		return
	}
	if context.DeadlineExceeded == ctx.Err() {
		s.writeError(ctx, resp, twirp.NewError(twirp.DeadlineExceeded, "failed to read request: deadline exceeded"))
		return
	}
	s.writeError(ctx, resp, twirp.WrapError(malformedRequestError(msg), err))
}

// SIPPathPrefix is a convenience constant that may identify URL paths.
// Should be used with caution, it only matches routes generated by Twirp Go clients,
// with the default "/twirp" prefix and default CamelCase service and method names.
// More info: https://twitchtv.github.io/twirp/docs/routing.html
const SIPPathPrefix = "/twirp/livekit.SIP/"

func (s *sIPServer) ServeHTTP(resp http.ResponseWriter, req *http.Request) {
	ctx := req.Context()
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "SIP")
	ctx = ctxsetters.WithResponseWriter(ctx, resp)

	var err error
	ctx, err = callRequestReceived(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	if req.Method != "POST" {
		msg := fmt.Sprintf("unsupported method %q (only POST is allowed)", req.Method)
		s.writeError(ctx, resp, badRouteError(msg, req.Method, req.URL.Path))
		return
	}

	// Verify path format: [<prefix>]/<package>.<Service>/<Method>
	prefix, pkgService, method := parseTwirpPath(req.URL.Path)
	if pkgService != "livekit.SIP" {
		msg := fmt.Sprintf("no handler for path %q", req.URL.Path)
		s.writeError(ctx, resp, badRouteError(msg, req.Method, req.URL.Path))
		return
	}
	if prefix != s.pathPrefix {
		msg := fmt.Sprintf("invalid path prefix %q, expected %q, on path %q", prefix, s.pathPrefix, req.URL.Path)
		s.writeError(ctx, resp, badRouteError(msg, req.Method, req.URL.Path))
		return
	}

	switch method {
	case "ListSIPTrunk":
		s.serveListSIPTrunk(ctx, resp, req)
		return
	case "CreateSIPInboundTrunk":
		s.serveCreateSIPInboundTrunk(ctx, resp, req)
		return
	case "CreateSIPOutboundTrunk":
		s.serveCreateSIPOutboundTrunk(ctx, resp, req)
		return
	case "UpdateSIPInboundTrunk":
		s.serveUpdateSIPInboundTrunk(ctx, resp, req)
		return
	case "UpdateSIPOutboundTrunk":
		s.serveUpdateSIPOutboundTrunk(ctx, resp, req)
		return
	case "GetSIPInboundTrunk":
		s.serveGetSIPInboundTrunk(ctx, resp, req)
		return
	case "GetSIPOutboundTrunk":
		s.serveGetSIPOutboundTrunk(ctx, resp, req)
		return
	case "ListSIPInboundTrunk":
		s.serveListSIPInboundTrunk(ctx, resp, req)
		return
	case "ListSIPOutboundTrunk":
		s.serveListSIPOutboundTrunk(ctx, resp, req)
		return
	case "DeleteSIPTrunk":
		s.serveDeleteSIPTrunk(ctx, resp, req)
		return
	case "CreateSIPDispatchRule":
		s.serveCreateSIPDispatchRule(ctx, resp, req)
		return
	case "UpdateSIPDispatchRule":
		s.serveUpdateSIPDispatchRule(ctx, resp, req)
		return
	case "ListSIPDispatchRule":
		s.serveListSIPDispatchRule(ctx, resp, req)
		return
	case "DeleteSIPDispatchRule":
		s.serveDeleteSIPDispatchRule(ctx, resp, req)
		return
	case "CreateSIPParticipant":
		s.serveCreateSIPParticipant(ctx, resp, req)
		return
	case "TransferSIPParticipant":
		s.serveTransferSIPParticipant(ctx, resp, req)
		return
	default:
		msg := fmt.Sprintf("no handler for path %q", req.URL.Path)
		s.writeError(ctx, resp, badRouteError(msg, req.Method, req.URL.Path))
		return
	}
}

func (s *sIPServer) serveListSIPTrunk(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveListSIPTrunkJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveListSIPTrunkProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *sIPServer) serveListSIPTrunkJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "ListSIPTrunk")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(ListSIPTrunkRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.SIP.ListSIPTrunk
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ListSIPTrunkRequest) (*ListSIPTrunkResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListSIPTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListSIPTrunkRequest) when calling interceptor")
					}
					return s.SIP.ListSIPTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListSIPTrunkResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListSIPTrunkResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ListSIPTrunkResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ListSIPTrunkResponse and nil error while calling ListSIPTrunk. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveListSIPTrunkProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "ListSIPTrunk")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(ListSIPTrunkRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.SIP.ListSIPTrunk
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ListSIPTrunkRequest) (*ListSIPTrunkResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListSIPTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListSIPTrunkRequest) when calling interceptor")
					}
					return s.SIP.ListSIPTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListSIPTrunkResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListSIPTrunkResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ListSIPTrunkResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ListSIPTrunkResponse and nil error while calling ListSIPTrunk. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveCreateSIPInboundTrunk(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveCreateSIPInboundTrunkJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveCreateSIPInboundTrunkProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *sIPServer) serveCreateSIPInboundTrunkJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "CreateSIPInboundTrunk")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(CreateSIPInboundTrunkRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.SIP.CreateSIPInboundTrunk
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *CreateSIPInboundTrunkRequest) (*SIPInboundTrunkInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateSIPInboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateSIPInboundTrunkRequest) when calling interceptor")
					}
					return s.SIP.CreateSIPInboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPInboundTrunkInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPInboundTrunkInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *SIPInboundTrunkInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *SIPInboundTrunkInfo and nil error while calling CreateSIPInboundTrunk. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveCreateSIPInboundTrunkProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "CreateSIPInboundTrunk")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(CreateSIPInboundTrunkRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.SIP.CreateSIPInboundTrunk
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *CreateSIPInboundTrunkRequest) (*SIPInboundTrunkInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateSIPInboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateSIPInboundTrunkRequest) when calling interceptor")
					}
					return s.SIP.CreateSIPInboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPInboundTrunkInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPInboundTrunkInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *SIPInboundTrunkInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *SIPInboundTrunkInfo and nil error while calling CreateSIPInboundTrunk. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveCreateSIPOutboundTrunk(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveCreateSIPOutboundTrunkJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveCreateSIPOutboundTrunkProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *sIPServer) serveCreateSIPOutboundTrunkJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "CreateSIPOutboundTrunk")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(CreateSIPOutboundTrunkRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.SIP.CreateSIPOutboundTrunk
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *CreateSIPOutboundTrunkRequest) (*SIPOutboundTrunkInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateSIPOutboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateSIPOutboundTrunkRequest) when calling interceptor")
					}
					return s.SIP.CreateSIPOutboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPOutboundTrunkInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPOutboundTrunkInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *SIPOutboundTrunkInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *SIPOutboundTrunkInfo and nil error while calling CreateSIPOutboundTrunk. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveCreateSIPOutboundTrunkProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "CreateSIPOutboundTrunk")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(CreateSIPOutboundTrunkRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.SIP.CreateSIPOutboundTrunk
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *CreateSIPOutboundTrunkRequest) (*SIPOutboundTrunkInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateSIPOutboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateSIPOutboundTrunkRequest) when calling interceptor")
					}
					return s.SIP.CreateSIPOutboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPOutboundTrunkInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPOutboundTrunkInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *SIPOutboundTrunkInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *SIPOutboundTrunkInfo and nil error while calling CreateSIPOutboundTrunk. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveUpdateSIPInboundTrunk(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveUpdateSIPInboundTrunkJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveUpdateSIPInboundTrunkProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *sIPServer) serveUpdateSIPInboundTrunkJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "UpdateSIPInboundTrunk")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(UpdateSIPInboundTrunkRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.SIP.UpdateSIPInboundTrunk
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *UpdateSIPInboundTrunkRequest) (*SIPInboundTrunkInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateSIPInboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateSIPInboundTrunkRequest) when calling interceptor")
					}
					return s.SIP.UpdateSIPInboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPInboundTrunkInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPInboundTrunkInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *SIPInboundTrunkInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *SIPInboundTrunkInfo and nil error while calling UpdateSIPInboundTrunk. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveUpdateSIPInboundTrunkProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "UpdateSIPInboundTrunk")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(UpdateSIPInboundTrunkRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.SIP.UpdateSIPInboundTrunk
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *UpdateSIPInboundTrunkRequest) (*SIPInboundTrunkInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateSIPInboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateSIPInboundTrunkRequest) when calling interceptor")
					}
					return s.SIP.UpdateSIPInboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPInboundTrunkInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPInboundTrunkInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *SIPInboundTrunkInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *SIPInboundTrunkInfo and nil error while calling UpdateSIPInboundTrunk. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveUpdateSIPOutboundTrunk(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveUpdateSIPOutboundTrunkJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveUpdateSIPOutboundTrunkProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *sIPServer) serveUpdateSIPOutboundTrunkJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "UpdateSIPOutboundTrunk")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(UpdateSIPOutboundTrunkRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.SIP.UpdateSIPOutboundTrunk
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *UpdateSIPOutboundTrunkRequest) (*SIPOutboundTrunkInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateSIPOutboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateSIPOutboundTrunkRequest) when calling interceptor")
					}
					return s.SIP.UpdateSIPOutboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPOutboundTrunkInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPOutboundTrunkInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *SIPOutboundTrunkInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *SIPOutboundTrunkInfo and nil error while calling UpdateSIPOutboundTrunk. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveUpdateSIPOutboundTrunkProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "UpdateSIPOutboundTrunk")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(UpdateSIPOutboundTrunkRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.SIP.UpdateSIPOutboundTrunk
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *UpdateSIPOutboundTrunkRequest) (*SIPOutboundTrunkInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateSIPOutboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateSIPOutboundTrunkRequest) when calling interceptor")
					}
					return s.SIP.UpdateSIPOutboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPOutboundTrunkInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPOutboundTrunkInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *SIPOutboundTrunkInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *SIPOutboundTrunkInfo and nil error while calling UpdateSIPOutboundTrunk. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveGetSIPInboundTrunk(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveGetSIPInboundTrunkJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveGetSIPInboundTrunkProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *sIPServer) serveGetSIPInboundTrunkJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "GetSIPInboundTrunk")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(GetSIPInboundTrunkRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.SIP.GetSIPInboundTrunk
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *GetSIPInboundTrunkRequest) (*GetSIPInboundTrunkResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*GetSIPInboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*GetSIPInboundTrunkRequest) when calling interceptor")
					}
					return s.SIP.GetSIPInboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*GetSIPInboundTrunkResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*GetSIPInboundTrunkResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *GetSIPInboundTrunkResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *GetSIPInboundTrunkResponse and nil error while calling GetSIPInboundTrunk. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveGetSIPInboundTrunkProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "GetSIPInboundTrunk")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(GetSIPInboundTrunkRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.SIP.GetSIPInboundTrunk
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *GetSIPInboundTrunkRequest) (*GetSIPInboundTrunkResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*GetSIPInboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*GetSIPInboundTrunkRequest) when calling interceptor")
					}
					return s.SIP.GetSIPInboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*GetSIPInboundTrunkResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*GetSIPInboundTrunkResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *GetSIPInboundTrunkResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *GetSIPInboundTrunkResponse and nil error while calling GetSIPInboundTrunk. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveGetSIPOutboundTrunk(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveGetSIPOutboundTrunkJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveGetSIPOutboundTrunkProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *sIPServer) serveGetSIPOutboundTrunkJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "GetSIPOutboundTrunk")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(GetSIPOutboundTrunkRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.SIP.GetSIPOutboundTrunk
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *GetSIPOutboundTrunkRequest) (*GetSIPOutboundTrunkResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*GetSIPOutboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*GetSIPOutboundTrunkRequest) when calling interceptor")
					}
					return s.SIP.GetSIPOutboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*GetSIPOutboundTrunkResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*GetSIPOutboundTrunkResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *GetSIPOutboundTrunkResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *GetSIPOutboundTrunkResponse and nil error while calling GetSIPOutboundTrunk. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveGetSIPOutboundTrunkProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "GetSIPOutboundTrunk")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(GetSIPOutboundTrunkRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.SIP.GetSIPOutboundTrunk
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *GetSIPOutboundTrunkRequest) (*GetSIPOutboundTrunkResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*GetSIPOutboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*GetSIPOutboundTrunkRequest) when calling interceptor")
					}
					return s.SIP.GetSIPOutboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*GetSIPOutboundTrunkResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*GetSIPOutboundTrunkResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *GetSIPOutboundTrunkResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *GetSIPOutboundTrunkResponse and nil error while calling GetSIPOutboundTrunk. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveListSIPInboundTrunk(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveListSIPInboundTrunkJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveListSIPInboundTrunkProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *sIPServer) serveListSIPInboundTrunkJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "ListSIPInboundTrunk")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(ListSIPInboundTrunkRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.SIP.ListSIPInboundTrunk
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ListSIPInboundTrunkRequest) (*ListSIPInboundTrunkResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListSIPInboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListSIPInboundTrunkRequest) when calling interceptor")
					}
					return s.SIP.ListSIPInboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListSIPInboundTrunkResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListSIPInboundTrunkResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ListSIPInboundTrunkResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ListSIPInboundTrunkResponse and nil error while calling ListSIPInboundTrunk. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveListSIPInboundTrunkProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "ListSIPInboundTrunk")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(ListSIPInboundTrunkRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.SIP.ListSIPInboundTrunk
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ListSIPInboundTrunkRequest) (*ListSIPInboundTrunkResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListSIPInboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListSIPInboundTrunkRequest) when calling interceptor")
					}
					return s.SIP.ListSIPInboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListSIPInboundTrunkResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListSIPInboundTrunkResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ListSIPInboundTrunkResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ListSIPInboundTrunkResponse and nil error while calling ListSIPInboundTrunk. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveListSIPOutboundTrunk(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveListSIPOutboundTrunkJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveListSIPOutboundTrunkProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *sIPServer) serveListSIPOutboundTrunkJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "ListSIPOutboundTrunk")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(ListSIPOutboundTrunkRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.SIP.ListSIPOutboundTrunk
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ListSIPOutboundTrunkRequest) (*ListSIPOutboundTrunkResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListSIPOutboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListSIPOutboundTrunkRequest) when calling interceptor")
					}
					return s.SIP.ListSIPOutboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListSIPOutboundTrunkResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListSIPOutboundTrunkResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ListSIPOutboundTrunkResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ListSIPOutboundTrunkResponse and nil error while calling ListSIPOutboundTrunk. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveListSIPOutboundTrunkProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "ListSIPOutboundTrunk")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(ListSIPOutboundTrunkRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.SIP.ListSIPOutboundTrunk
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ListSIPOutboundTrunkRequest) (*ListSIPOutboundTrunkResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListSIPOutboundTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListSIPOutboundTrunkRequest) when calling interceptor")
					}
					return s.SIP.ListSIPOutboundTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListSIPOutboundTrunkResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListSIPOutboundTrunkResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ListSIPOutboundTrunkResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ListSIPOutboundTrunkResponse and nil error while calling ListSIPOutboundTrunk. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveDeleteSIPTrunk(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveDeleteSIPTrunkJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveDeleteSIPTrunkProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *sIPServer) serveDeleteSIPTrunkJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "DeleteSIPTrunk")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(DeleteSIPTrunkRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.SIP.DeleteSIPTrunk
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *DeleteSIPTrunkRequest) (*SIPTrunkInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*DeleteSIPTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*DeleteSIPTrunkRequest) when calling interceptor")
					}
					return s.SIP.DeleteSIPTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPTrunkInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPTrunkInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *SIPTrunkInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *SIPTrunkInfo and nil error while calling DeleteSIPTrunk. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveDeleteSIPTrunkProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "DeleteSIPTrunk")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(DeleteSIPTrunkRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.SIP.DeleteSIPTrunk
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *DeleteSIPTrunkRequest) (*SIPTrunkInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*DeleteSIPTrunkRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*DeleteSIPTrunkRequest) when calling interceptor")
					}
					return s.SIP.DeleteSIPTrunk(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPTrunkInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPTrunkInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *SIPTrunkInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *SIPTrunkInfo and nil error while calling DeleteSIPTrunk. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveCreateSIPDispatchRule(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveCreateSIPDispatchRuleJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveCreateSIPDispatchRuleProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *sIPServer) serveCreateSIPDispatchRuleJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "CreateSIPDispatchRule")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(CreateSIPDispatchRuleRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.SIP.CreateSIPDispatchRule
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *CreateSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateSIPDispatchRuleRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateSIPDispatchRuleRequest) when calling interceptor")
					}
					return s.SIP.CreateSIPDispatchRule(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPDispatchRuleInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPDispatchRuleInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *SIPDispatchRuleInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *SIPDispatchRuleInfo and nil error while calling CreateSIPDispatchRule. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveCreateSIPDispatchRuleProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "CreateSIPDispatchRule")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(CreateSIPDispatchRuleRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.SIP.CreateSIPDispatchRule
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *CreateSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateSIPDispatchRuleRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateSIPDispatchRuleRequest) when calling interceptor")
					}
					return s.SIP.CreateSIPDispatchRule(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPDispatchRuleInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPDispatchRuleInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *SIPDispatchRuleInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *SIPDispatchRuleInfo and nil error while calling CreateSIPDispatchRule. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveUpdateSIPDispatchRule(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveUpdateSIPDispatchRuleJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveUpdateSIPDispatchRuleProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *sIPServer) serveUpdateSIPDispatchRuleJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "UpdateSIPDispatchRule")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(UpdateSIPDispatchRuleRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.SIP.UpdateSIPDispatchRule
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *UpdateSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateSIPDispatchRuleRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateSIPDispatchRuleRequest) when calling interceptor")
					}
					return s.SIP.UpdateSIPDispatchRule(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPDispatchRuleInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPDispatchRuleInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *SIPDispatchRuleInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *SIPDispatchRuleInfo and nil error while calling UpdateSIPDispatchRule. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveUpdateSIPDispatchRuleProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "UpdateSIPDispatchRule")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(UpdateSIPDispatchRuleRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.SIP.UpdateSIPDispatchRule
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *UpdateSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateSIPDispatchRuleRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateSIPDispatchRuleRequest) when calling interceptor")
					}
					return s.SIP.UpdateSIPDispatchRule(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPDispatchRuleInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPDispatchRuleInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *SIPDispatchRuleInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *SIPDispatchRuleInfo and nil error while calling UpdateSIPDispatchRule. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveListSIPDispatchRule(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveListSIPDispatchRuleJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveListSIPDispatchRuleProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *sIPServer) serveListSIPDispatchRuleJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "ListSIPDispatchRule")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(ListSIPDispatchRuleRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.SIP.ListSIPDispatchRule
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ListSIPDispatchRuleRequest) (*ListSIPDispatchRuleResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListSIPDispatchRuleRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListSIPDispatchRuleRequest) when calling interceptor")
					}
					return s.SIP.ListSIPDispatchRule(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListSIPDispatchRuleResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListSIPDispatchRuleResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ListSIPDispatchRuleResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ListSIPDispatchRuleResponse and nil error while calling ListSIPDispatchRule. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveListSIPDispatchRuleProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "ListSIPDispatchRule")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(ListSIPDispatchRuleRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.SIP.ListSIPDispatchRule
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ListSIPDispatchRuleRequest) (*ListSIPDispatchRuleResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListSIPDispatchRuleRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListSIPDispatchRuleRequest) when calling interceptor")
					}
					return s.SIP.ListSIPDispatchRule(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListSIPDispatchRuleResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListSIPDispatchRuleResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ListSIPDispatchRuleResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ListSIPDispatchRuleResponse and nil error while calling ListSIPDispatchRule. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveDeleteSIPDispatchRule(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveDeleteSIPDispatchRuleJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveDeleteSIPDispatchRuleProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *sIPServer) serveDeleteSIPDispatchRuleJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "DeleteSIPDispatchRule")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(DeleteSIPDispatchRuleRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.SIP.DeleteSIPDispatchRule
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *DeleteSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*DeleteSIPDispatchRuleRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*DeleteSIPDispatchRuleRequest) when calling interceptor")
					}
					return s.SIP.DeleteSIPDispatchRule(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPDispatchRuleInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPDispatchRuleInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *SIPDispatchRuleInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *SIPDispatchRuleInfo and nil error while calling DeleteSIPDispatchRule. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveDeleteSIPDispatchRuleProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "DeleteSIPDispatchRule")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(DeleteSIPDispatchRuleRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.SIP.DeleteSIPDispatchRule
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *DeleteSIPDispatchRuleRequest) (*SIPDispatchRuleInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*DeleteSIPDispatchRuleRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*DeleteSIPDispatchRuleRequest) when calling interceptor")
					}
					return s.SIP.DeleteSIPDispatchRule(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPDispatchRuleInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPDispatchRuleInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *SIPDispatchRuleInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *SIPDispatchRuleInfo and nil error while calling DeleteSIPDispatchRule. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveCreateSIPParticipant(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveCreateSIPParticipantJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveCreateSIPParticipantProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *sIPServer) serveCreateSIPParticipantJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "CreateSIPParticipant")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(CreateSIPParticipantRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.SIP.CreateSIPParticipant
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *CreateSIPParticipantRequest) (*SIPParticipantInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateSIPParticipantRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateSIPParticipantRequest) when calling interceptor")
					}
					return s.SIP.CreateSIPParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPParticipantInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPParticipantInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *SIPParticipantInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *SIPParticipantInfo and nil error while calling CreateSIPParticipant. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveCreateSIPParticipantProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "CreateSIPParticipant")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(CreateSIPParticipantRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.SIP.CreateSIPParticipant
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *CreateSIPParticipantRequest) (*SIPParticipantInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateSIPParticipantRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateSIPParticipantRequest) when calling interceptor")
					}
					return s.SIP.CreateSIPParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*SIPParticipantInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*SIPParticipantInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *SIPParticipantInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *SIPParticipantInfo and nil error while calling CreateSIPParticipant. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveTransferSIPParticipant(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveTransferSIPParticipantJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveTransferSIPParticipantProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *sIPServer) serveTransferSIPParticipantJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "TransferSIPParticipant")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(TransferSIPParticipantRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.SIP.TransferSIPParticipant
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *TransferSIPParticipantRequest) (*google_protobuf2.Empty, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*TransferSIPParticipantRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*TransferSIPParticipantRequest) when calling interceptor")
					}
					return s.SIP.TransferSIPParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*google_protobuf2.Empty)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*google_protobuf2.Empty) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *google_protobuf2.Empty
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *google_protobuf2.Empty and nil error while calling TransferSIPParticipant. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) serveTransferSIPParticipantProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "TransferSIPParticipant")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(TransferSIPParticipantRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.SIP.TransferSIPParticipant
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *TransferSIPParticipantRequest) (*google_protobuf2.Empty, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*TransferSIPParticipantRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*TransferSIPParticipantRequest) when calling interceptor")
					}
					return s.SIP.TransferSIPParticipant(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*google_protobuf2.Empty)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*google_protobuf2.Empty) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *google_protobuf2.Empty
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *google_protobuf2.Empty and nil error while calling TransferSIPParticipant. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *sIPServer) ServiceDescriptor() ([]byte, int) {
	return twirpFileDescriptor4, 0
}

func (s *sIPServer) ProtocGenTwirpVersion() string {
	return "v8.1.3"
}

// PathPrefix returns the base service path, in the form: "/<prefix>/<package>.<Service>/"
// that is everything in a Twirp route except for the <Method>. This can be used for routing,
// for example to identify the requests that are targeted to this service in a mux.
func (s *sIPServer) PathPrefix() string {
	return baseServicePath(s.pathPrefix, "livekit", "SIP")
}

var twirpFileDescriptor4 = []byte{
	// 4464 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe4, 0x5c, 0x59, 0x8c, 0x1b, 0x5b,
	0x5a, 0x8e, 0x5d, 0x6e, 0xb7, 0xfd, 0xbb, 0x97, 0xea, 0xd3, 0x4b, 0x1c, 0x27, 0x9d, 0x64, 0x9c,
	0xbb, 0x24, 0x7d, 0x87, 0xce, 0x9d, 0x04, 0x66, 0xee, 0x0d, 0xb3, 0x95, 0xed, 0xea, 0xee, 0x22,
	0xee, 0x2a, 0xdf, 0xaa, 0x72, 0x92, 0x1e, 0x0d, 0x14, 0x15, 0x57, 0x75, 0xa7, 0x26, 0xb6, 0xcb,
	0xd8, 0xe5, 0xe4, 0x06, 0xc1, 0x03, 0x12, 0x0f, 0x97, 0x17, 0xc4, 0xbe, 0x49, 0x6c, 0x62, 0x93,
	0x40, 0x20, 0x81, 0xd8, 0x9e, 0x90, 0x06, 0xf1, 0x00, 0x48, 0x3c, 0x83, 0xc4, 0x22, 0x18, 0x56,
	0xf1, 0x00, 0x3c, 0xb0, 0x3e, 0x81, 0xce, 0x52, 0xab, 0xcb, 0x4b, 0x27, 0x19, 0x09, 0xc1, 0x5b,
	0xd5, 0x7f, 0xfe, 0xf3, 0x9f, 0xff, 0x9c, 0xf3, 0xfd, 0xcb, 0xf9, 0xeb, 0xd8, 0xb0, 0xd1, 0x75,
	0x9e, 0xd9, 0x4f, 0x1d, 0xcf, 0x18, 0x39, 0x83, 0xfd, 0xc1, 0xd0, 0xf5, 0x5c, 0xb4, 0xcc, 0x48,
	0x95, 0xab, 0x67, 0xae, 0x7b, 0xd6, 0xb5, 0x6f, 0x13, 0xf2, 0xe3, 0xf1, 0xe9, 0x6d, 0x6b, 0x3c,
	0x34, 0x3d, 0xc7, 0xed, 0x53, 0xc6, 0xca, 0xe5, 0x64, 0xbb, 0xdd, 0x1b, 0x78, 0x2f, 0x58, 0xe3,
	0x96, 0x2f, 0xb8, 0xe7, 0x5a, 0x76, 0x77, 0xc4, 0xa8, 0xc8, 0xa7, 0x0e, 0x5d, 0xb7, 0x47, 0x69,
	0x55, 0x05, 0x8a, 0x9a, 0xd4, 0xd2, 0x3c, 0xd3, 0x1b, 0x8f, 0xd0, 0x1e, 0xe4, 0x3a, 0xae, 0x65,
	0x97, 0x33, 0xd7, 0x33, 0x37, 0xd7, 0xee, 0xec, 0xec, 0x33, 0xfe, 0xfd, 0x80, 0xa3, 0xee, 0x5a,
	0xb6, 0x4a, 0x78, 0xd0, 0x0e, 0xe4, 0x47, 0x84, 0x56, 0xce, 0x5e, 0xcf, 0xdc, 0x2c, 0xaa, 0xec,
	0xad, 0xfa, 0xe7, 0x1c, 0x6c, 0xd7, 0x87, 0xb6, 0xe9, 0xd9, 0x9a, 0xd4, 0xd2, 0x87, 0xe3, 0xfe,
	0x53, 0xd5, 0xfe, 0x96, 0xb1, 0x3d, 0xf2, 0xd0, 0x3b, 0xb0, 0xe1, 0xf4, 0x1f, 0xbb, 0xe3, 0xbe,
	0x65, 0x98, 0x96, 0x35, 0xb4, 0x47, 0x23, 0x7b, 0x54, 0xce, 0x5c, 0xe7, 0x6e, 0x16, 0x55, 0x9e,
	0x35, 0x08, 0x3e, 0x1d, 0xdd, 0x02, 0xde, 0x1d, 0x7b, 0x31, 0x6e, 0x36, 0xd0, 0xba, 0x4f, 0x67,
	0xcc, 0xe8, 0x6d, 0x08, 0x48, 0x46, 0x7f, 0xdc, 0x7b, 0x6c, 0x0f, 0xcb, 0x1c, 0xe1, 0x5c, 0xf3,
	0xc9, 0x32, 0xa1, 0xa2, 0x4f, 0xc2, 0xb6, 0xaf, 0x00, 0xe5, 0x1b, 0x19, 0x43, 0xfb, 0xcc, 0xfe,
	0xb0, 0x9c, 0xc3, 0x4a, 0xd4, 0xb2, 0xe5, 0x8c, 0xba, 0xc9, 0x18, 0x68, 0x8f, 0x91, 0x8a, 0x9b,
	0xf1, 0x00, 0x89, 0x7e, 0xe5, 0x22, 0x51, 0x7b, 0x2d, 0xce, 0x8d, 0x95, 0xf6, 0x19, 0xc7, 0x23,
	0x7b, 0xd8, 0x37, 0x7b, 0x76, 0x79, 0x89, 0x2a, 0xcd, 0xe8, 0x6d, 0x46, 0x8e, 0xb2, 0x0e, 0xcc,
	0xd1, 0xe8, 0xb9, 0x3b, 0xb4, 0xca, 0xf9, 0x18, 0x6b, 0x8b, 0x91, 0xf1, 0xba, 0x05, 0xf3, 0x0b,
	0xc4, 0x2e, 0x13, 0xde, 0x60, 0x8d, 0x02, 0xb9, 0x51, 0xe6, 0x40, 0x70, 0x21, 0xce, 0x1c, 0x48,
	0x46, 0x90, 0x23, 0xc2, 0x80, 0xb4, 0x93, 0x67, 0x54, 0x81, 0x42, 0xcf, 0xf6, 0x4c, 0xcb, 0xf4,
	0xcc, 0x72, 0x89, 0xd0, 0x83, 0xf7, 0x7b, 0xd9, 0x72, 0xa6, 0xfa, 0xf3, 0x4b, 0xb0, 0xe2, 0xef,
	0xac, 0xd4, 0x3f, 0x75, 0xd1, 0x75, 0x58, 0x19, 0x39, 0x03, 0xc3, 0xc3, 0x04, 0xc3, 0xb1, 0x08,
	0x78, 0x8a, 0x2a, 0x8c, 0x9c, 0x01, 0xe5, 0xb1, 0xd0, 0x5d, 0xc8, 0x3d, 0x75, 0xfa, 0x56, 0x79,
	0x8d, 0xc0, 0xea, 0x5a, 0x14, 0x56, 0x81, 0x98, 0x7d, 0xf2, 0x74, 0xdf, 0xe9, 0x5b, 0x2a, 0x61,
	0x4e, 0x47, 0x4b, 0xf6, 0x1c, 0x68, 0xe1, 0x16, 0x46, 0x4b, 0x2e, 0x15, 0x2d, 0x77, 0xa1, 0xe8,
	0x0d, 0xcd, 0xfe, 0x68, 0xe0, 0x0e, 0xbd, 0xf2, 0x2a, 0x51, 0x7d, 0x3b, 0xae, 0x3a, 0x6b, 0x54,
	0x43, 0xbe, 0xe9, 0x10, 0x5b, 0x3a, 0x37, 0xc4, 0x60, 0x61, 0x88, 0xe5, 0x17, 0x87, 0xd8, 0xf2,
	0x39, 0x20, 0x56, 0x38, 0x0f, 0xc4, 0x8a, 0x73, 0x20, 0x56, 0x9a, 0x02, 0xb1, 0x95, 0x38, 0xc4,
	0xaa, 0x0d, 0x28, 0x06, 0x48, 0x40, 0x3c, 0xac, 0xe8, 0x6a, 0x5b, 0xbe, 0x6f, 0x34, 0xc5, 0x43,
	0xa1, 0x7e, 0xc2, 0x5f, 0x40, 0x1b, 0xb0, 0x4a, 0x29, 0x92, 0x5c, 0x53, 0xda, 0x72, 0x83, 0xcf,
	0x20, 0x04, 0x6b, 0x94, 0xa4, 0xb4, 0x75, 0x4a, 0xcb, 0x12, 0xa0, 0xaa, 0x70, 0x25, 0xf0, 0x43,
	0x12, 0x9d, 0x6f, 0xcc, 0x1d, 0xdd, 0x81, 0x25, 0x82, 0x59, 0x02, 0xd8, 0xd2, 0x9d, 0x2b, 0xd1,
	0xbd, 0x8d, 0xf2, 0x63, 0x74, 0xaa, 0x94, 0xb5, 0xfa, 0x3b, 0x19, 0xb8, 0xd2, 0x1e, 0x58, 0xd3,
	0x85, 0xce, 0x37, 0x86, 0xf7, 0x60, 0x79, 0x68, 0x0f, 0xba, 0x66, 0xc7, 0x26, 0xfe, 0x6c, 0xce,
	0xc0, 0x47, 0x17, 0x54, 0x9f, 0x1d, 0xbd, 0x07, 0xf9, 0x31, 0x19, 0x9b, 0x40, 0xbb, 0x74, 0xe7,
	0xea, 0xb4, 0x8e, 0x54, 0xc3, 0xa3, 0x0b, 0x2a, 0xe3, 0xaf, 0x15, 0x20, 0x6f, 0x76, 0x70, 0xec,
	0xa8, 0xfe, 0x5e, 0x01, 0x36, 0x53, 0x86, 0x59, 0x40, 0x6f, 0x7f, 0x23, 0xb3, 0x53, 0x36, 0x92,
	0x8b, 0x6f, 0x24, 0x2a, 0xc3, 0xb2, 0x8f, 0x64, 0xe2, 0x5e, 0x55, 0xff, 0x15, 0xe3, 0xc7, 0xec,
	0x76, 0xdd, 0xe7, 0x76, 0xd4, 0xb2, 0x97, 0xa8, 0x65, 0xb3, 0x86, 0xd0, 0xb2, 0xdf, 0x86, 0x75,
	0x9f, 0xd9, 0x17, 0x97, 0xa7, 0x86, 0xc1, 0xc8, 0xbe, 0x61, 0xdc, 0x80, 0x55, 0x73, 0xec, 0x3d,
	0x49, 0x7a, 0xc8, 0x15, 0x4c, 0x0c, 0xa0, 0xeb, 0x33, 0x25, 0x3c, 0x23, 0x61, 0x0a, 0x20, 0x5b,
	0x87, 0xe5, 0x27, 0xb6, 0x69, 0xf9, 0x6e, 0xbe, 0x74, 0xe7, 0xd6, 0xac, 0x1d, 0xda, 0x3f, 0xa2,
	0xbc, 0x62, 0xdf, 0x1b, 0xbe, 0x50, 0xfd, 0x9e, 0xc8, 0x81, 0x6d, 0xf6, 0x68, 0x78, 0xae, 0x61,
	0x7a, 0xde, 0xd0, 0x79, 0x3c, 0xf6, 0x6c, 0x6a, 0xd6, 0xa5, 0x3b, 0x5f, 0xb7, 0x88, 0x48, 0xdd,
	0x15, 0x82, 0x7e, 0x54, 0xfc, 0xe6, 0x93, 0xc9, 0x16, 0x3c, 0x54, 0x28, 0x1f, 0x8f, 0xe6, 0x6b,
	0xbf, 0xb6, 0xc0, 0x50, 0xa1, 0x1c, 0xdd, 0x8d, 0xcd, 0x64, 0xd3, 0x9c, 0x6c, 0x41, 0x35, 0xec,
	0xa6, 0x3a, 0xdd, 0xb1, 0x65, 0x07, 0x83, 0xac, 0x13, 0xcf, 0x78, 0x29, 0x3a, 0x08, 0xe5, 0x56,
	0x06, 0x18, 0x71, 0x23, 0xec, 0xc1, 0x48, 0x8f, 0x88, 0x8c, 0xa1, 0xd3, 0x3f, 0x73, 0xfa, 0x67,
	0x86, 0xe7, 0xf4, 0x6c, 0x77, 0xec, 0x11, 0xe7, 0x50, 0xba, 0x73, 0x69, 0x9f, 0xa6, 0x34, 0xfb,
	0x7e, 0x4a, 0xb3, 0xdf, 0x60, 0x29, 0x8f, 0xba, 0xc6, 0x7a, 0xe8, 0xb4, 0x03, 0x12, 0x61, 0xa3,
	0x67, 0x7e, 0x68, 0x74, 0xcc, 0x6e, 0xd7, 0xf0, 0xf3, 0x22, 0xe2, 0x4a, 0x66, 0x4a, 0x59, 0xef,
	0x99, 0x1f, 0xd6, 0xcd, 0x6e, 0xd7, 0x27, 0x60, 0x38, 0x3c, 0x1d, 0x3a, 0xa3, 0x81, 0x61, 0xf7,
	0xcd, 0xc7, 0x5d, 0xdb, 0x22, 0x6e, 0xbe, 0xa0, 0xae, 0x10, 0xa2, 0x48, 0x69, 0xe8, 0x00, 0xf8,
	0x9e, 0x6d, 0x39, 0xa6, 0x61, 0xf7, 0x3b, 0xc3, 0x17, 0x64, 0x52, 0x65, 0x9e, 0x4c, 0xfa, 0x72,
	0x74, 0xd2, 0xc7, 0x98, 0x47, 0x0c, 0x58, 0xd4, 0xf5, 0x5e, 0x9c, 0x50, 0xb9, 0x07, 0x2b, 0xd1,
	0x05, 0x46, 0x3c, 0x70, 0x4f, 0xed, 0x17, 0xcc, 0xd2, 0xf0, 0x23, 0xda, 0x82, 0xa5, 0x67, 0x66,
	0x77, 0xec, 0xdb, 0x18, 0x7d, 0xb9, 0x97, 0x7d, 0x2f, 0x53, 0x39, 0x80, 0xf2, 0x34, 0x4c, 0x9c,
	0x57, 0xce, 0xb4, 0x0d, 0x3f, 0x8f, 0x9c, 0xea, 0xbf, 0x70, 0xb0, 0x9d, 0xea, 0x74, 0xd0, 0xd7,
	0x84, 0x66, 0x4f, 0xfd, 0xea, 0x66, 0xb0, 0x48, 0x4d, 0x67, 0xe4, 0x51, 0xae, 0xd0, 0x17, 0x7c,
	0x3e, 0xcd, 0x17, 0x64, 0xa7, 0x77, 0x9c, 0x74, 0x10, 0x9f, 0x9e, 0x74, 0x10, 0xdc, 0xf4, 0xfe,
	0x49, 0xaf, 0x71, 0x33, 0xe9, 0x35, 0x48, 0x2e, 0x70, 0x74, 0x21, 0xee, 0x37, 0x3e, 0xca, 0x64,
	0x02, 0xce, 0xc0, 0x75, 0x90, 0xc4, 0xee, 0x28, 0x13, 0x77, 0x1e, 0x98, 0xf3, 0x22, 0xf3, 0x94,
	0x24, 0x2c, 0x1f, 0x65, 0xa9, 0xaf, 0xc4, 0x0d, 0xd7, 0x22, 0xee, 0x92, 0x78, 0xa7, 0x23, 0x2e,
	0x74, 0x98, 0x98, 0xe1, 0x7e, 0x0a, 0xd4, 0x0a, 0x73, 0xa1, 0x76, 0x94, 0x9b, 0x00, 0xdb, 0x47,
	0x99, 0x4c, 0x8d, 0x87, 0x35, 0x23, 0x36, 0xb7, 0x90, 0xe2, 0xcf, 0xa1, 0xb6, 0x0c, 0x4b, 0x06,
	0x69, 0x2a, 0x41, 0xd1, 0xf0, 0x35, 0xa9, 0x6d, 0xc2, 0x86, 0x91, 0xd4, 0xa3, 0xaa, 0xc3, 0x6e,
	0x10, 0x4e, 0x15, 0x16, 0xe5, 0x63, 0xa1, 0xef, 0x6e, 0x3c, 0x9e, 0xee, 0x46, 0x35, 0x8e, 0x75,
	0x88, 0x06, 0xd4, 0x2f, 0x67, 0x60, 0x37, 0x08, 0xa8, 0xa9, 0x62, 0xe7, 0x47, 0xa6, 0xf7, 0x93,
	0x11, 0x75, 0xf6, 0xd0, 0xd1, 0x90, 0xfa, 0x7e, 0x22, 0xa4, 0x5e, 0x9b, 0xda, 0x73, 0x46, 0x4c,
	0xfd, 0xdd, 0x65, 0xd8, 0x4a, 0x1b, 0xe8, 0xab, 0x13, 0x54, 0xfd, 0xf4, 0x96, 0x26, 0xad, 0xfe,
	0x2b, 0xba, 0x0d, 0x9b, 0x96, 0x3d, 0xf2, 0x9c, 0x3e, 0xf1, 0x6c, 0x46, 0xc7, 0x1d, 0x63, 0xa3,
	0x26, 0x29, 0x77, 0x51, 0x45, 0x91, 0xa6, 0x3a, 0x6d, 0x89, 0xa7, 0xb7, 0x4b, 0x0b, 0xa6, 0xb7,
	0x91, 0xa0, 0x9e, 0x8f, 0x07, 0xf5, 0xd7, 0x17, 0x7e, 0x1b, 0xc9, 0xf0, 0xbb, 0x37, 0x73, 0x3b,
	0xa7, 0xc4, 0xdf, 0x2f, 0xcd, 0x8e, 0xbf, 0x9f, 0x5c, 0x48, 0xe6, 0x82, 0x01, 0xf8, 0x4b, 0xd3,
	0x02, 0x70, 0x69, 0x91, 0xb1, 0x5e, 0x39, 0x02, 0xaf, 0x9c, 0x37, 0x02, 0xa7, 0x45, 0xb4, 0xd5,
	0xff, 0xe7, 0x11, 0xed, 0xd7, 0x72, 0xb0, 0x93, 0x6e, 0xf3, 0x68, 0x37, 0x34, 0xba, 0x0c, 0x8b,
	0x0e, 0x3e, 0x01, 0x3b, 0xed, 0xf7, 0xa2, 0x86, 0x94, 0x9d, 0x61, 0x48, 0x47, 0x99, 0x88, 0x29,
	0xe1, 0x9e, 0x5f, 0x9b, 0x6e, 0xb3, 0x45, 0x16, 0x37, 0x52, 0xac, 0x16, 0xf7, 0x8a, 0x44, 0x58,
	0x6e, 0x81, 0x08, 0x3b, 0x25, 0xc2, 0x71, 0x0b, 0x47, 0xb8, 0xdc, 0xbc, 0x08, 0xb7, 0x34, 0x2b,
	0xc2, 0xe5, 0x5f, 0x4b, 0x84, 0x5b, 0x4e, 0x8d, 0x70, 0x00, 0x05, 0x3f, 0x6b, 0xa8, 0xad, 0x00,
	0x18, 0xc1, 0xe2, 0xd6, 0x76, 0x60, 0xcb, 0x48, 0x59, 0xda, 0xd7, 0x1d, 0x13, 0x3f, 0x03, 0x97,
	0x0e, 0x6d, 0xef, 0x65, 0x8f, 0x82, 0xd5, 0x16, 0x54, 0xd2, 0xba, 0x8f, 0x06, 0x6e, 0x7f, 0x64,
	0xbf, 0xd4, 0xf9, 0xf4, 0xb3, 0xbe, 0xc4, 0x97, 0x0b, 0xa5, 0x55, 0x15, 0x2e, 0xa7, 0xf6, 0x67,
	0x2a, 0xbd, 0x54, 0x88, 0xaf, 0xc1, 0x26, 0xc6, 0x65, 0xb2, 0x1a, 0xf8, 0x36, 0xe4, 0x06, 0xe6,
	0x99, 0x3d, 0x91, 0x25, 0xb6, 0xcc, 0x33, 0xb6, 0x6b, 0x2a, 0x61, 0x20, 0x67, 0xf9, 0x43, 0xd8,
	0x8a, 0xcb, 0x60, 0x0a, 0xbd, 0x03, 0x4b, 0x8e, 0x67, 0xf7, 0x68, 0x19, 0xb1, 0x94, 0xb4, 0xbb,
	0x40, 0x11, 0xc2, 0x43, 0x04, 0x7d, 0x1b, 0x54, 0x98, 0xa0, 0xb4, 0x2d, 0xf3, 0x75, 0xe2, 0xe6,
	0xe8, 0x84, 0x2e, 0x63, 0x9b, 0xa7, 0xab, 0xe8, 0x97, 0x30, 0x0b, 0x1e, 0x5d, 0xc3, 0x51, 0x34,
	0x48, 0x66, 0x63, 0x41, 0xb2, 0xfa, 0x01, 0x5c, 0x4e, 0x1d, 0x3d, 0xdc, 0xf1, 0xe8, 0x6c, 0xe6,
	0xec, 0x38, 0x61, 0xad, 0x7e, 0x7b, 0x20, 0x32, 0x75, 0xcb, 0xbf, 0xda, 0x33, 0xd2, 0xe0, 0x4a,
	0xfa, 0xf0, 0x21, 0x62, 0xa2, 0x53, 0x9a, 0x87, 0x18, 0x3a, 0xa7, 0xf7, 0x61, 0xbb, 0x61, 0x77,
	0xed, 0xc9, 0x0a, 0xf2, 0x7c, 0x00, 0x1f, 0x90, 0x73, 0x49, 0xc3, 0x19, 0x0d, 0x4c, 0xaf, 0xf3,
	0x44, 0x1d, 0x77, 0xed, 0x86, 0x33, 0xb4, 0x3b, 0x1e, 0x9e, 0xdf, 0xd0, 0x75, 0x7b, 0xc4, 0xb2,
	0x59, 0xbf, 0x02, 0x26, 0xc8, 0x38, 0x2f, 0xe1, 0x81, 0x1b, 0x38, 0x7d, 0x16, 0x14, 0xf0, 0x63,
	0x55, 0x86, 0x4b, 0x09, 0x39, 0x52, 0xdf, 0x72, 0x9e, 0x39, 0xd6, 0xd8, 0xec, 0xa2, 0x6b, 0x50,
	0x22, 0xb2, 0x06, 0x43, 0xfb, 0xd4, 0xf9, 0xd0, 0xd7, 0x02, 0x93, 0x5a, 0x84, 0x92, 0x22, 0xef,
	0xc9, 0x84, 0x5e, 0xf8, 0x20, 0x6a, 0xdb, 0x2f, 0x21, 0x0b, 0x5d, 0x81, 0xe2, 0xd0, 0xec, 0x5b,
	0x6e, 0xcf, 0xf9, 0x56, 0xba, 0xb1, 0x05, 0x35, 0x24, 0x54, 0x7f, 0x3a, 0x0b, 0xeb, 0x89, 0xa1,
	0x90, 0x0a, 0x5b, 0x16, 0x7b, 0x37, 0x86, 0xe3, 0xae, 0x6d, 0x58, 0x64, 0x51, 0x98, 0xed, 0xc5,
	0xea, 0x48, 0x93, 0x4b, 0x77, 0x74, 0x41, 0x45, 0xd6, 0xe4, 0x82, 0x7e, 0x13, 0x94, 0xe3, 0x32,
	0x9d, 0x60, 0x81, 0x58, 0x1a, 0x5e, 0x9d, 0x26, 0x37, 0x5c, 0xca, 0xa3, 0x0b, 0xea, 0x8e, 0x95,
	0xbe, 0xc8, 0x13, 0x3a, 0x77, 0xc8, 0x82, 0xa5, 0xd5, 0xbe, 0x26, 0x97, 0x35, 0xa9, 0x33, 0xa5,
	0xd6, 0xf2, 0x90, 0xc3, 0xa2, 0xaa, 0x7f, 0x90, 0x8b, 0xd4, 0x06, 0xa3, 0xbd, 0x7d, 0xa0, 0x09,
	0xb0, 0x1a, 0x1b, 0x9c, 0x54, 0xc8, 0x13, 0x16, 0x19, 0x9f, 0xd1, 0xa9, 0xab, 0xae, 0x44, 0x47,
	0x44, 0xef, 0xd2, 0xb1, 0xd8, 0x1a, 0x97, 0xa7, 0xf5, 0x24, 0x25, 0x61, 0xc2, 0x89, 0xae, 0x45,
	0x4d, 0x30, 0x1b, 0xd4, 0x8b, 0x43, 0x33, 0xdc, 0x87, 0x8d, 0x27, 0x8e, 0x65, 0x1b, 0x83, 0x27,
	0x6e, 0xdf, 0x8e, 0x7e, 0xea, 0x28, 0x10, 0xc6, 0x75, 0xdc, 0xd8, 0xc2, 0x6d, 0xac, 0x82, 0xfd,
	0xce, 0x64, 0x51, 0x39, 0x1f, 0x88, 0x4d, 0x16, 0x96, 0x77, 0x58, 0x4c, 0x27, 0xe9, 0x01, 0xd5,
	0x8a, 0xe4, 0xec, 0x57, 0x23, 0x21, 0x7d, 0x29, 0x68, 0x0b, 0x8f, 0x24, 0x27, 0x00, 0x91, 0xec,
	0x7a, 0x39, 0x51, 0x72, 0x9a, 0xb5, 0xca, 0xfb, 0x89, 0xbc, 0x8f, 0x08, 0x8e, 0x08, 0x43, 0x37,
	0x42, 0xdb, 0x18, 0xd9, 0x1e, 0x3d, 0x2c, 0x50, 0x26, 0x66, 0x1f, 0x23, 0xdb, 0x43, 0x9f, 0x63,
	0x4c, 0x1d, 0xb7, 0x7f, 0xea, 0x9c, 0x91, 0xe4, 0xa9, 0x74, 0xa7, 0x12, 0x28, 0xa0, 0xba, 0x6e,
	0xaf, 0x4e, 0x9a, 0x58, 0xd1, 0x27, 0x14, 0x40, 0xc9, 0x95, 0xcf, 0xc0, 0xfa, 0x2b, 0x24, 0xa0,
	0xd5, 0x3f, 0x8a, 0x96, 0x84, 0xd3, 0xb0, 0x74, 0x1b, 0xb6, 0xb0, 0xd3, 0x4a, 0x18, 0x8b, 0xef,
	0xbc, 0x36, 0x46, 0xce, 0x20, 0x86, 0xa4, 0x79, 0x15, 0xe2, 0x24, 0xec, 0x16, 0xae, 0x10, 0x47,
	0x3b, 0xce, 0x38, 0xcd, 0xfe, 0x53, 0x8e, 0x54, 0x88, 0x93, 0xc3, 0x9c, 0x7f, 0x1a, 0x1f, 0x67,
	0x06, 0x90, 0x9d, 0x6d, 0x00, 0x0c, 0xfc, 0xb1, 0xf8, 0xc3, 0x25, 0xe2, 0xcf, 0x5e, 0x1a, 0xf0,
	0x73, 0xc4, 0xf3, 0x4d, 0x80, 0x3e, 0xe5, 0x4b, 0xca, 0x72, 0xea, 0x97, 0x14, 0xff, 0xec, 0xbd,
	0x34, 0xe5, 0xec, 0x9d, 0x4f, 0x9c, 0xbd, 0x9b, 0x31, 0xa0, 0x17, 0x08, 0xd0, 0x3f, 0x3e, 0x6b,
	0x67, 0x92, 0xf8, 0x8e, 0x61, 0xfb, 0x5a, 0x1c, 0xdb, 0xc5, 0x98, 0xdf, 0xc7, 0xb8, 0xfe, 0xfa,
	0x38, 0xae, 0x61, 0x1e, 0xae, 0xa3, 0x98, 0x9e, 0x2c, 0x6c, 0x96, 0x16, 0x2c, 0x6c, 0xae, 0xbc,
	0xc4, 0x31, 0xf0, 0x15, 0x0d, 0xe8, 0xb7, 0xb9, 0x89, 0xd8, 0xc8, 0x0e, 0x5e, 0xef, 0xc6, 0x73,
	0x92, 0xa9, 0x67, 0x9d, 0x10, 0x28, 0xe7, 0xc3, 0x9c, 0x7f, 0x8c, 0xe1, 0xd8, 0xa9, 0x2e, 0xf5,
	0x18, 0x93, 0x63, 0x65, 0xbe, 0xd8, 0x31, 0x46, 0x8e, 0x61, 0x61, 0x89, 0x60, 0x61, 0x7f, 0xb6,
	0xb1, 0xcd, 0x44, 0x43, 0xda, 0xb1, 0x28, 0x3f, 0xff, 0x58, 0x94, 0x4d, 0x3b, 0x16, 0xbd, 0xe2,
	0x7e, 0x9c, 0xe7, 0xfc, 0xf3, 0xdd, 0x99, 0x20, 0x9d, 0x4e, 0xf3, 0x7c, 0x0b, 0x27, 0x9f, 0x7b,
	0xb0, 0x91, 0xf4, 0x2b, 0x7e, 0x12, 0xba, 0x1e, 0x4b, 0x0f, 0xac, 0x51, 0xdc, 0x51, 0x64, 0xe3,
	0x8e, 0x22, 0x92, 0x60, 0xc7, 0xf5, 0x59, 0x20, 0xc1, 0x9e, 0x08, 0xe7, 0x2c, 0x19, 0x55, 0xe0,
	0x4a, 0x90, 0x8c, 0xbe, 0x0e, 0xf7, 0x5e, 0xfd, 0x72, 0x0e, 0x36, 0x22, 0xd9, 0x2f, 0xb3, 0xd8,
	0x0a, 0x14, 0x9e, 0xb8, 0x23, 0x2f, 0x9a, 0x9e, 0xfa, 0xef, 0xd3, 0x6a, 0x7b, 0xcb, 0x8b, 0xd5,
	0xf6, 0xb2, 0x0b, 0xd6, 0xf6, 0x26, 0x2a, 0x78, 0xdc, 0x22, 0x15, 0xbc, 0x5c, 0x4a, 0x05, 0xef,
	0x6c, 0x5a, 0xed, 0x8d, 0x1a, 0xca, 0xdd, 0xb4, 0x43, 0x00, 0x5d, 0x86, 0x73, 0x16, 0xde, 0xce,
	0xa6, 0x15, 0xde, 0xf2, 0x73, 0x07, 0x3a, 0x57, 0xd5, 0xed, 0x7f, 0x5d, 0xb5, 0xea, 0x67, 0x8a,
	0x70, 0x39, 0x48, 0xad, 0x5a, 0xe6, 0xd0, 0x73, 0x3a, 0xce, 0xc0, 0xec, 0x7b, 0x8b, 0x17, 0xcd,
	0xdf, 0xf5, 0x8f, 0xf2, 0x5b, 0x89, 0xc0, 0x32, 0xb1, 0x54, 0xec, 0x1c, 0x8f, 0xae, 0x42, 0x09,
	0xcb, 0x24, 0xdf, 0xdc, 0x3c, 0x97, 0xe9, 0x54, 0x1c, 0x39, 0x03, 0x9c, 0x5c, 0xeb, 0x2e, 0xda,
	0x05, 0x2c, 0xdf, 0x8f, 0xce, 0xeb, 0x41, 0x33, 0x8b, 0xcb, 0xb1, 0x03, 0x18, 0x97, 0x38, 0x80,
	0x7d, 0x02, 0xb6, 0x06, 0xe1, 0x2c, 0x0c, 0xc7, 0xb2, 0xfb, 0x9e, 0xe3, 0xbd, 0x60, 0xe8, 0xda,
	0x8c, 0xb4, 0x49, 0xac, 0x09, 0xdd, 0x02, 0x3e, 0xda, 0x25, 0x52, 0x73, 0x5e, 0x8f, 0xd0, 0xd3,
	0xa4, 0x07, 0xae, 0xbd, 0x30, 0x21, 0xfd, 0xd8, 0x0f, 0xf6, 0xcf, 0x60, 0x27, 0xda, 0x25, 0x82,
	0x61, 0x5a, 0x93, 0xfe, 0xdc, 0x64, 0x86, 0x3b, 0xb9, 0x0d, 0xfb, 0x11, 0x52, 0x12, 0xcf, 0xdb,
	0x83, 0xb4, 0x36, 0x9c, 0x94, 0x58, 0x5e, 0xef, 0xd4, 0x4f, 0x4a, 0xf0, 0x33, 0x7a, 0x1b, 0x56,
	0x07, 0x5d, 0xf3, 0x85, 0x31, 0x74, 0xfa, 0x67, 0x9e, 0xdb, 0xa7, 0x65, 0x37, 0x9a, 0xf2, 0xaf,
	0xe0, 0x06, 0x95, 0xd1, 0xb1, 0x71, 0x12, 0x46, 0xcb, 0x31, 0xbb, 0x84, 0x91, 0x7d, 0xce, 0xc4,
	0xc4, 0x06, 0xa3, 0xa5, 0xe7, 0x52, 0x90, 0x9e, 0x4b, 0xdd, 0x0f, 0x4b, 0xf1, 0x3c, 0x99, 0xf6,
	0x27, 0x16, 0x9a, 0x76, 0x7a, 0x45, 0x3e, 0xa5, 0x72, 0xbd, 0xf1, 0x7f, 0xe9, 0xdb, 0xf1, 0xda,
	0x82, 0x29, 0x16, 0x3a, 0x7f, 0x8a, 0x85, 0xf6, 0x61, 0xf3, 0xb9, 0xe9, 0x78, 0xc6, 0xb8, 0xef,
	0x39, 0x5d, 0xc3, 0xec, 0x8f, 0x9e, 0xdb, 0x43, 0xdb, 0x2a, 0x6f, 0x92, 0x21, 0x37, 0x70, 0x53,
	0x1b, 0xb7, 0x08, 0xac, 0xa1, 0x72, 0x04, 0x95, 0xe9, 0xd8, 0x3b, 0x97, 0xc7, 0x7a, 0x85, 0x1a,
	0x7f, 0xf5, 0x97, 0x32, 0x80, 0xe2, 0x08, 0x21, 0x27, 0x89, 0x37, 0x61, 0x2d, 0x6e, 0xec, 0x4c,
	0xda, 0x6a, 0xcc, 0xcc, 0xa7, 0xfa, 0x84, 0xec, 0x74, 0x9f, 0x30, 0xd3, 0xc7, 0x44, 0xfd, 0x97,
	0xe3, 0x07, 0x2e, 0xdf, 0x7f, 0x49, 0x56, 0xf5, 0x3b, 0x39, 0xd8, 0x25, 0x81, 0xf1, 0xd4, 0x1e,
	0xa6, 0x7b, 0xd5, 0x69, 0x1a, 0x65, 0x16, 0xd4, 0x28, 0x9b, 0xd0, 0xe8, 0x1a, 0x94, 0x3c, 0x36,
	0x20, 0xf6, 0xa8, 0x54, 0x61, 0xf0, 0x49, 0xba, 0x3b, 0x69, 0xd0, 0xb9, 0x14, 0x83, 0x3e, 0x0e,
	0x8d, 0x34, 0x19, 0x5f, 0x67, 0x4e, 0x67, 0xba, 0x99, 0x26, 0x4d, 0x2c, 0x7f, 0x4e, 0x13, 0x7b,
	0x25, 0xd0, 0x7c, 0xa5, 0x00, 0x25, 0x4d, 0x6a, 0x91, 0x4d, 0xc1, 0x68, 0xb9, 0x08, 0xcb, 0xfe,
	0x96, 0xd1, 0xfe, 0xf9, 0x0e, 0xd9, 0x2f, 0x74, 0x09, 0x0a, 0x41, 0x7c, 0xa3, 0x52, 0x96, 0x59,
	0x1e, 0x88, 0x6e, 0x02, 0x3f, 0x91, 0x8f, 0xf1, 0xf4, 0x92, 0x5f, 0x3c, 0x9d, 0x44, 0x3b, 0x90,
	0x1f, 0xda, 0x67, 0xd8, 0x2c, 0x37, 0xa8, 0x70, 0xfa, 0x36, 0x1b, 0x49, 0x17, 0x61, 0x99, 0x34,
	0x06, 0x28, 0xca, 0xe3, 0xd7, 0x19, 0x90, 0x5d, 0x9a, 0x0e, 0x90, 0xd3, 0xa9, 0x81, 0x06, 0x91,
	0xcd, 0xbc, 0x1d, 0xf5, 0x13, 0xfe, 0xa2, 0xbc, 0x44, 0x60, 0xd9, 0x83, 0xc2, 0xe9, 0xd0, 0xed,
	0x19, 0xe3, 0xa1, 0xc3, 0xf6, 0x73, 0x3d, 0x2a, 0xb9, 0x3d, 0x74, 0xd4, 0x65, 0xcc, 0xd0, 0x1e,
	0x3a, 0xe8, 0x2d, 0xc8, 0x7b, 0x2e, 0xe1, 0x5c, 0x4e, 0xe7, 0x5c, 0xf2, 0x5c, 0xcc, 0xf7, 0x31,
	0x80, 0x0e, 0x09, 0x03, 0x96, 0x61, 0xd2, 0x23, 0x2c, 0x47, 0xa2, 0x52, 0x91, 0x51, 0x05, 0x0f,
	0xb3, 0x8c, 0x3c, 0x73, 0xc8, 0x58, 0x20, 0x64, 0x61, 0x54, 0xc1, 0x43, 0xbb, 0x50, 0xb0, 0xfb,
	0x16, 0x65, 0x28, 0x05, 0x0c, 0xcb, 0x84, 0x26, 0x78, 0xe8, 0xb3, 0xc0, 0x33, 0x0f, 0x6b, 0x9c,
	0xda, 0xa6, 0x37, 0x1e, 0xda, 0xf4, 0x62, 0xd3, 0x5a, 0xe4, 0x40, 0xa1, 0x49, 0xad, 0x03, 0xda,
	0xa6, 0xae, 0x33, 0x66, 0xf6, 0x3e, 0x42, 0x9f, 0x87, 0x35, 0xea, 0xea, 0x49, 0xd9, 0x12, 0xef,
	0x74, 0xca, 0x8d, 0x25, 0xe2, 0xd9, 0x7d, 0x06, 0x75, 0xb5, 0x13, 0x7d, 0x45, 0x9f, 0x82, 0x12,
	0x91, 0xc0, 0xae, 0x3b, 0x17, 0x26, 0x2f, 0x47, 0xe3, 0xee, 0xf4, 0x82, 0xb4, 0x0a, 0x9d, 0xe0,
	0x19, 0x55, 0x61, 0x35, 0x5c, 0x1f, 0xa3, 0x3f, 0x2a, 0xef, 0xe0, 0xe9, 0xa9, 0xa5, 0x60, 0x79,
	0x64, 0xc2, 0x13, 0x2e, 0x10, 0xe6, 0xb9, 0x48, 0x79, 0x82, 0xf5, 0x91, 0x47, 0xd8, 0x73, 0xf9,
	0x2b, 0x84, 0x39, 0xca, 0x84, 0xa3, 0xc8, 0x16, 0x48, 0x1e, 0xa1, 0x03, 0x72, 0x7c, 0xea, 0xb8,
	0xfd, 0xbe, 0xdd, 0xf1, 0x8c, 0xa1, 0x6d, 0x8e, 0x82, 0x93, 0x7c, 0x38, 0xcb, 0x46, 0xc0, 0xa1,
	0x12, 0x06, 0x95, 0xb7, 0x12, 0x14, 0x6c, 0x94, 0xf6, 0x70, 0xe8, 0x0e, 0x49, 0xde, 0x50, 0x54,
	0xe9, 0x0b, 0xfa, 0x34, 0xf0, 0x91, 0xe9, 0x1b, 0xe4, 0x82, 0xf8, 0x26, 0xc1, 0x05, 0x9a, 0xbc,
	0x20, 0xae, 0xae, 0x85, 0xf3, 0xaf, 0xbb, 0x16, 0xf1, 0x71, 0xe6, 0xd8, 0x72, 0x5c, 0xd2, 0xaf,
	0x43, 0xb2, 0xcd, 0xa2, 0x0a, 0x84, 0x84, 0xdb, 0x3b, 0x38, 0x8f, 0x9b, 0x08, 0x91, 0xdb, 0x34,
	0x8f, 0x4b, 0x16, 0x1a, 0x5e, 0x5b, 0x54, 0xab, 0xfe, 0x37, 0x2d, 0x92, 0xfb, 0xfe, 0x91, 0x38,
	0x9a, 0xa8, 0x37, 0x0e, 0x53, 0x66, 0x9f, 0x24, 0x59, 0x51, 0x4f, 0x94, 0x8d, 0x79, 0xa2, 0xb9,
	0x7e, 0xfc, 0x53, 0x50, 0x0e, 0x45, 0xf7, 0x1d, 0xcf, 0x89, 0x60, 0x22, 0x47, 0x76, 0x73, 0x3b,
	0x18, 0xc7, 0x6f, 0x26, 0x3b, 0x1b, 0xed, 0xd8, 0x71, 0x7b, 0x03, 0x7c, 0x0e, 0xf5, 0x3b, 0x2e,
	0xc5, 0x3b, 0xd6, 0xfd, 0x66, 0xd2, 0xb1, 0x0e, 0xeb, 0x41, 0x47, 0x86, 0x5b, 0x5a, 0x4f, 0xa8,
	0x4c, 0x9c, 0x03, 0x71, 0x7c, 0x60, 0x7b, 0xe7, 0xc5, 0xde, 0x43, 0x3c, 0x2c, 0x47, 0xf1, 0xd0,
	0x80, 0xad, 0x84, 0x68, 0x8a, 0x89, 0xc2, 0x54, 0x4c, 0xa0, 0xb8, 0x5c, 0xbc, 0xef, 0xd5, 0xef,
	0xca, 0x40, 0x9e, 0x7a, 0x13, 0x9c, 0xf3, 0xe2, 0x33, 0x27, 0x5b, 0x71, 0xf2, 0x8c, 0x69, 0xf8,
	0xf8, 0xeb, 0x5f, 0x8c, 0xc1, 0xcf, 0x68, 0x0d, 0xb2, 0xce, 0x80, 0xad, 0x6e, 0xd6, 0x19, 0x60,
	0x1e, 0x72, 0xc0, 0xc5, 0x2b, 0xb8, 0xaa, 0x92, 0xe7, 0x97, 0xba, 0xd5, 0xb2, 0xf7, 0xcf, 0x2b,
	0xb0, 0x1a, 0xfb, 0x89, 0x03, 0xda, 0x21, 0x89, 0x8b, 0xa1, 0xe9, 0x82, 0xde, 0xd6, 0x8c, 0xb6,
	0x7c, 0x5f, 0x56, 0x1e, 0xca, 0xfc, 0x05, 0xb4, 0x4d, 0x8e, 0xee, 0x3e, 0x5d, 0x57, 0x4f, 0x24,
	0xf9, 0x90, 0xc7, 0xc8, 0x88, 0xb2, 0xab, 0x92, 0x7c, 0x88, 0xe9, 0xbf, 0x9e, 0x41, 0x1f, 0x83,
	0x2b, 0x91, 0x86, 0xba, 0xd0, 0x6c, 0x1a, 0x92, 0x66, 0x1c, 0x28, 0xea, 0x43, 0x41, 0x6d, 0x88,
	0x0d, 0xfe, 0x37, 0x32, 0x68, 0x27, 0x26, 0xf2, 0x83, 0xb6, 0xd8, 0x16, 0x1b, 0xfc, 0x6f, 0x66,
	0xd0, 0x75, 0xb8, 0x1c, 0xa1, 0x6b, 0xa2, 0xa6, 0x49, 0x8a, 0x6c, 0xb4, 0x54, 0xe5, 0x50, 0x15,
	0x35, 0x8d, 0xff, 0xad, 0x0c, 0x42, 0x44, 0x6b, 0x9f, 0x43, 0xb9, 0xcf, 0xff, 0x7e, 0x06, 0x95,
	0x49, 0xf1, 0xd6, 0xa7, 0x09, 0xf5, 0xba, 0xd8, 0xd2, 0xc5, 0x06, 0xff, 0x87, 0x49, 0x55, 0x8e,
	0x95, 0x07, 0x62, 0xc3, 0x68, 0x89, 0xea, 0xb1, 0x20, 0x8b, 0xb2, 0xde, 0x3c, 0xe1, 0x7f, 0x39,
	0x9b, 0xca, 0xa2, 0x8b, 0xc7, 0x2d, 0x45, 0x15, 0x54, 0xa9, 0x79, 0xc2, 0xff, 0x4a, 0x16, 0x5d,
	0x22, 0x57, 0x9d, 0x82, 0x85, 0xd1, 0x44, 0xac, 0xd1, 0xa3, 0x13, 0xfe, 0x57, 0xb3, 0xe8, 0x32,
	0xb9, 0x40, 0xe1, 0x37, 0xd5, 0x84, 0x86, 0xa1, 0x8a, 0x1f, 0xb4, 0x45, 0x4d, 0xe7, 0xbf, 0x87,
	0x43, 0x57, 0xe0, 0x62, 0x6c, 0x41, 0x85, 0xb6, 0x7e, 0xa4, 0xa8, 0xd2, 0x17, 0xc4, 0x06, 0xff,
	0xbd, 0x5c, 0x62, 0xae, 0x2d, 0xe1, 0xe4, 0x58, 0x94, 0x75, 0xd2, 0x5d, 0x52, 0xc5, 0x06, 0xff,
	0x7d, 0x5c, 0x62, 0xdc, 0x03, 0x45, 0xad, 0x49, 0x8d, 0x86, 0x28, 0xf3, 0xdf, 0xcf, 0x25, 0xa6,
	0x2c, 0x2b, 0xfa, 0x01, 0xb9, 0x04, 0xfe, 0x03, 0x1c, 0xaa, 0xc2, 0x6e, 0x74, 0x3e, 0xa2, 0x7e,
	0xa4, 0x34, 0x30, 0x83, 0x21, 0x34, 0x9b, 0xca, 0x43, 0xb1, 0xc1, 0xff, 0x20, 0x87, 0xae, 0x92,
	0x0f, 0x7d, 0x91, 0xde, 0x6c, 0xd1, 0x84, 0x5a, 0x53, 0xe4, 0x7f, 0x88, 0x43, 0x37, 0xe0, 0x6a,
	0x54, 0x35, 0x3c, 0x59, 0x03, 0x2b, 0x1f, 0x6a, 0xf7, 0xc3, 0x1c, 0xba, 0x06, 0x95, 0xe8, 0xfe,
	0xd3, 0x69, 0x1b, 0xba, 0x74, 0x2c, 0x2a, 0x6d, 0x9d, 0xff, 0x91, 0xa4, 0x8e, 0x75, 0x45, 0x3e,
	0x68, 0x4a, 0x75, 0x9d, 0xff, 0x51, 0x0e, 0x6d, 0x11, 0x47, 0xe4, 0xb7, 0x1c, 0x2a, 0xb2, 0xc8,
	0xff, 0x18, 0x87, 0x6e, 0xc2, 0x8d, 0x14, 0x81, 0xa2, 0xac, 0x4b, 0xfa, 0x89, 0xa1, 0x2b, 0x8a,
	0xd1, 0x14, 0xd4, 0x43, 0x91, 0xff, 0x71, 0x0e, 0xbd, 0x01, 0xd7, 0x52, 0x38, 0xdb, 0xaa, 0x44,
	0xd9, 0x14, 0xf9, 0x90, 0xff, 0x09, 0x0e, 0xbd, 0x05, 0x1f, 0x8b, 0x2d, 0xbf, 0xd6, 0x6e, 0xb5,
	0x14, 0x55, 0x17, 0x1b, 0xc6, 0xb1, 0xd8, 0x90, 0x04, 0x43, 0x3f, 0x69, 0x89, 0xfc, 0x4f, 0x72,
	0xe8, 0x36, 0xec, 0x4d, 0x4a, 0x13, 0x1b, 0x86, 0x2a, 0xc8, 0x87, 0x22, 0x59, 0x1d, 0x4d, 0xd0,
	0x25, 0xed, 0x40, 0x22, 0xcb, 0xf3, 0x53, 0x1c, 0xda, 0x85, 0x72, 0x62, 0xd3, 0xc5, 0x47, 0xba,
	0x28, 0x63, 0xac, 0xf2, 0x3f, 0x9b, 0xdc, 0x81, 0xa0, 0x29, 0x5c, 0xbc, 0x9f, 0x4b, 0xf2, 0x48,
	0xb2, 0x2e, 0xaa, 0x0f, 0x84, 0x26, 0x51, 0xbf, 0xa6, 0x4a, 0xe2, 0x01, 0xff, 0x0b, 0x1c, 0x7a,
	0x1b, 0xaa, 0x51, 0xbb, 0x0b, 0x31, 0x89, 0xa1, 0xf4, 0x40, 0x90, 0x9a, 0x44, 0x9f, 0xbf, 0xe4,
	0xd0, 0xbb, 0xf0, 0x4e, 0xd2, 0xe0, 0x74, 0x55, 0x90, 0x35, 0xa1, 0xae, 0xe3, 0x71, 0x1b, 0x8a,
	0x48, 0x37, 0x59, 0x7c, 0x24, 0x69, 0xba, 0xc6, 0xff, 0x55, 0x72, 0x06, 0x4d, 0x45, 0x69, 0x19,
	0x0d, 0x51, 0x17, 0xeb, 0xd8, 0x6c, 0xbe, 0x92, 0x6c, 0xc6, 0x4a, 0x1d, 0x0b, 0xf2, 0x89, 0x71,
	0xa4, 0xb4, 0x34, 0xfe, 0xaf, 0x93, 0xca, 0x0b, 0x8d, 0x06, 0x36, 0x4e, 0x43, 0x92, 0xeb, 0xca,
	0x71, 0xab, 0x29, 0xea, 0x22, 0xff, 0x37, 0x49, 0xec, 0x0a, 0xc7, 0x35, 0xe9, 0xb0, 0xad, 0xb4,
	0x35, 0xfe, 0x6f, 0x93, 0x4d, 0xb5, 0xb6, 0x76, 0x62, 0x1c, 0x89, 0xaa, 0xc8, 0xff, 0x5d, 0x52,
	0x72, 0x80, 0x29, 0x51, 0x3d, 0x96, 0x64, 0x01, 0x2b, 0xf7, 0xf7, 0x49, 0x70, 0xc6, 0xc1, 0x4b,
	0x05, 0xfd, 0x03, 0x87, 0xde, 0x84, 0xeb, 0xc9, 0xf5, 0x95, 0x85, 0xa6, 0xa1, 0x89, 0xea, 0x03,
	0x51, 0x35, 0x44, 0x55, 0x55, 0x54, 0xfe, 0x5f, 0x93, 0x18, 0xc6, 0xb2, 0x24, 0x3c, 0x05, 0x6c,
	0x89, 0x62, 0x83, 0xff, 0x37, 0x2e, 0xc5, 0xbe, 0x0f, 0x05, 0x5d, 0x7c, 0x28, 0x9c, 0xf0, 0xff,
	0x9e, 0xd4, 0x04, 0xcb, 0x96, 0xea, 0x62, 0x6c, 0x73, 0xfe, 0x23, 0x39, 0x04, 0xeb, 0x1d, 0x98,
	0xc9, 0x7f, 0x26, 0x55, 0x7d, 0x20, 0xaa, 0x04, 0x2c, 0x04, 0x76, 0x3e, 0x60, 0xf9, 0xff, 0xe2,
	0x92, 0x7e, 0x4a, 0xd4, 0x34, 0xe1, 0x50, 0x8c, 0x98, 0xc5, 0x77, 0xe4, 0x12, 0x80, 0x3f, 0x6c,
	0x2a, 0x35, 0xa1, 0x49, 0xd7, 0x57, 0x7c, 0x20, 0xaa, 0x27, 0x0f, 0xc9, 0xe2, 0xfc, 0x49, 0x2e,
	0x61, 0xfe, 0x8c, 0xaf, 0x21, 0xd6, 0x9b, 0x92, 0x2c, 0xf2, 0x7f, 0x9a, 0x43, 0xfb, 0x70, 0x2b,
	0xa5, 0x3d, 0x86, 0x22, 0x43, 0x90, 0x99, 0xbc, 0x3f, 0xcb, 0x25, 0x66, 0xc0, 0xf8, 0x13, 0x5e,
	0xe5, 0x2f, 0x72, 0x7b, 0x4f, 0xd9, 0x6f, 0xa8, 0xfc, 0xda, 0x2b, 0x8b, 0x37, 0x04, 0xaa, 0x78,
	0x9e, 0xd8, 0xc5, 0x28, 0x61, 0xbc, 0x09, 0xe9, 0xed, 0x46, 0x8b, 0xcf, 0x4c, 0x92, 0xf5, 0x7a,
	0x8b, 0xcf, 0xa6, 0x90, 0x9b, 0x1a, 0xcf, 0xed, 0xc9, 0xc0, 0x27, 0x0b, 0x2b, 0x08, 0xc1, 0x1a,
	0x66, 0x95, 0x15, 0xe3, 0x48, 0x14, 0x1a, 0xa2, 0xaa, 0xd1, 0xdf, 0xd6, 0x60, 0xda, 0xa3, 0x80,
	0x94, 0x41, 0x9b, 0xd4, 0x3b, 0x61, 0x33, 0xf2, 0x89, 0xd9, 0x3d, 0x97, 0x28, 0x9b, 0xa8, 0x59,
	0xa0, 0x5d, 0xba, 0x92, 0xd4, 0x9f, 0x88, 0x72, 0x5d, 0x3d, 0x69, 0xe9, 0x46, 0x43, 0xd2, 0xc8,
	0x94, 0x2f, 0xa0, 0xcb, 0x34, 0x00, 0xc4, 0x9b, 0x89, 0x1f, 0xe6, 0x33, 0xe9, 0x7d, 0x99, 0x8f,
	0xe0, 0xb3, 0x7b, 0xcf, 0x89, 0x62, 0x61, 0x92, 0x4d, 0x26, 0x5a, 0xf7, 0xe3, 0x29, 0xb6, 0x35,
	0x1c, 0x6e, 0x2f, 0xa0, 0x0a, 0xec, 0x60, 0x72, 0x4b, 0x50, 0x75, 0xa9, 0x2e, 0xb5, 0x04, 0x59,
	0x37, 0xbe, 0x41, 0x91, 0x64, 0xb1, 0xc1, 0x67, 0xd0, 0x1a, 0x00, 0x6e, 0xc3, 0x7e, 0xe0, 0x81,
	0xc8, 0x67, 0xd1, 0x16, 0xf0, 0xf8, 0xbd, 0x21, 0x69, 0x75, 0x45, 0x96, 0xa9, 0xb9, 0x73, 0x68,
	0x15, 0x8a, 0x98, 0x4a, 0x8d, 0x22, 0xb7, 0xd7, 0x21, 0x0b, 0x1a, 0xcf, 0x92, 0x50, 0x19, 0xb6,
	0x34, 0x5d, 0xa3, 0xab, 0x7c, 0x20, 0xaa, 0x86, 0x22, 0x1f, 0x2a, 0x74, 0xfc, 0x8b, 0xb0, 0x19,
	0x6b, 0x39, 0x10, 0xa4, 0x26, 0x19, 0x1c, 0x4f, 0x3e, 0xda, 0xa0, 0xb5, 0xeb, 0x75, 0x51, 0xd3,
	0x0e, 0xda, 0x4d, 0x3e, 0xbb, 0x77, 0x0b, 0x20, 0x3c, 0xbf, 0xa0, 0x02, 0xe4, 0x64, 0x1c, 0x04,
	0xc8, 0x76, 0xdc, 0x57, 0x25, 0xad, 0x65, 0x88, 0x32, 0x5e, 0xc3, 0x06, 0x9f, 0xd9, 0x3b, 0x20,
	0x3b, 0x19, 0x3b, 0xac, 0xa0, 0x75, 0x28, 0x69, 0xf5, 0x46, 0x24, 0x47, 0x61, 0x84, 0xf0, 0x07,
	0x52, 0x3c, 0xac, 0x60, 0x42, 0xf8, 0xf3, 0xa8, 0x3b, 0x7f, 0x5c, 0x02, 0x4e, 0x93, 0x5a, 0xa8,
	0x05, 0x2b, 0xd1, 0x6b, 0x55, 0xe8, 0x4a, 0xec, 0xeb, 0x5a, 0xe2, 0xf6, 0x4d, 0x65, 0x77, 0x4a,
	0x2b, 0xfd, 0xb8, 0x52, 0xe5, 0x3e, 0xca, 0x66, 0xd0, 0x17, 0x23, 0x3f, 0xfe, 0x8c, 0x5e, 0x59,
	0x42, 0x6f, 0x4e, 0x56, 0x0e, 0x53, 0x6e, 0x60, 0x55, 0x66, 0xde, 0x79, 0x42, 0x06, 0xec, 0xa4,
	0xdf, 0x41, 0x47, 0x6f, 0x4d, 0x8a, 0x4f, 0xbb, 0x0f, 0x55, 0x99, 0x7d, 0x01, 0x09, 0xab, 0x9f,
	0xfa, 0xf3, 0xae, 0x88, 0xfa, 0xb3, 0x7e, 0xfe, 0x35, 0x5f, 0xfd, 0xf4, 0xbb, 0xee, 0x11, 0xf5,
	0x67, 0x5e, 0x86, 0x9f, 0xa7, 0xfe, 0x37, 0x02, 0x9a, 0xbc, 0x50, 0x88, 0xc2, 0x7b, 0x38, 0x53,
	0x2f, 0x2b, 0x56, 0x6e, 0xcc, 0xe4, 0x61, 0x9f, 0xcf, 0xbe, 0x19, 0x36, 0x53, 0x6e, 0x07, 0xa2,
	0x64, 0xdf, 0x54, 0xcd, 0xdf, 0x98, 0xcd, 0x14, 0x8e, 0x90, 0x72, 0x41, 0x2e, 0x32, 0xc2, 0xf4,
	0xcb, 0x7b, 0x91, 0x11, 0x66, 0xdd, 0xb1, 0xeb, 0x04, 0x37, 0x09, 0xe3, 0x93, 0x98, 0xe8, 0x9d,
	0x3a, 0x8b, 0x37, 0xe7, 0x70, 0xb1, 0x41, 0x0e, 0x61, 0x2d, 0x7e, 0x81, 0x0d, 0x85, 0x37, 0x31,
	0x52, 0x6f, 0xb6, 0x55, 0xd2, 0x6f, 0x2e, 0xc6, 0xcc, 0x29, 0x76, 0xa3, 0xeb, 0xcd, 0x85, 0x6e,
	0xd8, 0x54, 0x66, 0x7e, 0xe1, 0x8c, 0xa1, 0x7d, 0x8a, 0xf4, 0x59, 0x37, 0x5b, 0xe6, 0x48, 0x0f,
	0xf7, 0x32, 0x26, 0x7b, 0x62, 0x2f, 0xd3, 0x24, 0xbf, 0x31, 0x9b, 0x89, 0x2d, 0xf3, 0x17, 0x23,
	0xf7, 0x04, 0xa7, 0xe8, 0x3f, 0xeb, 0xd3, 0xed, 0x1c, 0xfd, 0x4f, 0x60, 0x2b, 0xed, 0x23, 0x47,
	0x04, 0x29, 0x33, 0xbe, 0x81, 0x54, 0x62, 0xf5, 0xfd, 0x64, 0x05, 0xfc, 0x11, 0xec, 0xa4, 0x97,
	0x66, 0x23, 0x8e, 0x60, 0x66, 0xed, 0xb6, 0xb2, 0x33, 0x51, 0x8c, 0x15, 0x7b, 0x03, 0xef, 0x45,
	0xed, 0xe0, 0x0b, 0x37, 0xce, 0x1c, 0xef, 0xc9, 0xf8, 0xf1, 0x7e, 0xc7, 0xed, 0xdd, 0x66, 0xb2,
	0xe8, 0x7f, 0x04, 0x74, 0xdc, 0xae, 0x4f, 0xf8, 0xc5, 0xec, 0x6a, 0xd3, 0x79, 0x66, 0xdf, 0x77,
	0xbc, 0xfd, 0x16, 0x6e, 0xfa, 0xc7, 0xec, 0x1a, 0x7b, 0xbf, 0x77, 0x8f, 0x10, 0x1e, 0xe7, 0x49,
	0x97, 0xbb, 0xff, 0x13, 0x00, 0x00, 0xff, 0xff, 0xe2, 0x0c, 0x40, 0x6a, 0xa2, 0x40, 0x00, 0x00,
}
