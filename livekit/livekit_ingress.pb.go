// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: livekit_ingress.proto

package livekit

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type IngressInput int32

const (
	IngressInput_RTMP_INPUT IngressInput = 0
	IngressInput_WHIP_INPUT IngressInput = 1
	IngressInput_URL_INPUT  IngressInput = 2 // Pull from the provided URL. Only HTTP url are supported, serving either a single media file or a HLS stream
)

// Enum value maps for IngressInput.
var (
	IngressInput_name = map[int32]string{
		0: "RTMP_INPUT",
		1: "WHIP_INPUT",
		2: "URL_INPUT",
	}
	IngressInput_value = map[string]int32{
		"RTMP_INPUT": 0,
		"WHIP_INPUT": 1,
		"URL_INPUT":  2,
	}
)

func (x IngressInput) Enum() *IngressInput {
	p := new(IngressInput)
	*p = x
	return p
}

func (x IngressInput) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IngressInput) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_ingress_proto_enumTypes[0].Descriptor()
}

func (IngressInput) Type() protoreflect.EnumType {
	return &file_livekit_ingress_proto_enumTypes[0]
}

func (x IngressInput) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IngressInput.Descriptor instead.
func (IngressInput) EnumDescriptor() ([]byte, []int) {
	return file_livekit_ingress_proto_rawDescGZIP(), []int{0}
}

type IngressAudioEncodingPreset int32

const (
	IngressAudioEncodingPreset_OPUS_STEREO_96KBPS IngressAudioEncodingPreset = 0 // OPUS, 2 channels, 96kbps
	IngressAudioEncodingPreset_OPUS_MONO_64KBS    IngressAudioEncodingPreset = 1 // OPUS, 1 channel, 64kbps
)

// Enum value maps for IngressAudioEncodingPreset.
var (
	IngressAudioEncodingPreset_name = map[int32]string{
		0: "OPUS_STEREO_96KBPS",
		1: "OPUS_MONO_64KBS",
	}
	IngressAudioEncodingPreset_value = map[string]int32{
		"OPUS_STEREO_96KBPS": 0,
		"OPUS_MONO_64KBS":    1,
	}
)

func (x IngressAudioEncodingPreset) Enum() *IngressAudioEncodingPreset {
	p := new(IngressAudioEncodingPreset)
	*p = x
	return p
}

func (x IngressAudioEncodingPreset) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IngressAudioEncodingPreset) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_ingress_proto_enumTypes[1].Descriptor()
}

func (IngressAudioEncodingPreset) Type() protoreflect.EnumType {
	return &file_livekit_ingress_proto_enumTypes[1]
}

func (x IngressAudioEncodingPreset) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IngressAudioEncodingPreset.Descriptor instead.
func (IngressAudioEncodingPreset) EnumDescriptor() ([]byte, []int) {
	return file_livekit_ingress_proto_rawDescGZIP(), []int{1}
}

type IngressVideoEncodingPreset int32

const (
	IngressVideoEncodingPreset_H264_720P_30FPS_3_LAYERS              IngressVideoEncodingPreset = 0 // 1280x720,  30fps, 1900kbps main layer, 3 layers total
	IngressVideoEncodingPreset_H264_1080P_30FPS_3_LAYERS             IngressVideoEncodingPreset = 1 // 1980x1080, 30fps, 3500kbps main layer, 3 layers total
	IngressVideoEncodingPreset_H264_540P_25FPS_2_LAYERS              IngressVideoEncodingPreset = 2 //  960x540,  25fps, 1000kbps  main layer, 2 layers total
	IngressVideoEncodingPreset_H264_720P_30FPS_1_LAYER               IngressVideoEncodingPreset = 3 // 1280x720,  30fps, 1900kbps, no simulcast
	IngressVideoEncodingPreset_H264_1080P_30FPS_1_LAYER              IngressVideoEncodingPreset = 4 // 1980x1080, 30fps, 3500kbps, no simulcast
	IngressVideoEncodingPreset_H264_720P_30FPS_3_LAYERS_HIGH_MOTION  IngressVideoEncodingPreset = 5 // 1280x720,  30fps, 2500kbps main layer, 3 layers total, higher bitrate for high motion, harder to encode content
	IngressVideoEncodingPreset_H264_1080P_30FPS_3_LAYERS_HIGH_MOTION IngressVideoEncodingPreset = 6 // 1980x1080, 30fps, 4500kbps main layer, 3 layers total, higher bitrate for high motion, harder to encode content
	IngressVideoEncodingPreset_H264_540P_25FPS_2_LAYERS_HIGH_MOTION  IngressVideoEncodingPreset = 7 //  960x540,  25fps, 1300kbps  main layer, 2 layers total, higher bitrate for high motion, harder to encode content
	IngressVideoEncodingPreset_H264_720P_30FPS_1_LAYER_HIGH_MOTION   IngressVideoEncodingPreset = 8 // 1280x720,  30fps, 2500kbps, no simulcast, higher bitrate for high motion, harder to encode content
	IngressVideoEncodingPreset_H264_1080P_30FPS_1_LAYER_HIGH_MOTION  IngressVideoEncodingPreset = 9 // 1980x1080, 30fps, 4500kbps, no simulcast, higher bitrate for high motion, harder to encode content
)

// Enum value maps for IngressVideoEncodingPreset.
var (
	IngressVideoEncodingPreset_name = map[int32]string{
		0: "H264_720P_30FPS_3_LAYERS",
		1: "H264_1080P_30FPS_3_LAYERS",
		2: "H264_540P_25FPS_2_LAYERS",
		3: "H264_720P_30FPS_1_LAYER",
		4: "H264_1080P_30FPS_1_LAYER",
		5: "H264_720P_30FPS_3_LAYERS_HIGH_MOTION",
		6: "H264_1080P_30FPS_3_LAYERS_HIGH_MOTION",
		7: "H264_540P_25FPS_2_LAYERS_HIGH_MOTION",
		8: "H264_720P_30FPS_1_LAYER_HIGH_MOTION",
		9: "H264_1080P_30FPS_1_LAYER_HIGH_MOTION",
	}
	IngressVideoEncodingPreset_value = map[string]int32{
		"H264_720P_30FPS_3_LAYERS":              0,
		"H264_1080P_30FPS_3_LAYERS":             1,
		"H264_540P_25FPS_2_LAYERS":              2,
		"H264_720P_30FPS_1_LAYER":               3,
		"H264_1080P_30FPS_1_LAYER":              4,
		"H264_720P_30FPS_3_LAYERS_HIGH_MOTION":  5,
		"H264_1080P_30FPS_3_LAYERS_HIGH_MOTION": 6,
		"H264_540P_25FPS_2_LAYERS_HIGH_MOTION":  7,
		"H264_720P_30FPS_1_LAYER_HIGH_MOTION":   8,
		"H264_1080P_30FPS_1_LAYER_HIGH_MOTION":  9,
	}
)

func (x IngressVideoEncodingPreset) Enum() *IngressVideoEncodingPreset {
	p := new(IngressVideoEncodingPreset)
	*p = x
	return p
}

func (x IngressVideoEncodingPreset) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IngressVideoEncodingPreset) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_ingress_proto_enumTypes[2].Descriptor()
}

func (IngressVideoEncodingPreset) Type() protoreflect.EnumType {
	return &file_livekit_ingress_proto_enumTypes[2]
}

func (x IngressVideoEncodingPreset) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IngressVideoEncodingPreset.Descriptor instead.
func (IngressVideoEncodingPreset) EnumDescriptor() ([]byte, []int) {
	return file_livekit_ingress_proto_rawDescGZIP(), []int{2}
}

type IngressState_Status int32

const (
	IngressState_ENDPOINT_INACTIVE   IngressState_Status = 0
	IngressState_ENDPOINT_BUFFERING  IngressState_Status = 1
	IngressState_ENDPOINT_PUBLISHING IngressState_Status = 2
	IngressState_ENDPOINT_ERROR      IngressState_Status = 3
	IngressState_ENDPOINT_COMPLETE   IngressState_Status = 4
)

// Enum value maps for IngressState_Status.
var (
	IngressState_Status_name = map[int32]string{
		0: "ENDPOINT_INACTIVE",
		1: "ENDPOINT_BUFFERING",
		2: "ENDPOINT_PUBLISHING",
		3: "ENDPOINT_ERROR",
		4: "ENDPOINT_COMPLETE",
	}
	IngressState_Status_value = map[string]int32{
		"ENDPOINT_INACTIVE":   0,
		"ENDPOINT_BUFFERING":  1,
		"ENDPOINT_PUBLISHING": 2,
		"ENDPOINT_ERROR":      3,
		"ENDPOINT_COMPLETE":   4,
	}
)

func (x IngressState_Status) Enum() *IngressState_Status {
	p := new(IngressState_Status)
	*p = x
	return p
}

func (x IngressState_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IngressState_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_ingress_proto_enumTypes[3].Descriptor()
}

func (IngressState_Status) Type() protoreflect.EnumType {
	return &file_livekit_ingress_proto_enumTypes[3]
}

func (x IngressState_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IngressState_Status.Descriptor instead.
func (IngressState_Status) EnumDescriptor() ([]byte, []int) {
	return file_livekit_ingress_proto_rawDescGZIP(), []int{6, 0}
}

type CreateIngressRequest struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	InputType IngressInput           `protobuf:"varint,1,opt,name=input_type,json=inputType,proto3,enum=livekit.IngressInput" json:"input_type,omitempty"`
	// Where to pull media from, only for URL input type
	Url string `protobuf:"bytes,9,opt,name=url,proto3" json:"url,omitempty"`
	// User provided identifier for the ingress
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// room to publish to
	RoomName string `protobuf:"bytes,3,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`
	// publish as participant
	ParticipantIdentity string `protobuf:"bytes,4,opt,name=participant_identity,json=participantIdentity,proto3" json:"participant_identity,omitempty"`
	// name of publishing participant (used for display only)
	ParticipantName string `protobuf:"bytes,5,opt,name=participant_name,json=participantName,proto3" json:"participant_name,omitempty"`
	// metadata associated with the publishing participant
	ParticipantMetadata string `protobuf:"bytes,10,opt,name=participant_metadata,json=participantMetadata,proto3" json:"participant_metadata,omitempty"`
	// [depreacted ] whether to pass through the incoming media without transcoding, only compatible with some input types. Use `enable_transcoding` instead.
	//
	// Deprecated: Marked as deprecated in livekit_ingress.proto.
	BypassTranscoding bool `protobuf:"varint,8,opt,name=bypass_transcoding,json=bypassTranscoding,proto3" json:"bypass_transcoding,omitempty"`
	// Whether to transcode the ingested media. Only WHIP supports disabling transcoding currently. WHIP will default to transcoding disabled. Replaces `bypass_transcoding.
	EnableTranscoding *bool                `protobuf:"varint,11,opt,name=enable_transcoding,json=enableTranscoding,proto3,oneof" json:"enable_transcoding,omitempty"`
	Audio             *IngressAudioOptions `protobuf:"bytes,6,opt,name=audio,proto3" json:"audio,omitempty"`
	Video             *IngressVideoOptions `protobuf:"bytes,7,opt,name=video,proto3" json:"video,omitempty"`
	Enabled           *bool                `protobuf:"varint,12,opt,name=enabled,proto3,oneof" json:"enabled,omitempty"` // The default value is true and when set to false, the new connection attempts will be rejected
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CreateIngressRequest) Reset() {
	*x = CreateIngressRequest{}
	mi := &file_livekit_ingress_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateIngressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateIngressRequest) ProtoMessage() {}

func (x *CreateIngressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_ingress_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateIngressRequest.ProtoReflect.Descriptor instead.
func (*CreateIngressRequest) Descriptor() ([]byte, []int) {
	return file_livekit_ingress_proto_rawDescGZIP(), []int{0}
}

func (x *CreateIngressRequest) GetInputType() IngressInput {
	if x != nil {
		return x.InputType
	}
	return IngressInput_RTMP_INPUT
}

func (x *CreateIngressRequest) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *CreateIngressRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateIngressRequest) GetRoomName() string {
	if x != nil {
		return x.RoomName
	}
	return ""
}

func (x *CreateIngressRequest) GetParticipantIdentity() string {
	if x != nil {
		return x.ParticipantIdentity
	}
	return ""
}

func (x *CreateIngressRequest) GetParticipantName() string {
	if x != nil {
		return x.ParticipantName
	}
	return ""
}

func (x *CreateIngressRequest) GetParticipantMetadata() string {
	if x != nil {
		return x.ParticipantMetadata
	}
	return ""
}

// Deprecated: Marked as deprecated in livekit_ingress.proto.
func (x *CreateIngressRequest) GetBypassTranscoding() bool {
	if x != nil {
		return x.BypassTranscoding
	}
	return false
}

func (x *CreateIngressRequest) GetEnableTranscoding() bool {
	if x != nil && x.EnableTranscoding != nil {
		return *x.EnableTranscoding
	}
	return false
}

func (x *CreateIngressRequest) GetAudio() *IngressAudioOptions {
	if x != nil {
		return x.Audio
	}
	return nil
}

func (x *CreateIngressRequest) GetVideo() *IngressVideoOptions {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *CreateIngressRequest) GetEnabled() bool {
	if x != nil && x.Enabled != nil {
		return *x.Enabled
	}
	return false
}

type IngressAudioOptions struct {
	state  protoimpl.MessageState `protogen:"open.v1"`
	Name   string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Source TrackSource            `protobuf:"varint,2,opt,name=source,proto3,enum=livekit.TrackSource" json:"source,omitempty"`
	// Types that are valid to be assigned to EncodingOptions:
	//
	//	*IngressAudioOptions_Preset
	//	*IngressAudioOptions_Options
	EncodingOptions isIngressAudioOptions_EncodingOptions `protobuf_oneof:"encoding_options"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *IngressAudioOptions) Reset() {
	*x = IngressAudioOptions{}
	mi := &file_livekit_ingress_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IngressAudioOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IngressAudioOptions) ProtoMessage() {}

func (x *IngressAudioOptions) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_ingress_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IngressAudioOptions.ProtoReflect.Descriptor instead.
func (*IngressAudioOptions) Descriptor() ([]byte, []int) {
	return file_livekit_ingress_proto_rawDescGZIP(), []int{1}
}

func (x *IngressAudioOptions) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *IngressAudioOptions) GetSource() TrackSource {
	if x != nil {
		return x.Source
	}
	return TrackSource_UNKNOWN
}

func (x *IngressAudioOptions) GetEncodingOptions() isIngressAudioOptions_EncodingOptions {
	if x != nil {
		return x.EncodingOptions
	}
	return nil
}

func (x *IngressAudioOptions) GetPreset() IngressAudioEncodingPreset {
	if x != nil {
		if x, ok := x.EncodingOptions.(*IngressAudioOptions_Preset); ok {
			return x.Preset
		}
	}
	return IngressAudioEncodingPreset_OPUS_STEREO_96KBPS
}

func (x *IngressAudioOptions) GetOptions() *IngressAudioEncodingOptions {
	if x != nil {
		if x, ok := x.EncodingOptions.(*IngressAudioOptions_Options); ok {
			return x.Options
		}
	}
	return nil
}

type isIngressAudioOptions_EncodingOptions interface {
	isIngressAudioOptions_EncodingOptions()
}

type IngressAudioOptions_Preset struct {
	Preset IngressAudioEncodingPreset `protobuf:"varint,3,opt,name=preset,proto3,enum=livekit.IngressAudioEncodingPreset,oneof"`
}

type IngressAudioOptions_Options struct {
	Options *IngressAudioEncodingOptions `protobuf:"bytes,4,opt,name=options,proto3,oneof"`
}

func (*IngressAudioOptions_Preset) isIngressAudioOptions_EncodingOptions() {}

func (*IngressAudioOptions_Options) isIngressAudioOptions_EncodingOptions() {}

type IngressVideoOptions struct {
	state  protoimpl.MessageState `protogen:"open.v1"`
	Name   string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Source TrackSource            `protobuf:"varint,2,opt,name=source,proto3,enum=livekit.TrackSource" json:"source,omitempty"`
	// Types that are valid to be assigned to EncodingOptions:
	//
	//	*IngressVideoOptions_Preset
	//	*IngressVideoOptions_Options
	EncodingOptions isIngressVideoOptions_EncodingOptions `protobuf_oneof:"encoding_options"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *IngressVideoOptions) Reset() {
	*x = IngressVideoOptions{}
	mi := &file_livekit_ingress_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IngressVideoOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IngressVideoOptions) ProtoMessage() {}

func (x *IngressVideoOptions) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_ingress_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IngressVideoOptions.ProtoReflect.Descriptor instead.
func (*IngressVideoOptions) Descriptor() ([]byte, []int) {
	return file_livekit_ingress_proto_rawDescGZIP(), []int{2}
}

func (x *IngressVideoOptions) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *IngressVideoOptions) GetSource() TrackSource {
	if x != nil {
		return x.Source
	}
	return TrackSource_UNKNOWN
}

func (x *IngressVideoOptions) GetEncodingOptions() isIngressVideoOptions_EncodingOptions {
	if x != nil {
		return x.EncodingOptions
	}
	return nil
}

func (x *IngressVideoOptions) GetPreset() IngressVideoEncodingPreset {
	if x != nil {
		if x, ok := x.EncodingOptions.(*IngressVideoOptions_Preset); ok {
			return x.Preset
		}
	}
	return IngressVideoEncodingPreset_H264_720P_30FPS_3_LAYERS
}

func (x *IngressVideoOptions) GetOptions() *IngressVideoEncodingOptions {
	if x != nil {
		if x, ok := x.EncodingOptions.(*IngressVideoOptions_Options); ok {
			return x.Options
		}
	}
	return nil
}

type isIngressVideoOptions_EncodingOptions interface {
	isIngressVideoOptions_EncodingOptions()
}

type IngressVideoOptions_Preset struct {
	Preset IngressVideoEncodingPreset `protobuf:"varint,3,opt,name=preset,proto3,enum=livekit.IngressVideoEncodingPreset,oneof"`
}

type IngressVideoOptions_Options struct {
	Options *IngressVideoEncodingOptions `protobuf:"bytes,4,opt,name=options,proto3,oneof"`
}

func (*IngressVideoOptions_Preset) isIngressVideoOptions_EncodingOptions() {}

func (*IngressVideoOptions_Options) isIngressVideoOptions_EncodingOptions() {}

type IngressAudioEncodingOptions struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// desired audio codec to publish to room
	AudioCodec    AudioCodec `protobuf:"varint,1,opt,name=audio_codec,json=audioCodec,proto3,enum=livekit.AudioCodec" json:"audio_codec,omitempty"`
	Bitrate       uint32     `protobuf:"varint,2,opt,name=bitrate,proto3" json:"bitrate,omitempty"`
	DisableDtx    bool       `protobuf:"varint,3,opt,name=disable_dtx,json=disableDtx,proto3" json:"disable_dtx,omitempty"`
	Channels      uint32     `protobuf:"varint,4,opt,name=channels,proto3" json:"channels,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IngressAudioEncodingOptions) Reset() {
	*x = IngressAudioEncodingOptions{}
	mi := &file_livekit_ingress_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IngressAudioEncodingOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IngressAudioEncodingOptions) ProtoMessage() {}

func (x *IngressAudioEncodingOptions) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_ingress_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IngressAudioEncodingOptions.ProtoReflect.Descriptor instead.
func (*IngressAudioEncodingOptions) Descriptor() ([]byte, []int) {
	return file_livekit_ingress_proto_rawDescGZIP(), []int{3}
}

func (x *IngressAudioEncodingOptions) GetAudioCodec() AudioCodec {
	if x != nil {
		return x.AudioCodec
	}
	return AudioCodec_DEFAULT_AC
}

func (x *IngressAudioEncodingOptions) GetBitrate() uint32 {
	if x != nil {
		return x.Bitrate
	}
	return 0
}

func (x *IngressAudioEncodingOptions) GetDisableDtx() bool {
	if x != nil {
		return x.DisableDtx
	}
	return false
}

func (x *IngressAudioEncodingOptions) GetChannels() uint32 {
	if x != nil {
		return x.Channels
	}
	return 0
}

type IngressVideoEncodingOptions struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// desired codec to publish to room
	VideoCodec VideoCodec `protobuf:"varint,1,opt,name=video_codec,json=videoCodec,proto3,enum=livekit.VideoCodec" json:"video_codec,omitempty"`
	FrameRate  float64    `protobuf:"fixed64,2,opt,name=frame_rate,json=frameRate,proto3" json:"frame_rate,omitempty"`
	// simulcast layers to publish, when empty, should usually be set to layers at 1/2 and 1/4 of the dimensions
	Layers        []*VideoLayer `protobuf:"bytes,3,rep,name=layers,proto3" json:"layers,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IngressVideoEncodingOptions) Reset() {
	*x = IngressVideoEncodingOptions{}
	mi := &file_livekit_ingress_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IngressVideoEncodingOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IngressVideoEncodingOptions) ProtoMessage() {}

func (x *IngressVideoEncodingOptions) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_ingress_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IngressVideoEncodingOptions.ProtoReflect.Descriptor instead.
func (*IngressVideoEncodingOptions) Descriptor() ([]byte, []int) {
	return file_livekit_ingress_proto_rawDescGZIP(), []int{4}
}

func (x *IngressVideoEncodingOptions) GetVideoCodec() VideoCodec {
	if x != nil {
		return x.VideoCodec
	}
	return VideoCodec_DEFAULT_VC
}

func (x *IngressVideoEncodingOptions) GetFrameRate() float64 {
	if x != nil {
		return x.FrameRate
	}
	return 0
}

func (x *IngressVideoEncodingOptions) GetLayers() []*VideoLayer {
	if x != nil {
		return x.Layers
	}
	return nil
}

type IngressInfo struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	IngressId string                 `protobuf:"bytes,1,opt,name=ingress_id,json=ingressId,proto3" json:"ingress_id,omitempty"`
	Name      string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	StreamKey string                 `protobuf:"bytes,3,opt,name=stream_key,json=streamKey,proto3" json:"stream_key,omitempty"`
	Url       string                 `protobuf:"bytes,4,opt,name=url,proto3" json:"url,omitempty"` // URL to point the encoder to for push (RTMP, WHIP), or location to pull media from for pull (URL)
	// for RTMP input, it'll be a rtmp:// URL
	// for FILE input, it'll be a http:// URL
	// for SRT input, it'll be a srt:// URL
	InputType IngressInput `protobuf:"varint,5,opt,name=input_type,json=inputType,proto3,enum=livekit.IngressInput" json:"input_type,omitempty"`
	// Deprecated: Marked as deprecated in livekit_ingress.proto.
	BypassTranscoding   bool                 `protobuf:"varint,13,opt,name=bypass_transcoding,json=bypassTranscoding,proto3" json:"bypass_transcoding,omitempty"`
	EnableTranscoding   *bool                `protobuf:"varint,15,opt,name=enable_transcoding,json=enableTranscoding,proto3,oneof" json:"enable_transcoding,omitempty"`
	Audio               *IngressAudioOptions `protobuf:"bytes,6,opt,name=audio,proto3" json:"audio,omitempty"`
	Video               *IngressVideoOptions `protobuf:"bytes,7,opt,name=video,proto3" json:"video,omitempty"`
	RoomName            string               `protobuf:"bytes,8,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`
	ParticipantIdentity string               `protobuf:"bytes,9,opt,name=participant_identity,json=participantIdentity,proto3" json:"participant_identity,omitempty"`
	ParticipantName     string               `protobuf:"bytes,10,opt,name=participant_name,json=participantName,proto3" json:"participant_name,omitempty"`
	ParticipantMetadata string               `protobuf:"bytes,14,opt,name=participant_metadata,json=participantMetadata,proto3" json:"participant_metadata,omitempty"`
	Reusable            bool                 `protobuf:"varint,11,opt,name=reusable,proto3" json:"reusable,omitempty"`
	State               *IngressState        `protobuf:"bytes,12,opt,name=state,proto3" json:"state,omitempty"`            // Description of error/stream non compliance and debug info for publisher otherwise (received bitrate, resolution, bandwidth)
	Enabled             *bool                `protobuf:"varint,16,opt,name=enabled,proto3,oneof" json:"enabled,omitempty"` // The default value is true and when set to false, the new connection attempts will be rejected
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *IngressInfo) Reset() {
	*x = IngressInfo{}
	mi := &file_livekit_ingress_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IngressInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IngressInfo) ProtoMessage() {}

func (x *IngressInfo) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_ingress_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IngressInfo.ProtoReflect.Descriptor instead.
func (*IngressInfo) Descriptor() ([]byte, []int) {
	return file_livekit_ingress_proto_rawDescGZIP(), []int{5}
}

func (x *IngressInfo) GetIngressId() string {
	if x != nil {
		return x.IngressId
	}
	return ""
}

func (x *IngressInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *IngressInfo) GetStreamKey() string {
	if x != nil {
		return x.StreamKey
	}
	return ""
}

func (x *IngressInfo) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *IngressInfo) GetInputType() IngressInput {
	if x != nil {
		return x.InputType
	}
	return IngressInput_RTMP_INPUT
}

// Deprecated: Marked as deprecated in livekit_ingress.proto.
func (x *IngressInfo) GetBypassTranscoding() bool {
	if x != nil {
		return x.BypassTranscoding
	}
	return false
}

func (x *IngressInfo) GetEnableTranscoding() bool {
	if x != nil && x.EnableTranscoding != nil {
		return *x.EnableTranscoding
	}
	return false
}

func (x *IngressInfo) GetAudio() *IngressAudioOptions {
	if x != nil {
		return x.Audio
	}
	return nil
}

func (x *IngressInfo) GetVideo() *IngressVideoOptions {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *IngressInfo) GetRoomName() string {
	if x != nil {
		return x.RoomName
	}
	return ""
}

func (x *IngressInfo) GetParticipantIdentity() string {
	if x != nil {
		return x.ParticipantIdentity
	}
	return ""
}

func (x *IngressInfo) GetParticipantName() string {
	if x != nil {
		return x.ParticipantName
	}
	return ""
}

func (x *IngressInfo) GetParticipantMetadata() string {
	if x != nil {
		return x.ParticipantMetadata
	}
	return ""
}

func (x *IngressInfo) GetReusable() bool {
	if x != nil {
		return x.Reusable
	}
	return false
}

func (x *IngressInfo) GetState() *IngressState {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *IngressInfo) GetEnabled() bool {
	if x != nil && x.Enabled != nil {
		return *x.Enabled
	}
	return false
}

type IngressState struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        IngressState_Status    `protobuf:"varint,1,opt,name=status,proto3,enum=livekit.IngressState_Status" json:"status,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"` // Error/non compliance description if any
	Video         *InputVideoState       `protobuf:"bytes,3,opt,name=video,proto3" json:"video,omitempty"`
	Audio         *InputAudioState       `protobuf:"bytes,4,opt,name=audio,proto3" json:"audio,omitempty"`
	RoomId        string                 `protobuf:"bytes,5,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"` // ID of the current/previous room published to
	StartedAt     int64                  `protobuf:"varint,7,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	EndedAt       int64                  `protobuf:"varint,8,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`
	UpdatedAt     int64                  `protobuf:"varint,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	ResourceId    string                 `protobuf:"bytes,9,opt,name=resource_id,json=resourceId,proto3" json:"resource_id,omitempty"`
	Tracks        []*TrackInfo           `protobuf:"bytes,6,rep,name=tracks,proto3" json:"tracks,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IngressState) Reset() {
	*x = IngressState{}
	mi := &file_livekit_ingress_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IngressState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IngressState) ProtoMessage() {}

func (x *IngressState) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_ingress_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IngressState.ProtoReflect.Descriptor instead.
func (*IngressState) Descriptor() ([]byte, []int) {
	return file_livekit_ingress_proto_rawDescGZIP(), []int{6}
}

func (x *IngressState) GetStatus() IngressState_Status {
	if x != nil {
		return x.Status
	}
	return IngressState_ENDPOINT_INACTIVE
}

func (x *IngressState) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *IngressState) GetVideo() *InputVideoState {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *IngressState) GetAudio() *InputAudioState {
	if x != nil {
		return x.Audio
	}
	return nil
}

func (x *IngressState) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *IngressState) GetStartedAt() int64 {
	if x != nil {
		return x.StartedAt
	}
	return 0
}

func (x *IngressState) GetEndedAt() int64 {
	if x != nil {
		return x.EndedAt
	}
	return 0
}

func (x *IngressState) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *IngressState) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

func (x *IngressState) GetTracks() []*TrackInfo {
	if x != nil {
		return x.Tracks
	}
	return nil
}

type InputVideoState struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	MimeType       string                 `protobuf:"bytes,1,opt,name=mime_type,json=mimeType,proto3" json:"mime_type,omitempty"`
	AverageBitrate uint32                 `protobuf:"varint,2,opt,name=average_bitrate,json=averageBitrate,proto3" json:"average_bitrate,omitempty"`
	Width          uint32                 `protobuf:"varint,3,opt,name=width,proto3" json:"width,omitempty"`
	Height         uint32                 `protobuf:"varint,4,opt,name=height,proto3" json:"height,omitempty"`
	Framerate      float64                `protobuf:"fixed64,5,opt,name=framerate,proto3" json:"framerate,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *InputVideoState) Reset() {
	*x = InputVideoState{}
	mi := &file_livekit_ingress_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InputVideoState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputVideoState) ProtoMessage() {}

func (x *InputVideoState) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_ingress_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputVideoState.ProtoReflect.Descriptor instead.
func (*InputVideoState) Descriptor() ([]byte, []int) {
	return file_livekit_ingress_proto_rawDescGZIP(), []int{7}
}

func (x *InputVideoState) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

func (x *InputVideoState) GetAverageBitrate() uint32 {
	if x != nil {
		return x.AverageBitrate
	}
	return 0
}

func (x *InputVideoState) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *InputVideoState) GetHeight() uint32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *InputVideoState) GetFramerate() float64 {
	if x != nil {
		return x.Framerate
	}
	return 0
}

type InputAudioState struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	MimeType       string                 `protobuf:"bytes,1,opt,name=mime_type,json=mimeType,proto3" json:"mime_type,omitempty"`
	AverageBitrate uint32                 `protobuf:"varint,2,opt,name=average_bitrate,json=averageBitrate,proto3" json:"average_bitrate,omitempty"`
	Channels       uint32                 `protobuf:"varint,3,opt,name=channels,proto3" json:"channels,omitempty"`
	SampleRate     uint32                 `protobuf:"varint,4,opt,name=sample_rate,json=sampleRate,proto3" json:"sample_rate,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *InputAudioState) Reset() {
	*x = InputAudioState{}
	mi := &file_livekit_ingress_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InputAudioState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputAudioState) ProtoMessage() {}

func (x *InputAudioState) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_ingress_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputAudioState.ProtoReflect.Descriptor instead.
func (*InputAudioState) Descriptor() ([]byte, []int) {
	return file_livekit_ingress_proto_rawDescGZIP(), []int{8}
}

func (x *InputAudioState) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

func (x *InputAudioState) GetAverageBitrate() uint32 {
	if x != nil {
		return x.AverageBitrate
	}
	return 0
}

func (x *InputAudioState) GetChannels() uint32 {
	if x != nil {
		return x.Channels
	}
	return 0
}

func (x *InputAudioState) GetSampleRate() uint32 {
	if x != nil {
		return x.SampleRate
	}
	return 0
}

type UpdateIngressRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	IngressId           string                 `protobuf:"bytes,1,opt,name=ingress_id,json=ingressId,proto3" json:"ingress_id,omitempty"`
	Name                string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	RoomName            string                 `protobuf:"bytes,3,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`
	ParticipantIdentity string                 `protobuf:"bytes,4,opt,name=participant_identity,json=participantIdentity,proto3" json:"participant_identity,omitempty"`
	ParticipantName     string                 `protobuf:"bytes,5,opt,name=participant_name,json=participantName,proto3" json:"participant_name,omitempty"`
	ParticipantMetadata string                 `protobuf:"bytes,9,opt,name=participant_metadata,json=participantMetadata,proto3" json:"participant_metadata,omitempty"`
	// Deprecated: Marked as deprecated in livekit_ingress.proto.
	BypassTranscoding *bool                `protobuf:"varint,8,opt,name=bypass_transcoding,json=bypassTranscoding,proto3,oneof" json:"bypass_transcoding,omitempty"`
	EnableTranscoding *bool                `protobuf:"varint,10,opt,name=enable_transcoding,json=enableTranscoding,proto3,oneof" json:"enable_transcoding,omitempty"`
	Audio             *IngressAudioOptions `protobuf:"bytes,6,opt,name=audio,proto3" json:"audio,omitempty"`
	Video             *IngressVideoOptions `protobuf:"bytes,7,opt,name=video,proto3" json:"video,omitempty"`
	Enabled           *bool                `protobuf:"varint,11,opt,name=enabled,proto3,oneof" json:"enabled,omitempty"` // The default value is true and when set to false, the new connection attempts will be rejected
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *UpdateIngressRequest) Reset() {
	*x = UpdateIngressRequest{}
	mi := &file_livekit_ingress_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateIngressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateIngressRequest) ProtoMessage() {}

func (x *UpdateIngressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_ingress_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateIngressRequest.ProtoReflect.Descriptor instead.
func (*UpdateIngressRequest) Descriptor() ([]byte, []int) {
	return file_livekit_ingress_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateIngressRequest) GetIngressId() string {
	if x != nil {
		return x.IngressId
	}
	return ""
}

func (x *UpdateIngressRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateIngressRequest) GetRoomName() string {
	if x != nil {
		return x.RoomName
	}
	return ""
}

func (x *UpdateIngressRequest) GetParticipantIdentity() string {
	if x != nil {
		return x.ParticipantIdentity
	}
	return ""
}

func (x *UpdateIngressRequest) GetParticipantName() string {
	if x != nil {
		return x.ParticipantName
	}
	return ""
}

func (x *UpdateIngressRequest) GetParticipantMetadata() string {
	if x != nil {
		return x.ParticipantMetadata
	}
	return ""
}

// Deprecated: Marked as deprecated in livekit_ingress.proto.
func (x *UpdateIngressRequest) GetBypassTranscoding() bool {
	if x != nil && x.BypassTranscoding != nil {
		return *x.BypassTranscoding
	}
	return false
}

func (x *UpdateIngressRequest) GetEnableTranscoding() bool {
	if x != nil && x.EnableTranscoding != nil {
		return *x.EnableTranscoding
	}
	return false
}

func (x *UpdateIngressRequest) GetAudio() *IngressAudioOptions {
	if x != nil {
		return x.Audio
	}
	return nil
}

func (x *UpdateIngressRequest) GetVideo() *IngressVideoOptions {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *UpdateIngressRequest) GetEnabled() bool {
	if x != nil && x.Enabled != nil {
		return *x.Enabled
	}
	return false
}

type ListIngressRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// when blank, lists all ingress endpoints
	RoomName      string `protobuf:"bytes,1,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`    // (optional, filter by room name)
	IngressId     string `protobuf:"bytes,2,opt,name=ingress_id,json=ingressId,proto3" json:"ingress_id,omitempty"` // (optional, filter by ingress ID)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListIngressRequest) Reset() {
	*x = ListIngressRequest{}
	mi := &file_livekit_ingress_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListIngressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListIngressRequest) ProtoMessage() {}

func (x *ListIngressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_ingress_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListIngressRequest.ProtoReflect.Descriptor instead.
func (*ListIngressRequest) Descriptor() ([]byte, []int) {
	return file_livekit_ingress_proto_rawDescGZIP(), []int{10}
}

func (x *ListIngressRequest) GetRoomName() string {
	if x != nil {
		return x.RoomName
	}
	return ""
}

func (x *ListIngressRequest) GetIngressId() string {
	if x != nil {
		return x.IngressId
	}
	return ""
}

type ListIngressResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*IngressInfo         `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListIngressResponse) Reset() {
	*x = ListIngressResponse{}
	mi := &file_livekit_ingress_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListIngressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListIngressResponse) ProtoMessage() {}

func (x *ListIngressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_ingress_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListIngressResponse.ProtoReflect.Descriptor instead.
func (*ListIngressResponse) Descriptor() ([]byte, []int) {
	return file_livekit_ingress_proto_rawDescGZIP(), []int{11}
}

func (x *ListIngressResponse) GetItems() []*IngressInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

type DeleteIngressRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IngressId     string                 `protobuf:"bytes,1,opt,name=ingress_id,json=ingressId,proto3" json:"ingress_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteIngressRequest) Reset() {
	*x = DeleteIngressRequest{}
	mi := &file_livekit_ingress_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteIngressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteIngressRequest) ProtoMessage() {}

func (x *DeleteIngressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_ingress_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteIngressRequest.ProtoReflect.Descriptor instead.
func (*DeleteIngressRequest) Descriptor() ([]byte, []int) {
	return file_livekit_ingress_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteIngressRequest) GetIngressId() string {
	if x != nil {
		return x.IngressId
	}
	return ""
}

var File_livekit_ingress_proto protoreflect.FileDescriptor

const file_livekit_ingress_proto_rawDesc = "" +
	"\n" +
	"\x15livekit_ingress.proto\x12\alivekit\x1a\x14livekit_models.proto\"\xb1\x04\n" +
	"\x14CreateIngressRequest\x124\n" +
	"\n" +
	"input_type\x18\x01 \x01(\x0e2\x15.livekit.IngressInputR\tinputType\x12\x10\n" +
	"\x03url\x18\t \x01(\tR\x03url\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1b\n" +
	"\troom_name\x18\x03 \x01(\tR\broomName\x121\n" +
	"\x14participant_identity\x18\x04 \x01(\tR\x13participantIdentity\x12)\n" +
	"\x10participant_name\x18\x05 \x01(\tR\x0fparticipantName\x121\n" +
	"\x14participant_metadata\x18\n" +
	" \x01(\tR\x13participantMetadata\x121\n" +
	"\x12bypass_transcoding\x18\b \x01(\bB\x02\x18\x01R\x11bypassTranscoding\x122\n" +
	"\x12enable_transcoding\x18\v \x01(\bH\x00R\x11enableTranscoding\x88\x01\x01\x122\n" +
	"\x05audio\x18\x06 \x01(\v2\x1c.livekit.IngressAudioOptionsR\x05audio\x122\n" +
	"\x05video\x18\a \x01(\v2\x1c.livekit.IngressVideoOptionsR\x05video\x12\x1d\n" +
	"\aenabled\x18\f \x01(\bH\x01R\aenabled\x88\x01\x01B\x15\n" +
	"\x13_enable_transcodingB\n" +
	"\n" +
	"\b_enabled\"\xec\x01\n" +
	"\x13IngressAudioOptions\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12,\n" +
	"\x06source\x18\x02 \x01(\x0e2\x14.livekit.TrackSourceR\x06source\x12=\n" +
	"\x06preset\x18\x03 \x01(\x0e2#.livekit.IngressAudioEncodingPresetH\x00R\x06preset\x12@\n" +
	"\aoptions\x18\x04 \x01(\v2$.livekit.IngressAudioEncodingOptionsH\x00R\aoptionsB\x12\n" +
	"\x10encoding_options\"\xec\x01\n" +
	"\x13IngressVideoOptions\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12,\n" +
	"\x06source\x18\x02 \x01(\x0e2\x14.livekit.TrackSourceR\x06source\x12=\n" +
	"\x06preset\x18\x03 \x01(\x0e2#.livekit.IngressVideoEncodingPresetH\x00R\x06preset\x12@\n" +
	"\aoptions\x18\x04 \x01(\v2$.livekit.IngressVideoEncodingOptionsH\x00R\aoptionsB\x12\n" +
	"\x10encoding_options\"\xaa\x01\n" +
	"\x1bIngressAudioEncodingOptions\x124\n" +
	"\vaudio_codec\x18\x01 \x01(\x0e2\x13.livekit.AudioCodecR\n" +
	"audioCodec\x12\x18\n" +
	"\abitrate\x18\x02 \x01(\rR\abitrate\x12\x1f\n" +
	"\vdisable_dtx\x18\x03 \x01(\bR\n" +
	"disableDtx\x12\x1a\n" +
	"\bchannels\x18\x04 \x01(\rR\bchannels\"\x9f\x01\n" +
	"\x1bIngressVideoEncodingOptions\x124\n" +
	"\vvideo_codec\x18\x01 \x01(\x0e2\x13.livekit.VideoCodecR\n" +
	"videoCodec\x12\x1d\n" +
	"\n" +
	"frame_rate\x18\x02 \x01(\x01R\tframeRate\x12+\n" +
	"\x06layers\x18\x03 \x03(\v2\x13.livekit.VideoLayerR\x06layers\"\xaf\x05\n" +
	"\vIngressInfo\x12\x1d\n" +
	"\n" +
	"ingress_id\x18\x01 \x01(\tR\tingressId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1d\n" +
	"\n" +
	"stream_key\x18\x03 \x01(\tR\tstreamKey\x12\x10\n" +
	"\x03url\x18\x04 \x01(\tR\x03url\x124\n" +
	"\n" +
	"input_type\x18\x05 \x01(\x0e2\x15.livekit.IngressInputR\tinputType\x121\n" +
	"\x12bypass_transcoding\x18\r \x01(\bB\x02\x18\x01R\x11bypassTranscoding\x122\n" +
	"\x12enable_transcoding\x18\x0f \x01(\bH\x00R\x11enableTranscoding\x88\x01\x01\x122\n" +
	"\x05audio\x18\x06 \x01(\v2\x1c.livekit.IngressAudioOptionsR\x05audio\x122\n" +
	"\x05video\x18\a \x01(\v2\x1c.livekit.IngressVideoOptionsR\x05video\x12\x1b\n" +
	"\troom_name\x18\b \x01(\tR\broomName\x121\n" +
	"\x14participant_identity\x18\t \x01(\tR\x13participantIdentity\x12)\n" +
	"\x10participant_name\x18\n" +
	" \x01(\tR\x0fparticipantName\x121\n" +
	"\x14participant_metadata\x18\x0e \x01(\tR\x13participantMetadata\x12\x1a\n" +
	"\breusable\x18\v \x01(\bR\breusable\x12+\n" +
	"\x05state\x18\f \x01(\v2\x15.livekit.IngressStateR\x05state\x12\x1d\n" +
	"\aenabled\x18\x10 \x01(\bH\x01R\aenabled\x88\x01\x01B\x15\n" +
	"\x13_enable_transcodingB\n" +
	"\n" +
	"\b_enabled\"\xf6\x03\n" +
	"\fIngressState\x124\n" +
	"\x06status\x18\x01 \x01(\x0e2\x1c.livekit.IngressState.StatusR\x06status\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\x12.\n" +
	"\x05video\x18\x03 \x01(\v2\x18.livekit.InputVideoStateR\x05video\x12.\n" +
	"\x05audio\x18\x04 \x01(\v2\x18.livekit.InputAudioStateR\x05audio\x12\x17\n" +
	"\aroom_id\x18\x05 \x01(\tR\x06roomId\x12\x1d\n" +
	"\n" +
	"started_at\x18\a \x01(\x03R\tstartedAt\x12\x19\n" +
	"\bended_at\x18\b \x01(\x03R\aendedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\n" +
	" \x01(\x03R\tupdatedAt\x12\x1f\n" +
	"\vresource_id\x18\t \x01(\tR\n" +
	"resourceId\x12*\n" +
	"\x06tracks\x18\x06 \x03(\v2\x12.livekit.TrackInfoR\x06tracks\"{\n" +
	"\x06Status\x12\x15\n" +
	"\x11ENDPOINT_INACTIVE\x10\x00\x12\x16\n" +
	"\x12ENDPOINT_BUFFERING\x10\x01\x12\x17\n" +
	"\x13ENDPOINT_PUBLISHING\x10\x02\x12\x12\n" +
	"\x0eENDPOINT_ERROR\x10\x03\x12\x15\n" +
	"\x11ENDPOINT_COMPLETE\x10\x04\"\xa3\x01\n" +
	"\x0fInputVideoState\x12\x1b\n" +
	"\tmime_type\x18\x01 \x01(\tR\bmimeType\x12'\n" +
	"\x0faverage_bitrate\x18\x02 \x01(\rR\x0eaverageBitrate\x12\x14\n" +
	"\x05width\x18\x03 \x01(\rR\x05width\x12\x16\n" +
	"\x06height\x18\x04 \x01(\rR\x06height\x12\x1c\n" +
	"\tframerate\x18\x05 \x01(\x01R\tframerate\"\x94\x01\n" +
	"\x0fInputAudioState\x12\x1b\n" +
	"\tmime_type\x18\x01 \x01(\tR\bmimeType\x12'\n" +
	"\x0faverage_bitrate\x18\x02 \x01(\rR\x0eaverageBitrate\x12\x1a\n" +
	"\bchannels\x18\x03 \x01(\rR\bchannels\x12\x1f\n" +
	"\vsample_rate\x18\x04 \x01(\rR\n" +
	"sampleRate\"\xa4\x04\n" +
	"\x14UpdateIngressRequest\x12\x1d\n" +
	"\n" +
	"ingress_id\x18\x01 \x01(\tR\tingressId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1b\n" +
	"\troom_name\x18\x03 \x01(\tR\broomName\x121\n" +
	"\x14participant_identity\x18\x04 \x01(\tR\x13participantIdentity\x12)\n" +
	"\x10participant_name\x18\x05 \x01(\tR\x0fparticipantName\x121\n" +
	"\x14participant_metadata\x18\t \x01(\tR\x13participantMetadata\x126\n" +
	"\x12bypass_transcoding\x18\b \x01(\bB\x02\x18\x01H\x00R\x11bypassTranscoding\x88\x01\x01\x122\n" +
	"\x12enable_transcoding\x18\n" +
	" \x01(\bH\x01R\x11enableTranscoding\x88\x01\x01\x122\n" +
	"\x05audio\x18\x06 \x01(\v2\x1c.livekit.IngressAudioOptionsR\x05audio\x122\n" +
	"\x05video\x18\a \x01(\v2\x1c.livekit.IngressVideoOptionsR\x05video\x12\x1d\n" +
	"\aenabled\x18\v \x01(\bH\x02R\aenabled\x88\x01\x01B\x15\n" +
	"\x13_bypass_transcodingB\x15\n" +
	"\x13_enable_transcodingB\n" +
	"\n" +
	"\b_enabled\"P\n" +
	"\x12ListIngressRequest\x12\x1b\n" +
	"\troom_name\x18\x01 \x01(\tR\broomName\x12\x1d\n" +
	"\n" +
	"ingress_id\x18\x02 \x01(\tR\tingressId\"A\n" +
	"\x13ListIngressResponse\x12*\n" +
	"\x05items\x18\x01 \x03(\v2\x14.livekit.IngressInfoR\x05items\"5\n" +
	"\x14DeleteIngressRequest\x12\x1d\n" +
	"\n" +
	"ingress_id\x18\x01 \x01(\tR\tingressId*=\n" +
	"\fIngressInput\x12\x0e\n" +
	"\n" +
	"RTMP_INPUT\x10\x00\x12\x0e\n" +
	"\n" +
	"WHIP_INPUT\x10\x01\x12\r\n" +
	"\tURL_INPUT\x10\x02*I\n" +
	"\x1aIngressAudioEncodingPreset\x12\x16\n" +
	"\x12OPUS_STEREO_96KBPS\x10\x00\x12\x13\n" +
	"\x0fOPUS_MONO_64KBS\x10\x01*\x84\x03\n" +
	"\x1aIngressVideoEncodingPreset\x12\x1c\n" +
	"\x18H264_720P_30FPS_3_LAYERS\x10\x00\x12\x1d\n" +
	"\x19H264_1080P_30FPS_3_LAYERS\x10\x01\x12\x1c\n" +
	"\x18H264_540P_25FPS_2_LAYERS\x10\x02\x12\x1b\n" +
	"\x17H264_720P_30FPS_1_LAYER\x10\x03\x12\x1c\n" +
	"\x18H264_1080P_30FPS_1_LAYER\x10\x04\x12(\n" +
	"$H264_720P_30FPS_3_LAYERS_HIGH_MOTION\x10\x05\x12)\n" +
	"%H264_1080P_30FPS_3_LAYERS_HIGH_MOTION\x10\x06\x12(\n" +
	"$H264_540P_25FPS_2_LAYERS_HIGH_MOTION\x10\a\x12'\n" +
	"#H264_720P_30FPS_1_LAYER_HIGH_MOTION\x10\b\x12(\n" +
	"$H264_1080P_30FPS_1_LAYER_HIGH_MOTION\x10\t2\xa5\x02\n" +
	"\aIngress\x12D\n" +
	"\rCreateIngress\x12\x1d.livekit.CreateIngressRequest\x1a\x14.livekit.IngressInfo\x12D\n" +
	"\rUpdateIngress\x12\x1d.livekit.UpdateIngressRequest\x1a\x14.livekit.IngressInfo\x12H\n" +
	"\vListIngress\x12\x1b.livekit.ListIngressRequest\x1a\x1c.livekit.ListIngressResponse\x12D\n" +
	"\rDeleteIngress\x12\x1d.livekit.DeleteIngressRequest\x1a\x14.livekit.IngressInfoBFZ#github.com/livekit/protocol/livekit\xaa\x02\rLiveKit.Proto\xea\x02\x0eLiveKit::Protob\x06proto3"

var (
	file_livekit_ingress_proto_rawDescOnce sync.Once
	file_livekit_ingress_proto_rawDescData []byte
)

func file_livekit_ingress_proto_rawDescGZIP() []byte {
	file_livekit_ingress_proto_rawDescOnce.Do(func() {
		file_livekit_ingress_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_livekit_ingress_proto_rawDesc), len(file_livekit_ingress_proto_rawDesc)))
	})
	return file_livekit_ingress_proto_rawDescData
}

var file_livekit_ingress_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_livekit_ingress_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_livekit_ingress_proto_goTypes = []any{
	(IngressInput)(0),                   // 0: livekit.IngressInput
	(IngressAudioEncodingPreset)(0),     // 1: livekit.IngressAudioEncodingPreset
	(IngressVideoEncodingPreset)(0),     // 2: livekit.IngressVideoEncodingPreset
	(IngressState_Status)(0),            // 3: livekit.IngressState.Status
	(*CreateIngressRequest)(nil),        // 4: livekit.CreateIngressRequest
	(*IngressAudioOptions)(nil),         // 5: livekit.IngressAudioOptions
	(*IngressVideoOptions)(nil),         // 6: livekit.IngressVideoOptions
	(*IngressAudioEncodingOptions)(nil), // 7: livekit.IngressAudioEncodingOptions
	(*IngressVideoEncodingOptions)(nil), // 8: livekit.IngressVideoEncodingOptions
	(*IngressInfo)(nil),                 // 9: livekit.IngressInfo
	(*IngressState)(nil),                // 10: livekit.IngressState
	(*InputVideoState)(nil),             // 11: livekit.InputVideoState
	(*InputAudioState)(nil),             // 12: livekit.InputAudioState
	(*UpdateIngressRequest)(nil),        // 13: livekit.UpdateIngressRequest
	(*ListIngressRequest)(nil),          // 14: livekit.ListIngressRequest
	(*ListIngressResponse)(nil),         // 15: livekit.ListIngressResponse
	(*DeleteIngressRequest)(nil),        // 16: livekit.DeleteIngressRequest
	(TrackSource)(0),                    // 17: livekit.TrackSource
	(AudioCodec)(0),                     // 18: livekit.AudioCodec
	(VideoCodec)(0),                     // 19: livekit.VideoCodec
	(*VideoLayer)(nil),                  // 20: livekit.VideoLayer
	(*TrackInfo)(nil),                   // 21: livekit.TrackInfo
}
var file_livekit_ingress_proto_depIdxs = []int32{
	0,  // 0: livekit.CreateIngressRequest.input_type:type_name -> livekit.IngressInput
	5,  // 1: livekit.CreateIngressRequest.audio:type_name -> livekit.IngressAudioOptions
	6,  // 2: livekit.CreateIngressRequest.video:type_name -> livekit.IngressVideoOptions
	17, // 3: livekit.IngressAudioOptions.source:type_name -> livekit.TrackSource
	1,  // 4: livekit.IngressAudioOptions.preset:type_name -> livekit.IngressAudioEncodingPreset
	7,  // 5: livekit.IngressAudioOptions.options:type_name -> livekit.IngressAudioEncodingOptions
	17, // 6: livekit.IngressVideoOptions.source:type_name -> livekit.TrackSource
	2,  // 7: livekit.IngressVideoOptions.preset:type_name -> livekit.IngressVideoEncodingPreset
	8,  // 8: livekit.IngressVideoOptions.options:type_name -> livekit.IngressVideoEncodingOptions
	18, // 9: livekit.IngressAudioEncodingOptions.audio_codec:type_name -> livekit.AudioCodec
	19, // 10: livekit.IngressVideoEncodingOptions.video_codec:type_name -> livekit.VideoCodec
	20, // 11: livekit.IngressVideoEncodingOptions.layers:type_name -> livekit.VideoLayer
	0,  // 12: livekit.IngressInfo.input_type:type_name -> livekit.IngressInput
	5,  // 13: livekit.IngressInfo.audio:type_name -> livekit.IngressAudioOptions
	6,  // 14: livekit.IngressInfo.video:type_name -> livekit.IngressVideoOptions
	10, // 15: livekit.IngressInfo.state:type_name -> livekit.IngressState
	3,  // 16: livekit.IngressState.status:type_name -> livekit.IngressState.Status
	11, // 17: livekit.IngressState.video:type_name -> livekit.InputVideoState
	12, // 18: livekit.IngressState.audio:type_name -> livekit.InputAudioState
	21, // 19: livekit.IngressState.tracks:type_name -> livekit.TrackInfo
	5,  // 20: livekit.UpdateIngressRequest.audio:type_name -> livekit.IngressAudioOptions
	6,  // 21: livekit.UpdateIngressRequest.video:type_name -> livekit.IngressVideoOptions
	9,  // 22: livekit.ListIngressResponse.items:type_name -> livekit.IngressInfo
	4,  // 23: livekit.Ingress.CreateIngress:input_type -> livekit.CreateIngressRequest
	13, // 24: livekit.Ingress.UpdateIngress:input_type -> livekit.UpdateIngressRequest
	14, // 25: livekit.Ingress.ListIngress:input_type -> livekit.ListIngressRequest
	16, // 26: livekit.Ingress.DeleteIngress:input_type -> livekit.DeleteIngressRequest
	9,  // 27: livekit.Ingress.CreateIngress:output_type -> livekit.IngressInfo
	9,  // 28: livekit.Ingress.UpdateIngress:output_type -> livekit.IngressInfo
	15, // 29: livekit.Ingress.ListIngress:output_type -> livekit.ListIngressResponse
	9,  // 30: livekit.Ingress.DeleteIngress:output_type -> livekit.IngressInfo
	27, // [27:31] is the sub-list for method output_type
	23, // [23:27] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_livekit_ingress_proto_init() }
func file_livekit_ingress_proto_init() {
	if File_livekit_ingress_proto != nil {
		return
	}
	file_livekit_models_proto_init()
	file_livekit_ingress_proto_msgTypes[0].OneofWrappers = []any{}
	file_livekit_ingress_proto_msgTypes[1].OneofWrappers = []any{
		(*IngressAudioOptions_Preset)(nil),
		(*IngressAudioOptions_Options)(nil),
	}
	file_livekit_ingress_proto_msgTypes[2].OneofWrappers = []any{
		(*IngressVideoOptions_Preset)(nil),
		(*IngressVideoOptions_Options)(nil),
	}
	file_livekit_ingress_proto_msgTypes[5].OneofWrappers = []any{}
	file_livekit_ingress_proto_msgTypes[9].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_livekit_ingress_proto_rawDesc), len(file_livekit_ingress_proto_rawDesc)),
			NumEnums:      4,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_livekit_ingress_proto_goTypes,
		DependencyIndexes: file_livekit_ingress_proto_depIdxs,
		EnumInfos:         file_livekit_ingress_proto_enumTypes,
		MessageInfos:      file_livekit_ingress_proto_msgTypes,
	}.Build()
	File_livekit_ingress_proto = out.File
	file_livekit_ingress_proto_goTypes = nil
	file_livekit_ingress_proto_depIdxs = nil
}
