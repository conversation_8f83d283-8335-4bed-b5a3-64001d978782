// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: livekit_sip.proto

package livekit

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SIPStatusCode int32

const (
	SIPStatusCode_SIP_STATUS_UNKNOWN                          SIPStatusCode = 0
	SIPStatusCode_SIP_STATUS_TRYING                           SIPStatusCode = 100
	SIPStatusCode_SIP_STATUS_RINGING                          SIPStatusCode = 180
	SIPStatusCode_SIP_STATUS_CALL_IS_FORWARDED                SIPStatusCode = 181
	SIPStatusCode_SIP_STATUS_QUEUED                           SIPStatusCode = 182
	SIPStatusCode_SIP_STATUS_SESSION_PROGRESS                 SIPStatusCode = 183
	SIPStatusCode_SIP_STATUS_OK                               SIPStatusCode = 200
	SIPStatusCode_SIP_STATUS_ACCEPTED                         SIPStatusCode = 202
	SIPStatusCode_SIP_STATUS_MOVED_PERMANENTLY                SIPStatusCode = 301
	SIPStatusCode_SIP_STATUS_MOVED_TEMPORARILY                SIPStatusCode = 302
	SIPStatusCode_SIP_STATUS_USE_PROXY                        SIPStatusCode = 305
	SIPStatusCode_SIP_STATUS_BAD_REQUEST                      SIPStatusCode = 400
	SIPStatusCode_SIP_STATUS_UNAUTHORIZED                     SIPStatusCode = 401
	SIPStatusCode_SIP_STATUS_PAYMENT_REQUIRED                 SIPStatusCode = 402
	SIPStatusCode_SIP_STATUS_FORBIDDEN                        SIPStatusCode = 403
	SIPStatusCode_SIP_STATUS_NOTFOUND                         SIPStatusCode = 404
	SIPStatusCode_SIP_STATUS_METHOD_NOT_ALLOWED               SIPStatusCode = 405
	SIPStatusCode_SIP_STATUS_NOT_ACCEPTABLE                   SIPStatusCode = 406
	SIPStatusCode_SIP_STATUS_PROXY_AUTH_REQUIRED              SIPStatusCode = 407
	SIPStatusCode_SIP_STATUS_REQUEST_TIMEOUT                  SIPStatusCode = 408
	SIPStatusCode_SIP_STATUS_CONFLICT                         SIPStatusCode = 409
	SIPStatusCode_SIP_STATUS_GONE                             SIPStatusCode = 410
	SIPStatusCode_SIP_STATUS_REQUEST_ENTITY_TOO_LARGE         SIPStatusCode = 413
	SIPStatusCode_SIP_STATUS_REQUEST_URI_TOO_LONG             SIPStatusCode = 414
	SIPStatusCode_SIP_STATUS_UNSUPPORTED_MEDIA_TYPE           SIPStatusCode = 415
	SIPStatusCode_SIP_STATUS_REQUESTED_RANGE_NOT_SATISFIABLE  SIPStatusCode = 416
	SIPStatusCode_SIP_STATUS_BAD_EXTENSION                    SIPStatusCode = 420
	SIPStatusCode_SIP_STATUS_EXTENSION_REQUIRED               SIPStatusCode = 421
	SIPStatusCode_SIP_STATUS_INTERVAL_TOO_BRIEF               SIPStatusCode = 423
	SIPStatusCode_SIP_STATUS_TEMPORARILY_UNAVAILABLE          SIPStatusCode = 480
	SIPStatusCode_SIP_STATUS_CALL_TRANSACTION_DOES_NOT_EXISTS SIPStatusCode = 481
	SIPStatusCode_SIP_STATUS_LOOP_DETECTED                    SIPStatusCode = 482
	SIPStatusCode_SIP_STATUS_TOO_MANY_HOPS                    SIPStatusCode = 483
	SIPStatusCode_SIP_STATUS_ADDRESS_INCOMPLETE               SIPStatusCode = 484
	SIPStatusCode_SIP_STATUS_AMBIGUOUS                        SIPStatusCode = 485
	SIPStatusCode_SIP_STATUS_BUSY_HERE                        SIPStatusCode = 486
	SIPStatusCode_SIP_STATUS_REQUEST_TERMINATED               SIPStatusCode = 487
	SIPStatusCode_SIP_STATUS_NOT_ACCEPTABLE_HERE              SIPStatusCode = 488
	SIPStatusCode_SIP_STATUS_INTERNAL_SERVER_ERROR            SIPStatusCode = 500
	SIPStatusCode_SIP_STATUS_NOT_IMPLEMENTED                  SIPStatusCode = 501
	SIPStatusCode_SIP_STATUS_BAD_GATEWAY                      SIPStatusCode = 502
	SIPStatusCode_SIP_STATUS_SERVICE_UNAVAILABLE              SIPStatusCode = 503
	SIPStatusCode_SIP_STATUS_GATEWAY_TIMEOUT                  SIPStatusCode = 504
	SIPStatusCode_SIP_STATUS_VERSION_NOT_SUPPORTED            SIPStatusCode = 505
	SIPStatusCode_SIP_STATUS_MESSAGE_TOO_LARGE                SIPStatusCode = 513
	SIPStatusCode_SIP_STATUS_GLOBAL_BUSY_EVERYWHERE           SIPStatusCode = 600
	SIPStatusCode_SIP_STATUS_GLOBAL_DECLINE                   SIPStatusCode = 603
	SIPStatusCode_SIP_STATUS_GLOBAL_DOES_NOT_EXIST_ANYWHERE   SIPStatusCode = 604
	SIPStatusCode_SIP_STATUS_GLOBAL_NOT_ACCEPTABLE            SIPStatusCode = 606
)

// Enum value maps for SIPStatusCode.
var (
	SIPStatusCode_name = map[int32]string{
		0:   "SIP_STATUS_UNKNOWN",
		100: "SIP_STATUS_TRYING",
		180: "SIP_STATUS_RINGING",
		181: "SIP_STATUS_CALL_IS_FORWARDED",
		182: "SIP_STATUS_QUEUED",
		183: "SIP_STATUS_SESSION_PROGRESS",
		200: "SIP_STATUS_OK",
		202: "SIP_STATUS_ACCEPTED",
		301: "SIP_STATUS_MOVED_PERMANENTLY",
		302: "SIP_STATUS_MOVED_TEMPORARILY",
		305: "SIP_STATUS_USE_PROXY",
		400: "SIP_STATUS_BAD_REQUEST",
		401: "SIP_STATUS_UNAUTHORIZED",
		402: "SIP_STATUS_PAYMENT_REQUIRED",
		403: "SIP_STATUS_FORBIDDEN",
		404: "SIP_STATUS_NOTFOUND",
		405: "SIP_STATUS_METHOD_NOT_ALLOWED",
		406: "SIP_STATUS_NOT_ACCEPTABLE",
		407: "SIP_STATUS_PROXY_AUTH_REQUIRED",
		408: "SIP_STATUS_REQUEST_TIMEOUT",
		409: "SIP_STATUS_CONFLICT",
		410: "SIP_STATUS_GONE",
		413: "SIP_STATUS_REQUEST_ENTITY_TOO_LARGE",
		414: "SIP_STATUS_REQUEST_URI_TOO_LONG",
		415: "SIP_STATUS_UNSUPPORTED_MEDIA_TYPE",
		416: "SIP_STATUS_REQUESTED_RANGE_NOT_SATISFIABLE",
		420: "SIP_STATUS_BAD_EXTENSION",
		421: "SIP_STATUS_EXTENSION_REQUIRED",
		423: "SIP_STATUS_INTERVAL_TOO_BRIEF",
		480: "SIP_STATUS_TEMPORARILY_UNAVAILABLE",
		481: "SIP_STATUS_CALL_TRANSACTION_DOES_NOT_EXISTS",
		482: "SIP_STATUS_LOOP_DETECTED",
		483: "SIP_STATUS_TOO_MANY_HOPS",
		484: "SIP_STATUS_ADDRESS_INCOMPLETE",
		485: "SIP_STATUS_AMBIGUOUS",
		486: "SIP_STATUS_BUSY_HERE",
		487: "SIP_STATUS_REQUEST_TERMINATED",
		488: "SIP_STATUS_NOT_ACCEPTABLE_HERE",
		500: "SIP_STATUS_INTERNAL_SERVER_ERROR",
		501: "SIP_STATUS_NOT_IMPLEMENTED",
		502: "SIP_STATUS_BAD_GATEWAY",
		503: "SIP_STATUS_SERVICE_UNAVAILABLE",
		504: "SIP_STATUS_GATEWAY_TIMEOUT",
		505: "SIP_STATUS_VERSION_NOT_SUPPORTED",
		513: "SIP_STATUS_MESSAGE_TOO_LARGE",
		600: "SIP_STATUS_GLOBAL_BUSY_EVERYWHERE",
		603: "SIP_STATUS_GLOBAL_DECLINE",
		604: "SIP_STATUS_GLOBAL_DOES_NOT_EXIST_ANYWHERE",
		606: "SIP_STATUS_GLOBAL_NOT_ACCEPTABLE",
	}
	SIPStatusCode_value = map[string]int32{
		"SIP_STATUS_UNKNOWN":                          0,
		"SIP_STATUS_TRYING":                           100,
		"SIP_STATUS_RINGING":                          180,
		"SIP_STATUS_CALL_IS_FORWARDED":                181,
		"SIP_STATUS_QUEUED":                           182,
		"SIP_STATUS_SESSION_PROGRESS":                 183,
		"SIP_STATUS_OK":                               200,
		"SIP_STATUS_ACCEPTED":                         202,
		"SIP_STATUS_MOVED_PERMANENTLY":                301,
		"SIP_STATUS_MOVED_TEMPORARILY":                302,
		"SIP_STATUS_USE_PROXY":                        305,
		"SIP_STATUS_BAD_REQUEST":                      400,
		"SIP_STATUS_UNAUTHORIZED":                     401,
		"SIP_STATUS_PAYMENT_REQUIRED":                 402,
		"SIP_STATUS_FORBIDDEN":                        403,
		"SIP_STATUS_NOTFOUND":                         404,
		"SIP_STATUS_METHOD_NOT_ALLOWED":               405,
		"SIP_STATUS_NOT_ACCEPTABLE":                   406,
		"SIP_STATUS_PROXY_AUTH_REQUIRED":              407,
		"SIP_STATUS_REQUEST_TIMEOUT":                  408,
		"SIP_STATUS_CONFLICT":                         409,
		"SIP_STATUS_GONE":                             410,
		"SIP_STATUS_REQUEST_ENTITY_TOO_LARGE":         413,
		"SIP_STATUS_REQUEST_URI_TOO_LONG":             414,
		"SIP_STATUS_UNSUPPORTED_MEDIA_TYPE":           415,
		"SIP_STATUS_REQUESTED_RANGE_NOT_SATISFIABLE":  416,
		"SIP_STATUS_BAD_EXTENSION":                    420,
		"SIP_STATUS_EXTENSION_REQUIRED":               421,
		"SIP_STATUS_INTERVAL_TOO_BRIEF":               423,
		"SIP_STATUS_TEMPORARILY_UNAVAILABLE":          480,
		"SIP_STATUS_CALL_TRANSACTION_DOES_NOT_EXISTS": 481,
		"SIP_STATUS_LOOP_DETECTED":                    482,
		"SIP_STATUS_TOO_MANY_HOPS":                    483,
		"SIP_STATUS_ADDRESS_INCOMPLETE":               484,
		"SIP_STATUS_AMBIGUOUS":                        485,
		"SIP_STATUS_BUSY_HERE":                        486,
		"SIP_STATUS_REQUEST_TERMINATED":               487,
		"SIP_STATUS_NOT_ACCEPTABLE_HERE":              488,
		"SIP_STATUS_INTERNAL_SERVER_ERROR":            500,
		"SIP_STATUS_NOT_IMPLEMENTED":                  501,
		"SIP_STATUS_BAD_GATEWAY":                      502,
		"SIP_STATUS_SERVICE_UNAVAILABLE":              503,
		"SIP_STATUS_GATEWAY_TIMEOUT":                  504,
		"SIP_STATUS_VERSION_NOT_SUPPORTED":            505,
		"SIP_STATUS_MESSAGE_TOO_LARGE":                513,
		"SIP_STATUS_GLOBAL_BUSY_EVERYWHERE":           600,
		"SIP_STATUS_GLOBAL_DECLINE":                   603,
		"SIP_STATUS_GLOBAL_DOES_NOT_EXIST_ANYWHERE":   604,
		"SIP_STATUS_GLOBAL_NOT_ACCEPTABLE":            606,
	}
)

func (x SIPStatusCode) Enum() *SIPStatusCode {
	p := new(SIPStatusCode)
	*p = x
	return p
}

func (x SIPStatusCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SIPStatusCode) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_sip_proto_enumTypes[0].Descriptor()
}

func (SIPStatusCode) Type() protoreflect.EnumType {
	return &file_livekit_sip_proto_enumTypes[0]
}

func (x SIPStatusCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SIPStatusCode.Descriptor instead.
func (SIPStatusCode) EnumDescriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{0}
}

type SIPTransport int32

const (
	SIPTransport_SIP_TRANSPORT_AUTO SIPTransport = 0
	SIPTransport_SIP_TRANSPORT_UDP  SIPTransport = 1
	SIPTransport_SIP_TRANSPORT_TCP  SIPTransport = 2
	SIPTransport_SIP_TRANSPORT_TLS  SIPTransport = 3
)

// Enum value maps for SIPTransport.
var (
	SIPTransport_name = map[int32]string{
		0: "SIP_TRANSPORT_AUTO",
		1: "SIP_TRANSPORT_UDP",
		2: "SIP_TRANSPORT_TCP",
		3: "SIP_TRANSPORT_TLS",
	}
	SIPTransport_value = map[string]int32{
		"SIP_TRANSPORT_AUTO": 0,
		"SIP_TRANSPORT_UDP":  1,
		"SIP_TRANSPORT_TCP":  2,
		"SIP_TRANSPORT_TLS":  3,
	}
)

func (x SIPTransport) Enum() *SIPTransport {
	p := new(SIPTransport)
	*p = x
	return p
}

func (x SIPTransport) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SIPTransport) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_sip_proto_enumTypes[1].Descriptor()
}

func (SIPTransport) Type() protoreflect.EnumType {
	return &file_livekit_sip_proto_enumTypes[1]
}

func (x SIPTransport) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SIPTransport.Descriptor instead.
func (SIPTransport) EnumDescriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{1}
}

type SIPHeaderOptions int32

const (
	SIPHeaderOptions_SIP_NO_HEADERS  SIPHeaderOptions = 0 // do not map any headers, except ones mapped explicitly
	SIPHeaderOptions_SIP_X_HEADERS   SIPHeaderOptions = 1 // map all X-* headers to sip.h.x-* attributes
	SIPHeaderOptions_SIP_ALL_HEADERS SIPHeaderOptions = 2 // map all headers to sip.h.* attributes
)

// Enum value maps for SIPHeaderOptions.
var (
	SIPHeaderOptions_name = map[int32]string{
		0: "SIP_NO_HEADERS",
		1: "SIP_X_HEADERS",
		2: "SIP_ALL_HEADERS",
	}
	SIPHeaderOptions_value = map[string]int32{
		"SIP_NO_HEADERS":  0,
		"SIP_X_HEADERS":   1,
		"SIP_ALL_HEADERS": 2,
	}
)

func (x SIPHeaderOptions) Enum() *SIPHeaderOptions {
	p := new(SIPHeaderOptions)
	*p = x
	return p
}

func (x SIPHeaderOptions) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SIPHeaderOptions) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_sip_proto_enumTypes[2].Descriptor()
}

func (SIPHeaderOptions) Type() protoreflect.EnumType {
	return &file_livekit_sip_proto_enumTypes[2]
}

func (x SIPHeaderOptions) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SIPHeaderOptions.Descriptor instead.
func (SIPHeaderOptions) EnumDescriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{2}
}

type SIPMediaEncryption int32

const (
	SIPMediaEncryption_SIP_MEDIA_ENCRYPT_DISABLE SIPMediaEncryption = 0 // do not enable encryption
	SIPMediaEncryption_SIP_MEDIA_ENCRYPT_ALLOW   SIPMediaEncryption = 1 // use encryption if available
	SIPMediaEncryption_SIP_MEDIA_ENCRYPT_REQUIRE SIPMediaEncryption = 2 // require encryption
)

// Enum value maps for SIPMediaEncryption.
var (
	SIPMediaEncryption_name = map[int32]string{
		0: "SIP_MEDIA_ENCRYPT_DISABLE",
		1: "SIP_MEDIA_ENCRYPT_ALLOW",
		2: "SIP_MEDIA_ENCRYPT_REQUIRE",
	}
	SIPMediaEncryption_value = map[string]int32{
		"SIP_MEDIA_ENCRYPT_DISABLE": 0,
		"SIP_MEDIA_ENCRYPT_ALLOW":   1,
		"SIP_MEDIA_ENCRYPT_REQUIRE": 2,
	}
)

func (x SIPMediaEncryption) Enum() *SIPMediaEncryption {
	p := new(SIPMediaEncryption)
	*p = x
	return p
}

func (x SIPMediaEncryption) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SIPMediaEncryption) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_sip_proto_enumTypes[3].Descriptor()
}

func (SIPMediaEncryption) Type() protoreflect.EnumType {
	return &file_livekit_sip_proto_enumTypes[3]
}

func (x SIPMediaEncryption) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SIPMediaEncryption.Descriptor instead.
func (SIPMediaEncryption) EnumDescriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{3}
}

type SIPCallStatus int32

const (
	SIPCallStatus_SCS_CALL_INCOMING      SIPCallStatus = 0 // Incoming call is being handled by the SIP service. The SIP participant hasn't joined a LiveKit room yet
	SIPCallStatus_SCS_PARTICIPANT_JOINED SIPCallStatus = 1 // SIP participant for outgoing call has been created. The SIP outgoing call is being established
	SIPCallStatus_SCS_ACTIVE             SIPCallStatus = 2 // Call is ongoing. SIP participant is active in the LiveKit room
	SIPCallStatus_SCS_DISCONNECTED       SIPCallStatus = 3 // Call has ended
	SIPCallStatus_SCS_ERROR              SIPCallStatus = 4 // Call has ended or never succeeded because of an error
)

// Enum value maps for SIPCallStatus.
var (
	SIPCallStatus_name = map[int32]string{
		0: "SCS_CALL_INCOMING",
		1: "SCS_PARTICIPANT_JOINED",
		2: "SCS_ACTIVE",
		3: "SCS_DISCONNECTED",
		4: "SCS_ERROR",
	}
	SIPCallStatus_value = map[string]int32{
		"SCS_CALL_INCOMING":      0,
		"SCS_PARTICIPANT_JOINED": 1,
		"SCS_ACTIVE":             2,
		"SCS_DISCONNECTED":       3,
		"SCS_ERROR":              4,
	}
)

func (x SIPCallStatus) Enum() *SIPCallStatus {
	p := new(SIPCallStatus)
	*p = x
	return p
}

func (x SIPCallStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SIPCallStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_sip_proto_enumTypes[4].Descriptor()
}

func (SIPCallStatus) Type() protoreflect.EnumType {
	return &file_livekit_sip_proto_enumTypes[4]
}

func (x SIPCallStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SIPCallStatus.Descriptor instead.
func (SIPCallStatus) EnumDescriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{4}
}

type SIPTransferStatus int32

const (
	SIPTransferStatus_STS_TRANSFER_ONGOING    SIPTransferStatus = 0
	SIPTransferStatus_STS_TRANSFER_FAILED     SIPTransferStatus = 1
	SIPTransferStatus_STS_TRANSFER_SUCCESSFUL SIPTransferStatus = 2
)

// Enum value maps for SIPTransferStatus.
var (
	SIPTransferStatus_name = map[int32]string{
		0: "STS_TRANSFER_ONGOING",
		1: "STS_TRANSFER_FAILED",
		2: "STS_TRANSFER_SUCCESSFUL",
	}
	SIPTransferStatus_value = map[string]int32{
		"STS_TRANSFER_ONGOING":    0,
		"STS_TRANSFER_FAILED":     1,
		"STS_TRANSFER_SUCCESSFUL": 2,
	}
)

func (x SIPTransferStatus) Enum() *SIPTransferStatus {
	p := new(SIPTransferStatus)
	*p = x
	return p
}

func (x SIPTransferStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SIPTransferStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_sip_proto_enumTypes[5].Descriptor()
}

func (SIPTransferStatus) Type() protoreflect.EnumType {
	return &file_livekit_sip_proto_enumTypes[5]
}

func (x SIPTransferStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SIPTransferStatus.Descriptor instead.
func (SIPTransferStatus) EnumDescriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{5}
}

type SIPFeature int32

const (
	SIPFeature_NONE          SIPFeature = 0
	SIPFeature_KRISP_ENABLED SIPFeature = 1
)

// Enum value maps for SIPFeature.
var (
	SIPFeature_name = map[int32]string{
		0: "NONE",
		1: "KRISP_ENABLED",
	}
	SIPFeature_value = map[string]int32{
		"NONE":          0,
		"KRISP_ENABLED": 1,
	}
)

func (x SIPFeature) Enum() *SIPFeature {
	p := new(SIPFeature)
	*p = x
	return p
}

func (x SIPFeature) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SIPFeature) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_sip_proto_enumTypes[6].Descriptor()
}

func (SIPFeature) Type() protoreflect.EnumType {
	return &file_livekit_sip_proto_enumTypes[6]
}

func (x SIPFeature) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SIPFeature.Descriptor instead.
func (SIPFeature) EnumDescriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{6}
}

type SIPCallDirection int32

const (
	SIPCallDirection_SCD_UNKNOWN  SIPCallDirection = 0
	SIPCallDirection_SCD_INBOUND  SIPCallDirection = 1
	SIPCallDirection_SCD_OUTBOUND SIPCallDirection = 2
)

// Enum value maps for SIPCallDirection.
var (
	SIPCallDirection_name = map[int32]string{
		0: "SCD_UNKNOWN",
		1: "SCD_INBOUND",
		2: "SCD_OUTBOUND",
	}
	SIPCallDirection_value = map[string]int32{
		"SCD_UNKNOWN":  0,
		"SCD_INBOUND":  1,
		"SCD_OUTBOUND": 2,
	}
)

func (x SIPCallDirection) Enum() *SIPCallDirection {
	p := new(SIPCallDirection)
	*p = x
	return p
}

func (x SIPCallDirection) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SIPCallDirection) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_sip_proto_enumTypes[7].Descriptor()
}

func (SIPCallDirection) Type() protoreflect.EnumType {
	return &file_livekit_sip_proto_enumTypes[7]
}

func (x SIPCallDirection) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SIPCallDirection.Descriptor instead.
func (SIPCallDirection) EnumDescriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{7}
}

type SIPTrunkInfo_TrunkKind int32

const (
	SIPTrunkInfo_TRUNK_LEGACY   SIPTrunkInfo_TrunkKind = 0
	SIPTrunkInfo_TRUNK_INBOUND  SIPTrunkInfo_TrunkKind = 1
	SIPTrunkInfo_TRUNK_OUTBOUND SIPTrunkInfo_TrunkKind = 2
)

// Enum value maps for SIPTrunkInfo_TrunkKind.
var (
	SIPTrunkInfo_TrunkKind_name = map[int32]string{
		0: "TRUNK_LEGACY",
		1: "TRUNK_INBOUND",
		2: "TRUNK_OUTBOUND",
	}
	SIPTrunkInfo_TrunkKind_value = map[string]int32{
		"TRUNK_LEGACY":   0,
		"TRUNK_INBOUND":  1,
		"TRUNK_OUTBOUND": 2,
	}
)

func (x SIPTrunkInfo_TrunkKind) Enum() *SIPTrunkInfo_TrunkKind {
	p := new(SIPTrunkInfo_TrunkKind)
	*p = x
	return p
}

func (x SIPTrunkInfo_TrunkKind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SIPTrunkInfo_TrunkKind) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_sip_proto_enumTypes[8].Descriptor()
}

func (SIPTrunkInfo_TrunkKind) Type() protoreflect.EnumType {
	return &file_livekit_sip_proto_enumTypes[8]
}

func (x SIPTrunkInfo_TrunkKind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SIPTrunkInfo_TrunkKind.Descriptor instead.
func (SIPTrunkInfo_TrunkKind) EnumDescriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{2, 0}
}

// SIPStatus is returned as an error detail in CreateSIPParticipant.
type SIPStatus struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          SIPStatusCode          `protobuf:"varint,1,opt,name=code,proto3,enum=livekit.SIPStatusCode" json:"code,omitempty"`
	Status        string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SIPStatus) Reset() {
	*x = SIPStatus{}
	mi := &file_livekit_sip_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SIPStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SIPStatus) ProtoMessage() {}

func (x *SIPStatus) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SIPStatus.ProtoReflect.Descriptor instead.
func (*SIPStatus) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{0}
}

func (x *SIPStatus) GetCode() SIPStatusCode {
	if x != nil {
		return x.Code
	}
	return SIPStatusCode_SIP_STATUS_UNKNOWN
}

func (x *SIPStatus) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

// Deprecated: Marked as deprecated in livekit_sip.proto.
type CreateSIPTrunkRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// CIDR or IPs that traffic is accepted from
	// An empty list means all inbound traffic is accepted.
	InboundAddresses []string `protobuf:"bytes,1,rep,name=inbound_addresses,json=inboundAddresses,proto3" json:"inbound_addresses,omitempty"`
	// IP that SIP INVITE is sent too
	OutboundAddress string `protobuf:"bytes,2,opt,name=outbound_address,json=outboundAddress,proto3" json:"outbound_address,omitempty"`
	// Number used to make outbound calls
	OutboundNumber string `protobuf:"bytes,3,opt,name=outbound_number,json=outboundNumber,proto3" json:"outbound_number,omitempty"`
	// Deprecated: Marked as deprecated in livekit_sip.proto.
	InboundNumbersRegex []string `protobuf:"bytes,4,rep,name=inbound_numbers_regex,json=inboundNumbersRegex,proto3" json:"inbound_numbers_regex,omitempty"`
	// Accepted `To` values. This Trunk will only accept a call made to
	// these numbers. This allows you to have distinct Trunks for different phone
	// numbers at the same provider.
	InboundNumbers []string `protobuf:"bytes,9,rep,name=inbound_numbers,json=inboundNumbers,proto3" json:"inbound_numbers,omitempty"`
	// Username and password used to authenticate inbound and outbound SIP invites
	// May be empty to have no Authentication
	InboundUsername  string `protobuf:"bytes,5,opt,name=inbound_username,json=inboundUsername,proto3" json:"inbound_username,omitempty"`
	InboundPassword  string `protobuf:"bytes,6,opt,name=inbound_password,json=inboundPassword,proto3" json:"inbound_password,omitempty"`
	OutboundUsername string `protobuf:"bytes,7,opt,name=outbound_username,json=outboundUsername,proto3" json:"outbound_username,omitempty"`
	OutboundPassword string `protobuf:"bytes,8,opt,name=outbound_password,json=outboundPassword,proto3" json:"outbound_password,omitempty"`
	// Optional human-readable name for the Trunk.
	Name string `protobuf:"bytes,10,opt,name=name,proto3" json:"name,omitempty"`
	// Optional user-defined metadata for the Trunk.
	Metadata      string `protobuf:"bytes,11,opt,name=metadata,proto3" json:"metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateSIPTrunkRequest) Reset() {
	*x = CreateSIPTrunkRequest{}
	mi := &file_livekit_sip_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSIPTrunkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSIPTrunkRequest) ProtoMessage() {}

func (x *CreateSIPTrunkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSIPTrunkRequest.ProtoReflect.Descriptor instead.
func (*CreateSIPTrunkRequest) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{1}
}

func (x *CreateSIPTrunkRequest) GetInboundAddresses() []string {
	if x != nil {
		return x.InboundAddresses
	}
	return nil
}

func (x *CreateSIPTrunkRequest) GetOutboundAddress() string {
	if x != nil {
		return x.OutboundAddress
	}
	return ""
}

func (x *CreateSIPTrunkRequest) GetOutboundNumber() string {
	if x != nil {
		return x.OutboundNumber
	}
	return ""
}

// Deprecated: Marked as deprecated in livekit_sip.proto.
func (x *CreateSIPTrunkRequest) GetInboundNumbersRegex() []string {
	if x != nil {
		return x.InboundNumbersRegex
	}
	return nil
}

func (x *CreateSIPTrunkRequest) GetInboundNumbers() []string {
	if x != nil {
		return x.InboundNumbers
	}
	return nil
}

func (x *CreateSIPTrunkRequest) GetInboundUsername() string {
	if x != nil {
		return x.InboundUsername
	}
	return ""
}

func (x *CreateSIPTrunkRequest) GetInboundPassword() string {
	if x != nil {
		return x.InboundPassword
	}
	return ""
}

func (x *CreateSIPTrunkRequest) GetOutboundUsername() string {
	if x != nil {
		return x.OutboundUsername
	}
	return ""
}

func (x *CreateSIPTrunkRequest) GetOutboundPassword() string {
	if x != nil {
		return x.OutboundPassword
	}
	return ""
}

func (x *CreateSIPTrunkRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateSIPTrunkRequest) GetMetadata() string {
	if x != nil {
		return x.Metadata
	}
	return ""
}

// Deprecated: Marked as deprecated in livekit_sip.proto.
type SIPTrunkInfo struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	SipTrunkId string                 `protobuf:"bytes,1,opt,name=sip_trunk_id,json=sipTrunkId,proto3" json:"sip_trunk_id,omitempty"`
	Kind       SIPTrunkInfo_TrunkKind `protobuf:"varint,14,opt,name=kind,proto3,enum=livekit.SIPTrunkInfo_TrunkKind" json:"kind,omitempty"`
	// CIDR or IPs that traffic is accepted from
	// An empty list means all inbound traffic is accepted.
	InboundAddresses []string `protobuf:"bytes,2,rep,name=inbound_addresses,json=inboundAddresses,proto3" json:"inbound_addresses,omitempty"`
	// IP that SIP INVITE is sent too
	OutboundAddress string `protobuf:"bytes,3,opt,name=outbound_address,json=outboundAddress,proto3" json:"outbound_address,omitempty"`
	// Number used to make outbound calls
	OutboundNumber string `protobuf:"bytes,4,opt,name=outbound_number,json=outboundNumber,proto3" json:"outbound_number,omitempty"`
	// Transport used for inbound and outbound calls.
	Transport SIPTransport `protobuf:"varint,13,opt,name=transport,proto3,enum=livekit.SIPTransport" json:"transport,omitempty"`
	// Deprecated: Marked as deprecated in livekit_sip.proto.
	InboundNumbersRegex []string `protobuf:"bytes,5,rep,name=inbound_numbers_regex,json=inboundNumbersRegex,proto3" json:"inbound_numbers_regex,omitempty"`
	// Accepted `To` values. This Trunk will only accept a call made to
	// these numbers. This allows you to have distinct Trunks for different phone
	// numbers at the same provider.
	InboundNumbers []string `protobuf:"bytes,10,rep,name=inbound_numbers,json=inboundNumbers,proto3" json:"inbound_numbers,omitempty"`
	// Username and password used to authenticate inbound and outbound SIP invites
	// May be empty to have no Authentication
	InboundUsername  string `protobuf:"bytes,6,opt,name=inbound_username,json=inboundUsername,proto3" json:"inbound_username,omitempty"`
	InboundPassword  string `protobuf:"bytes,7,opt,name=inbound_password,json=inboundPassword,proto3" json:"inbound_password,omitempty"`
	OutboundUsername string `protobuf:"bytes,8,opt,name=outbound_username,json=outboundUsername,proto3" json:"outbound_username,omitempty"`
	OutboundPassword string `protobuf:"bytes,9,opt,name=outbound_password,json=outboundPassword,proto3" json:"outbound_password,omitempty"`
	// Human-readable name for the Trunk.
	Name string `protobuf:"bytes,11,opt,name=name,proto3" json:"name,omitempty"`
	// User-defined metadata for the Trunk.
	Metadata      string `protobuf:"bytes,12,opt,name=metadata,proto3" json:"metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SIPTrunkInfo) Reset() {
	*x = SIPTrunkInfo{}
	mi := &file_livekit_sip_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SIPTrunkInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SIPTrunkInfo) ProtoMessage() {}

func (x *SIPTrunkInfo) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SIPTrunkInfo.ProtoReflect.Descriptor instead.
func (*SIPTrunkInfo) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{2}
}

func (x *SIPTrunkInfo) GetSipTrunkId() string {
	if x != nil {
		return x.SipTrunkId
	}
	return ""
}

func (x *SIPTrunkInfo) GetKind() SIPTrunkInfo_TrunkKind {
	if x != nil {
		return x.Kind
	}
	return SIPTrunkInfo_TRUNK_LEGACY
}

func (x *SIPTrunkInfo) GetInboundAddresses() []string {
	if x != nil {
		return x.InboundAddresses
	}
	return nil
}

func (x *SIPTrunkInfo) GetOutboundAddress() string {
	if x != nil {
		return x.OutboundAddress
	}
	return ""
}

func (x *SIPTrunkInfo) GetOutboundNumber() string {
	if x != nil {
		return x.OutboundNumber
	}
	return ""
}

func (x *SIPTrunkInfo) GetTransport() SIPTransport {
	if x != nil {
		return x.Transport
	}
	return SIPTransport_SIP_TRANSPORT_AUTO
}

// Deprecated: Marked as deprecated in livekit_sip.proto.
func (x *SIPTrunkInfo) GetInboundNumbersRegex() []string {
	if x != nil {
		return x.InboundNumbersRegex
	}
	return nil
}

func (x *SIPTrunkInfo) GetInboundNumbers() []string {
	if x != nil {
		return x.InboundNumbers
	}
	return nil
}

func (x *SIPTrunkInfo) GetInboundUsername() string {
	if x != nil {
		return x.InboundUsername
	}
	return ""
}

func (x *SIPTrunkInfo) GetInboundPassword() string {
	if x != nil {
		return x.InboundPassword
	}
	return ""
}

func (x *SIPTrunkInfo) GetOutboundUsername() string {
	if x != nil {
		return x.OutboundUsername
	}
	return ""
}

func (x *SIPTrunkInfo) GetOutboundPassword() string {
	if x != nil {
		return x.OutboundPassword
	}
	return ""
}

func (x *SIPTrunkInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SIPTrunkInfo) GetMetadata() string {
	if x != nil {
		return x.Metadata
	}
	return ""
}

type CreateSIPInboundTrunkRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Trunk         *SIPInboundTrunkInfo   `protobuf:"bytes,1,opt,name=trunk,proto3" json:"trunk,omitempty"` // Trunk ID is ignored
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateSIPInboundTrunkRequest) Reset() {
	*x = CreateSIPInboundTrunkRequest{}
	mi := &file_livekit_sip_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSIPInboundTrunkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSIPInboundTrunkRequest) ProtoMessage() {}

func (x *CreateSIPInboundTrunkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSIPInboundTrunkRequest.ProtoReflect.Descriptor instead.
func (*CreateSIPInboundTrunkRequest) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{3}
}

func (x *CreateSIPInboundTrunkRequest) GetTrunk() *SIPInboundTrunkInfo {
	if x != nil {
		return x.Trunk
	}
	return nil
}

type UpdateSIPInboundTrunkRequest struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	SipTrunkId string                 `protobuf:"bytes,1,opt,name=sip_trunk_id,json=sipTrunkId,proto3" json:"sip_trunk_id,omitempty"`
	// Types that are valid to be assigned to Action:
	//
	//	*UpdateSIPInboundTrunkRequest_Replace
	//	*UpdateSIPInboundTrunkRequest_Update
	Action        isUpdateSIPInboundTrunkRequest_Action `protobuf_oneof:"action"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateSIPInboundTrunkRequest) Reset() {
	*x = UpdateSIPInboundTrunkRequest{}
	mi := &file_livekit_sip_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateSIPInboundTrunkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSIPInboundTrunkRequest) ProtoMessage() {}

func (x *UpdateSIPInboundTrunkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSIPInboundTrunkRequest.ProtoReflect.Descriptor instead.
func (*UpdateSIPInboundTrunkRequest) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateSIPInboundTrunkRequest) GetSipTrunkId() string {
	if x != nil {
		return x.SipTrunkId
	}
	return ""
}

func (x *UpdateSIPInboundTrunkRequest) GetAction() isUpdateSIPInboundTrunkRequest_Action {
	if x != nil {
		return x.Action
	}
	return nil
}

func (x *UpdateSIPInboundTrunkRequest) GetReplace() *SIPInboundTrunkInfo {
	if x != nil {
		if x, ok := x.Action.(*UpdateSIPInboundTrunkRequest_Replace); ok {
			return x.Replace
		}
	}
	return nil
}

func (x *UpdateSIPInboundTrunkRequest) GetUpdate() *SIPInboundTrunkUpdate {
	if x != nil {
		if x, ok := x.Action.(*UpdateSIPInboundTrunkRequest_Update); ok {
			return x.Update
		}
	}
	return nil
}

type isUpdateSIPInboundTrunkRequest_Action interface {
	isUpdateSIPInboundTrunkRequest_Action()
}

type UpdateSIPInboundTrunkRequest_Replace struct {
	Replace *SIPInboundTrunkInfo `protobuf:"bytes,2,opt,name=replace,proto3,oneof"`
}

type UpdateSIPInboundTrunkRequest_Update struct {
	Update *SIPInboundTrunkUpdate `protobuf:"bytes,3,opt,name=update,proto3,oneof"`
}

func (*UpdateSIPInboundTrunkRequest_Replace) isUpdateSIPInboundTrunkRequest_Action() {}

func (*UpdateSIPInboundTrunkRequest_Update) isUpdateSIPInboundTrunkRequest_Action() {}

type SIPInboundTrunkInfo struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	SipTrunkId string                 `protobuf:"bytes,1,opt,name=sip_trunk_id,json=sipTrunkId,proto3" json:"sip_trunk_id,omitempty"`
	// Human-readable name for the Trunk.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// User-defined metadata for the Trunk.
	Metadata string `protobuf:"bytes,3,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// Numbers associated with LiveKit SIP. The Trunk will only accept calls made to these numbers.
	// Creating multiple Trunks with different phone numbers allows having different rules for a single provider.
	Numbers []string `protobuf:"bytes,4,rep,name=numbers,proto3" json:"numbers,omitempty"`
	// CIDR or IPs that traffic is accepted from.
	// An empty list means all inbound traffic is accepted.
	AllowedAddresses []string `protobuf:"bytes,5,rep,name=allowed_addresses,json=allowedAddresses,proto3" json:"allowed_addresses,omitempty"`
	// Numbers that are allowed to make calls to this Trunk.
	// An empty list means calls from any phone number is accepted.
	AllowedNumbers []string `protobuf:"bytes,6,rep,name=allowed_numbers,json=allowedNumbers,proto3" json:"allowed_numbers,omitempty"`
	// Username and password used to authenticate inbound SIP invites.
	// May be empty to have no authentication.
	AuthUsername string `protobuf:"bytes,7,opt,name=auth_username,json=authUsername,proto3" json:"auth_username,omitempty"`
	AuthPassword string `protobuf:"bytes,8,opt,name=auth_password,json=authPassword,proto3" json:"auth_password,omitempty"`
	// Include these SIP X-* headers in 200 OK responses.
	Headers map[string]string `protobuf:"bytes,9,rep,name=headers,proto3" json:"headers,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Map SIP X-* headers from INVITE to SIP participant attributes.
	HeadersToAttributes map[string]string `protobuf:"bytes,10,rep,name=headers_to_attributes,json=headersToAttributes,proto3" json:"headers_to_attributes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Map LiveKit attributes to SIP X-* headers when sending BYE or REFER requests.
	// Keys are the names of attributes and values are the names of X-* headers they will be mapped to.
	AttributesToHeaders map[string]string `protobuf:"bytes,14,rep,name=attributes_to_headers,json=attributesToHeaders,proto3" json:"attributes_to_headers,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Map SIP headers from INVITE to sip.h.* participant attributes automatically.
	//
	// When the names of required headers is known, using headers_to_attributes is strongly recommended.
	//
	// When mapping INVITE headers to response headers with attributes_to_headers map,
	// lowercase header names should be used, for example: sip.h.x-custom-header.
	IncludeHeaders SIPHeaderOptions `protobuf:"varint,15,opt,name=include_headers,json=includeHeaders,proto3,enum=livekit.SIPHeaderOptions" json:"include_headers,omitempty"`
	// Max time for the caller to wait for track subscription.
	RingingTimeout *durationpb.Duration `protobuf:"bytes,11,opt,name=ringing_timeout,json=ringingTimeout,proto3" json:"ringing_timeout,omitempty"`
	// Max call duration.
	MaxCallDuration *durationpb.Duration `protobuf:"bytes,12,opt,name=max_call_duration,json=maxCallDuration,proto3" json:"max_call_duration,omitempty"`
	KrispEnabled    bool                 `protobuf:"varint,13,opt,name=krisp_enabled,json=krispEnabled,proto3" json:"krisp_enabled,omitempty"`
	MediaEncryption SIPMediaEncryption   `protobuf:"varint,16,opt,name=media_encryption,json=mediaEncryption,proto3,enum=livekit.SIPMediaEncryption" json:"media_encryption,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *SIPInboundTrunkInfo) Reset() {
	*x = SIPInboundTrunkInfo{}
	mi := &file_livekit_sip_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SIPInboundTrunkInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SIPInboundTrunkInfo) ProtoMessage() {}

func (x *SIPInboundTrunkInfo) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SIPInboundTrunkInfo.ProtoReflect.Descriptor instead.
func (*SIPInboundTrunkInfo) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{5}
}

func (x *SIPInboundTrunkInfo) GetSipTrunkId() string {
	if x != nil {
		return x.SipTrunkId
	}
	return ""
}

func (x *SIPInboundTrunkInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SIPInboundTrunkInfo) GetMetadata() string {
	if x != nil {
		return x.Metadata
	}
	return ""
}

func (x *SIPInboundTrunkInfo) GetNumbers() []string {
	if x != nil {
		return x.Numbers
	}
	return nil
}

func (x *SIPInboundTrunkInfo) GetAllowedAddresses() []string {
	if x != nil {
		return x.AllowedAddresses
	}
	return nil
}

func (x *SIPInboundTrunkInfo) GetAllowedNumbers() []string {
	if x != nil {
		return x.AllowedNumbers
	}
	return nil
}

func (x *SIPInboundTrunkInfo) GetAuthUsername() string {
	if x != nil {
		return x.AuthUsername
	}
	return ""
}

func (x *SIPInboundTrunkInfo) GetAuthPassword() string {
	if x != nil {
		return x.AuthPassword
	}
	return ""
}

func (x *SIPInboundTrunkInfo) GetHeaders() map[string]string {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *SIPInboundTrunkInfo) GetHeadersToAttributes() map[string]string {
	if x != nil {
		return x.HeadersToAttributes
	}
	return nil
}

func (x *SIPInboundTrunkInfo) GetAttributesToHeaders() map[string]string {
	if x != nil {
		return x.AttributesToHeaders
	}
	return nil
}

func (x *SIPInboundTrunkInfo) GetIncludeHeaders() SIPHeaderOptions {
	if x != nil {
		return x.IncludeHeaders
	}
	return SIPHeaderOptions_SIP_NO_HEADERS
}

func (x *SIPInboundTrunkInfo) GetRingingTimeout() *durationpb.Duration {
	if x != nil {
		return x.RingingTimeout
	}
	return nil
}

func (x *SIPInboundTrunkInfo) GetMaxCallDuration() *durationpb.Duration {
	if x != nil {
		return x.MaxCallDuration
	}
	return nil
}

func (x *SIPInboundTrunkInfo) GetKrispEnabled() bool {
	if x != nil {
		return x.KrispEnabled
	}
	return false
}

func (x *SIPInboundTrunkInfo) GetMediaEncryption() SIPMediaEncryption {
	if x != nil {
		return x.MediaEncryption
	}
	return SIPMediaEncryption_SIP_MEDIA_ENCRYPT_DISABLE
}

type SIPInboundTrunkUpdate struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Numbers          *ListUpdate            `protobuf:"bytes,1,opt,name=numbers,proto3" json:"numbers,omitempty"`
	AllowedAddresses *ListUpdate            `protobuf:"bytes,2,opt,name=allowed_addresses,json=allowedAddresses,proto3" json:"allowed_addresses,omitempty"`
	AllowedNumbers   *ListUpdate            `protobuf:"bytes,3,opt,name=allowed_numbers,json=allowedNumbers,proto3" json:"allowed_numbers,omitempty"`
	AuthUsername     *string                `protobuf:"bytes,4,opt,name=auth_username,json=authUsername,proto3,oneof" json:"auth_username,omitempty"`
	AuthPassword     *string                `protobuf:"bytes,5,opt,name=auth_password,json=authPassword,proto3,oneof" json:"auth_password,omitempty"`
	Name             *string                `protobuf:"bytes,6,opt,name=name,proto3,oneof" json:"name,omitempty"`
	Metadata         *string                `protobuf:"bytes,7,opt,name=metadata,proto3,oneof" json:"metadata,omitempty"`
	MediaEncryption  *SIPMediaEncryption    `protobuf:"varint,8,opt,name=media_encryption,json=mediaEncryption,proto3,enum=livekit.SIPMediaEncryption,oneof" json:"media_encryption,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *SIPInboundTrunkUpdate) Reset() {
	*x = SIPInboundTrunkUpdate{}
	mi := &file_livekit_sip_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SIPInboundTrunkUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SIPInboundTrunkUpdate) ProtoMessage() {}

func (x *SIPInboundTrunkUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SIPInboundTrunkUpdate.ProtoReflect.Descriptor instead.
func (*SIPInboundTrunkUpdate) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{6}
}

func (x *SIPInboundTrunkUpdate) GetNumbers() *ListUpdate {
	if x != nil {
		return x.Numbers
	}
	return nil
}

func (x *SIPInboundTrunkUpdate) GetAllowedAddresses() *ListUpdate {
	if x != nil {
		return x.AllowedAddresses
	}
	return nil
}

func (x *SIPInboundTrunkUpdate) GetAllowedNumbers() *ListUpdate {
	if x != nil {
		return x.AllowedNumbers
	}
	return nil
}

func (x *SIPInboundTrunkUpdate) GetAuthUsername() string {
	if x != nil && x.AuthUsername != nil {
		return *x.AuthUsername
	}
	return ""
}

func (x *SIPInboundTrunkUpdate) GetAuthPassword() string {
	if x != nil && x.AuthPassword != nil {
		return *x.AuthPassword
	}
	return ""
}

func (x *SIPInboundTrunkUpdate) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *SIPInboundTrunkUpdate) GetMetadata() string {
	if x != nil && x.Metadata != nil {
		return *x.Metadata
	}
	return ""
}

func (x *SIPInboundTrunkUpdate) GetMediaEncryption() SIPMediaEncryption {
	if x != nil && x.MediaEncryption != nil {
		return *x.MediaEncryption
	}
	return SIPMediaEncryption_SIP_MEDIA_ENCRYPT_DISABLE
}

type CreateSIPOutboundTrunkRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Trunk         *SIPOutboundTrunkInfo  `protobuf:"bytes,1,opt,name=trunk,proto3" json:"trunk,omitempty"` // Trunk ID is ignored
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateSIPOutboundTrunkRequest) Reset() {
	*x = CreateSIPOutboundTrunkRequest{}
	mi := &file_livekit_sip_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSIPOutboundTrunkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSIPOutboundTrunkRequest) ProtoMessage() {}

func (x *CreateSIPOutboundTrunkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSIPOutboundTrunkRequest.ProtoReflect.Descriptor instead.
func (*CreateSIPOutboundTrunkRequest) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{7}
}

func (x *CreateSIPOutboundTrunkRequest) GetTrunk() *SIPOutboundTrunkInfo {
	if x != nil {
		return x.Trunk
	}
	return nil
}

type UpdateSIPOutboundTrunkRequest struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	SipTrunkId string                 `protobuf:"bytes,1,opt,name=sip_trunk_id,json=sipTrunkId,proto3" json:"sip_trunk_id,omitempty"`
	// Types that are valid to be assigned to Action:
	//
	//	*UpdateSIPOutboundTrunkRequest_Replace
	//	*UpdateSIPOutboundTrunkRequest_Update
	Action        isUpdateSIPOutboundTrunkRequest_Action `protobuf_oneof:"action"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateSIPOutboundTrunkRequest) Reset() {
	*x = UpdateSIPOutboundTrunkRequest{}
	mi := &file_livekit_sip_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateSIPOutboundTrunkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSIPOutboundTrunkRequest) ProtoMessage() {}

func (x *UpdateSIPOutboundTrunkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSIPOutboundTrunkRequest.ProtoReflect.Descriptor instead.
func (*UpdateSIPOutboundTrunkRequest) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateSIPOutboundTrunkRequest) GetSipTrunkId() string {
	if x != nil {
		return x.SipTrunkId
	}
	return ""
}

func (x *UpdateSIPOutboundTrunkRequest) GetAction() isUpdateSIPOutboundTrunkRequest_Action {
	if x != nil {
		return x.Action
	}
	return nil
}

func (x *UpdateSIPOutboundTrunkRequest) GetReplace() *SIPOutboundTrunkInfo {
	if x != nil {
		if x, ok := x.Action.(*UpdateSIPOutboundTrunkRequest_Replace); ok {
			return x.Replace
		}
	}
	return nil
}

func (x *UpdateSIPOutboundTrunkRequest) GetUpdate() *SIPOutboundTrunkUpdate {
	if x != nil {
		if x, ok := x.Action.(*UpdateSIPOutboundTrunkRequest_Update); ok {
			return x.Update
		}
	}
	return nil
}

type isUpdateSIPOutboundTrunkRequest_Action interface {
	isUpdateSIPOutboundTrunkRequest_Action()
}

type UpdateSIPOutboundTrunkRequest_Replace struct {
	Replace *SIPOutboundTrunkInfo `protobuf:"bytes,2,opt,name=replace,proto3,oneof"`
}

type UpdateSIPOutboundTrunkRequest_Update struct {
	Update *SIPOutboundTrunkUpdate `protobuf:"bytes,3,opt,name=update,proto3,oneof"`
}

func (*UpdateSIPOutboundTrunkRequest_Replace) isUpdateSIPOutboundTrunkRequest_Action() {}

func (*UpdateSIPOutboundTrunkRequest_Update) isUpdateSIPOutboundTrunkRequest_Action() {}

type SIPOutboundTrunkInfo struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	SipTrunkId string                 `protobuf:"bytes,1,opt,name=sip_trunk_id,json=sipTrunkId,proto3" json:"sip_trunk_id,omitempty"`
	// Human-readable name for the Trunk.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// User-defined metadata for the Trunk.
	Metadata string `protobuf:"bytes,3,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// Hostname or IP that SIP INVITE is sent too.
	// Note that this is not a SIP URI and should not contain the 'sip:' protocol prefix.
	Address string `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	// country where the call terminates as ISO 3166-1 alpha-2 (https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2). This will be used by the livekit infrastructure to route calls.
	DestinationCountry string `protobuf:"bytes,14,opt,name=destination_country,json=destinationCountry,proto3" json:"destination_country,omitempty"`
	// SIP Transport used for outbound call.
	Transport SIPTransport `protobuf:"varint,5,opt,name=transport,proto3,enum=livekit.SIPTransport" json:"transport,omitempty"`
	// Numbers used to make the calls. Random one from this list will be selected.
	Numbers []string `protobuf:"bytes,6,rep,name=numbers,proto3" json:"numbers,omitempty"`
	// Username and password used to authenticate with SIP server.
	// May be empty to have no authentication.
	AuthUsername string `protobuf:"bytes,7,opt,name=auth_username,json=authUsername,proto3" json:"auth_username,omitempty"`
	AuthPassword string `protobuf:"bytes,8,opt,name=auth_password,json=authPassword,proto3" json:"auth_password,omitempty"`
	// Include these SIP X-* headers in INVITE request.
	// These headers are sent as-is and may help identify this call as coming from LiveKit for the other SIP endpoint.
	Headers map[string]string `protobuf:"bytes,9,rep,name=headers,proto3" json:"headers,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Map SIP X-* headers from 200 OK to SIP participant attributes.
	// Keys are the names of X-* headers and values are the names of attributes they will be mapped to.
	HeadersToAttributes map[string]string `protobuf:"bytes,10,rep,name=headers_to_attributes,json=headersToAttributes,proto3" json:"headers_to_attributes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Map LiveKit attributes to SIP X-* headers when sending BYE or REFER requests.
	// Keys are the names of attributes and values are the names of X-* headers they will be mapped to.
	AttributesToHeaders map[string]string `protobuf:"bytes,11,rep,name=attributes_to_headers,json=attributesToHeaders,proto3" json:"attributes_to_headers,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Map SIP headers from 200 OK to sip.h.* participant attributes automatically.
	//
	// When the names of required headers is known, using headers_to_attributes is strongly recommended.
	//
	// When mapping 200 OK headers to follow-up request headers with attributes_to_headers map,
	// lowercase header names should be used, for example: sip.h.x-custom-header.
	IncludeHeaders  SIPHeaderOptions   `protobuf:"varint,12,opt,name=include_headers,json=includeHeaders,proto3,enum=livekit.SIPHeaderOptions" json:"include_headers,omitempty"`
	MediaEncryption SIPMediaEncryption `protobuf:"varint,13,opt,name=media_encryption,json=mediaEncryption,proto3,enum=livekit.SIPMediaEncryption" json:"media_encryption,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *SIPOutboundTrunkInfo) Reset() {
	*x = SIPOutboundTrunkInfo{}
	mi := &file_livekit_sip_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SIPOutboundTrunkInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SIPOutboundTrunkInfo) ProtoMessage() {}

func (x *SIPOutboundTrunkInfo) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SIPOutboundTrunkInfo.ProtoReflect.Descriptor instead.
func (*SIPOutboundTrunkInfo) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{9}
}

func (x *SIPOutboundTrunkInfo) GetSipTrunkId() string {
	if x != nil {
		return x.SipTrunkId
	}
	return ""
}

func (x *SIPOutboundTrunkInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SIPOutboundTrunkInfo) GetMetadata() string {
	if x != nil {
		return x.Metadata
	}
	return ""
}

func (x *SIPOutboundTrunkInfo) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *SIPOutboundTrunkInfo) GetDestinationCountry() string {
	if x != nil {
		return x.DestinationCountry
	}
	return ""
}

func (x *SIPOutboundTrunkInfo) GetTransport() SIPTransport {
	if x != nil {
		return x.Transport
	}
	return SIPTransport_SIP_TRANSPORT_AUTO
}

func (x *SIPOutboundTrunkInfo) GetNumbers() []string {
	if x != nil {
		return x.Numbers
	}
	return nil
}

func (x *SIPOutboundTrunkInfo) GetAuthUsername() string {
	if x != nil {
		return x.AuthUsername
	}
	return ""
}

func (x *SIPOutboundTrunkInfo) GetAuthPassword() string {
	if x != nil {
		return x.AuthPassword
	}
	return ""
}

func (x *SIPOutboundTrunkInfo) GetHeaders() map[string]string {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *SIPOutboundTrunkInfo) GetHeadersToAttributes() map[string]string {
	if x != nil {
		return x.HeadersToAttributes
	}
	return nil
}

func (x *SIPOutboundTrunkInfo) GetAttributesToHeaders() map[string]string {
	if x != nil {
		return x.AttributesToHeaders
	}
	return nil
}

func (x *SIPOutboundTrunkInfo) GetIncludeHeaders() SIPHeaderOptions {
	if x != nil {
		return x.IncludeHeaders
	}
	return SIPHeaderOptions_SIP_NO_HEADERS
}

func (x *SIPOutboundTrunkInfo) GetMediaEncryption() SIPMediaEncryption {
	if x != nil {
		return x.MediaEncryption
	}
	return SIPMediaEncryption_SIP_MEDIA_ENCRYPT_DISABLE
}

type SIPOutboundTrunkUpdate struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Address            *string                `protobuf:"bytes,1,opt,name=address,proto3,oneof" json:"address,omitempty"`
	Transport          *SIPTransport          `protobuf:"varint,2,opt,name=transport,proto3,enum=livekit.SIPTransport,oneof" json:"transport,omitempty"`
	DestinationCountry *string                `protobuf:"bytes,9,opt,name=destination_country,json=destinationCountry,proto3,oneof" json:"destination_country,omitempty"`
	Numbers            *ListUpdate            `protobuf:"bytes,3,opt,name=numbers,proto3" json:"numbers,omitempty"`
	AuthUsername       *string                `protobuf:"bytes,4,opt,name=auth_username,json=authUsername,proto3,oneof" json:"auth_username,omitempty"`
	AuthPassword       *string                `protobuf:"bytes,5,opt,name=auth_password,json=authPassword,proto3,oneof" json:"auth_password,omitempty"`
	Name               *string                `protobuf:"bytes,6,opt,name=name,proto3,oneof" json:"name,omitempty"`
	Metadata           *string                `protobuf:"bytes,7,opt,name=metadata,proto3,oneof" json:"metadata,omitempty"`
	MediaEncryption    *SIPMediaEncryption    `protobuf:"varint,8,opt,name=media_encryption,json=mediaEncryption,proto3,enum=livekit.SIPMediaEncryption,oneof" json:"media_encryption,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *SIPOutboundTrunkUpdate) Reset() {
	*x = SIPOutboundTrunkUpdate{}
	mi := &file_livekit_sip_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SIPOutboundTrunkUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SIPOutboundTrunkUpdate) ProtoMessage() {}

func (x *SIPOutboundTrunkUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SIPOutboundTrunkUpdate.ProtoReflect.Descriptor instead.
func (*SIPOutboundTrunkUpdate) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{10}
}

func (x *SIPOutboundTrunkUpdate) GetAddress() string {
	if x != nil && x.Address != nil {
		return *x.Address
	}
	return ""
}

func (x *SIPOutboundTrunkUpdate) GetTransport() SIPTransport {
	if x != nil && x.Transport != nil {
		return *x.Transport
	}
	return SIPTransport_SIP_TRANSPORT_AUTO
}

func (x *SIPOutboundTrunkUpdate) GetDestinationCountry() string {
	if x != nil && x.DestinationCountry != nil {
		return *x.DestinationCountry
	}
	return ""
}

func (x *SIPOutboundTrunkUpdate) GetNumbers() *ListUpdate {
	if x != nil {
		return x.Numbers
	}
	return nil
}

func (x *SIPOutboundTrunkUpdate) GetAuthUsername() string {
	if x != nil && x.AuthUsername != nil {
		return *x.AuthUsername
	}
	return ""
}

func (x *SIPOutboundTrunkUpdate) GetAuthPassword() string {
	if x != nil && x.AuthPassword != nil {
		return *x.AuthPassword
	}
	return ""
}

func (x *SIPOutboundTrunkUpdate) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *SIPOutboundTrunkUpdate) GetMetadata() string {
	if x != nil && x.Metadata != nil {
		return *x.Metadata
	}
	return ""
}

func (x *SIPOutboundTrunkUpdate) GetMediaEncryption() SIPMediaEncryption {
	if x != nil && x.MediaEncryption != nil {
		return *x.MediaEncryption
	}
	return SIPMediaEncryption_SIP_MEDIA_ENCRYPT_DISABLE
}

type GetSIPInboundTrunkRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SipTrunkId    string                 `protobuf:"bytes,1,opt,name=sip_trunk_id,json=sipTrunkId,proto3" json:"sip_trunk_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSIPInboundTrunkRequest) Reset() {
	*x = GetSIPInboundTrunkRequest{}
	mi := &file_livekit_sip_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSIPInboundTrunkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSIPInboundTrunkRequest) ProtoMessage() {}

func (x *GetSIPInboundTrunkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSIPInboundTrunkRequest.ProtoReflect.Descriptor instead.
func (*GetSIPInboundTrunkRequest) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{11}
}

func (x *GetSIPInboundTrunkRequest) GetSipTrunkId() string {
	if x != nil {
		return x.SipTrunkId
	}
	return ""
}

type GetSIPInboundTrunkResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Trunk         *SIPInboundTrunkInfo   `protobuf:"bytes,1,opt,name=trunk,proto3" json:"trunk,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSIPInboundTrunkResponse) Reset() {
	*x = GetSIPInboundTrunkResponse{}
	mi := &file_livekit_sip_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSIPInboundTrunkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSIPInboundTrunkResponse) ProtoMessage() {}

func (x *GetSIPInboundTrunkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSIPInboundTrunkResponse.ProtoReflect.Descriptor instead.
func (*GetSIPInboundTrunkResponse) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{12}
}

func (x *GetSIPInboundTrunkResponse) GetTrunk() *SIPInboundTrunkInfo {
	if x != nil {
		return x.Trunk
	}
	return nil
}

type GetSIPOutboundTrunkRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SipTrunkId    string                 `protobuf:"bytes,1,opt,name=sip_trunk_id,json=sipTrunkId,proto3" json:"sip_trunk_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSIPOutboundTrunkRequest) Reset() {
	*x = GetSIPOutboundTrunkRequest{}
	mi := &file_livekit_sip_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSIPOutboundTrunkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSIPOutboundTrunkRequest) ProtoMessage() {}

func (x *GetSIPOutboundTrunkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSIPOutboundTrunkRequest.ProtoReflect.Descriptor instead.
func (*GetSIPOutboundTrunkRequest) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{13}
}

func (x *GetSIPOutboundTrunkRequest) GetSipTrunkId() string {
	if x != nil {
		return x.SipTrunkId
	}
	return ""
}

type GetSIPOutboundTrunkResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Trunk         *SIPOutboundTrunkInfo  `protobuf:"bytes,1,opt,name=trunk,proto3" json:"trunk,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSIPOutboundTrunkResponse) Reset() {
	*x = GetSIPOutboundTrunkResponse{}
	mi := &file_livekit_sip_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSIPOutboundTrunkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSIPOutboundTrunkResponse) ProtoMessage() {}

func (x *GetSIPOutboundTrunkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSIPOutboundTrunkResponse.ProtoReflect.Descriptor instead.
func (*GetSIPOutboundTrunkResponse) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{14}
}

func (x *GetSIPOutboundTrunkResponse) GetTrunk() *SIPOutboundTrunkInfo {
	if x != nil {
		return x.Trunk
	}
	return nil
}

// Deprecated: Marked as deprecated in livekit_sip.proto.
type ListSIPTrunkRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          *Pagination            `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSIPTrunkRequest) Reset() {
	*x = ListSIPTrunkRequest{}
	mi := &file_livekit_sip_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSIPTrunkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSIPTrunkRequest) ProtoMessage() {}

func (x *ListSIPTrunkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSIPTrunkRequest.ProtoReflect.Descriptor instead.
func (*ListSIPTrunkRequest) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{15}
}

func (x *ListSIPTrunkRequest) GetPage() *Pagination {
	if x != nil {
		return x.Page
	}
	return nil
}

// Deprecated: Marked as deprecated in livekit_sip.proto.
type ListSIPTrunkResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*SIPTrunkInfo        `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSIPTrunkResponse) Reset() {
	*x = ListSIPTrunkResponse{}
	mi := &file_livekit_sip_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSIPTrunkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSIPTrunkResponse) ProtoMessage() {}

func (x *ListSIPTrunkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSIPTrunkResponse.ProtoReflect.Descriptor instead.
func (*ListSIPTrunkResponse) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{16}
}

func (x *ListSIPTrunkResponse) GetItems() []*SIPTrunkInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

// ListSIPInboundTrunkRequest lists inbound trunks for given filters. If no filters are set, all trunks are listed.
type ListSIPInboundTrunkRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Page  *Pagination            `protobuf:"bytes,3,opt,name=page,proto3" json:"page,omitempty"`
	// Trunk IDs to list. If this option is set, the response will contains trunks in the same order.
	// If any of the trunks is missing, a nil item in that position will be sent in the response.
	TrunkIds []string `protobuf:"bytes,1,rep,name=trunk_ids,json=trunkIds,proto3" json:"trunk_ids,omitempty"`
	// Only list trunks that contain one of the numbers, including wildcard trunks.
	Numbers       []string `protobuf:"bytes,2,rep,name=numbers,proto3" json:"numbers,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSIPInboundTrunkRequest) Reset() {
	*x = ListSIPInboundTrunkRequest{}
	mi := &file_livekit_sip_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSIPInboundTrunkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSIPInboundTrunkRequest) ProtoMessage() {}

func (x *ListSIPInboundTrunkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSIPInboundTrunkRequest.ProtoReflect.Descriptor instead.
func (*ListSIPInboundTrunkRequest) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{17}
}

func (x *ListSIPInboundTrunkRequest) GetPage() *Pagination {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *ListSIPInboundTrunkRequest) GetTrunkIds() []string {
	if x != nil {
		return x.TrunkIds
	}
	return nil
}

func (x *ListSIPInboundTrunkRequest) GetNumbers() []string {
	if x != nil {
		return x.Numbers
	}
	return nil
}

type ListSIPInboundTrunkResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*SIPInboundTrunkInfo `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSIPInboundTrunkResponse) Reset() {
	*x = ListSIPInboundTrunkResponse{}
	mi := &file_livekit_sip_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSIPInboundTrunkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSIPInboundTrunkResponse) ProtoMessage() {}

func (x *ListSIPInboundTrunkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSIPInboundTrunkResponse.ProtoReflect.Descriptor instead.
func (*ListSIPInboundTrunkResponse) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{18}
}

func (x *ListSIPInboundTrunkResponse) GetItems() []*SIPInboundTrunkInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

// ListSIPOutboundTrunkRequest lists outbound trunks for given filters. If no filters are set, all trunks are listed.
type ListSIPOutboundTrunkRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Page  *Pagination            `protobuf:"bytes,3,opt,name=page,proto3" json:"page,omitempty"`
	// Trunk IDs to list. If this option is set, the response will contains trunks in the same order.
	// If any of the trunks is missing, a nil item in that position will be sent in the response.
	TrunkIds []string `protobuf:"bytes,1,rep,name=trunk_ids,json=trunkIds,proto3" json:"trunk_ids,omitempty"`
	// Only list trunks that contain one of the numbers, including wildcard trunks.
	Numbers       []string `protobuf:"bytes,2,rep,name=numbers,proto3" json:"numbers,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSIPOutboundTrunkRequest) Reset() {
	*x = ListSIPOutboundTrunkRequest{}
	mi := &file_livekit_sip_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSIPOutboundTrunkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSIPOutboundTrunkRequest) ProtoMessage() {}

func (x *ListSIPOutboundTrunkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSIPOutboundTrunkRequest.ProtoReflect.Descriptor instead.
func (*ListSIPOutboundTrunkRequest) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{19}
}

func (x *ListSIPOutboundTrunkRequest) GetPage() *Pagination {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *ListSIPOutboundTrunkRequest) GetTrunkIds() []string {
	if x != nil {
		return x.TrunkIds
	}
	return nil
}

func (x *ListSIPOutboundTrunkRequest) GetNumbers() []string {
	if x != nil {
		return x.Numbers
	}
	return nil
}

type ListSIPOutboundTrunkResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Items         []*SIPOutboundTrunkInfo `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSIPOutboundTrunkResponse) Reset() {
	*x = ListSIPOutboundTrunkResponse{}
	mi := &file_livekit_sip_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSIPOutboundTrunkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSIPOutboundTrunkResponse) ProtoMessage() {}

func (x *ListSIPOutboundTrunkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSIPOutboundTrunkResponse.ProtoReflect.Descriptor instead.
func (*ListSIPOutboundTrunkResponse) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{20}
}

func (x *ListSIPOutboundTrunkResponse) GetItems() []*SIPOutboundTrunkInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

type DeleteSIPTrunkRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SipTrunkId    string                 `protobuf:"bytes,1,opt,name=sip_trunk_id,json=sipTrunkId,proto3" json:"sip_trunk_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteSIPTrunkRequest) Reset() {
	*x = DeleteSIPTrunkRequest{}
	mi := &file_livekit_sip_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteSIPTrunkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSIPTrunkRequest) ProtoMessage() {}

func (x *DeleteSIPTrunkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSIPTrunkRequest.ProtoReflect.Descriptor instead.
func (*DeleteSIPTrunkRequest) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{21}
}

func (x *DeleteSIPTrunkRequest) GetSipTrunkId() string {
	if x != nil {
		return x.SipTrunkId
	}
	return ""
}

type SIPDispatchRuleDirect struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// What room should call be directed into
	RoomName string `protobuf:"bytes,1,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`
	// Optional pin required to enter room
	Pin           string `protobuf:"bytes,2,opt,name=pin,proto3" json:"pin,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SIPDispatchRuleDirect) Reset() {
	*x = SIPDispatchRuleDirect{}
	mi := &file_livekit_sip_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SIPDispatchRuleDirect) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SIPDispatchRuleDirect) ProtoMessage() {}

func (x *SIPDispatchRuleDirect) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SIPDispatchRuleDirect.ProtoReflect.Descriptor instead.
func (*SIPDispatchRuleDirect) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{22}
}

func (x *SIPDispatchRuleDirect) GetRoomName() string {
	if x != nil {
		return x.RoomName
	}
	return ""
}

func (x *SIPDispatchRuleDirect) GetPin() string {
	if x != nil {
		return x.Pin
	}
	return ""
}

type SIPDispatchRuleIndividual struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Prefix used on new room name
	RoomPrefix string `protobuf:"bytes,1,opt,name=room_prefix,json=roomPrefix,proto3" json:"room_prefix,omitempty"`
	// Optional pin required to enter room
	Pin           string `protobuf:"bytes,2,opt,name=pin,proto3" json:"pin,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SIPDispatchRuleIndividual) Reset() {
	*x = SIPDispatchRuleIndividual{}
	mi := &file_livekit_sip_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SIPDispatchRuleIndividual) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SIPDispatchRuleIndividual) ProtoMessage() {}

func (x *SIPDispatchRuleIndividual) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SIPDispatchRuleIndividual.ProtoReflect.Descriptor instead.
func (*SIPDispatchRuleIndividual) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{23}
}

func (x *SIPDispatchRuleIndividual) GetRoomPrefix() string {
	if x != nil {
		return x.RoomPrefix
	}
	return ""
}

func (x *SIPDispatchRuleIndividual) GetPin() string {
	if x != nil {
		return x.Pin
	}
	return ""
}

type SIPDispatchRuleCallee struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Prefix used on new room name
	RoomPrefix string `protobuf:"bytes,1,opt,name=room_prefix,json=roomPrefix,proto3" json:"room_prefix,omitempty"`
	// Optional pin required to enter room
	Pin string `protobuf:"bytes,2,opt,name=pin,proto3" json:"pin,omitempty"`
	// Optionally append random suffix
	Randomize     bool `protobuf:"varint,3,opt,name=randomize,proto3" json:"randomize,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SIPDispatchRuleCallee) Reset() {
	*x = SIPDispatchRuleCallee{}
	mi := &file_livekit_sip_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SIPDispatchRuleCallee) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SIPDispatchRuleCallee) ProtoMessage() {}

func (x *SIPDispatchRuleCallee) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SIPDispatchRuleCallee.ProtoReflect.Descriptor instead.
func (*SIPDispatchRuleCallee) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{24}
}

func (x *SIPDispatchRuleCallee) GetRoomPrefix() string {
	if x != nil {
		return x.RoomPrefix
	}
	return ""
}

func (x *SIPDispatchRuleCallee) GetPin() string {
	if x != nil {
		return x.Pin
	}
	return ""
}

func (x *SIPDispatchRuleCallee) GetRandomize() bool {
	if x != nil {
		return x.Randomize
	}
	return false
}

type SIPDispatchRule struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Rule:
	//
	//	*SIPDispatchRule_DispatchRuleDirect
	//	*SIPDispatchRule_DispatchRuleIndividual
	//	*SIPDispatchRule_DispatchRuleCallee
	Rule          isSIPDispatchRule_Rule `protobuf_oneof:"rule"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SIPDispatchRule) Reset() {
	*x = SIPDispatchRule{}
	mi := &file_livekit_sip_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SIPDispatchRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SIPDispatchRule) ProtoMessage() {}

func (x *SIPDispatchRule) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SIPDispatchRule.ProtoReflect.Descriptor instead.
func (*SIPDispatchRule) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{25}
}

func (x *SIPDispatchRule) GetRule() isSIPDispatchRule_Rule {
	if x != nil {
		return x.Rule
	}
	return nil
}

func (x *SIPDispatchRule) GetDispatchRuleDirect() *SIPDispatchRuleDirect {
	if x != nil {
		if x, ok := x.Rule.(*SIPDispatchRule_DispatchRuleDirect); ok {
			return x.DispatchRuleDirect
		}
	}
	return nil
}

func (x *SIPDispatchRule) GetDispatchRuleIndividual() *SIPDispatchRuleIndividual {
	if x != nil {
		if x, ok := x.Rule.(*SIPDispatchRule_DispatchRuleIndividual); ok {
			return x.DispatchRuleIndividual
		}
	}
	return nil
}

func (x *SIPDispatchRule) GetDispatchRuleCallee() *SIPDispatchRuleCallee {
	if x != nil {
		if x, ok := x.Rule.(*SIPDispatchRule_DispatchRuleCallee); ok {
			return x.DispatchRuleCallee
		}
	}
	return nil
}

type isSIPDispatchRule_Rule interface {
	isSIPDispatchRule_Rule()
}

type SIPDispatchRule_DispatchRuleDirect struct {
	// SIPDispatchRuleDirect is a `SIP Dispatch Rule` that puts a user directly into a room
	// This places users into an existing room. Optionally you can require a pin before a user can
	// enter the room
	DispatchRuleDirect *SIPDispatchRuleDirect `protobuf:"bytes,1,opt,name=dispatch_rule_direct,json=dispatchRuleDirect,proto3,oneof"`
}

type SIPDispatchRule_DispatchRuleIndividual struct {
	// SIPDispatchRuleIndividual is a `SIP Dispatch Rule` that creates a new room for each caller.
	DispatchRuleIndividual *SIPDispatchRuleIndividual `protobuf:"bytes,2,opt,name=dispatch_rule_individual,json=dispatchRuleIndividual,proto3,oneof"`
}

type SIPDispatchRule_DispatchRuleCallee struct {
	// SIPDispatchRuleCallee is a `SIP Dispatch Rule` that creates a new room for each callee.
	DispatchRuleCallee *SIPDispatchRuleCallee `protobuf:"bytes,3,opt,name=dispatch_rule_callee,json=dispatchRuleCallee,proto3,oneof"`
}

func (*SIPDispatchRule_DispatchRuleDirect) isSIPDispatchRule_Rule() {}

func (*SIPDispatchRule_DispatchRuleIndividual) isSIPDispatchRule_Rule() {}

func (*SIPDispatchRule_DispatchRuleCallee) isSIPDispatchRule_Rule() {}

type CreateSIPDispatchRuleRequest struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	DispatchRule *SIPDispatchRuleInfo   `protobuf:"bytes,10,opt,name=dispatch_rule,json=dispatchRule,proto3" json:"dispatch_rule,omitempty"` // Rule ID is ignored
	// Deprecated: Marked as deprecated in livekit_sip.proto.
	Rule *SIPDispatchRule `protobuf:"bytes,1,opt,name=rule,proto3" json:"rule,omitempty"`
	// What trunks are accepted for this dispatch rule
	// If empty all trunks will match this dispatch rule
	//
	// Deprecated: Marked as deprecated in livekit_sip.proto.
	TrunkIds []string `protobuf:"bytes,2,rep,name=trunk_ids,json=trunkIds,proto3" json:"trunk_ids,omitempty"`
	// By default the From value (Phone number) is used for participant name/identity and added to attributes.
	// If true, a random value for identity will be used and numbers will be omitted from attributes.
	//
	// Deprecated: Marked as deprecated in livekit_sip.proto.
	HidePhoneNumber bool `protobuf:"varint,3,opt,name=hide_phone_number,json=hidePhoneNumber,proto3" json:"hide_phone_number,omitempty"`
	// Dispatch Rule will only accept a call made to these numbers (if set).
	//
	// Deprecated: Marked as deprecated in livekit_sip.proto.
	InboundNumbers []string `protobuf:"bytes,6,rep,name=inbound_numbers,json=inboundNumbers,proto3" json:"inbound_numbers,omitempty"`
	// Optional human-readable name for the Dispatch Rule.
	//
	// Deprecated: Marked as deprecated in livekit_sip.proto.
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// User-defined metadata for the Dispatch Rule.
	// Participants created by this rule will inherit this metadata.
	//
	// Deprecated: Marked as deprecated in livekit_sip.proto.
	Metadata string `protobuf:"bytes,5,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// User-defined attributes for the Dispatch Rule.
	// Participants created by this rule will inherit these attributes.
	//
	// Deprecated: Marked as deprecated in livekit_sip.proto.
	Attributes map[string]string `protobuf:"bytes,7,rep,name=attributes,proto3" json:"attributes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Cloud-only, config preset to use
	//
	// Deprecated: Marked as deprecated in livekit_sip.proto.
	RoomPreset string `protobuf:"bytes,8,opt,name=room_preset,json=roomPreset,proto3" json:"room_preset,omitempty"`
	// RoomConfiguration to use if the participant initiates the room
	//
	// Deprecated: Marked as deprecated in livekit_sip.proto.
	RoomConfig    *RoomConfiguration `protobuf:"bytes,9,opt,name=room_config,json=roomConfig,proto3" json:"room_config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateSIPDispatchRuleRequest) Reset() {
	*x = CreateSIPDispatchRuleRequest{}
	mi := &file_livekit_sip_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSIPDispatchRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSIPDispatchRuleRequest) ProtoMessage() {}

func (x *CreateSIPDispatchRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSIPDispatchRuleRequest.ProtoReflect.Descriptor instead.
func (*CreateSIPDispatchRuleRequest) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{26}
}

func (x *CreateSIPDispatchRuleRequest) GetDispatchRule() *SIPDispatchRuleInfo {
	if x != nil {
		return x.DispatchRule
	}
	return nil
}

// Deprecated: Marked as deprecated in livekit_sip.proto.
func (x *CreateSIPDispatchRuleRequest) GetRule() *SIPDispatchRule {
	if x != nil {
		return x.Rule
	}
	return nil
}

// Deprecated: Marked as deprecated in livekit_sip.proto.
func (x *CreateSIPDispatchRuleRequest) GetTrunkIds() []string {
	if x != nil {
		return x.TrunkIds
	}
	return nil
}

// Deprecated: Marked as deprecated in livekit_sip.proto.
func (x *CreateSIPDispatchRuleRequest) GetHidePhoneNumber() bool {
	if x != nil {
		return x.HidePhoneNumber
	}
	return false
}

// Deprecated: Marked as deprecated in livekit_sip.proto.
func (x *CreateSIPDispatchRuleRequest) GetInboundNumbers() []string {
	if x != nil {
		return x.InboundNumbers
	}
	return nil
}

// Deprecated: Marked as deprecated in livekit_sip.proto.
func (x *CreateSIPDispatchRuleRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Deprecated: Marked as deprecated in livekit_sip.proto.
func (x *CreateSIPDispatchRuleRequest) GetMetadata() string {
	if x != nil {
		return x.Metadata
	}
	return ""
}

// Deprecated: Marked as deprecated in livekit_sip.proto.
func (x *CreateSIPDispatchRuleRequest) GetAttributes() map[string]string {
	if x != nil {
		return x.Attributes
	}
	return nil
}

// Deprecated: Marked as deprecated in livekit_sip.proto.
func (x *CreateSIPDispatchRuleRequest) GetRoomPreset() string {
	if x != nil {
		return x.RoomPreset
	}
	return ""
}

// Deprecated: Marked as deprecated in livekit_sip.proto.
func (x *CreateSIPDispatchRuleRequest) GetRoomConfig() *RoomConfiguration {
	if x != nil {
		return x.RoomConfig
	}
	return nil
}

type UpdateSIPDispatchRuleRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	SipDispatchRuleId string                 `protobuf:"bytes,1,opt,name=sip_dispatch_rule_id,json=sipDispatchRuleId,proto3" json:"sip_dispatch_rule_id,omitempty"`
	// Types that are valid to be assigned to Action:
	//
	//	*UpdateSIPDispatchRuleRequest_Replace
	//	*UpdateSIPDispatchRuleRequest_Update
	Action        isUpdateSIPDispatchRuleRequest_Action `protobuf_oneof:"action"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateSIPDispatchRuleRequest) Reset() {
	*x = UpdateSIPDispatchRuleRequest{}
	mi := &file_livekit_sip_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateSIPDispatchRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSIPDispatchRuleRequest) ProtoMessage() {}

func (x *UpdateSIPDispatchRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSIPDispatchRuleRequest.ProtoReflect.Descriptor instead.
func (*UpdateSIPDispatchRuleRequest) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{27}
}

func (x *UpdateSIPDispatchRuleRequest) GetSipDispatchRuleId() string {
	if x != nil {
		return x.SipDispatchRuleId
	}
	return ""
}

func (x *UpdateSIPDispatchRuleRequest) GetAction() isUpdateSIPDispatchRuleRequest_Action {
	if x != nil {
		return x.Action
	}
	return nil
}

func (x *UpdateSIPDispatchRuleRequest) GetReplace() *SIPDispatchRuleInfo {
	if x != nil {
		if x, ok := x.Action.(*UpdateSIPDispatchRuleRequest_Replace); ok {
			return x.Replace
		}
	}
	return nil
}

func (x *UpdateSIPDispatchRuleRequest) GetUpdate() *SIPDispatchRuleUpdate {
	if x != nil {
		if x, ok := x.Action.(*UpdateSIPDispatchRuleRequest_Update); ok {
			return x.Update
		}
	}
	return nil
}

type isUpdateSIPDispatchRuleRequest_Action interface {
	isUpdateSIPDispatchRuleRequest_Action()
}

type UpdateSIPDispatchRuleRequest_Replace struct {
	Replace *SIPDispatchRuleInfo `protobuf:"bytes,2,opt,name=replace,proto3,oneof"`
}

type UpdateSIPDispatchRuleRequest_Update struct {
	Update *SIPDispatchRuleUpdate `protobuf:"bytes,3,opt,name=update,proto3,oneof"`
}

func (*UpdateSIPDispatchRuleRequest_Replace) isUpdateSIPDispatchRuleRequest_Action() {}

func (*UpdateSIPDispatchRuleRequest_Update) isUpdateSIPDispatchRuleRequest_Action() {}

type SIPDispatchRuleInfo struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	SipDispatchRuleId string                 `protobuf:"bytes,1,opt,name=sip_dispatch_rule_id,json=sipDispatchRuleId,proto3" json:"sip_dispatch_rule_id,omitempty"`
	Rule              *SIPDispatchRule       `protobuf:"bytes,2,opt,name=rule,proto3" json:"rule,omitempty"`
	TrunkIds          []string               `protobuf:"bytes,3,rep,name=trunk_ids,json=trunkIds,proto3" json:"trunk_ids,omitempty"`
	HidePhoneNumber   bool                   `protobuf:"varint,4,opt,name=hide_phone_number,json=hidePhoneNumber,proto3" json:"hide_phone_number,omitempty"`
	// Dispatch Rule will only accept a call made to these numbers (if set).
	InboundNumbers []string `protobuf:"bytes,7,rep,name=inbound_numbers,json=inboundNumbers,proto3" json:"inbound_numbers,omitempty"`
	// Human-readable name for the Dispatch Rule.
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	// User-defined metadata for the Dispatch Rule.
	// Participants created by this rule will inherit this metadata.
	Metadata string `protobuf:"bytes,6,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// User-defined attributes for the Dispatch Rule.
	// Participants created by this rule will inherit these attributes.
	Attributes map[string]string `protobuf:"bytes,8,rep,name=attributes,proto3" json:"attributes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Cloud-only, config preset to use
	RoomPreset string `protobuf:"bytes,9,opt,name=room_preset,json=roomPreset,proto3" json:"room_preset,omitempty"`
	// RoomConfiguration to use if the participant initiates the room
	RoomConfig      *RoomConfiguration `protobuf:"bytes,10,opt,name=room_config,json=roomConfig,proto3" json:"room_config,omitempty"`
	KrispEnabled    bool               `protobuf:"varint,11,opt,name=krisp_enabled,json=krispEnabled,proto3" json:"krisp_enabled,omitempty"`
	MediaEncryption SIPMediaEncryption `protobuf:"varint,12,opt,name=media_encryption,json=mediaEncryption,proto3,enum=livekit.SIPMediaEncryption" json:"media_encryption,omitempty"` // NEXT ID: 13
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *SIPDispatchRuleInfo) Reset() {
	*x = SIPDispatchRuleInfo{}
	mi := &file_livekit_sip_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SIPDispatchRuleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SIPDispatchRuleInfo) ProtoMessage() {}

func (x *SIPDispatchRuleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SIPDispatchRuleInfo.ProtoReflect.Descriptor instead.
func (*SIPDispatchRuleInfo) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{28}
}

func (x *SIPDispatchRuleInfo) GetSipDispatchRuleId() string {
	if x != nil {
		return x.SipDispatchRuleId
	}
	return ""
}

func (x *SIPDispatchRuleInfo) GetRule() *SIPDispatchRule {
	if x != nil {
		return x.Rule
	}
	return nil
}

func (x *SIPDispatchRuleInfo) GetTrunkIds() []string {
	if x != nil {
		return x.TrunkIds
	}
	return nil
}

func (x *SIPDispatchRuleInfo) GetHidePhoneNumber() bool {
	if x != nil {
		return x.HidePhoneNumber
	}
	return false
}

func (x *SIPDispatchRuleInfo) GetInboundNumbers() []string {
	if x != nil {
		return x.InboundNumbers
	}
	return nil
}

func (x *SIPDispatchRuleInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SIPDispatchRuleInfo) GetMetadata() string {
	if x != nil {
		return x.Metadata
	}
	return ""
}

func (x *SIPDispatchRuleInfo) GetAttributes() map[string]string {
	if x != nil {
		return x.Attributes
	}
	return nil
}

func (x *SIPDispatchRuleInfo) GetRoomPreset() string {
	if x != nil {
		return x.RoomPreset
	}
	return ""
}

func (x *SIPDispatchRuleInfo) GetRoomConfig() *RoomConfiguration {
	if x != nil {
		return x.RoomConfig
	}
	return nil
}

func (x *SIPDispatchRuleInfo) GetKrispEnabled() bool {
	if x != nil {
		return x.KrispEnabled
	}
	return false
}

func (x *SIPDispatchRuleInfo) GetMediaEncryption() SIPMediaEncryption {
	if x != nil {
		return x.MediaEncryption
	}
	return SIPMediaEncryption_SIP_MEDIA_ENCRYPT_DISABLE
}

type SIPDispatchRuleUpdate struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	TrunkIds        *ListUpdate            `protobuf:"bytes,1,opt,name=trunk_ids,json=trunkIds,proto3" json:"trunk_ids,omitempty"`
	Rule            *SIPDispatchRule       `protobuf:"bytes,2,opt,name=rule,proto3" json:"rule,omitempty"`
	Name            *string                `protobuf:"bytes,3,opt,name=name,proto3,oneof" json:"name,omitempty"`
	Metadata        *string                `protobuf:"bytes,4,opt,name=metadata,proto3,oneof" json:"metadata,omitempty"`
	Attributes      map[string]string      `protobuf:"bytes,5,rep,name=attributes,proto3" json:"attributes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MediaEncryption *SIPMediaEncryption    `protobuf:"varint,6,opt,name=media_encryption,json=mediaEncryption,proto3,enum=livekit.SIPMediaEncryption,oneof" json:"media_encryption,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *SIPDispatchRuleUpdate) Reset() {
	*x = SIPDispatchRuleUpdate{}
	mi := &file_livekit_sip_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SIPDispatchRuleUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SIPDispatchRuleUpdate) ProtoMessage() {}

func (x *SIPDispatchRuleUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SIPDispatchRuleUpdate.ProtoReflect.Descriptor instead.
func (*SIPDispatchRuleUpdate) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{29}
}

func (x *SIPDispatchRuleUpdate) GetTrunkIds() *ListUpdate {
	if x != nil {
		return x.TrunkIds
	}
	return nil
}

func (x *SIPDispatchRuleUpdate) GetRule() *SIPDispatchRule {
	if x != nil {
		return x.Rule
	}
	return nil
}

func (x *SIPDispatchRuleUpdate) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *SIPDispatchRuleUpdate) GetMetadata() string {
	if x != nil && x.Metadata != nil {
		return *x.Metadata
	}
	return ""
}

func (x *SIPDispatchRuleUpdate) GetAttributes() map[string]string {
	if x != nil {
		return x.Attributes
	}
	return nil
}

func (x *SIPDispatchRuleUpdate) GetMediaEncryption() SIPMediaEncryption {
	if x != nil && x.MediaEncryption != nil {
		return *x.MediaEncryption
	}
	return SIPMediaEncryption_SIP_MEDIA_ENCRYPT_DISABLE
}

// ListSIPDispatchRuleRequest lists dispatch rules for given filters. If no filters are set, all rules are listed.
type ListSIPDispatchRuleRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Page  *Pagination            `protobuf:"bytes,3,opt,name=page,proto3" json:"page,omitempty"`
	// Rule IDs to list. If this option is set, the response will contains rules in the same order.
	// If any of the rules is missing, a nil item in that position will be sent in the response.
	DispatchRuleIds []string `protobuf:"bytes,1,rep,name=dispatch_rule_ids,json=dispatchRuleIds,proto3" json:"dispatch_rule_ids,omitempty"`
	// Only list rules that contain one of the Trunk IDs, including wildcard rules.
	TrunkIds      []string `protobuf:"bytes,2,rep,name=trunk_ids,json=trunkIds,proto3" json:"trunk_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSIPDispatchRuleRequest) Reset() {
	*x = ListSIPDispatchRuleRequest{}
	mi := &file_livekit_sip_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSIPDispatchRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSIPDispatchRuleRequest) ProtoMessage() {}

func (x *ListSIPDispatchRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSIPDispatchRuleRequest.ProtoReflect.Descriptor instead.
func (*ListSIPDispatchRuleRequest) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{30}
}

func (x *ListSIPDispatchRuleRequest) GetPage() *Pagination {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *ListSIPDispatchRuleRequest) GetDispatchRuleIds() []string {
	if x != nil {
		return x.DispatchRuleIds
	}
	return nil
}

func (x *ListSIPDispatchRuleRequest) GetTrunkIds() []string {
	if x != nil {
		return x.TrunkIds
	}
	return nil
}

type ListSIPDispatchRuleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*SIPDispatchRuleInfo `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSIPDispatchRuleResponse) Reset() {
	*x = ListSIPDispatchRuleResponse{}
	mi := &file_livekit_sip_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSIPDispatchRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSIPDispatchRuleResponse) ProtoMessage() {}

func (x *ListSIPDispatchRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSIPDispatchRuleResponse.ProtoReflect.Descriptor instead.
func (*ListSIPDispatchRuleResponse) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{31}
}

func (x *ListSIPDispatchRuleResponse) GetItems() []*SIPDispatchRuleInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

type DeleteSIPDispatchRuleRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	SipDispatchRuleId string                 `protobuf:"bytes,1,opt,name=sip_dispatch_rule_id,json=sipDispatchRuleId,proto3" json:"sip_dispatch_rule_id,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *DeleteSIPDispatchRuleRequest) Reset() {
	*x = DeleteSIPDispatchRuleRequest{}
	mi := &file_livekit_sip_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteSIPDispatchRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSIPDispatchRuleRequest) ProtoMessage() {}

func (x *DeleteSIPDispatchRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSIPDispatchRuleRequest.ProtoReflect.Descriptor instead.
func (*DeleteSIPDispatchRuleRequest) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{32}
}

func (x *DeleteSIPDispatchRuleRequest) GetSipDispatchRuleId() string {
	if x != nil {
		return x.SipDispatchRuleId
	}
	return ""
}

type SIPOutboundConfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// SIP server address
	Hostname string `protobuf:"bytes,1,opt,name=hostname,proto3" json:"hostname,omitempty"`
	// country where the call terminates as ISO 3166-1 alpha-2 (https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2). This will be used by the livekit infrastructure to route calls.
	DestinationCountry string `protobuf:"bytes,7,opt,name=destination_country,json=destinationCountry,proto3" json:"destination_country,omitempty"`
	// SIP Transport used for outbound call.
	Transport SIPTransport `protobuf:"varint,2,opt,name=transport,proto3,enum=livekit.SIPTransport" json:"transport,omitempty"`
	// Username and password used to authenticate with SIP server.
	// May be empty to have no authentication.
	AuthUsername string `protobuf:"bytes,3,opt,name=auth_username,json=authUsername,proto3" json:"auth_username,omitempty"`
	AuthPassword string `protobuf:"bytes,4,opt,name=auth_password,json=authPassword,proto3" json:"auth_password,omitempty"`
	// Map SIP X-* headers from 200 OK to SIP participant attributes.
	// Keys are the names of X-* headers and values are the names of attributes they will be mapped to.
	HeadersToAttributes map[string]string `protobuf:"bytes,5,rep,name=headers_to_attributes,json=headersToAttributes,proto3" json:"headers_to_attributes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Map LiveKit attributes to SIP X-* headers when sending BYE or REFER requests.
	// Keys are the names of attributes and values are the names of X-* headers they will be mapped to.
	AttributesToHeaders map[string]string `protobuf:"bytes,6,rep,name=attributes_to_headers,json=attributesToHeaders,proto3" json:"attributes_to_headers,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *SIPOutboundConfig) Reset() {
	*x = SIPOutboundConfig{}
	mi := &file_livekit_sip_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SIPOutboundConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SIPOutboundConfig) ProtoMessage() {}

func (x *SIPOutboundConfig) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SIPOutboundConfig.ProtoReflect.Descriptor instead.
func (*SIPOutboundConfig) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{33}
}

func (x *SIPOutboundConfig) GetHostname() string {
	if x != nil {
		return x.Hostname
	}
	return ""
}

func (x *SIPOutboundConfig) GetDestinationCountry() string {
	if x != nil {
		return x.DestinationCountry
	}
	return ""
}

func (x *SIPOutboundConfig) GetTransport() SIPTransport {
	if x != nil {
		return x.Transport
	}
	return SIPTransport_SIP_TRANSPORT_AUTO
}

func (x *SIPOutboundConfig) GetAuthUsername() string {
	if x != nil {
		return x.AuthUsername
	}
	return ""
}

func (x *SIPOutboundConfig) GetAuthPassword() string {
	if x != nil {
		return x.AuthPassword
	}
	return ""
}

func (x *SIPOutboundConfig) GetHeadersToAttributes() map[string]string {
	if x != nil {
		return x.HeadersToAttributes
	}
	return nil
}

func (x *SIPOutboundConfig) GetAttributesToHeaders() map[string]string {
	if x != nil {
		return x.AttributesToHeaders
	}
	return nil
}

// A SIP Participant is a singular SIP session connected to a LiveKit room via
// a SIP Trunk into a SIP DispatchRule
type CreateSIPParticipantRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// What SIP Trunk should be used to dial the user
	SipTrunkId string             `protobuf:"bytes,1,opt,name=sip_trunk_id,json=sipTrunkId,proto3" json:"sip_trunk_id,omitempty"`
	Trunk      *SIPOutboundConfig `protobuf:"bytes,20,opt,name=trunk,proto3" json:"trunk,omitempty"`
	// What number should be dialed via SIP
	SipCallTo string `protobuf:"bytes,2,opt,name=sip_call_to,json=sipCallTo,proto3" json:"sip_call_to,omitempty"`
	// Optional SIP From number to use. If empty, trunk number is used.
	SipNumber string `protobuf:"bytes,15,opt,name=sip_number,json=sipNumber,proto3" json:"sip_number,omitempty"`
	// What LiveKit room should this participant be connected too
	RoomName string `protobuf:"bytes,3,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`
	// Optional identity of the participant in LiveKit room
	ParticipantIdentity string `protobuf:"bytes,4,opt,name=participant_identity,json=participantIdentity,proto3" json:"participant_identity,omitempty"`
	// Optional name of the participant in LiveKit room
	ParticipantName string `protobuf:"bytes,7,opt,name=participant_name,json=participantName,proto3" json:"participant_name,omitempty"`
	// Optional user-defined metadata. Will be attached to a created Participant in the room.
	ParticipantMetadata string `protobuf:"bytes,8,opt,name=participant_metadata,json=participantMetadata,proto3" json:"participant_metadata,omitempty"`
	// Optional user-defined attributes. Will be attached to a created Participant in the room.
	ParticipantAttributes map[string]string `protobuf:"bytes,9,rep,name=participant_attributes,json=participantAttributes,proto3" json:"participant_attributes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Optionally send following DTMF digits (extension codes) when making a call.
	// Character 'w' can be used to add a 0.5 sec delay.
	Dtmf string `protobuf:"bytes,5,opt,name=dtmf,proto3" json:"dtmf,omitempty"`
	// Optionally play dialtone in the room as an audible indicator for existing participants. The `play_ringtone` option is deprectated but has the same effect.
	//
	// Deprecated: Marked as deprecated in livekit_sip.proto.
	PlayRingtone bool `protobuf:"varint,6,opt,name=play_ringtone,json=playRingtone,proto3" json:"play_ringtone,omitempty"`
	PlayDialtone bool `protobuf:"varint,13,opt,name=play_dialtone,json=playDialtone,proto3" json:"play_dialtone,omitempty"`
	// By default the From value (Phone number) is used for participant name/identity (if not set) and added to attributes.
	// If true, a random value for identity will be used and numbers will be omitted from attributes.
	HidePhoneNumber bool `protobuf:"varint,10,opt,name=hide_phone_number,json=hidePhoneNumber,proto3" json:"hide_phone_number,omitempty"`
	// These headers are sent as-is and may help identify this call as coming from LiveKit for the other SIP endpoint.
	Headers map[string]string `protobuf:"bytes,16,rep,name=headers,proto3" json:"headers,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Map SIP headers from 200 OK to sip.h.* participant attributes automatically.
	//
	// When the names of required headers is known, using headers_to_attributes is strongly recommended.
	//
	// When mapping 200 OK headers to follow-up request headers with attributes_to_headers map,
	// lowercase header names should be used, for example: sip.h.x-custom-header.
	IncludeHeaders SIPHeaderOptions `protobuf:"varint,17,opt,name=include_headers,json=includeHeaders,proto3,enum=livekit.SIPHeaderOptions" json:"include_headers,omitempty"`
	// Max time for the callee to answer the call.
	RingingTimeout *durationpb.Duration `protobuf:"bytes,11,opt,name=ringing_timeout,json=ringingTimeout,proto3" json:"ringing_timeout,omitempty"`
	// Max call duration.
	MaxCallDuration *durationpb.Duration `protobuf:"bytes,12,opt,name=max_call_duration,json=maxCallDuration,proto3" json:"max_call_duration,omitempty"`
	// Enable voice isolation for the callee.
	KrispEnabled    bool               `protobuf:"varint,14,opt,name=krisp_enabled,json=krispEnabled,proto3" json:"krisp_enabled,omitempty"`
	MediaEncryption SIPMediaEncryption `protobuf:"varint,18,opt,name=media_encryption,json=mediaEncryption,proto3,enum=livekit.SIPMediaEncryption" json:"media_encryption,omitempty"`
	// Wait for the answer for the call before returning.
	WaitUntilAnswered bool `protobuf:"varint,19,opt,name=wait_until_answered,json=waitUntilAnswered,proto3" json:"wait_until_answered,omitempty"` // NEXT ID: 21
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CreateSIPParticipantRequest) Reset() {
	*x = CreateSIPParticipantRequest{}
	mi := &file_livekit_sip_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSIPParticipantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSIPParticipantRequest) ProtoMessage() {}

func (x *CreateSIPParticipantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSIPParticipantRequest.ProtoReflect.Descriptor instead.
func (*CreateSIPParticipantRequest) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{34}
}

func (x *CreateSIPParticipantRequest) GetSipTrunkId() string {
	if x != nil {
		return x.SipTrunkId
	}
	return ""
}

func (x *CreateSIPParticipantRequest) GetTrunk() *SIPOutboundConfig {
	if x != nil {
		return x.Trunk
	}
	return nil
}

func (x *CreateSIPParticipantRequest) GetSipCallTo() string {
	if x != nil {
		return x.SipCallTo
	}
	return ""
}

func (x *CreateSIPParticipantRequest) GetSipNumber() string {
	if x != nil {
		return x.SipNumber
	}
	return ""
}

func (x *CreateSIPParticipantRequest) GetRoomName() string {
	if x != nil {
		return x.RoomName
	}
	return ""
}

func (x *CreateSIPParticipantRequest) GetParticipantIdentity() string {
	if x != nil {
		return x.ParticipantIdentity
	}
	return ""
}

func (x *CreateSIPParticipantRequest) GetParticipantName() string {
	if x != nil {
		return x.ParticipantName
	}
	return ""
}

func (x *CreateSIPParticipantRequest) GetParticipantMetadata() string {
	if x != nil {
		return x.ParticipantMetadata
	}
	return ""
}

func (x *CreateSIPParticipantRequest) GetParticipantAttributes() map[string]string {
	if x != nil {
		return x.ParticipantAttributes
	}
	return nil
}

func (x *CreateSIPParticipantRequest) GetDtmf() string {
	if x != nil {
		return x.Dtmf
	}
	return ""
}

// Deprecated: Marked as deprecated in livekit_sip.proto.
func (x *CreateSIPParticipantRequest) GetPlayRingtone() bool {
	if x != nil {
		return x.PlayRingtone
	}
	return false
}

func (x *CreateSIPParticipantRequest) GetPlayDialtone() bool {
	if x != nil {
		return x.PlayDialtone
	}
	return false
}

func (x *CreateSIPParticipantRequest) GetHidePhoneNumber() bool {
	if x != nil {
		return x.HidePhoneNumber
	}
	return false
}

func (x *CreateSIPParticipantRequest) GetHeaders() map[string]string {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *CreateSIPParticipantRequest) GetIncludeHeaders() SIPHeaderOptions {
	if x != nil {
		return x.IncludeHeaders
	}
	return SIPHeaderOptions_SIP_NO_HEADERS
}

func (x *CreateSIPParticipantRequest) GetRingingTimeout() *durationpb.Duration {
	if x != nil {
		return x.RingingTimeout
	}
	return nil
}

func (x *CreateSIPParticipantRequest) GetMaxCallDuration() *durationpb.Duration {
	if x != nil {
		return x.MaxCallDuration
	}
	return nil
}

func (x *CreateSIPParticipantRequest) GetKrispEnabled() bool {
	if x != nil {
		return x.KrispEnabled
	}
	return false
}

func (x *CreateSIPParticipantRequest) GetMediaEncryption() SIPMediaEncryption {
	if x != nil {
		return x.MediaEncryption
	}
	return SIPMediaEncryption_SIP_MEDIA_ENCRYPT_DISABLE
}

func (x *CreateSIPParticipantRequest) GetWaitUntilAnswered() bool {
	if x != nil {
		return x.WaitUntilAnswered
	}
	return false
}

type SIPParticipantInfo struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	ParticipantId       string                 `protobuf:"bytes,1,opt,name=participant_id,json=participantId,proto3" json:"participant_id,omitempty"`
	ParticipantIdentity string                 `protobuf:"bytes,2,opt,name=participant_identity,json=participantIdentity,proto3" json:"participant_identity,omitempty"`
	RoomName            string                 `protobuf:"bytes,3,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`
	SipCallId           string                 `protobuf:"bytes,4,opt,name=sip_call_id,json=sipCallId,proto3" json:"sip_call_id,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *SIPParticipantInfo) Reset() {
	*x = SIPParticipantInfo{}
	mi := &file_livekit_sip_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SIPParticipantInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SIPParticipantInfo) ProtoMessage() {}

func (x *SIPParticipantInfo) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SIPParticipantInfo.ProtoReflect.Descriptor instead.
func (*SIPParticipantInfo) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{35}
}

func (x *SIPParticipantInfo) GetParticipantId() string {
	if x != nil {
		return x.ParticipantId
	}
	return ""
}

func (x *SIPParticipantInfo) GetParticipantIdentity() string {
	if x != nil {
		return x.ParticipantIdentity
	}
	return ""
}

func (x *SIPParticipantInfo) GetRoomName() string {
	if x != nil {
		return x.RoomName
	}
	return ""
}

func (x *SIPParticipantInfo) GetSipCallId() string {
	if x != nil {
		return x.SipCallId
	}
	return ""
}

type TransferSIPParticipantRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	ParticipantIdentity string                 `protobuf:"bytes,1,opt,name=participant_identity,json=participantIdentity,proto3" json:"participant_identity,omitempty"`
	RoomName            string                 `protobuf:"bytes,2,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`
	TransferTo          string                 `protobuf:"bytes,3,opt,name=transfer_to,json=transferTo,proto3" json:"transfer_to,omitempty"`
	// Optionally play dialtone to the SIP participant as an audible indicator of being transferred
	PlayDialtone bool `protobuf:"varint,4,opt,name=play_dialtone,json=playDialtone,proto3" json:"play_dialtone,omitempty"`
	// Add the following headers to the REFER SIP request.
	Headers map[string]string `protobuf:"bytes,5,rep,name=headers,proto3" json:"headers,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Max time for the transfer destination to answer the call.
	RingingTimeout *durationpb.Duration `protobuf:"bytes,6,opt,name=ringing_timeout,json=ringingTimeout,proto3" json:"ringing_timeout,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *TransferSIPParticipantRequest) Reset() {
	*x = TransferSIPParticipantRequest{}
	mi := &file_livekit_sip_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransferSIPParticipantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransferSIPParticipantRequest) ProtoMessage() {}

func (x *TransferSIPParticipantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransferSIPParticipantRequest.ProtoReflect.Descriptor instead.
func (*TransferSIPParticipantRequest) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{36}
}

func (x *TransferSIPParticipantRequest) GetParticipantIdentity() string {
	if x != nil {
		return x.ParticipantIdentity
	}
	return ""
}

func (x *TransferSIPParticipantRequest) GetRoomName() string {
	if x != nil {
		return x.RoomName
	}
	return ""
}

func (x *TransferSIPParticipantRequest) GetTransferTo() string {
	if x != nil {
		return x.TransferTo
	}
	return ""
}

func (x *TransferSIPParticipantRequest) GetPlayDialtone() bool {
	if x != nil {
		return x.PlayDialtone
	}
	return false
}

func (x *TransferSIPParticipantRequest) GetHeaders() map[string]string {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *TransferSIPParticipantRequest) GetRingingTimeout() *durationpb.Duration {
	if x != nil {
		return x.RingingTimeout
	}
	return nil
}

type SIPCallInfo struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	CallId                string                 `protobuf:"bytes,1,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
	TrunkId               string                 `protobuf:"bytes,2,opt,name=trunk_id,json=trunkId,proto3" json:"trunk_id,omitempty"`
	DispatchRuleId        string                 `protobuf:"bytes,16,opt,name=dispatch_rule_id,json=dispatchRuleId,proto3" json:"dispatch_rule_id,omitempty"`
	Region                string                 `protobuf:"bytes,17,opt,name=region,proto3" json:"region,omitempty"`
	RoomName              string                 `protobuf:"bytes,3,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`
	RoomId                string                 `protobuf:"bytes,4,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"` // ID of the current/previous room published to
	ParticipantIdentity   string                 `protobuf:"bytes,5,opt,name=participant_identity,json=participantIdentity,proto3" json:"participant_identity,omitempty"`
	ParticipantAttributes map[string]string      `protobuf:"bytes,18,rep,name=participant_attributes,json=participantAttributes,proto3" json:"participant_attributes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	FromUri               *SIPUri                `protobuf:"bytes,6,opt,name=from_uri,json=fromUri,proto3" json:"from_uri,omitempty"`
	ToUri                 *SIPUri                `protobuf:"bytes,7,opt,name=to_uri,json=toUri,proto3" json:"to_uri,omitempty"`
	// Deprecated: Marked as deprecated in livekit_sip.proto.
	CreatedAt int64 `protobuf:"varint,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// Deprecated: Marked as deprecated in livekit_sip.proto.
	StartedAt int64 `protobuf:"varint,10,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	// Deprecated: Marked as deprecated in livekit_sip.proto.
	EndedAt          int64            `protobuf:"varint,11,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`
	EnabledFeatures  []SIPFeature     `protobuf:"varint,14,rep,packed,name=enabled_features,json=enabledFeatures,proto3,enum=livekit.SIPFeature" json:"enabled_features,omitempty"`
	CallDirection    SIPCallDirection `protobuf:"varint,15,opt,name=call_direction,json=callDirection,proto3,enum=livekit.SIPCallDirection" json:"call_direction,omitempty"`
	CallStatus       SIPCallStatus    `protobuf:"varint,8,opt,name=call_status,json=callStatus,proto3,enum=livekit.SIPCallStatus" json:"call_status,omitempty"`
	CreatedAtNs      int64            `protobuf:"varint,22,opt,name=created_at_ns,json=createdAtNs,proto3" json:"created_at_ns,omitempty"`
	StartedAtNs      int64            `protobuf:"varint,23,opt,name=started_at_ns,json=startedAtNs,proto3" json:"started_at_ns,omitempty"`
	EndedAtNs        int64            `protobuf:"varint,24,opt,name=ended_at_ns,json=endedAtNs,proto3" json:"ended_at_ns,omitempty"`
	DisconnectReason DisconnectReason `protobuf:"varint,12,opt,name=disconnect_reason,json=disconnectReason,proto3,enum=livekit.DisconnectReason" json:"disconnect_reason,omitempty"`
	Error            string           `protobuf:"bytes,13,opt,name=error,proto3" json:"error,omitempty"`
	CallStatusCode   *SIPStatus       `protobuf:"bytes,19,opt,name=call_status_code,json=callStatusCode,proto3" json:"call_status_code,omitempty"`
	AudioCodec       string           `protobuf:"bytes,20,opt,name=audio_codec,json=audioCodec,proto3" json:"audio_codec,omitempty"`
	MediaEncryption  string           `protobuf:"bytes,21,opt,name=media_encryption,json=mediaEncryption,proto3" json:"media_encryption,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *SIPCallInfo) Reset() {
	*x = SIPCallInfo{}
	mi := &file_livekit_sip_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SIPCallInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SIPCallInfo) ProtoMessage() {}

func (x *SIPCallInfo) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SIPCallInfo.ProtoReflect.Descriptor instead.
func (*SIPCallInfo) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{37}
}

func (x *SIPCallInfo) GetCallId() string {
	if x != nil {
		return x.CallId
	}
	return ""
}

func (x *SIPCallInfo) GetTrunkId() string {
	if x != nil {
		return x.TrunkId
	}
	return ""
}

func (x *SIPCallInfo) GetDispatchRuleId() string {
	if x != nil {
		return x.DispatchRuleId
	}
	return ""
}

func (x *SIPCallInfo) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *SIPCallInfo) GetRoomName() string {
	if x != nil {
		return x.RoomName
	}
	return ""
}

func (x *SIPCallInfo) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *SIPCallInfo) GetParticipantIdentity() string {
	if x != nil {
		return x.ParticipantIdentity
	}
	return ""
}

func (x *SIPCallInfo) GetParticipantAttributes() map[string]string {
	if x != nil {
		return x.ParticipantAttributes
	}
	return nil
}

func (x *SIPCallInfo) GetFromUri() *SIPUri {
	if x != nil {
		return x.FromUri
	}
	return nil
}

func (x *SIPCallInfo) GetToUri() *SIPUri {
	if x != nil {
		return x.ToUri
	}
	return nil
}

// Deprecated: Marked as deprecated in livekit_sip.proto.
func (x *SIPCallInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_sip.proto.
func (x *SIPCallInfo) GetStartedAt() int64 {
	if x != nil {
		return x.StartedAt
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_sip.proto.
func (x *SIPCallInfo) GetEndedAt() int64 {
	if x != nil {
		return x.EndedAt
	}
	return 0
}

func (x *SIPCallInfo) GetEnabledFeatures() []SIPFeature {
	if x != nil {
		return x.EnabledFeatures
	}
	return nil
}

func (x *SIPCallInfo) GetCallDirection() SIPCallDirection {
	if x != nil {
		return x.CallDirection
	}
	return SIPCallDirection_SCD_UNKNOWN
}

func (x *SIPCallInfo) GetCallStatus() SIPCallStatus {
	if x != nil {
		return x.CallStatus
	}
	return SIPCallStatus_SCS_CALL_INCOMING
}

func (x *SIPCallInfo) GetCreatedAtNs() int64 {
	if x != nil {
		return x.CreatedAtNs
	}
	return 0
}

func (x *SIPCallInfo) GetStartedAtNs() int64 {
	if x != nil {
		return x.StartedAtNs
	}
	return 0
}

func (x *SIPCallInfo) GetEndedAtNs() int64 {
	if x != nil {
		return x.EndedAtNs
	}
	return 0
}

func (x *SIPCallInfo) GetDisconnectReason() DisconnectReason {
	if x != nil {
		return x.DisconnectReason
	}
	return DisconnectReason_UNKNOWN_REASON
}

func (x *SIPCallInfo) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *SIPCallInfo) GetCallStatusCode() *SIPStatus {
	if x != nil {
		return x.CallStatusCode
	}
	return nil
}

func (x *SIPCallInfo) GetAudioCodec() string {
	if x != nil {
		return x.AudioCodec
	}
	return ""
}

func (x *SIPCallInfo) GetMediaEncryption() string {
	if x != nil {
		return x.MediaEncryption
	}
	return ""
}

type SIPTransferInfo struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	TransferId            string                 `protobuf:"bytes,1,opt,name=transfer_id,json=transferId,proto3" json:"transfer_id,omitempty"`
	CallId                string                 `protobuf:"bytes,2,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
	TransferTo            string                 `protobuf:"bytes,3,opt,name=transfer_to,json=transferTo,proto3" json:"transfer_to,omitempty"`
	TransferInitiatedAtNs int64                  `protobuf:"varint,4,opt,name=transfer_initiated_at_ns,json=transferInitiatedAtNs,proto3" json:"transfer_initiated_at_ns,omitempty"`
	TransferCompletedAtNs int64                  `protobuf:"varint,5,opt,name=transfer_completed_at_ns,json=transferCompletedAtNs,proto3" json:"transfer_completed_at_ns,omitempty"`
	TransferStatus        SIPTransferStatus      `protobuf:"varint,6,opt,name=transfer_status,json=transferStatus,proto3,enum=livekit.SIPTransferStatus" json:"transfer_status,omitempty"`
	Error                 string                 `protobuf:"bytes,7,opt,name=error,proto3" json:"error,omitempty"`
	TransferStatusCode    *SIPStatus             `protobuf:"bytes,8,opt,name=transfer_status_code,json=transferStatusCode,proto3" json:"transfer_status_code,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *SIPTransferInfo) Reset() {
	*x = SIPTransferInfo{}
	mi := &file_livekit_sip_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SIPTransferInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SIPTransferInfo) ProtoMessage() {}

func (x *SIPTransferInfo) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SIPTransferInfo.ProtoReflect.Descriptor instead.
func (*SIPTransferInfo) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{38}
}

func (x *SIPTransferInfo) GetTransferId() string {
	if x != nil {
		return x.TransferId
	}
	return ""
}

func (x *SIPTransferInfo) GetCallId() string {
	if x != nil {
		return x.CallId
	}
	return ""
}

func (x *SIPTransferInfo) GetTransferTo() string {
	if x != nil {
		return x.TransferTo
	}
	return ""
}

func (x *SIPTransferInfo) GetTransferInitiatedAtNs() int64 {
	if x != nil {
		return x.TransferInitiatedAtNs
	}
	return 0
}

func (x *SIPTransferInfo) GetTransferCompletedAtNs() int64 {
	if x != nil {
		return x.TransferCompletedAtNs
	}
	return 0
}

func (x *SIPTransferInfo) GetTransferStatus() SIPTransferStatus {
	if x != nil {
		return x.TransferStatus
	}
	return SIPTransferStatus_STS_TRANSFER_ONGOING
}

func (x *SIPTransferInfo) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *SIPTransferInfo) GetTransferStatusCode() *SIPStatus {
	if x != nil {
		return x.TransferStatusCode
	}
	return nil
}

type SIPUri struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          string                 `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Host          string                 `protobuf:"bytes,2,opt,name=host,proto3" json:"host,omitempty"`
	Ip            string                 `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`
	Port          uint32                 `protobuf:"varint,4,opt,name=port,proto3" json:"port,omitempty"`
	Transport     SIPTransport           `protobuf:"varint,5,opt,name=transport,proto3,enum=livekit.SIPTransport" json:"transport,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SIPUri) Reset() {
	*x = SIPUri{}
	mi := &file_livekit_sip_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SIPUri) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SIPUri) ProtoMessage() {}

func (x *SIPUri) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_sip_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SIPUri.ProtoReflect.Descriptor instead.
func (*SIPUri) Descriptor() ([]byte, []int) {
	return file_livekit_sip_proto_rawDescGZIP(), []int{39}
}

func (x *SIPUri) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *SIPUri) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *SIPUri) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *SIPUri) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *SIPUri) GetTransport() SIPTransport {
	if x != nil {
		return x.Transport
	}
	return SIPTransport_SIP_TRANSPORT_AUTO
}

var File_livekit_sip_proto protoreflect.FileDescriptor

const file_livekit_sip_proto_rawDesc = "" +
	"\n" +
	"\x11livekit_sip.proto\x12\alivekit\x1a\x1egoogle/protobuf/duration.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x14livekit_models.proto\x1a\x12livekit_room.proto\"O\n" +
	"\tSIPStatus\x12*\n" +
	"\x04code\x18\x01 \x01(\x0e2\x16.livekit.SIPStatusCodeR\x04code\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\"\xdd\x03\n" +
	"\x15CreateSIPTrunkRequest\x12+\n" +
	"\x11inbound_addresses\x18\x01 \x03(\tR\x10inboundAddresses\x12)\n" +
	"\x10outbound_address\x18\x02 \x01(\tR\x0foutboundAddress\x12'\n" +
	"\x0foutbound_number\x18\x03 \x01(\tR\x0eoutboundNumber\x126\n" +
	"\x15inbound_numbers_regex\x18\x04 \x03(\tB\x02\x18\x01R\x13inboundNumbersRegex\x12'\n" +
	"\x0finbound_numbers\x18\t \x03(\tR\x0einboundNumbers\x12)\n" +
	"\x10inbound_username\x18\x05 \x01(\tR\x0finboundUsername\x12)\n" +
	"\x10inbound_password\x18\x06 \x01(\tR\x0finboundPassword\x12+\n" +
	"\x11outbound_username\x18\a \x01(\tR\x10outboundUsername\x12+\n" +
	"\x11outbound_password\x18\b \x01(\tR\x10outboundPassword\x12\x12\n" +
	"\x04name\x18\n" +
	" \x01(\tR\x04name\x12\x1a\n" +
	"\bmetadata\x18\v \x01(\tR\bmetadata:\x02\x18\x01\"\xa6\x05\n" +
	"\fSIPTrunkInfo\x12 \n" +
	"\fsip_trunk_id\x18\x01 \x01(\tR\n" +
	"sipTrunkId\x123\n" +
	"\x04kind\x18\x0e \x01(\x0e2\x1f.livekit.SIPTrunkInfo.TrunkKindR\x04kind\x12+\n" +
	"\x11inbound_addresses\x18\x02 \x03(\tR\x10inboundAddresses\x12)\n" +
	"\x10outbound_address\x18\x03 \x01(\tR\x0foutboundAddress\x12'\n" +
	"\x0foutbound_number\x18\x04 \x01(\tR\x0eoutboundNumber\x123\n" +
	"\ttransport\x18\r \x01(\x0e2\x15.livekit.SIPTransportR\ttransport\x126\n" +
	"\x15inbound_numbers_regex\x18\x05 \x03(\tB\x02\x18\x01R\x13inboundNumbersRegex\x12'\n" +
	"\x0finbound_numbers\x18\n" +
	" \x03(\tR\x0einboundNumbers\x12)\n" +
	"\x10inbound_username\x18\x06 \x01(\tR\x0finboundUsername\x12)\n" +
	"\x10inbound_password\x18\a \x01(\tR\x0finboundPassword\x12+\n" +
	"\x11outbound_username\x18\b \x01(\tR\x10outboundUsername\x12+\n" +
	"\x11outbound_password\x18\t \x01(\tR\x10outboundPassword\x12\x12\n" +
	"\x04name\x18\v \x01(\tR\x04name\x12\x1a\n" +
	"\bmetadata\x18\f \x01(\tR\bmetadata\"D\n" +
	"\tTrunkKind\x12\x10\n" +
	"\fTRUNK_LEGACY\x10\x00\x12\x11\n" +
	"\rTRUNK_INBOUND\x10\x01\x12\x12\n" +
	"\x0eTRUNK_OUTBOUND\x10\x02:\x02\x18\x01\"R\n" +
	"\x1cCreateSIPInboundTrunkRequest\x122\n" +
	"\x05trunk\x18\x01 \x01(\v2\x1c.livekit.SIPInboundTrunkInfoR\x05trunk\"\xbe\x01\n" +
	"\x1cUpdateSIPInboundTrunkRequest\x12 \n" +
	"\fsip_trunk_id\x18\x01 \x01(\tR\n" +
	"sipTrunkId\x128\n" +
	"\areplace\x18\x02 \x01(\v2\x1c.livekit.SIPInboundTrunkInfoH\x00R\areplace\x128\n" +
	"\x06update\x18\x03 \x01(\v2\x1e.livekit.SIPInboundTrunkUpdateH\x00R\x06updateB\b\n" +
	"\x06action\"\xc4\b\n" +
	"\x13SIPInboundTrunkInfo\x12 \n" +
	"\fsip_trunk_id\x18\x01 \x01(\tR\n" +
	"sipTrunkId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1a\n" +
	"\bmetadata\x18\x03 \x01(\tR\bmetadata\x12\x18\n" +
	"\anumbers\x18\x04 \x03(\tR\anumbers\x12+\n" +
	"\x11allowed_addresses\x18\x05 \x03(\tR\x10allowedAddresses\x12'\n" +
	"\x0fallowed_numbers\x18\x06 \x03(\tR\x0eallowedNumbers\x12#\n" +
	"\rauth_username\x18\a \x01(\tR\fauthUsername\x12#\n" +
	"\rauth_password\x18\b \x01(\tR\fauthPassword\x12C\n" +
	"\aheaders\x18\t \x03(\v2).livekit.SIPInboundTrunkInfo.HeadersEntryR\aheaders\x12i\n" +
	"\x15headers_to_attributes\x18\n" +
	" \x03(\v25.livekit.SIPInboundTrunkInfo.HeadersToAttributesEntryR\x13headersToAttributes\x12i\n" +
	"\x15attributes_to_headers\x18\x0e \x03(\v25.livekit.SIPInboundTrunkInfo.AttributesToHeadersEntryR\x13attributesToHeaders\x12B\n" +
	"\x0finclude_headers\x18\x0f \x01(\x0e2\x19.livekit.SIPHeaderOptionsR\x0eincludeHeaders\x12B\n" +
	"\x0fringing_timeout\x18\v \x01(\v2\x19.google.protobuf.DurationR\x0eringingTimeout\x12E\n" +
	"\x11max_call_duration\x18\f \x01(\v2\x19.google.protobuf.DurationR\x0fmaxCallDuration\x12#\n" +
	"\rkrisp_enabled\x18\r \x01(\bR\fkrispEnabled\x12F\n" +
	"\x10media_encryption\x18\x10 \x01(\x0e2\x1b.livekit.SIPMediaEncryptionR\x0fmediaEncryption\x1a:\n" +
	"\fHeadersEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\x1aF\n" +
	"\x18HeadersToAttributesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\x1aF\n" +
	"\x18AttributesToHeadersEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xf0\x03\n" +
	"\x15SIPInboundTrunkUpdate\x12-\n" +
	"\anumbers\x18\x01 \x01(\v2\x13.livekit.ListUpdateR\anumbers\x12@\n" +
	"\x11allowed_addresses\x18\x02 \x01(\v2\x13.livekit.ListUpdateR\x10allowedAddresses\x12<\n" +
	"\x0fallowed_numbers\x18\x03 \x01(\v2\x13.livekit.ListUpdateR\x0eallowedNumbers\x12(\n" +
	"\rauth_username\x18\x04 \x01(\tH\x00R\fauthUsername\x88\x01\x01\x12(\n" +
	"\rauth_password\x18\x05 \x01(\tH\x01R\fauthPassword\x88\x01\x01\x12\x17\n" +
	"\x04name\x18\x06 \x01(\tH\x02R\x04name\x88\x01\x01\x12\x1f\n" +
	"\bmetadata\x18\a \x01(\tH\x03R\bmetadata\x88\x01\x01\x12K\n" +
	"\x10media_encryption\x18\b \x01(\x0e2\x1b.livekit.SIPMediaEncryptionH\x04R\x0fmediaEncryption\x88\x01\x01B\x10\n" +
	"\x0e_auth_usernameB\x10\n" +
	"\x0e_auth_passwordB\a\n" +
	"\x05_nameB\v\n" +
	"\t_metadataB\x13\n" +
	"\x11_media_encryption\"T\n" +
	"\x1dCreateSIPOutboundTrunkRequest\x123\n" +
	"\x05trunk\x18\x01 \x01(\v2\x1d.livekit.SIPOutboundTrunkInfoR\x05trunk\"\xc1\x01\n" +
	"\x1dUpdateSIPOutboundTrunkRequest\x12 \n" +
	"\fsip_trunk_id\x18\x01 \x01(\tR\n" +
	"sipTrunkId\x129\n" +
	"\areplace\x18\x02 \x01(\v2\x1d.livekit.SIPOutboundTrunkInfoH\x00R\areplace\x129\n" +
	"\x06update\x18\x03 \x01(\v2\x1f.livekit.SIPOutboundTrunkUpdateH\x00R\x06updateB\b\n" +
	"\x06action\"\xc2\a\n" +
	"\x14SIPOutboundTrunkInfo\x12 \n" +
	"\fsip_trunk_id\x18\x01 \x01(\tR\n" +
	"sipTrunkId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1a\n" +
	"\bmetadata\x18\x03 \x01(\tR\bmetadata\x12\x18\n" +
	"\aaddress\x18\x04 \x01(\tR\aaddress\x12/\n" +
	"\x13destination_country\x18\x0e \x01(\tR\x12destinationCountry\x123\n" +
	"\ttransport\x18\x05 \x01(\x0e2\x15.livekit.SIPTransportR\ttransport\x12\x18\n" +
	"\anumbers\x18\x06 \x03(\tR\anumbers\x12#\n" +
	"\rauth_username\x18\a \x01(\tR\fauthUsername\x12#\n" +
	"\rauth_password\x18\b \x01(\tR\fauthPassword\x12D\n" +
	"\aheaders\x18\t \x03(\v2*.livekit.SIPOutboundTrunkInfo.HeadersEntryR\aheaders\x12j\n" +
	"\x15headers_to_attributes\x18\n" +
	" \x03(\v26.livekit.SIPOutboundTrunkInfo.HeadersToAttributesEntryR\x13headersToAttributes\x12j\n" +
	"\x15attributes_to_headers\x18\v \x03(\v26.livekit.SIPOutboundTrunkInfo.AttributesToHeadersEntryR\x13attributesToHeaders\x12B\n" +
	"\x0finclude_headers\x18\f \x01(\x0e2\x19.livekit.SIPHeaderOptionsR\x0eincludeHeaders\x12F\n" +
	"\x10media_encryption\x18\r \x01(\x0e2\x1b.livekit.SIPMediaEncryptionR\x0fmediaEncryption\x1a:\n" +
	"\fHeadersEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\x1aF\n" +
	"\x18HeadersToAttributesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\x1aF\n" +
	"\x18AttributesToHeadersEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xb2\x04\n" +
	"\x16SIPOutboundTrunkUpdate\x12\x1d\n" +
	"\aaddress\x18\x01 \x01(\tH\x00R\aaddress\x88\x01\x01\x128\n" +
	"\ttransport\x18\x02 \x01(\x0e2\x15.livekit.SIPTransportH\x01R\ttransport\x88\x01\x01\x124\n" +
	"\x13destination_country\x18\t \x01(\tH\x02R\x12destinationCountry\x88\x01\x01\x12-\n" +
	"\anumbers\x18\x03 \x01(\v2\x13.livekit.ListUpdateR\anumbers\x12(\n" +
	"\rauth_username\x18\x04 \x01(\tH\x03R\fauthUsername\x88\x01\x01\x12(\n" +
	"\rauth_password\x18\x05 \x01(\tH\x04R\fauthPassword\x88\x01\x01\x12\x17\n" +
	"\x04name\x18\x06 \x01(\tH\x05R\x04name\x88\x01\x01\x12\x1f\n" +
	"\bmetadata\x18\a \x01(\tH\x06R\bmetadata\x88\x01\x01\x12K\n" +
	"\x10media_encryption\x18\b \x01(\x0e2\x1b.livekit.SIPMediaEncryptionH\aR\x0fmediaEncryption\x88\x01\x01B\n" +
	"\n" +
	"\b_addressB\f\n" +
	"\n" +
	"_transportB\x16\n" +
	"\x14_destination_countryB\x10\n" +
	"\x0e_auth_usernameB\x10\n" +
	"\x0e_auth_passwordB\a\n" +
	"\x05_nameB\v\n" +
	"\t_metadataB\x13\n" +
	"\x11_media_encryption\"=\n" +
	"\x19GetSIPInboundTrunkRequest\x12 \n" +
	"\fsip_trunk_id\x18\x01 \x01(\tR\n" +
	"sipTrunkId\"P\n" +
	"\x1aGetSIPInboundTrunkResponse\x122\n" +
	"\x05trunk\x18\x01 \x01(\v2\x1c.livekit.SIPInboundTrunkInfoR\x05trunk\">\n" +
	"\x1aGetSIPOutboundTrunkRequest\x12 \n" +
	"\fsip_trunk_id\x18\x01 \x01(\tR\n" +
	"sipTrunkId\"R\n" +
	"\x1bGetSIPOutboundTrunkResponse\x123\n" +
	"\x05trunk\x18\x01 \x01(\v2\x1d.livekit.SIPOutboundTrunkInfoR\x05trunk\"B\n" +
	"\x13ListSIPTrunkRequest\x12'\n" +
	"\x04page\x18\x01 \x01(\v2\x13.livekit.PaginationR\x04page:\x02\x18\x01\"G\n" +
	"\x14ListSIPTrunkResponse\x12+\n" +
	"\x05items\x18\x01 \x03(\v2\x15.livekit.SIPTrunkInfoR\x05items:\x02\x18\x01\"|\n" +
	"\x1aListSIPInboundTrunkRequest\x12'\n" +
	"\x04page\x18\x03 \x01(\v2\x13.livekit.PaginationR\x04page\x12\x1b\n" +
	"\ttrunk_ids\x18\x01 \x03(\tR\btrunkIds\x12\x18\n" +
	"\anumbers\x18\x02 \x03(\tR\anumbers\"Q\n" +
	"\x1bListSIPInboundTrunkResponse\x122\n" +
	"\x05items\x18\x01 \x03(\v2\x1c.livekit.SIPInboundTrunkInfoR\x05items\"}\n" +
	"\x1bListSIPOutboundTrunkRequest\x12'\n" +
	"\x04page\x18\x03 \x01(\v2\x13.livekit.PaginationR\x04page\x12\x1b\n" +
	"\ttrunk_ids\x18\x01 \x03(\tR\btrunkIds\x12\x18\n" +
	"\anumbers\x18\x02 \x03(\tR\anumbers\"S\n" +
	"\x1cListSIPOutboundTrunkResponse\x123\n" +
	"\x05items\x18\x01 \x03(\v2\x1d.livekit.SIPOutboundTrunkInfoR\x05items\"9\n" +
	"\x15DeleteSIPTrunkRequest\x12 \n" +
	"\fsip_trunk_id\x18\x01 \x01(\tR\n" +
	"sipTrunkId\"F\n" +
	"\x15SIPDispatchRuleDirect\x12\x1b\n" +
	"\troom_name\x18\x01 \x01(\tR\broomName\x12\x10\n" +
	"\x03pin\x18\x02 \x01(\tR\x03pin\"N\n" +
	"\x19SIPDispatchRuleIndividual\x12\x1f\n" +
	"\vroom_prefix\x18\x01 \x01(\tR\n" +
	"roomPrefix\x12\x10\n" +
	"\x03pin\x18\x02 \x01(\tR\x03pin\"h\n" +
	"\x15SIPDispatchRuleCallee\x12\x1f\n" +
	"\vroom_prefix\x18\x01 \x01(\tR\n" +
	"roomPrefix\x12\x10\n" +
	"\x03pin\x18\x02 \x01(\tR\x03pin\x12\x1c\n" +
	"\trandomize\x18\x03 \x01(\bR\trandomize\"\xa1\x02\n" +
	"\x0fSIPDispatchRule\x12R\n" +
	"\x14dispatch_rule_direct\x18\x01 \x01(\v2\x1e.livekit.SIPDispatchRuleDirectH\x00R\x12dispatchRuleDirect\x12^\n" +
	"\x18dispatch_rule_individual\x18\x02 \x01(\v2\".livekit.SIPDispatchRuleIndividualH\x00R\x16dispatchRuleIndividual\x12R\n" +
	"\x14dispatch_rule_callee\x18\x03 \x01(\v2\x1e.livekit.SIPDispatchRuleCalleeH\x00R\x12dispatchRuleCalleeB\x06\n" +
	"\x04rule\"\xc9\x04\n" +
	"\x1cCreateSIPDispatchRuleRequest\x12A\n" +
	"\rdispatch_rule\x18\n" +
	" \x01(\v2\x1c.livekit.SIPDispatchRuleInfoR\fdispatchRule\x120\n" +
	"\x04rule\x18\x01 \x01(\v2\x18.livekit.SIPDispatchRuleB\x02\x18\x01R\x04rule\x12\x1f\n" +
	"\ttrunk_ids\x18\x02 \x03(\tB\x02\x18\x01R\btrunkIds\x12.\n" +
	"\x11hide_phone_number\x18\x03 \x01(\bB\x02\x18\x01R\x0fhidePhoneNumber\x12+\n" +
	"\x0finbound_numbers\x18\x06 \x03(\tB\x02\x18\x01R\x0einboundNumbers\x12\x16\n" +
	"\x04name\x18\x04 \x01(\tB\x02\x18\x01R\x04name\x12\x1e\n" +
	"\bmetadata\x18\x05 \x01(\tB\x02\x18\x01R\bmetadata\x12Y\n" +
	"\n" +
	"attributes\x18\a \x03(\v25.livekit.CreateSIPDispatchRuleRequest.AttributesEntryB\x02\x18\x01R\n" +
	"attributes\x12#\n" +
	"\vroom_preset\x18\b \x01(\tB\x02\x18\x01R\n" +
	"roomPreset\x12?\n" +
	"\vroom_config\x18\t \x01(\v2\x1a.livekit.RoomConfigurationB\x02\x18\x01R\n" +
	"roomConfig\x1a=\n" +
	"\x0fAttributesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xcd\x01\n" +
	"\x1cUpdateSIPDispatchRuleRequest\x12/\n" +
	"\x14sip_dispatch_rule_id\x18\x01 \x01(\tR\x11sipDispatchRuleId\x128\n" +
	"\areplace\x18\x02 \x01(\v2\x1c.livekit.SIPDispatchRuleInfoH\x00R\areplace\x128\n" +
	"\x06update\x18\x03 \x01(\v2\x1e.livekit.SIPDispatchRuleUpdateH\x00R\x06updateB\b\n" +
	"\x06action\"\xee\x04\n" +
	"\x13SIPDispatchRuleInfo\x12/\n" +
	"\x14sip_dispatch_rule_id\x18\x01 \x01(\tR\x11sipDispatchRuleId\x12,\n" +
	"\x04rule\x18\x02 \x01(\v2\x18.livekit.SIPDispatchRuleR\x04rule\x12\x1b\n" +
	"\ttrunk_ids\x18\x03 \x03(\tR\btrunkIds\x12*\n" +
	"\x11hide_phone_number\x18\x04 \x01(\bR\x0fhidePhoneNumber\x12'\n" +
	"\x0finbound_numbers\x18\a \x03(\tR\x0einboundNumbers\x12\x12\n" +
	"\x04name\x18\x05 \x01(\tR\x04name\x12\x1a\n" +
	"\bmetadata\x18\x06 \x01(\tR\bmetadata\x12L\n" +
	"\n" +
	"attributes\x18\b \x03(\v2,.livekit.SIPDispatchRuleInfo.AttributesEntryR\n" +
	"attributes\x12\x1f\n" +
	"\vroom_preset\x18\t \x01(\tR\n" +
	"roomPreset\x12;\n" +
	"\vroom_config\x18\n" +
	" \x01(\v2\x1a.livekit.RoomConfigurationR\n" +
	"roomConfig\x12#\n" +
	"\rkrisp_enabled\x18\v \x01(\bR\fkrispEnabled\x12F\n" +
	"\x10media_encryption\x18\f \x01(\x0e2\x1b.livekit.SIPMediaEncryptionR\x0fmediaEncryption\x1a=\n" +
	"\x0fAttributesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xb8\x03\n" +
	"\x15SIPDispatchRuleUpdate\x120\n" +
	"\ttrunk_ids\x18\x01 \x01(\v2\x13.livekit.ListUpdateR\btrunkIds\x12,\n" +
	"\x04rule\x18\x02 \x01(\v2\x18.livekit.SIPDispatchRuleR\x04rule\x12\x17\n" +
	"\x04name\x18\x03 \x01(\tH\x00R\x04name\x88\x01\x01\x12\x1f\n" +
	"\bmetadata\x18\x04 \x01(\tH\x01R\bmetadata\x88\x01\x01\x12N\n" +
	"\n" +
	"attributes\x18\x05 \x03(\v2..livekit.SIPDispatchRuleUpdate.AttributesEntryR\n" +
	"attributes\x12K\n" +
	"\x10media_encryption\x18\x06 \x01(\x0e2\x1b.livekit.SIPMediaEncryptionH\x02R\x0fmediaEncryption\x88\x01\x01\x1a=\n" +
	"\x0fAttributesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01B\a\n" +
	"\x05_nameB\v\n" +
	"\t_metadataB\x13\n" +
	"\x11_media_encryption\"\x8e\x01\n" +
	"\x1aListSIPDispatchRuleRequest\x12'\n" +
	"\x04page\x18\x03 \x01(\v2\x13.livekit.PaginationR\x04page\x12*\n" +
	"\x11dispatch_rule_ids\x18\x01 \x03(\tR\x0fdispatchRuleIds\x12\x1b\n" +
	"\ttrunk_ids\x18\x02 \x03(\tR\btrunkIds\"Q\n" +
	"\x1bListSIPDispatchRuleResponse\x122\n" +
	"\x05items\x18\x01 \x03(\v2\x1c.livekit.SIPDispatchRuleInfoR\x05items\"O\n" +
	"\x1cDeleteSIPDispatchRuleRequest\x12/\n" +
	"\x14sip_dispatch_rule_id\x18\x01 \x01(\tR\x11sipDispatchRuleId\"\xc1\x04\n" +
	"\x11SIPOutboundConfig\x12\x1a\n" +
	"\bhostname\x18\x01 \x01(\tR\bhostname\x12/\n" +
	"\x13destination_country\x18\a \x01(\tR\x12destinationCountry\x123\n" +
	"\ttransport\x18\x02 \x01(\x0e2\x15.livekit.SIPTransportR\ttransport\x12#\n" +
	"\rauth_username\x18\x03 \x01(\tR\fauthUsername\x12#\n" +
	"\rauth_password\x18\x04 \x01(\tR\fauthPassword\x12g\n" +
	"\x15headers_to_attributes\x18\x05 \x03(\v23.livekit.SIPOutboundConfig.HeadersToAttributesEntryR\x13headersToAttributes\x12g\n" +
	"\x15attributes_to_headers\x18\x06 \x03(\v23.livekit.SIPOutboundConfig.AttributesToHeadersEntryR\x13attributesToHeaders\x1aF\n" +
	"\x18HeadersToAttributesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\x1aF\n" +
	"\x18AttributesToHeadersEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xa3\t\n" +
	"\x1bCreateSIPParticipantRequest\x12 \n" +
	"\fsip_trunk_id\x18\x01 \x01(\tR\n" +
	"sipTrunkId\x120\n" +
	"\x05trunk\x18\x14 \x01(\v2\x1a.livekit.SIPOutboundConfigR\x05trunk\x12\x1e\n" +
	"\vsip_call_to\x18\x02 \x01(\tR\tsipCallTo\x12\x1d\n" +
	"\n" +
	"sip_number\x18\x0f \x01(\tR\tsipNumber\x12\x1b\n" +
	"\troom_name\x18\x03 \x01(\tR\broomName\x121\n" +
	"\x14participant_identity\x18\x04 \x01(\tR\x13participantIdentity\x12)\n" +
	"\x10participant_name\x18\a \x01(\tR\x0fparticipantName\x121\n" +
	"\x14participant_metadata\x18\b \x01(\tR\x13participantMetadata\x12v\n" +
	"\x16participant_attributes\x18\t \x03(\v2?.livekit.CreateSIPParticipantRequest.ParticipantAttributesEntryR\x15participantAttributes\x12\x12\n" +
	"\x04dtmf\x18\x05 \x01(\tR\x04dtmf\x12'\n" +
	"\rplay_ringtone\x18\x06 \x01(\bB\x02\x18\x01R\fplayRingtone\x12#\n" +
	"\rplay_dialtone\x18\r \x01(\bR\fplayDialtone\x12*\n" +
	"\x11hide_phone_number\x18\n" +
	" \x01(\bR\x0fhidePhoneNumber\x12K\n" +
	"\aheaders\x18\x10 \x03(\v21.livekit.CreateSIPParticipantRequest.HeadersEntryR\aheaders\x12B\n" +
	"\x0finclude_headers\x18\x11 \x01(\x0e2\x19.livekit.SIPHeaderOptionsR\x0eincludeHeaders\x12B\n" +
	"\x0fringing_timeout\x18\v \x01(\v2\x19.google.protobuf.DurationR\x0eringingTimeout\x12E\n" +
	"\x11max_call_duration\x18\f \x01(\v2\x19.google.protobuf.DurationR\x0fmaxCallDuration\x12#\n" +
	"\rkrisp_enabled\x18\x0e \x01(\bR\fkrispEnabled\x12F\n" +
	"\x10media_encryption\x18\x12 \x01(\x0e2\x1b.livekit.SIPMediaEncryptionR\x0fmediaEncryption\x12.\n" +
	"\x13wait_until_answered\x18\x13 \x01(\bR\x11waitUntilAnswered\x1aH\n" +
	"\x1aParticipantAttributesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\x1a:\n" +
	"\fHeadersEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xab\x01\n" +
	"\x12SIPParticipantInfo\x12%\n" +
	"\x0eparticipant_id\x18\x01 \x01(\tR\rparticipantId\x121\n" +
	"\x14participant_identity\x18\x02 \x01(\tR\x13participantIdentity\x12\x1b\n" +
	"\troom_name\x18\x03 \x01(\tR\broomName\x12\x1e\n" +
	"\vsip_call_id\x18\x04 \x01(\tR\tsipCallId\"\x84\x03\n" +
	"\x1dTransferSIPParticipantRequest\x121\n" +
	"\x14participant_identity\x18\x01 \x01(\tR\x13participantIdentity\x12\x1b\n" +
	"\troom_name\x18\x02 \x01(\tR\broomName\x12\x1f\n" +
	"\vtransfer_to\x18\x03 \x01(\tR\n" +
	"transferTo\x12#\n" +
	"\rplay_dialtone\x18\x04 \x01(\bR\fplayDialtone\x12M\n" +
	"\aheaders\x18\x05 \x03(\v23.livekit.TransferSIPParticipantRequest.HeadersEntryR\aheaders\x12B\n" +
	"\x0fringing_timeout\x18\x06 \x01(\v2\x19.google.protobuf.DurationR\x0eringingTimeout\x1a:\n" +
	"\fHeadersEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xe2\b\n" +
	"\vSIPCallInfo\x12\x17\n" +
	"\acall_id\x18\x01 \x01(\tR\x06callId\x12\x19\n" +
	"\btrunk_id\x18\x02 \x01(\tR\atrunkId\x12(\n" +
	"\x10dispatch_rule_id\x18\x10 \x01(\tR\x0edispatchRuleId\x12\x16\n" +
	"\x06region\x18\x11 \x01(\tR\x06region\x12\x1b\n" +
	"\troom_name\x18\x03 \x01(\tR\broomName\x12\x17\n" +
	"\aroom_id\x18\x04 \x01(\tR\x06roomId\x121\n" +
	"\x14participant_identity\x18\x05 \x01(\tR\x13participantIdentity\x12f\n" +
	"\x16participant_attributes\x18\x12 \x03(\v2/.livekit.SIPCallInfo.ParticipantAttributesEntryR\x15participantAttributes\x12*\n" +
	"\bfrom_uri\x18\x06 \x01(\v2\x0f.livekit.SIPUriR\afromUri\x12&\n" +
	"\x06to_uri\x18\a \x01(\v2\x0f.livekit.SIPUriR\x05toUri\x12!\n" +
	"\n" +
	"created_at\x18\t \x01(\x03B\x02\x18\x01R\tcreatedAt\x12!\n" +
	"\n" +
	"started_at\x18\n" +
	" \x01(\x03B\x02\x18\x01R\tstartedAt\x12\x1d\n" +
	"\bended_at\x18\v \x01(\x03B\x02\x18\x01R\aendedAt\x12>\n" +
	"\x10enabled_features\x18\x0e \x03(\x0e2\x13.livekit.SIPFeatureR\x0fenabledFeatures\x12@\n" +
	"\x0ecall_direction\x18\x0f \x01(\x0e2\x19.livekit.SIPCallDirectionR\rcallDirection\x127\n" +
	"\vcall_status\x18\b \x01(\x0e2\x16.livekit.SIPCallStatusR\n" +
	"callStatus\x12\"\n" +
	"\rcreated_at_ns\x18\x16 \x01(\x03R\vcreatedAtNs\x12\"\n" +
	"\rstarted_at_ns\x18\x17 \x01(\x03R\vstartedAtNs\x12\x1e\n" +
	"\vended_at_ns\x18\x18 \x01(\x03R\tendedAtNs\x12F\n" +
	"\x11disconnect_reason\x18\f \x01(\x0e2\x19.livekit.DisconnectReasonR\x10disconnectReason\x12\x14\n" +
	"\x05error\x18\r \x01(\tR\x05error\x12<\n" +
	"\x10call_status_code\x18\x13 \x01(\v2\x12.livekit.SIPStatusR\x0ecallStatusCode\x12\x1f\n" +
	"\vaudio_codec\x18\x14 \x01(\tR\n" +
	"audioCodec\x12)\n" +
	"\x10media_encryption\x18\x15 \x01(\tR\x0fmediaEncryption\x1aH\n" +
	"\x1aParticipantAttributesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xff\x02\n" +
	"\x0fSIPTransferInfo\x12\x1f\n" +
	"\vtransfer_id\x18\x01 \x01(\tR\n" +
	"transferId\x12\x17\n" +
	"\acall_id\x18\x02 \x01(\tR\x06callId\x12\x1f\n" +
	"\vtransfer_to\x18\x03 \x01(\tR\n" +
	"transferTo\x127\n" +
	"\x18transfer_initiated_at_ns\x18\x04 \x01(\x03R\x15transferInitiatedAtNs\x127\n" +
	"\x18transfer_completed_at_ns\x18\x05 \x01(\x03R\x15transferCompletedAtNs\x12C\n" +
	"\x0ftransfer_status\x18\x06 \x01(\x0e2\x1a.livekit.SIPTransferStatusR\x0etransferStatus\x12\x14\n" +
	"\x05error\x18\a \x01(\tR\x05error\x12D\n" +
	"\x14transfer_status_code\x18\b \x01(\v2\x12.livekit.SIPStatusR\x12transferStatusCode\"\x89\x01\n" +
	"\x06SIPUri\x12\x12\n" +
	"\x04user\x18\x01 \x01(\tR\x04user\x12\x12\n" +
	"\x04host\x18\x02 \x01(\tR\x04host\x12\x0e\n" +
	"\x02ip\x18\x03 \x01(\tR\x02ip\x12\x12\n" +
	"\x04port\x18\x04 \x01(\rR\x04port\x123\n" +
	"\ttransport\x18\x05 \x01(\x0e2\x15.livekit.SIPTransportR\ttransport*\xef\f\n" +
	"\rSIPStatusCode\x12\x16\n" +
	"\x12SIP_STATUS_UNKNOWN\x10\x00\x12\x15\n" +
	"\x11SIP_STATUS_TRYING\x10d\x12\x17\n" +
	"\x12SIP_STATUS_RINGING\x10\xb4\x01\x12!\n" +
	"\x1cSIP_STATUS_CALL_IS_FORWARDED\x10\xb5\x01\x12\x16\n" +
	"\x11SIP_STATUS_QUEUED\x10\xb6\x01\x12 \n" +
	"\x1bSIP_STATUS_SESSION_PROGRESS\x10\xb7\x01\x12\x12\n" +
	"\rSIP_STATUS_OK\x10\xc8\x01\x12\x18\n" +
	"\x13SIP_STATUS_ACCEPTED\x10\xca\x01\x12!\n" +
	"\x1cSIP_STATUS_MOVED_PERMANENTLY\x10\xad\x02\x12!\n" +
	"\x1cSIP_STATUS_MOVED_TEMPORARILY\x10\xae\x02\x12\x19\n" +
	"\x14SIP_STATUS_USE_PROXY\x10\xb1\x02\x12\x1b\n" +
	"\x16SIP_STATUS_BAD_REQUEST\x10\x90\x03\x12\x1c\n" +
	"\x17SIP_STATUS_UNAUTHORIZED\x10\x91\x03\x12 \n" +
	"\x1bSIP_STATUS_PAYMENT_REQUIRED\x10\x92\x03\x12\x19\n" +
	"\x14SIP_STATUS_FORBIDDEN\x10\x93\x03\x12\x18\n" +
	"\x13SIP_STATUS_NOTFOUND\x10\x94\x03\x12\"\n" +
	"\x1dSIP_STATUS_METHOD_NOT_ALLOWED\x10\x95\x03\x12\x1e\n" +
	"\x19SIP_STATUS_NOT_ACCEPTABLE\x10\x96\x03\x12#\n" +
	"\x1eSIP_STATUS_PROXY_AUTH_REQUIRED\x10\x97\x03\x12\x1f\n" +
	"\x1aSIP_STATUS_REQUEST_TIMEOUT\x10\x98\x03\x12\x18\n" +
	"\x13SIP_STATUS_CONFLICT\x10\x99\x03\x12\x14\n" +
	"\x0fSIP_STATUS_GONE\x10\x9a\x03\x12(\n" +
	"#SIP_STATUS_REQUEST_ENTITY_TOO_LARGE\x10\x9d\x03\x12$\n" +
	"\x1fSIP_STATUS_REQUEST_URI_TOO_LONG\x10\x9e\x03\x12&\n" +
	"!SIP_STATUS_UNSUPPORTED_MEDIA_TYPE\x10\x9f\x03\x12/\n" +
	"*SIP_STATUS_REQUESTED_RANGE_NOT_SATISFIABLE\x10\xa0\x03\x12\x1d\n" +
	"\x18SIP_STATUS_BAD_EXTENSION\x10\xa4\x03\x12\"\n" +
	"\x1dSIP_STATUS_EXTENSION_REQUIRED\x10\xa5\x03\x12\"\n" +
	"\x1dSIP_STATUS_INTERVAL_TOO_BRIEF\x10\xa7\x03\x12'\n" +
	"\"SIP_STATUS_TEMPORARILY_UNAVAILABLE\x10\xe0\x03\x120\n" +
	"+SIP_STATUS_CALL_TRANSACTION_DOES_NOT_EXISTS\x10\xe1\x03\x12\x1d\n" +
	"\x18SIP_STATUS_LOOP_DETECTED\x10\xe2\x03\x12\x1d\n" +
	"\x18SIP_STATUS_TOO_MANY_HOPS\x10\xe3\x03\x12\"\n" +
	"\x1dSIP_STATUS_ADDRESS_INCOMPLETE\x10\xe4\x03\x12\x19\n" +
	"\x14SIP_STATUS_AMBIGUOUS\x10\xe5\x03\x12\x19\n" +
	"\x14SIP_STATUS_BUSY_HERE\x10\xe6\x03\x12\"\n" +
	"\x1dSIP_STATUS_REQUEST_TERMINATED\x10\xe7\x03\x12#\n" +
	"\x1eSIP_STATUS_NOT_ACCEPTABLE_HERE\x10\xe8\x03\x12%\n" +
	" SIP_STATUS_INTERNAL_SERVER_ERROR\x10\xf4\x03\x12\x1f\n" +
	"\x1aSIP_STATUS_NOT_IMPLEMENTED\x10\xf5\x03\x12\x1b\n" +
	"\x16SIP_STATUS_BAD_GATEWAY\x10\xf6\x03\x12#\n" +
	"\x1eSIP_STATUS_SERVICE_UNAVAILABLE\x10\xf7\x03\x12\x1f\n" +
	"\x1aSIP_STATUS_GATEWAY_TIMEOUT\x10\xf8\x03\x12%\n" +
	" SIP_STATUS_VERSION_NOT_SUPPORTED\x10\xf9\x03\x12!\n" +
	"\x1cSIP_STATUS_MESSAGE_TOO_LARGE\x10\x81\x04\x12&\n" +
	"!SIP_STATUS_GLOBAL_BUSY_EVERYWHERE\x10\xd8\x04\x12\x1e\n" +
	"\x19SIP_STATUS_GLOBAL_DECLINE\x10\xdb\x04\x12.\n" +
	")SIP_STATUS_GLOBAL_DOES_NOT_EXIST_ANYWHERE\x10\xdc\x04\x12%\n" +
	" SIP_STATUS_GLOBAL_NOT_ACCEPTABLE\x10\xde\x04*k\n" +
	"\fSIPTransport\x12\x16\n" +
	"\x12SIP_TRANSPORT_AUTO\x10\x00\x12\x15\n" +
	"\x11SIP_TRANSPORT_UDP\x10\x01\x12\x15\n" +
	"\x11SIP_TRANSPORT_TCP\x10\x02\x12\x15\n" +
	"\x11SIP_TRANSPORT_TLS\x10\x03*N\n" +
	"\x10SIPHeaderOptions\x12\x12\n" +
	"\x0eSIP_NO_HEADERS\x10\x00\x12\x11\n" +
	"\rSIP_X_HEADERS\x10\x01\x12\x13\n" +
	"\x0fSIP_ALL_HEADERS\x10\x02*o\n" +
	"\x12SIPMediaEncryption\x12\x1d\n" +
	"\x19SIP_MEDIA_ENCRYPT_DISABLE\x10\x00\x12\x1b\n" +
	"\x17SIP_MEDIA_ENCRYPT_ALLOW\x10\x01\x12\x1d\n" +
	"\x19SIP_MEDIA_ENCRYPT_REQUIRE\x10\x02*w\n" +
	"\rSIPCallStatus\x12\x15\n" +
	"\x11SCS_CALL_INCOMING\x10\x00\x12\x1a\n" +
	"\x16SCS_PARTICIPANT_JOINED\x10\x01\x12\x0e\n" +
	"\n" +
	"SCS_ACTIVE\x10\x02\x12\x14\n" +
	"\x10SCS_DISCONNECTED\x10\x03\x12\r\n" +
	"\tSCS_ERROR\x10\x04*c\n" +
	"\x11SIPTransferStatus\x12\x18\n" +
	"\x14STS_TRANSFER_ONGOING\x10\x00\x12\x17\n" +
	"\x13STS_TRANSFER_FAILED\x10\x01\x12\x1b\n" +
	"\x17STS_TRANSFER_SUCCESSFUL\x10\x02*)\n" +
	"\n" +
	"SIPFeature\x12\b\n" +
	"\x04NONE\x10\x00\x12\x11\n" +
	"\rKRISP_ENABLED\x10\x01*F\n" +
	"\x10SIPCallDirection\x12\x0f\n" +
	"\vSCD_UNKNOWN\x10\x00\x12\x0f\n" +
	"\vSCD_INBOUND\x10\x01\x12\x10\n" +
	"\fSCD_OUTBOUND\x10\x022\xd7\v\n" +
	"\x03SIP\x12P\n" +
	"\fListSIPTrunk\x12\x1c.livekit.ListSIPTrunkRequest\x1a\x1d.livekit.ListSIPTrunkResponse\"\x03\x88\x02\x01\x12\\\n" +
	"\x15CreateSIPInboundTrunk\x12%.livekit.CreateSIPInboundTrunkRequest\x1a\x1c.livekit.SIPInboundTrunkInfo\x12_\n" +
	"\x16CreateSIPOutboundTrunk\x12&.livekit.CreateSIPOutboundTrunkRequest\x1a\x1d.livekit.SIPOutboundTrunkInfo\x12\\\n" +
	"\x15UpdateSIPInboundTrunk\x12%.livekit.UpdateSIPInboundTrunkRequest\x1a\x1c.livekit.SIPInboundTrunkInfo\x12_\n" +
	"\x16UpdateSIPOutboundTrunk\x12&.livekit.UpdateSIPOutboundTrunkRequest\x1a\x1d.livekit.SIPOutboundTrunkInfo\x12]\n" +
	"\x12GetSIPInboundTrunk\x12\".livekit.GetSIPInboundTrunkRequest\x1a#.livekit.GetSIPInboundTrunkResponse\x12`\n" +
	"\x13GetSIPOutboundTrunk\x12#.livekit.GetSIPOutboundTrunkRequest\x1a$.livekit.GetSIPOutboundTrunkResponse\x12`\n" +
	"\x13ListSIPInboundTrunk\x12#.livekit.ListSIPInboundTrunkRequest\x1a$.livekit.ListSIPInboundTrunkResponse\x12c\n" +
	"\x14ListSIPOutboundTrunk\x12$.livekit.ListSIPOutboundTrunkRequest\x1a%.livekit.ListSIPOutboundTrunkResponse\x12G\n" +
	"\x0eDeleteSIPTrunk\x12\x1e.livekit.DeleteSIPTrunkRequest\x1a\x15.livekit.SIPTrunkInfo\x12\\\n" +
	"\x15CreateSIPDispatchRule\x12%.livekit.CreateSIPDispatchRuleRequest\x1a\x1c.livekit.SIPDispatchRuleInfo\x12\\\n" +
	"\x15UpdateSIPDispatchRule\x12%.livekit.UpdateSIPDispatchRuleRequest\x1a\x1c.livekit.SIPDispatchRuleInfo\x12`\n" +
	"\x13ListSIPDispatchRule\x12#.livekit.ListSIPDispatchRuleRequest\x1a$.livekit.ListSIPDispatchRuleResponse\x12\\\n" +
	"\x15DeleteSIPDispatchRule\x12%.livekit.DeleteSIPDispatchRuleRequest\x1a\x1c.livekit.SIPDispatchRuleInfo\x12Y\n" +
	"\x14CreateSIPParticipant\x12$.livekit.CreateSIPParticipantRequest\x1a\x1b.livekit.SIPParticipantInfo\x12X\n" +
	"\x16TransferSIPParticipant\x12&.livekit.TransferSIPParticipantRequest\x1a\x16.google.protobuf.EmptyBFZ#github.com/livekit/protocol/livekit\xaa\x02\rLiveKit.Proto\xea\x02\x0eLiveKit::Protob\x06proto3"

var (
	file_livekit_sip_proto_rawDescOnce sync.Once
	file_livekit_sip_proto_rawDescData []byte
)

func file_livekit_sip_proto_rawDescGZIP() []byte {
	file_livekit_sip_proto_rawDescOnce.Do(func() {
		file_livekit_sip_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_livekit_sip_proto_rawDesc), len(file_livekit_sip_proto_rawDesc)))
	})
	return file_livekit_sip_proto_rawDescData
}

var file_livekit_sip_proto_enumTypes = make([]protoimpl.EnumInfo, 9)
var file_livekit_sip_proto_msgTypes = make([]protoimpl.MessageInfo, 55)
var file_livekit_sip_proto_goTypes = []any{
	(SIPStatusCode)(0),                    // 0: livekit.SIPStatusCode
	(SIPTransport)(0),                     // 1: livekit.SIPTransport
	(SIPHeaderOptions)(0),                 // 2: livekit.SIPHeaderOptions
	(SIPMediaEncryption)(0),               // 3: livekit.SIPMediaEncryption
	(SIPCallStatus)(0),                    // 4: livekit.SIPCallStatus
	(SIPTransferStatus)(0),                // 5: livekit.SIPTransferStatus
	(SIPFeature)(0),                       // 6: livekit.SIPFeature
	(SIPCallDirection)(0),                 // 7: livekit.SIPCallDirection
	(SIPTrunkInfo_TrunkKind)(0),           // 8: livekit.SIPTrunkInfo.TrunkKind
	(*SIPStatus)(nil),                     // 9: livekit.SIPStatus
	(*CreateSIPTrunkRequest)(nil),         // 10: livekit.CreateSIPTrunkRequest
	(*SIPTrunkInfo)(nil),                  // 11: livekit.SIPTrunkInfo
	(*CreateSIPInboundTrunkRequest)(nil),  // 12: livekit.CreateSIPInboundTrunkRequest
	(*UpdateSIPInboundTrunkRequest)(nil),  // 13: livekit.UpdateSIPInboundTrunkRequest
	(*SIPInboundTrunkInfo)(nil),           // 14: livekit.SIPInboundTrunkInfo
	(*SIPInboundTrunkUpdate)(nil),         // 15: livekit.SIPInboundTrunkUpdate
	(*CreateSIPOutboundTrunkRequest)(nil), // 16: livekit.CreateSIPOutboundTrunkRequest
	(*UpdateSIPOutboundTrunkRequest)(nil), // 17: livekit.UpdateSIPOutboundTrunkRequest
	(*SIPOutboundTrunkInfo)(nil),          // 18: livekit.SIPOutboundTrunkInfo
	(*SIPOutboundTrunkUpdate)(nil),        // 19: livekit.SIPOutboundTrunkUpdate
	(*GetSIPInboundTrunkRequest)(nil),     // 20: livekit.GetSIPInboundTrunkRequest
	(*GetSIPInboundTrunkResponse)(nil),    // 21: livekit.GetSIPInboundTrunkResponse
	(*GetSIPOutboundTrunkRequest)(nil),    // 22: livekit.GetSIPOutboundTrunkRequest
	(*GetSIPOutboundTrunkResponse)(nil),   // 23: livekit.GetSIPOutboundTrunkResponse
	(*ListSIPTrunkRequest)(nil),           // 24: livekit.ListSIPTrunkRequest
	(*ListSIPTrunkResponse)(nil),          // 25: livekit.ListSIPTrunkResponse
	(*ListSIPInboundTrunkRequest)(nil),    // 26: livekit.ListSIPInboundTrunkRequest
	(*ListSIPInboundTrunkResponse)(nil),   // 27: livekit.ListSIPInboundTrunkResponse
	(*ListSIPOutboundTrunkRequest)(nil),   // 28: livekit.ListSIPOutboundTrunkRequest
	(*ListSIPOutboundTrunkResponse)(nil),  // 29: livekit.ListSIPOutboundTrunkResponse
	(*DeleteSIPTrunkRequest)(nil),         // 30: livekit.DeleteSIPTrunkRequest
	(*SIPDispatchRuleDirect)(nil),         // 31: livekit.SIPDispatchRuleDirect
	(*SIPDispatchRuleIndividual)(nil),     // 32: livekit.SIPDispatchRuleIndividual
	(*SIPDispatchRuleCallee)(nil),         // 33: livekit.SIPDispatchRuleCallee
	(*SIPDispatchRule)(nil),               // 34: livekit.SIPDispatchRule
	(*CreateSIPDispatchRuleRequest)(nil),  // 35: livekit.CreateSIPDispatchRuleRequest
	(*UpdateSIPDispatchRuleRequest)(nil),  // 36: livekit.UpdateSIPDispatchRuleRequest
	(*SIPDispatchRuleInfo)(nil),           // 37: livekit.SIPDispatchRuleInfo
	(*SIPDispatchRuleUpdate)(nil),         // 38: livekit.SIPDispatchRuleUpdate
	(*ListSIPDispatchRuleRequest)(nil),    // 39: livekit.ListSIPDispatchRuleRequest
	(*ListSIPDispatchRuleResponse)(nil),   // 40: livekit.ListSIPDispatchRuleResponse
	(*DeleteSIPDispatchRuleRequest)(nil),  // 41: livekit.DeleteSIPDispatchRuleRequest
	(*SIPOutboundConfig)(nil),             // 42: livekit.SIPOutboundConfig
	(*CreateSIPParticipantRequest)(nil),   // 43: livekit.CreateSIPParticipantRequest
	(*SIPParticipantInfo)(nil),            // 44: livekit.SIPParticipantInfo
	(*TransferSIPParticipantRequest)(nil), // 45: livekit.TransferSIPParticipantRequest
	(*SIPCallInfo)(nil),                   // 46: livekit.SIPCallInfo
	(*SIPTransferInfo)(nil),               // 47: livekit.SIPTransferInfo
	(*SIPUri)(nil),                        // 48: livekit.SIPUri
	nil,                                   // 49: livekit.SIPInboundTrunkInfo.HeadersEntry
	nil,                                   // 50: livekit.SIPInboundTrunkInfo.HeadersToAttributesEntry
	nil,                                   // 51: livekit.SIPInboundTrunkInfo.AttributesToHeadersEntry
	nil,                                   // 52: livekit.SIPOutboundTrunkInfo.HeadersEntry
	nil,                                   // 53: livekit.SIPOutboundTrunkInfo.HeadersToAttributesEntry
	nil,                                   // 54: livekit.SIPOutboundTrunkInfo.AttributesToHeadersEntry
	nil,                                   // 55: livekit.CreateSIPDispatchRuleRequest.AttributesEntry
	nil,                                   // 56: livekit.SIPDispatchRuleInfo.AttributesEntry
	nil,                                   // 57: livekit.SIPDispatchRuleUpdate.AttributesEntry
	nil,                                   // 58: livekit.SIPOutboundConfig.HeadersToAttributesEntry
	nil,                                   // 59: livekit.SIPOutboundConfig.AttributesToHeadersEntry
	nil,                                   // 60: livekit.CreateSIPParticipantRequest.ParticipantAttributesEntry
	nil,                                   // 61: livekit.CreateSIPParticipantRequest.HeadersEntry
	nil,                                   // 62: livekit.TransferSIPParticipantRequest.HeadersEntry
	nil,                                   // 63: livekit.SIPCallInfo.ParticipantAttributesEntry
	(*durationpb.Duration)(nil),           // 64: google.protobuf.Duration
	(*ListUpdate)(nil),                    // 65: livekit.ListUpdate
	(*Pagination)(nil),                    // 66: livekit.Pagination
	(*RoomConfiguration)(nil),             // 67: livekit.RoomConfiguration
	(DisconnectReason)(0),                 // 68: livekit.DisconnectReason
	(*emptypb.Empty)(nil),                 // 69: google.protobuf.Empty
}
var file_livekit_sip_proto_depIdxs = []int32{
	0,  // 0: livekit.SIPStatus.code:type_name -> livekit.SIPStatusCode
	8,  // 1: livekit.SIPTrunkInfo.kind:type_name -> livekit.SIPTrunkInfo.TrunkKind
	1,  // 2: livekit.SIPTrunkInfo.transport:type_name -> livekit.SIPTransport
	14, // 3: livekit.CreateSIPInboundTrunkRequest.trunk:type_name -> livekit.SIPInboundTrunkInfo
	14, // 4: livekit.UpdateSIPInboundTrunkRequest.replace:type_name -> livekit.SIPInboundTrunkInfo
	15, // 5: livekit.UpdateSIPInboundTrunkRequest.update:type_name -> livekit.SIPInboundTrunkUpdate
	49, // 6: livekit.SIPInboundTrunkInfo.headers:type_name -> livekit.SIPInboundTrunkInfo.HeadersEntry
	50, // 7: livekit.SIPInboundTrunkInfo.headers_to_attributes:type_name -> livekit.SIPInboundTrunkInfo.HeadersToAttributesEntry
	51, // 8: livekit.SIPInboundTrunkInfo.attributes_to_headers:type_name -> livekit.SIPInboundTrunkInfo.AttributesToHeadersEntry
	2,  // 9: livekit.SIPInboundTrunkInfo.include_headers:type_name -> livekit.SIPHeaderOptions
	64, // 10: livekit.SIPInboundTrunkInfo.ringing_timeout:type_name -> google.protobuf.Duration
	64, // 11: livekit.SIPInboundTrunkInfo.max_call_duration:type_name -> google.protobuf.Duration
	3,  // 12: livekit.SIPInboundTrunkInfo.media_encryption:type_name -> livekit.SIPMediaEncryption
	65, // 13: livekit.SIPInboundTrunkUpdate.numbers:type_name -> livekit.ListUpdate
	65, // 14: livekit.SIPInboundTrunkUpdate.allowed_addresses:type_name -> livekit.ListUpdate
	65, // 15: livekit.SIPInboundTrunkUpdate.allowed_numbers:type_name -> livekit.ListUpdate
	3,  // 16: livekit.SIPInboundTrunkUpdate.media_encryption:type_name -> livekit.SIPMediaEncryption
	18, // 17: livekit.CreateSIPOutboundTrunkRequest.trunk:type_name -> livekit.SIPOutboundTrunkInfo
	18, // 18: livekit.UpdateSIPOutboundTrunkRequest.replace:type_name -> livekit.SIPOutboundTrunkInfo
	19, // 19: livekit.UpdateSIPOutboundTrunkRequest.update:type_name -> livekit.SIPOutboundTrunkUpdate
	1,  // 20: livekit.SIPOutboundTrunkInfo.transport:type_name -> livekit.SIPTransport
	52, // 21: livekit.SIPOutboundTrunkInfo.headers:type_name -> livekit.SIPOutboundTrunkInfo.HeadersEntry
	53, // 22: livekit.SIPOutboundTrunkInfo.headers_to_attributes:type_name -> livekit.SIPOutboundTrunkInfo.HeadersToAttributesEntry
	54, // 23: livekit.SIPOutboundTrunkInfo.attributes_to_headers:type_name -> livekit.SIPOutboundTrunkInfo.AttributesToHeadersEntry
	2,  // 24: livekit.SIPOutboundTrunkInfo.include_headers:type_name -> livekit.SIPHeaderOptions
	3,  // 25: livekit.SIPOutboundTrunkInfo.media_encryption:type_name -> livekit.SIPMediaEncryption
	1,  // 26: livekit.SIPOutboundTrunkUpdate.transport:type_name -> livekit.SIPTransport
	65, // 27: livekit.SIPOutboundTrunkUpdate.numbers:type_name -> livekit.ListUpdate
	3,  // 28: livekit.SIPOutboundTrunkUpdate.media_encryption:type_name -> livekit.SIPMediaEncryption
	14, // 29: livekit.GetSIPInboundTrunkResponse.trunk:type_name -> livekit.SIPInboundTrunkInfo
	18, // 30: livekit.GetSIPOutboundTrunkResponse.trunk:type_name -> livekit.SIPOutboundTrunkInfo
	66, // 31: livekit.ListSIPTrunkRequest.page:type_name -> livekit.Pagination
	11, // 32: livekit.ListSIPTrunkResponse.items:type_name -> livekit.SIPTrunkInfo
	66, // 33: livekit.ListSIPInboundTrunkRequest.page:type_name -> livekit.Pagination
	14, // 34: livekit.ListSIPInboundTrunkResponse.items:type_name -> livekit.SIPInboundTrunkInfo
	66, // 35: livekit.ListSIPOutboundTrunkRequest.page:type_name -> livekit.Pagination
	18, // 36: livekit.ListSIPOutboundTrunkResponse.items:type_name -> livekit.SIPOutboundTrunkInfo
	31, // 37: livekit.SIPDispatchRule.dispatch_rule_direct:type_name -> livekit.SIPDispatchRuleDirect
	32, // 38: livekit.SIPDispatchRule.dispatch_rule_individual:type_name -> livekit.SIPDispatchRuleIndividual
	33, // 39: livekit.SIPDispatchRule.dispatch_rule_callee:type_name -> livekit.SIPDispatchRuleCallee
	37, // 40: livekit.CreateSIPDispatchRuleRequest.dispatch_rule:type_name -> livekit.SIPDispatchRuleInfo
	34, // 41: livekit.CreateSIPDispatchRuleRequest.rule:type_name -> livekit.SIPDispatchRule
	55, // 42: livekit.CreateSIPDispatchRuleRequest.attributes:type_name -> livekit.CreateSIPDispatchRuleRequest.AttributesEntry
	67, // 43: livekit.CreateSIPDispatchRuleRequest.room_config:type_name -> livekit.RoomConfiguration
	37, // 44: livekit.UpdateSIPDispatchRuleRequest.replace:type_name -> livekit.SIPDispatchRuleInfo
	38, // 45: livekit.UpdateSIPDispatchRuleRequest.update:type_name -> livekit.SIPDispatchRuleUpdate
	34, // 46: livekit.SIPDispatchRuleInfo.rule:type_name -> livekit.SIPDispatchRule
	56, // 47: livekit.SIPDispatchRuleInfo.attributes:type_name -> livekit.SIPDispatchRuleInfo.AttributesEntry
	67, // 48: livekit.SIPDispatchRuleInfo.room_config:type_name -> livekit.RoomConfiguration
	3,  // 49: livekit.SIPDispatchRuleInfo.media_encryption:type_name -> livekit.SIPMediaEncryption
	65, // 50: livekit.SIPDispatchRuleUpdate.trunk_ids:type_name -> livekit.ListUpdate
	34, // 51: livekit.SIPDispatchRuleUpdate.rule:type_name -> livekit.SIPDispatchRule
	57, // 52: livekit.SIPDispatchRuleUpdate.attributes:type_name -> livekit.SIPDispatchRuleUpdate.AttributesEntry
	3,  // 53: livekit.SIPDispatchRuleUpdate.media_encryption:type_name -> livekit.SIPMediaEncryption
	66, // 54: livekit.ListSIPDispatchRuleRequest.page:type_name -> livekit.Pagination
	37, // 55: livekit.ListSIPDispatchRuleResponse.items:type_name -> livekit.SIPDispatchRuleInfo
	1,  // 56: livekit.SIPOutboundConfig.transport:type_name -> livekit.SIPTransport
	58, // 57: livekit.SIPOutboundConfig.headers_to_attributes:type_name -> livekit.SIPOutboundConfig.HeadersToAttributesEntry
	59, // 58: livekit.SIPOutboundConfig.attributes_to_headers:type_name -> livekit.SIPOutboundConfig.AttributesToHeadersEntry
	42, // 59: livekit.CreateSIPParticipantRequest.trunk:type_name -> livekit.SIPOutboundConfig
	60, // 60: livekit.CreateSIPParticipantRequest.participant_attributes:type_name -> livekit.CreateSIPParticipantRequest.ParticipantAttributesEntry
	61, // 61: livekit.CreateSIPParticipantRequest.headers:type_name -> livekit.CreateSIPParticipantRequest.HeadersEntry
	2,  // 62: livekit.CreateSIPParticipantRequest.include_headers:type_name -> livekit.SIPHeaderOptions
	64, // 63: livekit.CreateSIPParticipantRequest.ringing_timeout:type_name -> google.protobuf.Duration
	64, // 64: livekit.CreateSIPParticipantRequest.max_call_duration:type_name -> google.protobuf.Duration
	3,  // 65: livekit.CreateSIPParticipantRequest.media_encryption:type_name -> livekit.SIPMediaEncryption
	62, // 66: livekit.TransferSIPParticipantRequest.headers:type_name -> livekit.TransferSIPParticipantRequest.HeadersEntry
	64, // 67: livekit.TransferSIPParticipantRequest.ringing_timeout:type_name -> google.protobuf.Duration
	63, // 68: livekit.SIPCallInfo.participant_attributes:type_name -> livekit.SIPCallInfo.ParticipantAttributesEntry
	48, // 69: livekit.SIPCallInfo.from_uri:type_name -> livekit.SIPUri
	48, // 70: livekit.SIPCallInfo.to_uri:type_name -> livekit.SIPUri
	6,  // 71: livekit.SIPCallInfo.enabled_features:type_name -> livekit.SIPFeature
	7,  // 72: livekit.SIPCallInfo.call_direction:type_name -> livekit.SIPCallDirection
	4,  // 73: livekit.SIPCallInfo.call_status:type_name -> livekit.SIPCallStatus
	68, // 74: livekit.SIPCallInfo.disconnect_reason:type_name -> livekit.DisconnectReason
	9,  // 75: livekit.SIPCallInfo.call_status_code:type_name -> livekit.SIPStatus
	5,  // 76: livekit.SIPTransferInfo.transfer_status:type_name -> livekit.SIPTransferStatus
	9,  // 77: livekit.SIPTransferInfo.transfer_status_code:type_name -> livekit.SIPStatus
	1,  // 78: livekit.SIPUri.transport:type_name -> livekit.SIPTransport
	24, // 79: livekit.SIP.ListSIPTrunk:input_type -> livekit.ListSIPTrunkRequest
	12, // 80: livekit.SIP.CreateSIPInboundTrunk:input_type -> livekit.CreateSIPInboundTrunkRequest
	16, // 81: livekit.SIP.CreateSIPOutboundTrunk:input_type -> livekit.CreateSIPOutboundTrunkRequest
	13, // 82: livekit.SIP.UpdateSIPInboundTrunk:input_type -> livekit.UpdateSIPInboundTrunkRequest
	17, // 83: livekit.SIP.UpdateSIPOutboundTrunk:input_type -> livekit.UpdateSIPOutboundTrunkRequest
	20, // 84: livekit.SIP.GetSIPInboundTrunk:input_type -> livekit.GetSIPInboundTrunkRequest
	22, // 85: livekit.SIP.GetSIPOutboundTrunk:input_type -> livekit.GetSIPOutboundTrunkRequest
	26, // 86: livekit.SIP.ListSIPInboundTrunk:input_type -> livekit.ListSIPInboundTrunkRequest
	28, // 87: livekit.SIP.ListSIPOutboundTrunk:input_type -> livekit.ListSIPOutboundTrunkRequest
	30, // 88: livekit.SIP.DeleteSIPTrunk:input_type -> livekit.DeleteSIPTrunkRequest
	35, // 89: livekit.SIP.CreateSIPDispatchRule:input_type -> livekit.CreateSIPDispatchRuleRequest
	36, // 90: livekit.SIP.UpdateSIPDispatchRule:input_type -> livekit.UpdateSIPDispatchRuleRequest
	39, // 91: livekit.SIP.ListSIPDispatchRule:input_type -> livekit.ListSIPDispatchRuleRequest
	41, // 92: livekit.SIP.DeleteSIPDispatchRule:input_type -> livekit.DeleteSIPDispatchRuleRequest
	43, // 93: livekit.SIP.CreateSIPParticipant:input_type -> livekit.CreateSIPParticipantRequest
	45, // 94: livekit.SIP.TransferSIPParticipant:input_type -> livekit.TransferSIPParticipantRequest
	25, // 95: livekit.SIP.ListSIPTrunk:output_type -> livekit.ListSIPTrunkResponse
	14, // 96: livekit.SIP.CreateSIPInboundTrunk:output_type -> livekit.SIPInboundTrunkInfo
	18, // 97: livekit.SIP.CreateSIPOutboundTrunk:output_type -> livekit.SIPOutboundTrunkInfo
	14, // 98: livekit.SIP.UpdateSIPInboundTrunk:output_type -> livekit.SIPInboundTrunkInfo
	18, // 99: livekit.SIP.UpdateSIPOutboundTrunk:output_type -> livekit.SIPOutboundTrunkInfo
	21, // 100: livekit.SIP.GetSIPInboundTrunk:output_type -> livekit.GetSIPInboundTrunkResponse
	23, // 101: livekit.SIP.GetSIPOutboundTrunk:output_type -> livekit.GetSIPOutboundTrunkResponse
	27, // 102: livekit.SIP.ListSIPInboundTrunk:output_type -> livekit.ListSIPInboundTrunkResponse
	29, // 103: livekit.SIP.ListSIPOutboundTrunk:output_type -> livekit.ListSIPOutboundTrunkResponse
	11, // 104: livekit.SIP.DeleteSIPTrunk:output_type -> livekit.SIPTrunkInfo
	37, // 105: livekit.SIP.CreateSIPDispatchRule:output_type -> livekit.SIPDispatchRuleInfo
	37, // 106: livekit.SIP.UpdateSIPDispatchRule:output_type -> livekit.SIPDispatchRuleInfo
	40, // 107: livekit.SIP.ListSIPDispatchRule:output_type -> livekit.ListSIPDispatchRuleResponse
	37, // 108: livekit.SIP.DeleteSIPDispatchRule:output_type -> livekit.SIPDispatchRuleInfo
	44, // 109: livekit.SIP.CreateSIPParticipant:output_type -> livekit.SIPParticipantInfo
	69, // 110: livekit.SIP.TransferSIPParticipant:output_type -> google.protobuf.Empty
	95, // [95:111] is the sub-list for method output_type
	79, // [79:95] is the sub-list for method input_type
	79, // [79:79] is the sub-list for extension type_name
	79, // [79:79] is the sub-list for extension extendee
	0,  // [0:79] is the sub-list for field type_name
}

func init() { file_livekit_sip_proto_init() }
func file_livekit_sip_proto_init() {
	if File_livekit_sip_proto != nil {
		return
	}
	file_livekit_models_proto_init()
	file_livekit_room_proto_init()
	file_livekit_sip_proto_msgTypes[4].OneofWrappers = []any{
		(*UpdateSIPInboundTrunkRequest_Replace)(nil),
		(*UpdateSIPInboundTrunkRequest_Update)(nil),
	}
	file_livekit_sip_proto_msgTypes[6].OneofWrappers = []any{}
	file_livekit_sip_proto_msgTypes[8].OneofWrappers = []any{
		(*UpdateSIPOutboundTrunkRequest_Replace)(nil),
		(*UpdateSIPOutboundTrunkRequest_Update)(nil),
	}
	file_livekit_sip_proto_msgTypes[10].OneofWrappers = []any{}
	file_livekit_sip_proto_msgTypes[25].OneofWrappers = []any{
		(*SIPDispatchRule_DispatchRuleDirect)(nil),
		(*SIPDispatchRule_DispatchRuleIndividual)(nil),
		(*SIPDispatchRule_DispatchRuleCallee)(nil),
	}
	file_livekit_sip_proto_msgTypes[27].OneofWrappers = []any{
		(*UpdateSIPDispatchRuleRequest_Replace)(nil),
		(*UpdateSIPDispatchRuleRequest_Update)(nil),
	}
	file_livekit_sip_proto_msgTypes[29].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_livekit_sip_proto_rawDesc), len(file_livekit_sip_proto_rawDesc)),
			NumEnums:      9,
			NumMessages:   55,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_livekit_sip_proto_goTypes,
		DependencyIndexes: file_livekit_sip_proto_depIdxs,
		EnumInfos:         file_livekit_sip_proto_enumTypes,
		MessageInfos:      file_livekit_sip_proto_msgTypes,
	}.Build()
	File_livekit_sip_proto = out.File
	file_livekit_sip_proto_goTypes = nil
	file_livekit_sip_proto_depIdxs = nil
}
