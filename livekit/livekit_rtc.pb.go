// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: livekit_rtc.proto

package livekit

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SignalTarget int32

const (
	SignalTarget_PUBLISHER  SignalTarget = 0
	SignalTarget_SUBSCRIBER SignalTarget = 1
)

// Enum value maps for SignalTarget.
var (
	SignalTarget_name = map[int32]string{
		0: "PUBLISHER",
		1: "SUBSCRIBER",
	}
	SignalTarget_value = map[string]int32{
		"PUBLISHER":  0,
		"SUBSCRIBER": 1,
	}
)

func (x SignalTarget) Enum() *SignalTarget {
	p := new(SignalTarget)
	*p = x
	return p
}

func (x SignalTarget) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SignalTarget) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_rtc_proto_enumTypes[0].Descriptor()
}

func (SignalTarget) Type() protoreflect.EnumType {
	return &file_livekit_rtc_proto_enumTypes[0]
}

func (x SignalTarget) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SignalTarget.Descriptor instead.
func (SignalTarget) EnumDescriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{0}
}

type StreamState int32

const (
	StreamState_ACTIVE StreamState = 0
	StreamState_PAUSED StreamState = 1
)

// Enum value maps for StreamState.
var (
	StreamState_name = map[int32]string{
		0: "ACTIVE",
		1: "PAUSED",
	}
	StreamState_value = map[string]int32{
		"ACTIVE": 0,
		"PAUSED": 1,
	}
)

func (x StreamState) Enum() *StreamState {
	p := new(StreamState)
	*p = x
	return p
}

func (x StreamState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StreamState) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_rtc_proto_enumTypes[1].Descriptor()
}

func (StreamState) Type() protoreflect.EnumType {
	return &file_livekit_rtc_proto_enumTypes[1]
}

func (x StreamState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StreamState.Descriptor instead.
func (StreamState) EnumDescriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{1}
}

type CandidateProtocol int32

const (
	CandidateProtocol_UDP CandidateProtocol = 0
	CandidateProtocol_TCP CandidateProtocol = 1
	CandidateProtocol_TLS CandidateProtocol = 2
)

// Enum value maps for CandidateProtocol.
var (
	CandidateProtocol_name = map[int32]string{
		0: "UDP",
		1: "TCP",
		2: "TLS",
	}
	CandidateProtocol_value = map[string]int32{
		"UDP": 0,
		"TCP": 1,
		"TLS": 2,
	}
)

func (x CandidateProtocol) Enum() *CandidateProtocol {
	p := new(CandidateProtocol)
	*p = x
	return p
}

func (x CandidateProtocol) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CandidateProtocol) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_rtc_proto_enumTypes[2].Descriptor()
}

func (CandidateProtocol) Type() protoreflect.EnumType {
	return &file_livekit_rtc_proto_enumTypes[2]
}

func (x CandidateProtocol) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CandidateProtocol.Descriptor instead.
func (CandidateProtocol) EnumDescriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{2}
}

// indicates action clients should take on receiving this message
type LeaveRequest_Action int32

const (
	LeaveRequest_DISCONNECT LeaveRequest_Action = 0 // should disconnect
	LeaveRequest_RESUME     LeaveRequest_Action = 1 // should attempt a resume with `reconnect=1` in join URL
	LeaveRequest_RECONNECT  LeaveRequest_Action = 2 // should attempt a reconnect, i. e. no `reconnect=1`
)

// Enum value maps for LeaveRequest_Action.
var (
	LeaveRequest_Action_name = map[int32]string{
		0: "DISCONNECT",
		1: "RESUME",
		2: "RECONNECT",
	}
	LeaveRequest_Action_value = map[string]int32{
		"DISCONNECT": 0,
		"RESUME":     1,
		"RECONNECT":  2,
	}
)

func (x LeaveRequest_Action) Enum() *LeaveRequest_Action {
	p := new(LeaveRequest_Action)
	*p = x
	return p
}

func (x LeaveRequest_Action) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LeaveRequest_Action) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_rtc_proto_enumTypes[3].Descriptor()
}

func (LeaveRequest_Action) Type() protoreflect.EnumType {
	return &file_livekit_rtc_proto_enumTypes[3]
}

func (x LeaveRequest_Action) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LeaveRequest_Action.Descriptor instead.
func (LeaveRequest_Action) EnumDescriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{17, 0}
}

type RequestResponse_Reason int32

const (
	RequestResponse_OK             RequestResponse_Reason = 0
	RequestResponse_NOT_FOUND      RequestResponse_Reason = 1
	RequestResponse_NOT_ALLOWED    RequestResponse_Reason = 2
	RequestResponse_LIMIT_EXCEEDED RequestResponse_Reason = 3
)

// Enum value maps for RequestResponse_Reason.
var (
	RequestResponse_Reason_name = map[int32]string{
		0: "OK",
		1: "NOT_FOUND",
		2: "NOT_ALLOWED",
		3: "LIMIT_EXCEEDED",
	}
	RequestResponse_Reason_value = map[string]int32{
		"OK":             0,
		"NOT_FOUND":      1,
		"NOT_ALLOWED":    2,
		"LIMIT_EXCEEDED": 3,
	}
)

func (x RequestResponse_Reason) Enum() *RequestResponse_Reason {
	p := new(RequestResponse_Reason)
	*p = x
	return p
}

func (x RequestResponse_Reason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RequestResponse_Reason) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_rtc_proto_enumTypes[4].Descriptor()
}

func (RequestResponse_Reason) Type() protoreflect.EnumType {
	return &file_livekit_rtc_proto_enumTypes[4]
}

func (x RequestResponse_Reason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RequestResponse_Reason.Descriptor instead.
func (RequestResponse_Reason) EnumDescriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{43, 0}
}

type SignalRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Message:
	//
	//	*SignalRequest_Offer
	//	*SignalRequest_Answer
	//	*SignalRequest_Trickle
	//	*SignalRequest_AddTrack
	//	*SignalRequest_Mute
	//	*SignalRequest_Subscription
	//	*SignalRequest_TrackSetting
	//	*SignalRequest_Leave
	//	*SignalRequest_UpdateLayers
	//	*SignalRequest_SubscriptionPermission
	//	*SignalRequest_SyncState
	//	*SignalRequest_Simulate
	//	*SignalRequest_Ping
	//	*SignalRequest_UpdateMetadata
	//	*SignalRequest_PingReq
	//	*SignalRequest_UpdateAudioTrack
	//	*SignalRequest_UpdateVideoTrack
	Message       isSignalRequest_Message `protobuf_oneof:"message"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignalRequest) Reset() {
	*x = SignalRequest{}
	mi := &file_livekit_rtc_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignalRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignalRequest) ProtoMessage() {}

func (x *SignalRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignalRequest.ProtoReflect.Descriptor instead.
func (*SignalRequest) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{0}
}

func (x *SignalRequest) GetMessage() isSignalRequest_Message {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *SignalRequest) GetOffer() *SessionDescription {
	if x != nil {
		if x, ok := x.Message.(*SignalRequest_Offer); ok {
			return x.Offer
		}
	}
	return nil
}

func (x *SignalRequest) GetAnswer() *SessionDescription {
	if x != nil {
		if x, ok := x.Message.(*SignalRequest_Answer); ok {
			return x.Answer
		}
	}
	return nil
}

func (x *SignalRequest) GetTrickle() *TrickleRequest {
	if x != nil {
		if x, ok := x.Message.(*SignalRequest_Trickle); ok {
			return x.Trickle
		}
	}
	return nil
}

func (x *SignalRequest) GetAddTrack() *AddTrackRequest {
	if x != nil {
		if x, ok := x.Message.(*SignalRequest_AddTrack); ok {
			return x.AddTrack
		}
	}
	return nil
}

func (x *SignalRequest) GetMute() *MuteTrackRequest {
	if x != nil {
		if x, ok := x.Message.(*SignalRequest_Mute); ok {
			return x.Mute
		}
	}
	return nil
}

func (x *SignalRequest) GetSubscription() *UpdateSubscription {
	if x != nil {
		if x, ok := x.Message.(*SignalRequest_Subscription); ok {
			return x.Subscription
		}
	}
	return nil
}

func (x *SignalRequest) GetTrackSetting() *UpdateTrackSettings {
	if x != nil {
		if x, ok := x.Message.(*SignalRequest_TrackSetting); ok {
			return x.TrackSetting
		}
	}
	return nil
}

func (x *SignalRequest) GetLeave() *LeaveRequest {
	if x != nil {
		if x, ok := x.Message.(*SignalRequest_Leave); ok {
			return x.Leave
		}
	}
	return nil
}

// Deprecated: Marked as deprecated in livekit_rtc.proto.
func (x *SignalRequest) GetUpdateLayers() *UpdateVideoLayers {
	if x != nil {
		if x, ok := x.Message.(*SignalRequest_UpdateLayers); ok {
			return x.UpdateLayers
		}
	}
	return nil
}

func (x *SignalRequest) GetSubscriptionPermission() *SubscriptionPermission {
	if x != nil {
		if x, ok := x.Message.(*SignalRequest_SubscriptionPermission); ok {
			return x.SubscriptionPermission
		}
	}
	return nil
}

func (x *SignalRequest) GetSyncState() *SyncState {
	if x != nil {
		if x, ok := x.Message.(*SignalRequest_SyncState); ok {
			return x.SyncState
		}
	}
	return nil
}

func (x *SignalRequest) GetSimulate() *SimulateScenario {
	if x != nil {
		if x, ok := x.Message.(*SignalRequest_Simulate); ok {
			return x.Simulate
		}
	}
	return nil
}

func (x *SignalRequest) GetPing() int64 {
	if x != nil {
		if x, ok := x.Message.(*SignalRequest_Ping); ok {
			return x.Ping
		}
	}
	return 0
}

func (x *SignalRequest) GetUpdateMetadata() *UpdateParticipantMetadata {
	if x != nil {
		if x, ok := x.Message.(*SignalRequest_UpdateMetadata); ok {
			return x.UpdateMetadata
		}
	}
	return nil
}

func (x *SignalRequest) GetPingReq() *Ping {
	if x != nil {
		if x, ok := x.Message.(*SignalRequest_PingReq); ok {
			return x.PingReq
		}
	}
	return nil
}

func (x *SignalRequest) GetUpdateAudioTrack() *UpdateLocalAudioTrack {
	if x != nil {
		if x, ok := x.Message.(*SignalRequest_UpdateAudioTrack); ok {
			return x.UpdateAudioTrack
		}
	}
	return nil
}

func (x *SignalRequest) GetUpdateVideoTrack() *UpdateLocalVideoTrack {
	if x != nil {
		if x, ok := x.Message.(*SignalRequest_UpdateVideoTrack); ok {
			return x.UpdateVideoTrack
		}
	}
	return nil
}

type isSignalRequest_Message interface {
	isSignalRequest_Message()
}

type SignalRequest_Offer struct {
	// initial join exchange, for publisher
	Offer *SessionDescription `protobuf:"bytes,1,opt,name=offer,proto3,oneof"`
}

type SignalRequest_Answer struct {
	// participant answering publisher offer
	Answer *SessionDescription `protobuf:"bytes,2,opt,name=answer,proto3,oneof"`
}

type SignalRequest_Trickle struct {
	Trickle *TrickleRequest `protobuf:"bytes,3,opt,name=trickle,proto3,oneof"`
}

type SignalRequest_AddTrack struct {
	AddTrack *AddTrackRequest `protobuf:"bytes,4,opt,name=add_track,json=addTrack,proto3,oneof"`
}

type SignalRequest_Mute struct {
	// mute the participant's published tracks
	Mute *MuteTrackRequest `protobuf:"bytes,5,opt,name=mute,proto3,oneof"`
}

type SignalRequest_Subscription struct {
	// Subscribe or unsubscribe from tracks
	Subscription *UpdateSubscription `protobuf:"bytes,6,opt,name=subscription,proto3,oneof"`
}

type SignalRequest_TrackSetting struct {
	// Update settings of subscribed tracks
	TrackSetting *UpdateTrackSettings `protobuf:"bytes,7,opt,name=track_setting,json=trackSetting,proto3,oneof"`
}

type SignalRequest_Leave struct {
	// Immediately terminate session
	Leave *LeaveRequest `protobuf:"bytes,8,opt,name=leave,proto3,oneof"`
}

type SignalRequest_UpdateLayers struct {
	// Update published video layers
	//
	// Deprecated: Marked as deprecated in livekit_rtc.proto.
	UpdateLayers *UpdateVideoLayers `protobuf:"bytes,10,opt,name=update_layers,json=updateLayers,proto3,oneof"`
}

type SignalRequest_SubscriptionPermission struct {
	// Update subscriber permissions
	SubscriptionPermission *SubscriptionPermission `protobuf:"bytes,11,opt,name=subscription_permission,json=subscriptionPermission,proto3,oneof"`
}

type SignalRequest_SyncState struct {
	// sync client's subscribe state to server during reconnect
	SyncState *SyncState `protobuf:"bytes,12,opt,name=sync_state,json=syncState,proto3,oneof"`
}

type SignalRequest_Simulate struct {
	// Simulate conditions, for client validations
	Simulate *SimulateScenario `protobuf:"bytes,13,opt,name=simulate,proto3,oneof"`
}

type SignalRequest_Ping struct {
	// client triggered ping to server
	Ping int64 `protobuf:"varint,14,opt,name=ping,proto3,oneof"` // deprecated by ping_req (message Ping)
}

type SignalRequest_UpdateMetadata struct {
	// update a participant's own metadata, name, or attributes
	// requires canUpdateOwnParticipantMetadata permission
	UpdateMetadata *UpdateParticipantMetadata `protobuf:"bytes,15,opt,name=update_metadata,json=updateMetadata,proto3,oneof"`
}

type SignalRequest_PingReq struct {
	PingReq *Ping `protobuf:"bytes,16,opt,name=ping_req,json=pingReq,proto3,oneof"`
}

type SignalRequest_UpdateAudioTrack struct {
	// Update local audio track settings
	UpdateAudioTrack *UpdateLocalAudioTrack `protobuf:"bytes,17,opt,name=update_audio_track,json=updateAudioTrack,proto3,oneof"`
}

type SignalRequest_UpdateVideoTrack struct {
	// Update local video track settings
	UpdateVideoTrack *UpdateLocalVideoTrack `protobuf:"bytes,18,opt,name=update_video_track,json=updateVideoTrack,proto3,oneof"`
}

func (*SignalRequest_Offer) isSignalRequest_Message() {}

func (*SignalRequest_Answer) isSignalRequest_Message() {}

func (*SignalRequest_Trickle) isSignalRequest_Message() {}

func (*SignalRequest_AddTrack) isSignalRequest_Message() {}

func (*SignalRequest_Mute) isSignalRequest_Message() {}

func (*SignalRequest_Subscription) isSignalRequest_Message() {}

func (*SignalRequest_TrackSetting) isSignalRequest_Message() {}

func (*SignalRequest_Leave) isSignalRequest_Message() {}

func (*SignalRequest_UpdateLayers) isSignalRequest_Message() {}

func (*SignalRequest_SubscriptionPermission) isSignalRequest_Message() {}

func (*SignalRequest_SyncState) isSignalRequest_Message() {}

func (*SignalRequest_Simulate) isSignalRequest_Message() {}

func (*SignalRequest_Ping) isSignalRequest_Message() {}

func (*SignalRequest_UpdateMetadata) isSignalRequest_Message() {}

func (*SignalRequest_PingReq) isSignalRequest_Message() {}

func (*SignalRequest_UpdateAudioTrack) isSignalRequest_Message() {}

func (*SignalRequest_UpdateVideoTrack) isSignalRequest_Message() {}

type SignalResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Message:
	//
	//	*SignalResponse_Join
	//	*SignalResponse_Answer
	//	*SignalResponse_Offer
	//	*SignalResponse_Trickle
	//	*SignalResponse_Update
	//	*SignalResponse_TrackPublished
	//	*SignalResponse_Leave
	//	*SignalResponse_Mute
	//	*SignalResponse_SpeakersChanged
	//	*SignalResponse_RoomUpdate
	//	*SignalResponse_ConnectionQuality
	//	*SignalResponse_StreamStateUpdate
	//	*SignalResponse_SubscribedQualityUpdate
	//	*SignalResponse_SubscriptionPermissionUpdate
	//	*SignalResponse_RefreshToken
	//	*SignalResponse_TrackUnpublished
	//	*SignalResponse_Pong
	//	*SignalResponse_Reconnect
	//	*SignalResponse_PongResp
	//	*SignalResponse_SubscriptionResponse
	//	*SignalResponse_RequestResponse
	//	*SignalResponse_TrackSubscribed
	//	*SignalResponse_RoomMoved
	Message       isSignalResponse_Message `protobuf_oneof:"message"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignalResponse) Reset() {
	*x = SignalResponse{}
	mi := &file_livekit_rtc_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignalResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignalResponse) ProtoMessage() {}

func (x *SignalResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignalResponse.ProtoReflect.Descriptor instead.
func (*SignalResponse) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{1}
}

func (x *SignalResponse) GetMessage() isSignalResponse_Message {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *SignalResponse) GetJoin() *JoinResponse {
	if x != nil {
		if x, ok := x.Message.(*SignalResponse_Join); ok {
			return x.Join
		}
	}
	return nil
}

func (x *SignalResponse) GetAnswer() *SessionDescription {
	if x != nil {
		if x, ok := x.Message.(*SignalResponse_Answer); ok {
			return x.Answer
		}
	}
	return nil
}

func (x *SignalResponse) GetOffer() *SessionDescription {
	if x != nil {
		if x, ok := x.Message.(*SignalResponse_Offer); ok {
			return x.Offer
		}
	}
	return nil
}

func (x *SignalResponse) GetTrickle() *TrickleRequest {
	if x != nil {
		if x, ok := x.Message.(*SignalResponse_Trickle); ok {
			return x.Trickle
		}
	}
	return nil
}

func (x *SignalResponse) GetUpdate() *ParticipantUpdate {
	if x != nil {
		if x, ok := x.Message.(*SignalResponse_Update); ok {
			return x.Update
		}
	}
	return nil
}

func (x *SignalResponse) GetTrackPublished() *TrackPublishedResponse {
	if x != nil {
		if x, ok := x.Message.(*SignalResponse_TrackPublished); ok {
			return x.TrackPublished
		}
	}
	return nil
}

func (x *SignalResponse) GetLeave() *LeaveRequest {
	if x != nil {
		if x, ok := x.Message.(*SignalResponse_Leave); ok {
			return x.Leave
		}
	}
	return nil
}

func (x *SignalResponse) GetMute() *MuteTrackRequest {
	if x != nil {
		if x, ok := x.Message.(*SignalResponse_Mute); ok {
			return x.Mute
		}
	}
	return nil
}

func (x *SignalResponse) GetSpeakersChanged() *SpeakersChanged {
	if x != nil {
		if x, ok := x.Message.(*SignalResponse_SpeakersChanged); ok {
			return x.SpeakersChanged
		}
	}
	return nil
}

func (x *SignalResponse) GetRoomUpdate() *RoomUpdate {
	if x != nil {
		if x, ok := x.Message.(*SignalResponse_RoomUpdate); ok {
			return x.RoomUpdate
		}
	}
	return nil
}

func (x *SignalResponse) GetConnectionQuality() *ConnectionQualityUpdate {
	if x != nil {
		if x, ok := x.Message.(*SignalResponse_ConnectionQuality); ok {
			return x.ConnectionQuality
		}
	}
	return nil
}

func (x *SignalResponse) GetStreamStateUpdate() *StreamStateUpdate {
	if x != nil {
		if x, ok := x.Message.(*SignalResponse_StreamStateUpdate); ok {
			return x.StreamStateUpdate
		}
	}
	return nil
}

func (x *SignalResponse) GetSubscribedQualityUpdate() *SubscribedQualityUpdate {
	if x != nil {
		if x, ok := x.Message.(*SignalResponse_SubscribedQualityUpdate); ok {
			return x.SubscribedQualityUpdate
		}
	}
	return nil
}

func (x *SignalResponse) GetSubscriptionPermissionUpdate() *SubscriptionPermissionUpdate {
	if x != nil {
		if x, ok := x.Message.(*SignalResponse_SubscriptionPermissionUpdate); ok {
			return x.SubscriptionPermissionUpdate
		}
	}
	return nil
}

func (x *SignalResponse) GetRefreshToken() string {
	if x != nil {
		if x, ok := x.Message.(*SignalResponse_RefreshToken); ok {
			return x.RefreshToken
		}
	}
	return ""
}

func (x *SignalResponse) GetTrackUnpublished() *TrackUnpublishedResponse {
	if x != nil {
		if x, ok := x.Message.(*SignalResponse_TrackUnpublished); ok {
			return x.TrackUnpublished
		}
	}
	return nil
}

func (x *SignalResponse) GetPong() int64 {
	if x != nil {
		if x, ok := x.Message.(*SignalResponse_Pong); ok {
			return x.Pong
		}
	}
	return 0
}

func (x *SignalResponse) GetReconnect() *ReconnectResponse {
	if x != nil {
		if x, ok := x.Message.(*SignalResponse_Reconnect); ok {
			return x.Reconnect
		}
	}
	return nil
}

func (x *SignalResponse) GetPongResp() *Pong {
	if x != nil {
		if x, ok := x.Message.(*SignalResponse_PongResp); ok {
			return x.PongResp
		}
	}
	return nil
}

func (x *SignalResponse) GetSubscriptionResponse() *SubscriptionResponse {
	if x != nil {
		if x, ok := x.Message.(*SignalResponse_SubscriptionResponse); ok {
			return x.SubscriptionResponse
		}
	}
	return nil
}

func (x *SignalResponse) GetRequestResponse() *RequestResponse {
	if x != nil {
		if x, ok := x.Message.(*SignalResponse_RequestResponse); ok {
			return x.RequestResponse
		}
	}
	return nil
}

func (x *SignalResponse) GetTrackSubscribed() *TrackSubscribed {
	if x != nil {
		if x, ok := x.Message.(*SignalResponse_TrackSubscribed); ok {
			return x.TrackSubscribed
		}
	}
	return nil
}

func (x *SignalResponse) GetRoomMoved() *RoomMovedResponse {
	if x != nil {
		if x, ok := x.Message.(*SignalResponse_RoomMoved); ok {
			return x.RoomMoved
		}
	}
	return nil
}

type isSignalResponse_Message interface {
	isSignalResponse_Message()
}

type SignalResponse_Join struct {
	// sent when join is accepted
	Join *JoinResponse `protobuf:"bytes,1,opt,name=join,proto3,oneof"`
}

type SignalResponse_Answer struct {
	// sent when server answers publisher
	Answer *SessionDescription `protobuf:"bytes,2,opt,name=answer,proto3,oneof"`
}

type SignalResponse_Offer struct {
	// sent when server is sending subscriber an offer
	Offer *SessionDescription `protobuf:"bytes,3,opt,name=offer,proto3,oneof"`
}

type SignalResponse_Trickle struct {
	// sent when an ICE candidate is available
	Trickle *TrickleRequest `protobuf:"bytes,4,opt,name=trickle,proto3,oneof"`
}

type SignalResponse_Update struct {
	// sent when participants in the room has changed
	Update *ParticipantUpdate `protobuf:"bytes,5,opt,name=update,proto3,oneof"`
}

type SignalResponse_TrackPublished struct {
	// sent to the participant when their track has been published
	TrackPublished *TrackPublishedResponse `protobuf:"bytes,6,opt,name=track_published,json=trackPublished,proto3,oneof"`
}

type SignalResponse_Leave struct {
	// Immediately terminate session
	Leave *LeaveRequest `protobuf:"bytes,8,opt,name=leave,proto3,oneof"`
}

type SignalResponse_Mute struct {
	// server initiated mute
	Mute *MuteTrackRequest `protobuf:"bytes,9,opt,name=mute,proto3,oneof"`
}

type SignalResponse_SpeakersChanged struct {
	// indicates changes to speaker status, including when they've gone to not speaking
	SpeakersChanged *SpeakersChanged `protobuf:"bytes,10,opt,name=speakers_changed,json=speakersChanged,proto3,oneof"`
}

type SignalResponse_RoomUpdate struct {
	// sent when metadata of the room has changed
	RoomUpdate *RoomUpdate `protobuf:"bytes,11,opt,name=room_update,json=roomUpdate,proto3,oneof"`
}

type SignalResponse_ConnectionQuality struct {
	// when connection quality changed
	ConnectionQuality *ConnectionQualityUpdate `protobuf:"bytes,12,opt,name=connection_quality,json=connectionQuality,proto3,oneof"`
}

type SignalResponse_StreamStateUpdate struct {
	// when streamed tracks state changed, used to notify when any of the streams were paused due to
	// congestion
	StreamStateUpdate *StreamStateUpdate `protobuf:"bytes,13,opt,name=stream_state_update,json=streamStateUpdate,proto3,oneof"`
}

type SignalResponse_SubscribedQualityUpdate struct {
	// when max subscribe quality changed, used by dynamic broadcasting to disable unused layers
	SubscribedQualityUpdate *SubscribedQualityUpdate `protobuf:"bytes,14,opt,name=subscribed_quality_update,json=subscribedQualityUpdate,proto3,oneof"`
}

type SignalResponse_SubscriptionPermissionUpdate struct {
	// when subscription permission changed
	SubscriptionPermissionUpdate *SubscriptionPermissionUpdate `protobuf:"bytes,15,opt,name=subscription_permission_update,json=subscriptionPermissionUpdate,proto3,oneof"`
}

type SignalResponse_RefreshToken struct {
	// update the token the client was using, to prevent an active client from using an expired token
	RefreshToken string `protobuf:"bytes,16,opt,name=refresh_token,json=refreshToken,proto3,oneof"`
}

type SignalResponse_TrackUnpublished struct {
	// server initiated track unpublish
	TrackUnpublished *TrackUnpublishedResponse `protobuf:"bytes,17,opt,name=track_unpublished,json=trackUnpublished,proto3,oneof"`
}

type SignalResponse_Pong struct {
	// respond to ping
	Pong int64 `protobuf:"varint,18,opt,name=pong,proto3,oneof"` // deprecated by pong_resp (message Pong)
}

type SignalResponse_Reconnect struct {
	// sent when client reconnects
	Reconnect *ReconnectResponse `protobuf:"bytes,19,opt,name=reconnect,proto3,oneof"`
}

type SignalResponse_PongResp struct {
	// respond to Ping
	PongResp *Pong `protobuf:"bytes,20,opt,name=pong_resp,json=pongResp,proto3,oneof"`
}

type SignalResponse_SubscriptionResponse struct {
	// Subscription response, client should not expect any media from this subscription if it fails
	SubscriptionResponse *SubscriptionResponse `protobuf:"bytes,21,opt,name=subscription_response,json=subscriptionResponse,proto3,oneof"`
}

type SignalResponse_RequestResponse struct {
	// Response relating to user inititated requests that carry a `request_id`
	RequestResponse *RequestResponse `protobuf:"bytes,22,opt,name=request_response,json=requestResponse,proto3,oneof"`
}

type SignalResponse_TrackSubscribed struct {
	// notify to the publisher when a published track has been subscribed for the first time
	TrackSubscribed *TrackSubscribed `protobuf:"bytes,23,opt,name=track_subscribed,json=trackSubscribed,proto3,oneof"`
}

type SignalResponse_RoomMoved struct {
	// notify to the participant when they have been moved to a new room
	RoomMoved *RoomMovedResponse `protobuf:"bytes,24,opt,name=room_moved,json=roomMoved,proto3,oneof"`
}

func (*SignalResponse_Join) isSignalResponse_Message() {}

func (*SignalResponse_Answer) isSignalResponse_Message() {}

func (*SignalResponse_Offer) isSignalResponse_Message() {}

func (*SignalResponse_Trickle) isSignalResponse_Message() {}

func (*SignalResponse_Update) isSignalResponse_Message() {}

func (*SignalResponse_TrackPublished) isSignalResponse_Message() {}

func (*SignalResponse_Leave) isSignalResponse_Message() {}

func (*SignalResponse_Mute) isSignalResponse_Message() {}

func (*SignalResponse_SpeakersChanged) isSignalResponse_Message() {}

func (*SignalResponse_RoomUpdate) isSignalResponse_Message() {}

func (*SignalResponse_ConnectionQuality) isSignalResponse_Message() {}

func (*SignalResponse_StreamStateUpdate) isSignalResponse_Message() {}

func (*SignalResponse_SubscribedQualityUpdate) isSignalResponse_Message() {}

func (*SignalResponse_SubscriptionPermissionUpdate) isSignalResponse_Message() {}

func (*SignalResponse_RefreshToken) isSignalResponse_Message() {}

func (*SignalResponse_TrackUnpublished) isSignalResponse_Message() {}

func (*SignalResponse_Pong) isSignalResponse_Message() {}

func (*SignalResponse_Reconnect) isSignalResponse_Message() {}

func (*SignalResponse_PongResp) isSignalResponse_Message() {}

func (*SignalResponse_SubscriptionResponse) isSignalResponse_Message() {}

func (*SignalResponse_RequestResponse) isSignalResponse_Message() {}

func (*SignalResponse_TrackSubscribed) isSignalResponse_Message() {}

func (*SignalResponse_RoomMoved) isSignalResponse_Message() {}

type SimulcastCodec struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Codec         string                 `protobuf:"bytes,1,opt,name=codec,proto3" json:"codec,omitempty"`
	Cid           string                 `protobuf:"bytes,2,opt,name=cid,proto3" json:"cid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SimulcastCodec) Reset() {
	*x = SimulcastCodec{}
	mi := &file_livekit_rtc_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SimulcastCodec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimulcastCodec) ProtoMessage() {}

func (x *SimulcastCodec) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimulcastCodec.ProtoReflect.Descriptor instead.
func (*SimulcastCodec) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{2}
}

func (x *SimulcastCodec) GetCodec() string {
	if x != nil {
		return x.Codec
	}
	return ""
}

func (x *SimulcastCodec) GetCid() string {
	if x != nil {
		return x.Cid
	}
	return ""
}

type AddTrackRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// client ID of track, to match it when RTC track is received
	Cid  string    `protobuf:"bytes,1,opt,name=cid,proto3" json:"cid,omitempty"`
	Name string    `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type TrackType `protobuf:"varint,3,opt,name=type,proto3,enum=livekit.TrackType" json:"type,omitempty"`
	// to be deprecated in favor of layers
	Width  uint32 `protobuf:"varint,4,opt,name=width,proto3" json:"width,omitempty"`
	Height uint32 `protobuf:"varint,5,opt,name=height,proto3" json:"height,omitempty"`
	// true to add track and initialize to muted
	Muted bool `protobuf:"varint,6,opt,name=muted,proto3" json:"muted,omitempty"`
	// true if DTX (Discontinuous Transmission) is disabled for audio
	//
	// Deprecated: Marked as deprecated in livekit_rtc.proto.
	DisableDtx      bool              `protobuf:"varint,7,opt,name=disable_dtx,json=disableDtx,proto3" json:"disable_dtx,omitempty"` // deprecated in favor of audio_features
	Source          TrackSource       `protobuf:"varint,8,opt,name=source,proto3,enum=livekit.TrackSource" json:"source,omitempty"`
	Layers          []*VideoLayer     `protobuf:"bytes,9,rep,name=layers,proto3" json:"layers,omitempty"`
	SimulcastCodecs []*SimulcastCodec `protobuf:"bytes,10,rep,name=simulcast_codecs,json=simulcastCodecs,proto3" json:"simulcast_codecs,omitempty"`
	// server ID of track, publish new codec to exist track
	Sid string `protobuf:"bytes,11,opt,name=sid,proto3" json:"sid,omitempty"`
	// Deprecated: Marked as deprecated in livekit_rtc.proto.
	Stereo bool `protobuf:"varint,12,opt,name=stereo,proto3" json:"stereo,omitempty"` // deprecated in favor of audio_features
	// true if RED (Redundant Encoding) is disabled for audio
	DisableRed bool            `protobuf:"varint,13,opt,name=disable_red,json=disableRed,proto3" json:"disable_red,omitempty"`
	Encryption Encryption_Type `protobuf:"varint,14,opt,name=encryption,proto3,enum=livekit.Encryption_Type" json:"encryption,omitempty"`
	// which stream the track belongs to, used to group tracks together.
	// if not specified, server will infer it from track source to bundle camera/microphone, screenshare/audio together
	Stream            string              `protobuf:"bytes,15,opt,name=stream,proto3" json:"stream,omitempty"`
	BackupCodecPolicy BackupCodecPolicy   `protobuf:"varint,16,opt,name=backup_codec_policy,json=backupCodecPolicy,proto3,enum=livekit.BackupCodecPolicy" json:"backup_codec_policy,omitempty"`
	AudioFeatures     []AudioTrackFeature `protobuf:"varint,17,rep,packed,name=audio_features,json=audioFeatures,proto3,enum=livekit.AudioTrackFeature" json:"audio_features,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *AddTrackRequest) Reset() {
	*x = AddTrackRequest{}
	mi := &file_livekit_rtc_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddTrackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddTrackRequest) ProtoMessage() {}

func (x *AddTrackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddTrackRequest.ProtoReflect.Descriptor instead.
func (*AddTrackRequest) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{3}
}

func (x *AddTrackRequest) GetCid() string {
	if x != nil {
		return x.Cid
	}
	return ""
}

func (x *AddTrackRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AddTrackRequest) GetType() TrackType {
	if x != nil {
		return x.Type
	}
	return TrackType_AUDIO
}

func (x *AddTrackRequest) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *AddTrackRequest) GetHeight() uint32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *AddTrackRequest) GetMuted() bool {
	if x != nil {
		return x.Muted
	}
	return false
}

// Deprecated: Marked as deprecated in livekit_rtc.proto.
func (x *AddTrackRequest) GetDisableDtx() bool {
	if x != nil {
		return x.DisableDtx
	}
	return false
}

func (x *AddTrackRequest) GetSource() TrackSource {
	if x != nil {
		return x.Source
	}
	return TrackSource_UNKNOWN
}

func (x *AddTrackRequest) GetLayers() []*VideoLayer {
	if x != nil {
		return x.Layers
	}
	return nil
}

func (x *AddTrackRequest) GetSimulcastCodecs() []*SimulcastCodec {
	if x != nil {
		return x.SimulcastCodecs
	}
	return nil
}

func (x *AddTrackRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

// Deprecated: Marked as deprecated in livekit_rtc.proto.
func (x *AddTrackRequest) GetStereo() bool {
	if x != nil {
		return x.Stereo
	}
	return false
}

func (x *AddTrackRequest) GetDisableRed() bool {
	if x != nil {
		return x.DisableRed
	}
	return false
}

func (x *AddTrackRequest) GetEncryption() Encryption_Type {
	if x != nil {
		return x.Encryption
	}
	return Encryption_NONE
}

func (x *AddTrackRequest) GetStream() string {
	if x != nil {
		return x.Stream
	}
	return ""
}

func (x *AddTrackRequest) GetBackupCodecPolicy() BackupCodecPolicy {
	if x != nil {
		return x.BackupCodecPolicy
	}
	return BackupCodecPolicy_PREFER_REGRESSION
}

func (x *AddTrackRequest) GetAudioFeatures() []AudioTrackFeature {
	if x != nil {
		return x.AudioFeatures
	}
	return nil
}

type TrickleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CandidateInit string                 `protobuf:"bytes,1,opt,name=candidateInit,proto3" json:"candidateInit,omitempty"`
	Target        SignalTarget           `protobuf:"varint,2,opt,name=target,proto3,enum=livekit.SignalTarget" json:"target,omitempty"`
	Final         bool                   `protobuf:"varint,3,opt,name=final,proto3" json:"final,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrickleRequest) Reset() {
	*x = TrickleRequest{}
	mi := &file_livekit_rtc_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrickleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrickleRequest) ProtoMessage() {}

func (x *TrickleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrickleRequest.ProtoReflect.Descriptor instead.
func (*TrickleRequest) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{4}
}

func (x *TrickleRequest) GetCandidateInit() string {
	if x != nil {
		return x.CandidateInit
	}
	return ""
}

func (x *TrickleRequest) GetTarget() SignalTarget {
	if x != nil {
		return x.Target
	}
	return SignalTarget_PUBLISHER
}

func (x *TrickleRequest) GetFinal() bool {
	if x != nil {
		return x.Final
	}
	return false
}

type MuteTrackRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Sid           string                 `protobuf:"bytes,1,opt,name=sid,proto3" json:"sid,omitempty"`
	Muted         bool                   `protobuf:"varint,2,opt,name=muted,proto3" json:"muted,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MuteTrackRequest) Reset() {
	*x = MuteTrackRequest{}
	mi := &file_livekit_rtc_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MuteTrackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MuteTrackRequest) ProtoMessage() {}

func (x *MuteTrackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MuteTrackRequest.ProtoReflect.Descriptor instead.
func (*MuteTrackRequest) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{5}
}

func (x *MuteTrackRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *MuteTrackRequest) GetMuted() bool {
	if x != nil {
		return x.Muted
	}
	return false
}

type JoinResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Room              *Room                  `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	Participant       *ParticipantInfo       `protobuf:"bytes,2,opt,name=participant,proto3" json:"participant,omitempty"`
	OtherParticipants []*ParticipantInfo     `protobuf:"bytes,3,rep,name=other_participants,json=otherParticipants,proto3" json:"other_participants,omitempty"`
	// deprecated. use server_info.version instead.
	ServerVersion string       `protobuf:"bytes,4,opt,name=server_version,json=serverVersion,proto3" json:"server_version,omitempty"`
	IceServers    []*ICEServer `protobuf:"bytes,5,rep,name=ice_servers,json=iceServers,proto3" json:"ice_servers,omitempty"`
	// use subscriber as the primary PeerConnection
	SubscriberPrimary bool `protobuf:"varint,6,opt,name=subscriber_primary,json=subscriberPrimary,proto3" json:"subscriber_primary,omitempty"`
	// when the current server isn't available, return alternate url to retry connection
	// when this is set, the other fields will be largely empty
	AlternativeUrl      string               `protobuf:"bytes,7,opt,name=alternative_url,json=alternativeUrl,proto3" json:"alternative_url,omitempty"`
	ClientConfiguration *ClientConfiguration `protobuf:"bytes,8,opt,name=client_configuration,json=clientConfiguration,proto3" json:"client_configuration,omitempty"`
	// deprecated. use server_info.region instead.
	ServerRegion string      `protobuf:"bytes,9,opt,name=server_region,json=serverRegion,proto3" json:"server_region,omitempty"`
	PingTimeout  int32       `protobuf:"varint,10,opt,name=ping_timeout,json=pingTimeout,proto3" json:"ping_timeout,omitempty"`
	PingInterval int32       `protobuf:"varint,11,opt,name=ping_interval,json=pingInterval,proto3" json:"ping_interval,omitempty"`
	ServerInfo   *ServerInfo `protobuf:"bytes,12,opt,name=server_info,json=serverInfo,proto3" json:"server_info,omitempty"`
	// Server-Injected-Frame byte trailer, used to identify unencrypted frames when e2ee is enabled
	SifTrailer           []byte   `protobuf:"bytes,13,opt,name=sif_trailer,json=sifTrailer,proto3" json:"sif_trailer,omitempty"`
	EnabledPublishCodecs []*Codec `protobuf:"bytes,14,rep,name=enabled_publish_codecs,json=enabledPublishCodecs,proto3" json:"enabled_publish_codecs,omitempty"`
	// when set, client should attempt to establish publish peer connection when joining room to speed up publishing
	FastPublish   bool `protobuf:"varint,15,opt,name=fast_publish,json=fastPublish,proto3" json:"fast_publish,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JoinResponse) Reset() {
	*x = JoinResponse{}
	mi := &file_livekit_rtc_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JoinResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinResponse) ProtoMessage() {}

func (x *JoinResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinResponse.ProtoReflect.Descriptor instead.
func (*JoinResponse) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{6}
}

func (x *JoinResponse) GetRoom() *Room {
	if x != nil {
		return x.Room
	}
	return nil
}

func (x *JoinResponse) GetParticipant() *ParticipantInfo {
	if x != nil {
		return x.Participant
	}
	return nil
}

func (x *JoinResponse) GetOtherParticipants() []*ParticipantInfo {
	if x != nil {
		return x.OtherParticipants
	}
	return nil
}

func (x *JoinResponse) GetServerVersion() string {
	if x != nil {
		return x.ServerVersion
	}
	return ""
}

func (x *JoinResponse) GetIceServers() []*ICEServer {
	if x != nil {
		return x.IceServers
	}
	return nil
}

func (x *JoinResponse) GetSubscriberPrimary() bool {
	if x != nil {
		return x.SubscriberPrimary
	}
	return false
}

func (x *JoinResponse) GetAlternativeUrl() string {
	if x != nil {
		return x.AlternativeUrl
	}
	return ""
}

func (x *JoinResponse) GetClientConfiguration() *ClientConfiguration {
	if x != nil {
		return x.ClientConfiguration
	}
	return nil
}

func (x *JoinResponse) GetServerRegion() string {
	if x != nil {
		return x.ServerRegion
	}
	return ""
}

func (x *JoinResponse) GetPingTimeout() int32 {
	if x != nil {
		return x.PingTimeout
	}
	return 0
}

func (x *JoinResponse) GetPingInterval() int32 {
	if x != nil {
		return x.PingInterval
	}
	return 0
}

func (x *JoinResponse) GetServerInfo() *ServerInfo {
	if x != nil {
		return x.ServerInfo
	}
	return nil
}

func (x *JoinResponse) GetSifTrailer() []byte {
	if x != nil {
		return x.SifTrailer
	}
	return nil
}

func (x *JoinResponse) GetEnabledPublishCodecs() []*Codec {
	if x != nil {
		return x.EnabledPublishCodecs
	}
	return nil
}

func (x *JoinResponse) GetFastPublish() bool {
	if x != nil {
		return x.FastPublish
	}
	return false
}

type ReconnectResponse struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	IceServers          []*ICEServer           `protobuf:"bytes,1,rep,name=ice_servers,json=iceServers,proto3" json:"ice_servers,omitempty"`
	ClientConfiguration *ClientConfiguration   `protobuf:"bytes,2,opt,name=client_configuration,json=clientConfiguration,proto3" json:"client_configuration,omitempty"`
	ServerInfo          *ServerInfo            `protobuf:"bytes,3,opt,name=server_info,json=serverInfo,proto3" json:"server_info,omitempty"`
	// last sequence number of reliable message received before resuming
	LastMessageSeq uint32 `protobuf:"varint,4,opt,name=last_message_seq,json=lastMessageSeq,proto3" json:"last_message_seq,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ReconnectResponse) Reset() {
	*x = ReconnectResponse{}
	mi := &file_livekit_rtc_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReconnectResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReconnectResponse) ProtoMessage() {}

func (x *ReconnectResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReconnectResponse.ProtoReflect.Descriptor instead.
func (*ReconnectResponse) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{7}
}

func (x *ReconnectResponse) GetIceServers() []*ICEServer {
	if x != nil {
		return x.IceServers
	}
	return nil
}

func (x *ReconnectResponse) GetClientConfiguration() *ClientConfiguration {
	if x != nil {
		return x.ClientConfiguration
	}
	return nil
}

func (x *ReconnectResponse) GetServerInfo() *ServerInfo {
	if x != nil {
		return x.ServerInfo
	}
	return nil
}

func (x *ReconnectResponse) GetLastMessageSeq() uint32 {
	if x != nil {
		return x.LastMessageSeq
	}
	return 0
}

type TrackPublishedResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Cid           string                 `protobuf:"bytes,1,opt,name=cid,proto3" json:"cid,omitempty"`
	Track         *TrackInfo             `protobuf:"bytes,2,opt,name=track,proto3" json:"track,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrackPublishedResponse) Reset() {
	*x = TrackPublishedResponse{}
	mi := &file_livekit_rtc_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrackPublishedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackPublishedResponse) ProtoMessage() {}

func (x *TrackPublishedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackPublishedResponse.ProtoReflect.Descriptor instead.
func (*TrackPublishedResponse) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{8}
}

func (x *TrackPublishedResponse) GetCid() string {
	if x != nil {
		return x.Cid
	}
	return ""
}

func (x *TrackPublishedResponse) GetTrack() *TrackInfo {
	if x != nil {
		return x.Track
	}
	return nil
}

type TrackUnpublishedResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TrackSid      string                 `protobuf:"bytes,1,opt,name=track_sid,json=trackSid,proto3" json:"track_sid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrackUnpublishedResponse) Reset() {
	*x = TrackUnpublishedResponse{}
	mi := &file_livekit_rtc_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrackUnpublishedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackUnpublishedResponse) ProtoMessage() {}

func (x *TrackUnpublishedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackUnpublishedResponse.ProtoReflect.Descriptor instead.
func (*TrackUnpublishedResponse) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{9}
}

func (x *TrackUnpublishedResponse) GetTrackSid() string {
	if x != nil {
		return x.TrackSid
	}
	return ""
}

type SessionDescription struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"` // "answer" | "offer" | "pranswer" | "rollback"
	Sdp           string                 `protobuf:"bytes,2,opt,name=sdp,proto3" json:"sdp,omitempty"`
	Id            uint32                 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SessionDescription) Reset() {
	*x = SessionDescription{}
	mi := &file_livekit_rtc_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SessionDescription) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SessionDescription) ProtoMessage() {}

func (x *SessionDescription) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SessionDescription.ProtoReflect.Descriptor instead.
func (*SessionDescription) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{10}
}

func (x *SessionDescription) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *SessionDescription) GetSdp() string {
	if x != nil {
		return x.Sdp
	}
	return ""
}

func (x *SessionDescription) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ParticipantUpdate struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Participants  []*ParticipantInfo     `protobuf:"bytes,1,rep,name=participants,proto3" json:"participants,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ParticipantUpdate) Reset() {
	*x = ParticipantUpdate{}
	mi := &file_livekit_rtc_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParticipantUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParticipantUpdate) ProtoMessage() {}

func (x *ParticipantUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParticipantUpdate.ProtoReflect.Descriptor instead.
func (*ParticipantUpdate) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{11}
}

func (x *ParticipantUpdate) GetParticipants() []*ParticipantInfo {
	if x != nil {
		return x.Participants
	}
	return nil
}

type RemoteParticipantUpdate struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Participants  []*RemoteParticipantInfo `protobuf:"bytes,1,rep,name=participants,proto3" json:"participants,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoteParticipantUpdate) Reset() {
	*x = RemoteParticipantUpdate{}
	mi := &file_livekit_rtc_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoteParticipantUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoteParticipantUpdate) ProtoMessage() {}

func (x *RemoteParticipantUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoteParticipantUpdate.ProtoReflect.Descriptor instead.
func (*RemoteParticipantUpdate) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{12}
}

func (x *RemoteParticipantUpdate) GetParticipants() []*RemoteParticipantInfo {
	if x != nil {
		return x.Participants
	}
	return nil
}

type UpdateSubscription struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	TrackSids         []string               `protobuf:"bytes,1,rep,name=track_sids,json=trackSids,proto3" json:"track_sids,omitempty"`
	Subscribe         bool                   `protobuf:"varint,2,opt,name=subscribe,proto3" json:"subscribe,omitempty"`
	ParticipantTracks []*ParticipantTracks   `protobuf:"bytes,3,rep,name=participant_tracks,json=participantTracks,proto3" json:"participant_tracks,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *UpdateSubscription) Reset() {
	*x = UpdateSubscription{}
	mi := &file_livekit_rtc_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateSubscription) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSubscription) ProtoMessage() {}

func (x *UpdateSubscription) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSubscription.ProtoReflect.Descriptor instead.
func (*UpdateSubscription) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{13}
}

func (x *UpdateSubscription) GetTrackSids() []string {
	if x != nil {
		return x.TrackSids
	}
	return nil
}

func (x *UpdateSubscription) GetSubscribe() bool {
	if x != nil {
		return x.Subscribe
	}
	return false
}

func (x *UpdateSubscription) GetParticipantTracks() []*ParticipantTracks {
	if x != nil {
		return x.ParticipantTracks
	}
	return nil
}

type UpdateTrackSettings struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	TrackSids []string               `protobuf:"bytes,1,rep,name=track_sids,json=trackSids,proto3" json:"track_sids,omitempty"`
	// when true, the track is placed in a paused state, with no new data returned
	Disabled bool `protobuf:"varint,3,opt,name=disabled,proto3" json:"disabled,omitempty"`
	// deprecated in favor of width & height
	Quality VideoQuality `protobuf:"varint,4,opt,name=quality,proto3,enum=livekit.VideoQuality" json:"quality,omitempty"`
	// for video, width to receive
	Width uint32 `protobuf:"varint,5,opt,name=width,proto3" json:"width,omitempty"`
	// for video, height to receive
	Height uint32 `protobuf:"varint,6,opt,name=height,proto3" json:"height,omitempty"`
	Fps    uint32 `protobuf:"varint,7,opt,name=fps,proto3" json:"fps,omitempty"`
	// subscription priority. 1 being the highest (0 is unset)
	// when unset, server sill assign priority based on the order of subscription
	// server will use priority in the following ways:
	//  1. when subscribed tracks exceed per-participant subscription limit, server will
	//     pause the lowest priority tracks
	//  2. when the network is congested, server will assign available bandwidth to
	//     higher priority tracks first. lowest priority tracks can be paused
	Priority      uint32 `protobuf:"varint,8,opt,name=priority,proto3" json:"priority,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateTrackSettings) Reset() {
	*x = UpdateTrackSettings{}
	mi := &file_livekit_rtc_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTrackSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTrackSettings) ProtoMessage() {}

func (x *UpdateTrackSettings) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTrackSettings.ProtoReflect.Descriptor instead.
func (*UpdateTrackSettings) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateTrackSettings) GetTrackSids() []string {
	if x != nil {
		return x.TrackSids
	}
	return nil
}

func (x *UpdateTrackSettings) GetDisabled() bool {
	if x != nil {
		return x.Disabled
	}
	return false
}

func (x *UpdateTrackSettings) GetQuality() VideoQuality {
	if x != nil {
		return x.Quality
	}
	return VideoQuality_LOW
}

func (x *UpdateTrackSettings) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *UpdateTrackSettings) GetHeight() uint32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *UpdateTrackSettings) GetFps() uint32 {
	if x != nil {
		return x.Fps
	}
	return 0
}

func (x *UpdateTrackSettings) GetPriority() uint32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

type UpdateLocalAudioTrack struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TrackSid      string                 `protobuf:"bytes,1,opt,name=track_sid,json=trackSid,proto3" json:"track_sid,omitempty"`
	Features      []AudioTrackFeature    `protobuf:"varint,2,rep,packed,name=features,proto3,enum=livekit.AudioTrackFeature" json:"features,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateLocalAudioTrack) Reset() {
	*x = UpdateLocalAudioTrack{}
	mi := &file_livekit_rtc_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateLocalAudioTrack) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLocalAudioTrack) ProtoMessage() {}

func (x *UpdateLocalAudioTrack) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLocalAudioTrack.ProtoReflect.Descriptor instead.
func (*UpdateLocalAudioTrack) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateLocalAudioTrack) GetTrackSid() string {
	if x != nil {
		return x.TrackSid
	}
	return ""
}

func (x *UpdateLocalAudioTrack) GetFeatures() []AudioTrackFeature {
	if x != nil {
		return x.Features
	}
	return nil
}

type UpdateLocalVideoTrack struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TrackSid      string                 `protobuf:"bytes,1,opt,name=track_sid,json=trackSid,proto3" json:"track_sid,omitempty"`
	Width         uint32                 `protobuf:"varint,2,opt,name=width,proto3" json:"width,omitempty"`
	Height        uint32                 `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateLocalVideoTrack) Reset() {
	*x = UpdateLocalVideoTrack{}
	mi := &file_livekit_rtc_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateLocalVideoTrack) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLocalVideoTrack) ProtoMessage() {}

func (x *UpdateLocalVideoTrack) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLocalVideoTrack.ProtoReflect.Descriptor instead.
func (*UpdateLocalVideoTrack) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{16}
}

func (x *UpdateLocalVideoTrack) GetTrackSid() string {
	if x != nil {
		return x.TrackSid
	}
	return ""
}

func (x *UpdateLocalVideoTrack) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *UpdateLocalVideoTrack) GetHeight() uint32 {
	if x != nil {
		return x.Height
	}
	return 0
}

type LeaveRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// sent when server initiates the disconnect due to server-restart
	// indicates clients should attempt full-reconnect sequence
	// NOTE: `can_reconnect` obsoleted by `action` starting in protocol version 13
	CanReconnect  bool                `protobuf:"varint,1,opt,name=can_reconnect,json=canReconnect,proto3" json:"can_reconnect,omitempty"`
	Reason        DisconnectReason    `protobuf:"varint,2,opt,name=reason,proto3,enum=livekit.DisconnectReason" json:"reason,omitempty"`
	Action        LeaveRequest_Action `protobuf:"varint,3,opt,name=action,proto3,enum=livekit.LeaveRequest_Action" json:"action,omitempty"`
	Regions       *RegionSettings     `protobuf:"bytes,4,opt,name=regions,proto3" json:"regions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LeaveRequest) Reset() {
	*x = LeaveRequest{}
	mi := &file_livekit_rtc_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LeaveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeaveRequest) ProtoMessage() {}

func (x *LeaveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeaveRequest.ProtoReflect.Descriptor instead.
func (*LeaveRequest) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{17}
}

func (x *LeaveRequest) GetCanReconnect() bool {
	if x != nil {
		return x.CanReconnect
	}
	return false
}

func (x *LeaveRequest) GetReason() DisconnectReason {
	if x != nil {
		return x.Reason
	}
	return DisconnectReason_UNKNOWN_REASON
}

func (x *LeaveRequest) GetAction() LeaveRequest_Action {
	if x != nil {
		return x.Action
	}
	return LeaveRequest_DISCONNECT
}

func (x *LeaveRequest) GetRegions() *RegionSettings {
	if x != nil {
		return x.Regions
	}
	return nil
}

// message to indicate published video track dimensions are changing
//
// Deprecated: Marked as deprecated in livekit_rtc.proto.
type UpdateVideoLayers struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TrackSid      string                 `protobuf:"bytes,1,opt,name=track_sid,json=trackSid,proto3" json:"track_sid,omitempty"`
	Layers        []*VideoLayer          `protobuf:"bytes,2,rep,name=layers,proto3" json:"layers,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateVideoLayers) Reset() {
	*x = UpdateVideoLayers{}
	mi := &file_livekit_rtc_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateVideoLayers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVideoLayers) ProtoMessage() {}

func (x *UpdateVideoLayers) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVideoLayers.ProtoReflect.Descriptor instead.
func (*UpdateVideoLayers) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{18}
}

func (x *UpdateVideoLayers) GetTrackSid() string {
	if x != nil {
		return x.TrackSid
	}
	return ""
}

func (x *UpdateVideoLayers) GetLayers() []*VideoLayer {
	if x != nil {
		return x.Layers
	}
	return nil
}

type UpdateParticipantMetadata struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	Metadata string                 `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Name     string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// attributes to update. it only updates attributes that have been set
	// to delete attributes, set the value to an empty string
	Attributes    map[string]string `protobuf:"bytes,3,rep,name=attributes,proto3" json:"attributes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	RequestId     uint32            `protobuf:"varint,4,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateParticipantMetadata) Reset() {
	*x = UpdateParticipantMetadata{}
	mi := &file_livekit_rtc_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateParticipantMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateParticipantMetadata) ProtoMessage() {}

func (x *UpdateParticipantMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateParticipantMetadata.ProtoReflect.Descriptor instead.
func (*UpdateParticipantMetadata) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{19}
}

func (x *UpdateParticipantMetadata) GetMetadata() string {
	if x != nil {
		return x.Metadata
	}
	return ""
}

func (x *UpdateParticipantMetadata) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateParticipantMetadata) GetAttributes() map[string]string {
	if x != nil {
		return x.Attributes
	}
	return nil
}

func (x *UpdateParticipantMetadata) GetRequestId() uint32 {
	if x != nil {
		return x.RequestId
	}
	return 0
}

type ICEServer struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Urls          []string               `protobuf:"bytes,1,rep,name=urls,proto3" json:"urls,omitempty"`
	Username      string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Credential    string                 `protobuf:"bytes,3,opt,name=credential,proto3" json:"credential,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ICEServer) Reset() {
	*x = ICEServer{}
	mi := &file_livekit_rtc_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ICEServer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ICEServer) ProtoMessage() {}

func (x *ICEServer) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ICEServer.ProtoReflect.Descriptor instead.
func (*ICEServer) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{20}
}

func (x *ICEServer) GetUrls() []string {
	if x != nil {
		return x.Urls
	}
	return nil
}

func (x *ICEServer) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ICEServer) GetCredential() string {
	if x != nil {
		return x.Credential
	}
	return ""
}

type SpeakersChanged struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Speakers      []*SpeakerInfo         `protobuf:"bytes,1,rep,name=speakers,proto3" json:"speakers,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SpeakersChanged) Reset() {
	*x = SpeakersChanged{}
	mi := &file_livekit_rtc_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SpeakersChanged) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpeakersChanged) ProtoMessage() {}

func (x *SpeakersChanged) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpeakersChanged.ProtoReflect.Descriptor instead.
func (*SpeakersChanged) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{21}
}

func (x *SpeakersChanged) GetSpeakers() []*SpeakerInfo {
	if x != nil {
		return x.Speakers
	}
	return nil
}

type RoomUpdate struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Room          *Room                  `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RoomUpdate) Reset() {
	*x = RoomUpdate{}
	mi := &file_livekit_rtc_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoomUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomUpdate) ProtoMessage() {}

func (x *RoomUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomUpdate.ProtoReflect.Descriptor instead.
func (*RoomUpdate) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{22}
}

func (x *RoomUpdate) GetRoom() *Room {
	if x != nil {
		return x.Room
	}
	return nil
}

type ConnectionQualityInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ParticipantSid string                 `protobuf:"bytes,1,opt,name=participant_sid,json=participantSid,proto3" json:"participant_sid,omitempty"`
	Quality        ConnectionQuality      `protobuf:"varint,2,opt,name=quality,proto3,enum=livekit.ConnectionQuality" json:"quality,omitempty"`
	Score          float32                `protobuf:"fixed32,3,opt,name=score,proto3" json:"score,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ConnectionQualityInfo) Reset() {
	*x = ConnectionQualityInfo{}
	mi := &file_livekit_rtc_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConnectionQualityInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectionQualityInfo) ProtoMessage() {}

func (x *ConnectionQualityInfo) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectionQualityInfo.ProtoReflect.Descriptor instead.
func (*ConnectionQualityInfo) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{23}
}

func (x *ConnectionQualityInfo) GetParticipantSid() string {
	if x != nil {
		return x.ParticipantSid
	}
	return ""
}

func (x *ConnectionQualityInfo) GetQuality() ConnectionQuality {
	if x != nil {
		return x.Quality
	}
	return ConnectionQuality_POOR
}

func (x *ConnectionQualityInfo) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

type ConnectionQualityUpdate struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Updates       []*ConnectionQualityInfo `protobuf:"bytes,1,rep,name=updates,proto3" json:"updates,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConnectionQualityUpdate) Reset() {
	*x = ConnectionQualityUpdate{}
	mi := &file_livekit_rtc_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConnectionQualityUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectionQualityUpdate) ProtoMessage() {}

func (x *ConnectionQualityUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectionQualityUpdate.ProtoReflect.Descriptor instead.
func (*ConnectionQualityUpdate) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{24}
}

func (x *ConnectionQualityUpdate) GetUpdates() []*ConnectionQualityInfo {
	if x != nil {
		return x.Updates
	}
	return nil
}

type StreamStateInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ParticipantSid string                 `protobuf:"bytes,1,opt,name=participant_sid,json=participantSid,proto3" json:"participant_sid,omitempty"`
	TrackSid       string                 `protobuf:"bytes,2,opt,name=track_sid,json=trackSid,proto3" json:"track_sid,omitempty"`
	State          StreamState            `protobuf:"varint,3,opt,name=state,proto3,enum=livekit.StreamState" json:"state,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *StreamStateInfo) Reset() {
	*x = StreamStateInfo{}
	mi := &file_livekit_rtc_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamStateInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamStateInfo) ProtoMessage() {}

func (x *StreamStateInfo) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamStateInfo.ProtoReflect.Descriptor instead.
func (*StreamStateInfo) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{25}
}

func (x *StreamStateInfo) GetParticipantSid() string {
	if x != nil {
		return x.ParticipantSid
	}
	return ""
}

func (x *StreamStateInfo) GetTrackSid() string {
	if x != nil {
		return x.TrackSid
	}
	return ""
}

func (x *StreamStateInfo) GetState() StreamState {
	if x != nil {
		return x.State
	}
	return StreamState_ACTIVE
}

type StreamStateUpdate struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamStates  []*StreamStateInfo     `protobuf:"bytes,1,rep,name=stream_states,json=streamStates,proto3" json:"stream_states,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamStateUpdate) Reset() {
	*x = StreamStateUpdate{}
	mi := &file_livekit_rtc_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamStateUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamStateUpdate) ProtoMessage() {}

func (x *StreamStateUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamStateUpdate.ProtoReflect.Descriptor instead.
func (*StreamStateUpdate) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{26}
}

func (x *StreamStateUpdate) GetStreamStates() []*StreamStateInfo {
	if x != nil {
		return x.StreamStates
	}
	return nil
}

type SubscribedQuality struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Quality       VideoQuality           `protobuf:"varint,1,opt,name=quality,proto3,enum=livekit.VideoQuality" json:"quality,omitempty"`
	Enabled       bool                   `protobuf:"varint,2,opt,name=enabled,proto3" json:"enabled,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubscribedQuality) Reset() {
	*x = SubscribedQuality{}
	mi := &file_livekit_rtc_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribedQuality) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribedQuality) ProtoMessage() {}

func (x *SubscribedQuality) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribedQuality.ProtoReflect.Descriptor instead.
func (*SubscribedQuality) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{27}
}

func (x *SubscribedQuality) GetQuality() VideoQuality {
	if x != nil {
		return x.Quality
	}
	return VideoQuality_LOW
}

func (x *SubscribedQuality) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type SubscribedCodec struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Codec         string                 `protobuf:"bytes,1,opt,name=codec,proto3" json:"codec,omitempty"`
	Qualities     []*SubscribedQuality   `protobuf:"bytes,2,rep,name=qualities,proto3" json:"qualities,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubscribedCodec) Reset() {
	*x = SubscribedCodec{}
	mi := &file_livekit_rtc_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribedCodec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribedCodec) ProtoMessage() {}

func (x *SubscribedCodec) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribedCodec.ProtoReflect.Descriptor instead.
func (*SubscribedCodec) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{28}
}

func (x *SubscribedCodec) GetCodec() string {
	if x != nil {
		return x.Codec
	}
	return ""
}

func (x *SubscribedCodec) GetQualities() []*SubscribedQuality {
	if x != nil {
		return x.Qualities
	}
	return nil
}

type SubscribedQualityUpdate struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	TrackSid string                 `protobuf:"bytes,1,opt,name=track_sid,json=trackSid,proto3" json:"track_sid,omitempty"`
	// Deprecated: Marked as deprecated in livekit_rtc.proto.
	SubscribedQualities []*SubscribedQuality `protobuf:"bytes,2,rep,name=subscribed_qualities,json=subscribedQualities,proto3" json:"subscribed_qualities,omitempty"`
	SubscribedCodecs    []*SubscribedCodec   `protobuf:"bytes,3,rep,name=subscribed_codecs,json=subscribedCodecs,proto3" json:"subscribed_codecs,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *SubscribedQualityUpdate) Reset() {
	*x = SubscribedQualityUpdate{}
	mi := &file_livekit_rtc_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribedQualityUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribedQualityUpdate) ProtoMessage() {}

func (x *SubscribedQualityUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribedQualityUpdate.ProtoReflect.Descriptor instead.
func (*SubscribedQualityUpdate) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{29}
}

func (x *SubscribedQualityUpdate) GetTrackSid() string {
	if x != nil {
		return x.TrackSid
	}
	return ""
}

// Deprecated: Marked as deprecated in livekit_rtc.proto.
func (x *SubscribedQualityUpdate) GetSubscribedQualities() []*SubscribedQuality {
	if x != nil {
		return x.SubscribedQualities
	}
	return nil
}

func (x *SubscribedQualityUpdate) GetSubscribedCodecs() []*SubscribedCodec {
	if x != nil {
		return x.SubscribedCodecs
	}
	return nil
}

type TrackPermission struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// permission could be granted either by participant sid or identity
	ParticipantSid      string   `protobuf:"bytes,1,opt,name=participant_sid,json=participantSid,proto3" json:"participant_sid,omitempty"`
	AllTracks           bool     `protobuf:"varint,2,opt,name=all_tracks,json=allTracks,proto3" json:"all_tracks,omitempty"`
	TrackSids           []string `protobuf:"bytes,3,rep,name=track_sids,json=trackSids,proto3" json:"track_sids,omitempty"`
	ParticipantIdentity string   `protobuf:"bytes,4,opt,name=participant_identity,json=participantIdentity,proto3" json:"participant_identity,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *TrackPermission) Reset() {
	*x = TrackPermission{}
	mi := &file_livekit_rtc_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrackPermission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackPermission) ProtoMessage() {}

func (x *TrackPermission) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackPermission.ProtoReflect.Descriptor instead.
func (*TrackPermission) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{30}
}

func (x *TrackPermission) GetParticipantSid() string {
	if x != nil {
		return x.ParticipantSid
	}
	return ""
}

func (x *TrackPermission) GetAllTracks() bool {
	if x != nil {
		return x.AllTracks
	}
	return false
}

func (x *TrackPermission) GetTrackSids() []string {
	if x != nil {
		return x.TrackSids
	}
	return nil
}

func (x *TrackPermission) GetParticipantIdentity() string {
	if x != nil {
		return x.ParticipantIdentity
	}
	return ""
}

type SubscriptionPermission struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	AllParticipants  bool                   `protobuf:"varint,1,opt,name=all_participants,json=allParticipants,proto3" json:"all_participants,omitempty"`
	TrackPermissions []*TrackPermission     `protobuf:"bytes,2,rep,name=track_permissions,json=trackPermissions,proto3" json:"track_permissions,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *SubscriptionPermission) Reset() {
	*x = SubscriptionPermission{}
	mi := &file_livekit_rtc_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscriptionPermission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionPermission) ProtoMessage() {}

func (x *SubscriptionPermission) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionPermission.ProtoReflect.Descriptor instead.
func (*SubscriptionPermission) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{31}
}

func (x *SubscriptionPermission) GetAllParticipants() bool {
	if x != nil {
		return x.AllParticipants
	}
	return false
}

func (x *SubscriptionPermission) GetTrackPermissions() []*TrackPermission {
	if x != nil {
		return x.TrackPermissions
	}
	return nil
}

type SubscriptionPermissionUpdate struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ParticipantSid string                 `protobuf:"bytes,1,opt,name=participant_sid,json=participantSid,proto3" json:"participant_sid,omitempty"`
	TrackSid       string                 `protobuf:"bytes,2,opt,name=track_sid,json=trackSid,proto3" json:"track_sid,omitempty"`
	Allowed        bool                   `protobuf:"varint,3,opt,name=allowed,proto3" json:"allowed,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SubscriptionPermissionUpdate) Reset() {
	*x = SubscriptionPermissionUpdate{}
	mi := &file_livekit_rtc_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscriptionPermissionUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionPermissionUpdate) ProtoMessage() {}

func (x *SubscriptionPermissionUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionPermissionUpdate.ProtoReflect.Descriptor instead.
func (*SubscriptionPermissionUpdate) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{32}
}

func (x *SubscriptionPermissionUpdate) GetParticipantSid() string {
	if x != nil {
		return x.ParticipantSid
	}
	return ""
}

func (x *SubscriptionPermissionUpdate) GetTrackSid() string {
	if x != nil {
		return x.TrackSid
	}
	return ""
}

func (x *SubscriptionPermissionUpdate) GetAllowed() bool {
	if x != nil {
		return x.Allowed
	}
	return false
}

type RoomMovedResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// information about the new room
	Room *Room `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	// new reconnect token that can be used to reconnect to the new room
	Token             string             `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	Participant       *ParticipantInfo   `protobuf:"bytes,3,opt,name=participant,proto3" json:"participant,omitempty"`
	OtherParticipants []*ParticipantInfo `protobuf:"bytes,4,rep,name=other_participants,json=otherParticipants,proto3" json:"other_participants,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *RoomMovedResponse) Reset() {
	*x = RoomMovedResponse{}
	mi := &file_livekit_rtc_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoomMovedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomMovedResponse) ProtoMessage() {}

func (x *RoomMovedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomMovedResponse.ProtoReflect.Descriptor instead.
func (*RoomMovedResponse) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{33}
}

func (x *RoomMovedResponse) GetRoom() *Room {
	if x != nil {
		return x.Room
	}
	return nil
}

func (x *RoomMovedResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *RoomMovedResponse) GetParticipant() *ParticipantInfo {
	if x != nil {
		return x.Participant
	}
	return nil
}

func (x *RoomMovedResponse) GetOtherParticipants() []*ParticipantInfo {
	if x != nil {
		return x.OtherParticipants
	}
	return nil
}

type SyncState struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// last subscribe answer before reconnecting
	Answer        *SessionDescription       `protobuf:"bytes,1,opt,name=answer,proto3" json:"answer,omitempty"`
	Subscription  *UpdateSubscription       `protobuf:"bytes,2,opt,name=subscription,proto3" json:"subscription,omitempty"`
	PublishTracks []*TrackPublishedResponse `protobuf:"bytes,3,rep,name=publish_tracks,json=publishTracks,proto3" json:"publish_tracks,omitempty"`
	DataChannels  []*DataChannelInfo        `protobuf:"bytes,4,rep,name=data_channels,json=dataChannels,proto3" json:"data_channels,omitempty"`
	// last received server side offer before reconnecting
	Offer                    *SessionDescription        `protobuf:"bytes,5,opt,name=offer,proto3" json:"offer,omitempty"`
	TrackSidsDisabled        []string                   `protobuf:"bytes,6,rep,name=track_sids_disabled,json=trackSidsDisabled,proto3" json:"track_sids_disabled,omitempty"`
	DatachannelReceiveStates []*DataChannelReceiveState `protobuf:"bytes,7,rep,name=datachannel_receive_states,json=datachannelReceiveStates,proto3" json:"datachannel_receive_states,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *SyncState) Reset() {
	*x = SyncState{}
	mi := &file_livekit_rtc_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncState) ProtoMessage() {}

func (x *SyncState) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncState.ProtoReflect.Descriptor instead.
func (*SyncState) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{34}
}

func (x *SyncState) GetAnswer() *SessionDescription {
	if x != nil {
		return x.Answer
	}
	return nil
}

func (x *SyncState) GetSubscription() *UpdateSubscription {
	if x != nil {
		return x.Subscription
	}
	return nil
}

func (x *SyncState) GetPublishTracks() []*TrackPublishedResponse {
	if x != nil {
		return x.PublishTracks
	}
	return nil
}

func (x *SyncState) GetDataChannels() []*DataChannelInfo {
	if x != nil {
		return x.DataChannels
	}
	return nil
}

func (x *SyncState) GetOffer() *SessionDescription {
	if x != nil {
		return x.Offer
	}
	return nil
}

func (x *SyncState) GetTrackSidsDisabled() []string {
	if x != nil {
		return x.TrackSidsDisabled
	}
	return nil
}

func (x *SyncState) GetDatachannelReceiveStates() []*DataChannelReceiveState {
	if x != nil {
		return x.DatachannelReceiveStates
	}
	return nil
}

type DataChannelReceiveState struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PublisherSid  string                 `protobuf:"bytes,1,opt,name=publisher_sid,json=publisherSid,proto3" json:"publisher_sid,omitempty"`
	LastSeq       uint32                 `protobuf:"varint,2,opt,name=last_seq,json=lastSeq,proto3" json:"last_seq,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DataChannelReceiveState) Reset() {
	*x = DataChannelReceiveState{}
	mi := &file_livekit_rtc_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataChannelReceiveState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataChannelReceiveState) ProtoMessage() {}

func (x *DataChannelReceiveState) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataChannelReceiveState.ProtoReflect.Descriptor instead.
func (*DataChannelReceiveState) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{35}
}

func (x *DataChannelReceiveState) GetPublisherSid() string {
	if x != nil {
		return x.PublisherSid
	}
	return ""
}

func (x *DataChannelReceiveState) GetLastSeq() uint32 {
	if x != nil {
		return x.LastSeq
	}
	return 0
}

type DataChannelInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Label         string                 `protobuf:"bytes,1,opt,name=label,proto3" json:"label,omitempty"`
	Id            uint32                 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	Target        SignalTarget           `protobuf:"varint,3,opt,name=target,proto3,enum=livekit.SignalTarget" json:"target,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DataChannelInfo) Reset() {
	*x = DataChannelInfo{}
	mi := &file_livekit_rtc_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataChannelInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataChannelInfo) ProtoMessage() {}

func (x *DataChannelInfo) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataChannelInfo.ProtoReflect.Descriptor instead.
func (*DataChannelInfo) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{36}
}

func (x *DataChannelInfo) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *DataChannelInfo) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DataChannelInfo) GetTarget() SignalTarget {
	if x != nil {
		return x.Target
	}
	return SignalTarget_PUBLISHER
}

type SimulateScenario struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Scenario:
	//
	//	*SimulateScenario_SpeakerUpdate
	//	*SimulateScenario_NodeFailure
	//	*SimulateScenario_Migration
	//	*SimulateScenario_ServerLeave
	//	*SimulateScenario_SwitchCandidateProtocol
	//	*SimulateScenario_SubscriberBandwidth
	//	*SimulateScenario_DisconnectSignalOnResume
	//	*SimulateScenario_DisconnectSignalOnResumeNoMessages
	//	*SimulateScenario_LeaveRequestFullReconnect
	Scenario      isSimulateScenario_Scenario `protobuf_oneof:"scenario"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SimulateScenario) Reset() {
	*x = SimulateScenario{}
	mi := &file_livekit_rtc_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SimulateScenario) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimulateScenario) ProtoMessage() {}

func (x *SimulateScenario) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimulateScenario.ProtoReflect.Descriptor instead.
func (*SimulateScenario) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{37}
}

func (x *SimulateScenario) GetScenario() isSimulateScenario_Scenario {
	if x != nil {
		return x.Scenario
	}
	return nil
}

func (x *SimulateScenario) GetSpeakerUpdate() int32 {
	if x != nil {
		if x, ok := x.Scenario.(*SimulateScenario_SpeakerUpdate); ok {
			return x.SpeakerUpdate
		}
	}
	return 0
}

func (x *SimulateScenario) GetNodeFailure() bool {
	if x != nil {
		if x, ok := x.Scenario.(*SimulateScenario_NodeFailure); ok {
			return x.NodeFailure
		}
	}
	return false
}

func (x *SimulateScenario) GetMigration() bool {
	if x != nil {
		if x, ok := x.Scenario.(*SimulateScenario_Migration); ok {
			return x.Migration
		}
	}
	return false
}

func (x *SimulateScenario) GetServerLeave() bool {
	if x != nil {
		if x, ok := x.Scenario.(*SimulateScenario_ServerLeave); ok {
			return x.ServerLeave
		}
	}
	return false
}

func (x *SimulateScenario) GetSwitchCandidateProtocol() CandidateProtocol {
	if x != nil {
		if x, ok := x.Scenario.(*SimulateScenario_SwitchCandidateProtocol); ok {
			return x.SwitchCandidateProtocol
		}
	}
	return CandidateProtocol_UDP
}

func (x *SimulateScenario) GetSubscriberBandwidth() int64 {
	if x != nil {
		if x, ok := x.Scenario.(*SimulateScenario_SubscriberBandwidth); ok {
			return x.SubscriberBandwidth
		}
	}
	return 0
}

func (x *SimulateScenario) GetDisconnectSignalOnResume() bool {
	if x != nil {
		if x, ok := x.Scenario.(*SimulateScenario_DisconnectSignalOnResume); ok {
			return x.DisconnectSignalOnResume
		}
	}
	return false
}

func (x *SimulateScenario) GetDisconnectSignalOnResumeNoMessages() bool {
	if x != nil {
		if x, ok := x.Scenario.(*SimulateScenario_DisconnectSignalOnResumeNoMessages); ok {
			return x.DisconnectSignalOnResumeNoMessages
		}
	}
	return false
}

func (x *SimulateScenario) GetLeaveRequestFullReconnect() bool {
	if x != nil {
		if x, ok := x.Scenario.(*SimulateScenario_LeaveRequestFullReconnect); ok {
			return x.LeaveRequestFullReconnect
		}
	}
	return false
}

type isSimulateScenario_Scenario interface {
	isSimulateScenario_Scenario()
}

type SimulateScenario_SpeakerUpdate struct {
	// simulate N seconds of speaker activity
	SpeakerUpdate int32 `protobuf:"varint,1,opt,name=speaker_update,json=speakerUpdate,proto3,oneof"`
}

type SimulateScenario_NodeFailure struct {
	// simulate local node failure
	NodeFailure bool `protobuf:"varint,2,opt,name=node_failure,json=nodeFailure,proto3,oneof"`
}

type SimulateScenario_Migration struct {
	// simulate migration
	Migration bool `protobuf:"varint,3,opt,name=migration,proto3,oneof"`
}

type SimulateScenario_ServerLeave struct {
	// server to send leave
	ServerLeave bool `protobuf:"varint,4,opt,name=server_leave,json=serverLeave,proto3,oneof"`
}

type SimulateScenario_SwitchCandidateProtocol struct {
	// switch candidate protocol to tcp
	SwitchCandidateProtocol CandidateProtocol `protobuf:"varint,5,opt,name=switch_candidate_protocol,json=switchCandidateProtocol,proto3,enum=livekit.CandidateProtocol,oneof"`
}

type SimulateScenario_SubscriberBandwidth struct {
	// maximum bandwidth for subscribers, in bps
	// when zero, clears artificial bandwidth limit
	SubscriberBandwidth int64 `protobuf:"varint,6,opt,name=subscriber_bandwidth,json=subscriberBandwidth,proto3,oneof"`
}

type SimulateScenario_DisconnectSignalOnResume struct {
	// disconnect signal on resume
	DisconnectSignalOnResume bool `protobuf:"varint,7,opt,name=disconnect_signal_on_resume,json=disconnectSignalOnResume,proto3,oneof"`
}

type SimulateScenario_DisconnectSignalOnResumeNoMessages struct {
	// disconnect signal on resume before sending any messages from server
	DisconnectSignalOnResumeNoMessages bool `protobuf:"varint,8,opt,name=disconnect_signal_on_resume_no_messages,json=disconnectSignalOnResumeNoMessages,proto3,oneof"`
}

type SimulateScenario_LeaveRequestFullReconnect struct {
	// full reconnect leave request
	LeaveRequestFullReconnect bool `protobuf:"varint,9,opt,name=leave_request_full_reconnect,json=leaveRequestFullReconnect,proto3,oneof"`
}

func (*SimulateScenario_SpeakerUpdate) isSimulateScenario_Scenario() {}

func (*SimulateScenario_NodeFailure) isSimulateScenario_Scenario() {}

func (*SimulateScenario_Migration) isSimulateScenario_Scenario() {}

func (*SimulateScenario_ServerLeave) isSimulateScenario_Scenario() {}

func (*SimulateScenario_SwitchCandidateProtocol) isSimulateScenario_Scenario() {}

func (*SimulateScenario_SubscriberBandwidth) isSimulateScenario_Scenario() {}

func (*SimulateScenario_DisconnectSignalOnResume) isSimulateScenario_Scenario() {}

func (*SimulateScenario_DisconnectSignalOnResumeNoMessages) isSimulateScenario_Scenario() {}

func (*SimulateScenario_LeaveRequestFullReconnect) isSimulateScenario_Scenario() {}

type Ping struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	Timestamp int64                  `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// rtt in milliseconds calculated by client
	Rtt           int64 `protobuf:"varint,2,opt,name=rtt,proto3" json:"rtt,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Ping) Reset() {
	*x = Ping{}
	mi := &file_livekit_rtc_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Ping) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Ping) ProtoMessage() {}

func (x *Ping) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Ping.ProtoReflect.Descriptor instead.
func (*Ping) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{38}
}

func (x *Ping) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *Ping) GetRtt() int64 {
	if x != nil {
		return x.Rtt
	}
	return 0
}

type Pong struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// timestamp field of last received ping request
	LastPingTimestamp int64 `protobuf:"varint,1,opt,name=last_ping_timestamp,json=lastPingTimestamp,proto3" json:"last_ping_timestamp,omitempty"`
	Timestamp         int64 `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *Pong) Reset() {
	*x = Pong{}
	mi := &file_livekit_rtc_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Pong) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pong) ProtoMessage() {}

func (x *Pong) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pong.ProtoReflect.Descriptor instead.
func (*Pong) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{39}
}

func (x *Pong) GetLastPingTimestamp() int64 {
	if x != nil {
		return x.LastPingTimestamp
	}
	return 0
}

func (x *Pong) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type RegionSettings struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Regions       []*RegionInfo          `protobuf:"bytes,1,rep,name=regions,proto3" json:"regions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegionSettings) Reset() {
	*x = RegionSettings{}
	mi := &file_livekit_rtc_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegionSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegionSettings) ProtoMessage() {}

func (x *RegionSettings) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegionSettings.ProtoReflect.Descriptor instead.
func (*RegionSettings) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{40}
}

func (x *RegionSettings) GetRegions() []*RegionInfo {
	if x != nil {
		return x.Regions
	}
	return nil
}

type RegionInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Region        string                 `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	Url           string                 `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Distance      int64                  `protobuf:"varint,3,opt,name=distance,proto3" json:"distance,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegionInfo) Reset() {
	*x = RegionInfo{}
	mi := &file_livekit_rtc_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegionInfo) ProtoMessage() {}

func (x *RegionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegionInfo.ProtoReflect.Descriptor instead.
func (*RegionInfo) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{41}
}

func (x *RegionInfo) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *RegionInfo) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *RegionInfo) GetDistance() int64 {
	if x != nil {
		return x.Distance
	}
	return 0
}

type SubscriptionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TrackSid      string                 `protobuf:"bytes,1,opt,name=track_sid,json=trackSid,proto3" json:"track_sid,omitempty"`
	Err           SubscriptionError      `protobuf:"varint,2,opt,name=err,proto3,enum=livekit.SubscriptionError" json:"err,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubscriptionResponse) Reset() {
	*x = SubscriptionResponse{}
	mi := &file_livekit_rtc_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscriptionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionResponse) ProtoMessage() {}

func (x *SubscriptionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionResponse.ProtoReflect.Descriptor instead.
func (*SubscriptionResponse) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{42}
}

func (x *SubscriptionResponse) GetTrackSid() string {
	if x != nil {
		return x.TrackSid
	}
	return ""
}

func (x *SubscriptionResponse) GetErr() SubscriptionError {
	if x != nil {
		return x.Err
	}
	return SubscriptionError_SE_UNKNOWN
}

type RequestResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RequestId     uint32                 `protobuf:"varint,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	Reason        RequestResponse_Reason `protobuf:"varint,2,opt,name=reason,proto3,enum=livekit.RequestResponse_Reason" json:"reason,omitempty"`
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RequestResponse) Reset() {
	*x = RequestResponse{}
	mi := &file_livekit_rtc_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestResponse) ProtoMessage() {}

func (x *RequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestResponse.ProtoReflect.Descriptor instead.
func (*RequestResponse) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{43}
}

func (x *RequestResponse) GetRequestId() uint32 {
	if x != nil {
		return x.RequestId
	}
	return 0
}

func (x *RequestResponse) GetReason() RequestResponse_Reason {
	if x != nil {
		return x.Reason
	}
	return RequestResponse_OK
}

func (x *RequestResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type TrackSubscribed struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TrackSid      string                 `protobuf:"bytes,1,opt,name=track_sid,json=trackSid,proto3" json:"track_sid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrackSubscribed) Reset() {
	*x = TrackSubscribed{}
	mi := &file_livekit_rtc_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrackSubscribed) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackSubscribed) ProtoMessage() {}

func (x *TrackSubscribed) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_rtc_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackSubscribed.ProtoReflect.Descriptor instead.
func (*TrackSubscribed) Descriptor() ([]byte, []int) {
	return file_livekit_rtc_proto_rawDescGZIP(), []int{44}
}

func (x *TrackSubscribed) GetTrackSid() string {
	if x != nil {
		return x.TrackSid
	}
	return ""
}

var File_livekit_rtc_proto protoreflect.FileDescriptor

const file_livekit_rtc_proto_rawDesc = "" +
	"\n" +
	"\x11livekit_rtc.proto\x12\alivekit\x1a\x14livekit_models.proto\"\x9e\b\n" +
	"\rSignalRequest\x123\n" +
	"\x05offer\x18\x01 \x01(\v2\x1b.livekit.SessionDescriptionH\x00R\x05offer\x125\n" +
	"\x06answer\x18\x02 \x01(\v2\x1b.livekit.SessionDescriptionH\x00R\x06answer\x123\n" +
	"\atrickle\x18\x03 \x01(\v2\x17.livekit.TrickleRequestH\x00R\atrickle\x127\n" +
	"\tadd_track\x18\x04 \x01(\v2\x18.livekit.AddTrackRequestH\x00R\baddTrack\x12/\n" +
	"\x04mute\x18\x05 \x01(\v2\x19.livekit.MuteTrackRequestH\x00R\x04mute\x12A\n" +
	"\fsubscription\x18\x06 \x01(\v2\x1b.livekit.UpdateSubscriptionH\x00R\fsubscription\x12C\n" +
	"\rtrack_setting\x18\a \x01(\v2\x1c.livekit.UpdateTrackSettingsH\x00R\ftrackSetting\x12-\n" +
	"\x05leave\x18\b \x01(\v2\x15.livekit.LeaveRequestH\x00R\x05leave\x12E\n" +
	"\rupdate_layers\x18\n" +
	" \x01(\v2\x1a.livekit.UpdateVideoLayersB\x02\x18\x01H\x00R\fupdateLayers\x12Z\n" +
	"\x17subscription_permission\x18\v \x01(\v2\x1f.livekit.SubscriptionPermissionH\x00R\x16subscriptionPermission\x123\n" +
	"\n" +
	"sync_state\x18\f \x01(\v2\x12.livekit.SyncStateH\x00R\tsyncState\x127\n" +
	"\bsimulate\x18\r \x01(\v2\x19.livekit.SimulateScenarioH\x00R\bsimulate\x12\x14\n" +
	"\x04ping\x18\x0e \x01(\x03H\x00R\x04ping\x12M\n" +
	"\x0fupdate_metadata\x18\x0f \x01(\v2\".livekit.UpdateParticipantMetadataH\x00R\x0eupdateMetadata\x12*\n" +
	"\bping_req\x18\x10 \x01(\v2\r.livekit.PingH\x00R\apingReq\x12N\n" +
	"\x12update_audio_track\x18\x11 \x01(\v2\x1e.livekit.UpdateLocalAudioTrackH\x00R\x10updateAudioTrack\x12N\n" +
	"\x12update_video_track\x18\x12 \x01(\v2\x1e.livekit.UpdateLocalVideoTrackH\x00R\x10updateVideoTrackB\t\n" +
	"\amessage\"\xd4\v\n" +
	"\x0eSignalResponse\x12+\n" +
	"\x04join\x18\x01 \x01(\v2\x15.livekit.JoinResponseH\x00R\x04join\x125\n" +
	"\x06answer\x18\x02 \x01(\v2\x1b.livekit.SessionDescriptionH\x00R\x06answer\x123\n" +
	"\x05offer\x18\x03 \x01(\v2\x1b.livekit.SessionDescriptionH\x00R\x05offer\x123\n" +
	"\atrickle\x18\x04 \x01(\v2\x17.livekit.TrickleRequestH\x00R\atrickle\x124\n" +
	"\x06update\x18\x05 \x01(\v2\x1a.livekit.ParticipantUpdateH\x00R\x06update\x12J\n" +
	"\x0ftrack_published\x18\x06 \x01(\v2\x1f.livekit.TrackPublishedResponseH\x00R\x0etrackPublished\x12-\n" +
	"\x05leave\x18\b \x01(\v2\x15.livekit.LeaveRequestH\x00R\x05leave\x12/\n" +
	"\x04mute\x18\t \x01(\v2\x19.livekit.MuteTrackRequestH\x00R\x04mute\x12E\n" +
	"\x10speakers_changed\x18\n" +
	" \x01(\v2\x18.livekit.SpeakersChangedH\x00R\x0fspeakersChanged\x126\n" +
	"\vroom_update\x18\v \x01(\v2\x13.livekit.RoomUpdateH\x00R\n" +
	"roomUpdate\x12Q\n" +
	"\x12connection_quality\x18\f \x01(\v2 .livekit.ConnectionQualityUpdateH\x00R\x11connectionQuality\x12L\n" +
	"\x13stream_state_update\x18\r \x01(\v2\x1a.livekit.StreamStateUpdateH\x00R\x11streamStateUpdate\x12^\n" +
	"\x19subscribed_quality_update\x18\x0e \x01(\v2 .livekit.SubscribedQualityUpdateH\x00R\x17subscribedQualityUpdate\x12m\n" +
	"\x1esubscription_permission_update\x18\x0f \x01(\v2%.livekit.SubscriptionPermissionUpdateH\x00R\x1csubscriptionPermissionUpdate\x12%\n" +
	"\rrefresh_token\x18\x10 \x01(\tH\x00R\frefreshToken\x12P\n" +
	"\x11track_unpublished\x18\x11 \x01(\v2!.livekit.TrackUnpublishedResponseH\x00R\x10trackUnpublished\x12\x14\n" +
	"\x04pong\x18\x12 \x01(\x03H\x00R\x04pong\x12:\n" +
	"\treconnect\x18\x13 \x01(\v2\x1a.livekit.ReconnectResponseH\x00R\treconnect\x12,\n" +
	"\tpong_resp\x18\x14 \x01(\v2\r.livekit.PongH\x00R\bpongResp\x12T\n" +
	"\x15subscription_response\x18\x15 \x01(\v2\x1d.livekit.SubscriptionResponseH\x00R\x14subscriptionResponse\x12E\n" +
	"\x10request_response\x18\x16 \x01(\v2\x18.livekit.RequestResponseH\x00R\x0frequestResponse\x12E\n" +
	"\x10track_subscribed\x18\x17 \x01(\v2\x18.livekit.TrackSubscribedH\x00R\x0ftrackSubscribed\x12;\n" +
	"\n" +
	"room_moved\x18\x18 \x01(\v2\x1a.livekit.RoomMovedResponseH\x00R\troomMovedB\t\n" +
	"\amessage\"8\n" +
	"\x0eSimulcastCodec\x12\x14\n" +
	"\x05codec\x18\x01 \x01(\tR\x05codec\x12\x10\n" +
	"\x03cid\x18\x02 \x01(\tR\x03cid\"\x97\x05\n" +
	"\x0fAddTrackRequest\x12\x10\n" +
	"\x03cid\x18\x01 \x01(\tR\x03cid\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12&\n" +
	"\x04type\x18\x03 \x01(\x0e2\x12.livekit.TrackTypeR\x04type\x12\x14\n" +
	"\x05width\x18\x04 \x01(\rR\x05width\x12\x16\n" +
	"\x06height\x18\x05 \x01(\rR\x06height\x12\x14\n" +
	"\x05muted\x18\x06 \x01(\bR\x05muted\x12#\n" +
	"\vdisable_dtx\x18\a \x01(\bB\x02\x18\x01R\n" +
	"disableDtx\x12,\n" +
	"\x06source\x18\b \x01(\x0e2\x14.livekit.TrackSourceR\x06source\x12+\n" +
	"\x06layers\x18\t \x03(\v2\x13.livekit.VideoLayerR\x06layers\x12B\n" +
	"\x10simulcast_codecs\x18\n" +
	" \x03(\v2\x17.livekit.SimulcastCodecR\x0fsimulcastCodecs\x12\x10\n" +
	"\x03sid\x18\v \x01(\tR\x03sid\x12\x1a\n" +
	"\x06stereo\x18\f \x01(\bB\x02\x18\x01R\x06stereo\x12\x1f\n" +
	"\vdisable_red\x18\r \x01(\bR\n" +
	"disableRed\x128\n" +
	"\n" +
	"encryption\x18\x0e \x01(\x0e2\x18.livekit.Encryption.TypeR\n" +
	"encryption\x12\x16\n" +
	"\x06stream\x18\x0f \x01(\tR\x06stream\x12J\n" +
	"\x13backup_codec_policy\x18\x10 \x01(\x0e2\x1a.livekit.BackupCodecPolicyR\x11backupCodecPolicy\x12A\n" +
	"\x0eaudio_features\x18\x11 \x03(\x0e2\x1a.livekit.AudioTrackFeatureR\raudioFeatures\"{\n" +
	"\x0eTrickleRequest\x12$\n" +
	"\rcandidateInit\x18\x01 \x01(\tR\rcandidateInit\x12-\n" +
	"\x06target\x18\x02 \x01(\x0e2\x15.livekit.SignalTargetR\x06target\x12\x14\n" +
	"\x05final\x18\x03 \x01(\bR\x05final\":\n" +
	"\x10MuteTrackRequest\x12\x10\n" +
	"\x03sid\x18\x01 \x01(\tR\x03sid\x12\x14\n" +
	"\x05muted\x18\x02 \x01(\bR\x05muted\"\xe8\x05\n" +
	"\fJoinResponse\x12!\n" +
	"\x04room\x18\x01 \x01(\v2\r.livekit.RoomR\x04room\x12:\n" +
	"\vparticipant\x18\x02 \x01(\v2\x18.livekit.ParticipantInfoR\vparticipant\x12G\n" +
	"\x12other_participants\x18\x03 \x03(\v2\x18.livekit.ParticipantInfoR\x11otherParticipants\x12%\n" +
	"\x0eserver_version\x18\x04 \x01(\tR\rserverVersion\x123\n" +
	"\vice_servers\x18\x05 \x03(\v2\x12.livekit.ICEServerR\n" +
	"iceServers\x12-\n" +
	"\x12subscriber_primary\x18\x06 \x01(\bR\x11subscriberPrimary\x12'\n" +
	"\x0falternative_url\x18\a \x01(\tR\x0ealternativeUrl\x12O\n" +
	"\x14client_configuration\x18\b \x01(\v2\x1c.livekit.ClientConfigurationR\x13clientConfiguration\x12#\n" +
	"\rserver_region\x18\t \x01(\tR\fserverRegion\x12!\n" +
	"\fping_timeout\x18\n" +
	" \x01(\x05R\vpingTimeout\x12#\n" +
	"\rping_interval\x18\v \x01(\x05R\fpingInterval\x124\n" +
	"\vserver_info\x18\f \x01(\v2\x13.livekit.ServerInfoR\n" +
	"serverInfo\x12\x1f\n" +
	"\vsif_trailer\x18\r \x01(\fR\n" +
	"sifTrailer\x12D\n" +
	"\x16enabled_publish_codecs\x18\x0e \x03(\v2\x0e.livekit.CodecR\x14enabledPublishCodecs\x12!\n" +
	"\ffast_publish\x18\x0f \x01(\bR\vfastPublish\"\xf9\x01\n" +
	"\x11ReconnectResponse\x123\n" +
	"\vice_servers\x18\x01 \x03(\v2\x12.livekit.ICEServerR\n" +
	"iceServers\x12O\n" +
	"\x14client_configuration\x18\x02 \x01(\v2\x1c.livekit.ClientConfigurationR\x13clientConfiguration\x124\n" +
	"\vserver_info\x18\x03 \x01(\v2\x13.livekit.ServerInfoR\n" +
	"serverInfo\x12(\n" +
	"\x10last_message_seq\x18\x04 \x01(\rR\x0elastMessageSeq\"T\n" +
	"\x16TrackPublishedResponse\x12\x10\n" +
	"\x03cid\x18\x01 \x01(\tR\x03cid\x12(\n" +
	"\x05track\x18\x02 \x01(\v2\x12.livekit.TrackInfoR\x05track\"7\n" +
	"\x18TrackUnpublishedResponse\x12\x1b\n" +
	"\ttrack_sid\x18\x01 \x01(\tR\btrackSid\"J\n" +
	"\x12SessionDescription\x12\x12\n" +
	"\x04type\x18\x01 \x01(\tR\x04type\x12\x10\n" +
	"\x03sdp\x18\x02 \x01(\tR\x03sdp\x12\x0e\n" +
	"\x02id\x18\x03 \x01(\rR\x02id\"Q\n" +
	"\x11ParticipantUpdate\x12<\n" +
	"\fparticipants\x18\x01 \x03(\v2\x18.livekit.ParticipantInfoR\fparticipants\"]\n" +
	"\x17RemoteParticipantUpdate\x12B\n" +
	"\fparticipants\x18\x01 \x03(\v2\x1e.livekit.RemoteParticipantInfoR\fparticipants\"\x9c\x01\n" +
	"\x12UpdateSubscription\x12\x1d\n" +
	"\n" +
	"track_sids\x18\x01 \x03(\tR\ttrackSids\x12\x1c\n" +
	"\tsubscribe\x18\x02 \x01(\bR\tsubscribe\x12I\n" +
	"\x12participant_tracks\x18\x03 \x03(\v2\x1a.livekit.ParticipantTracksR\x11participantTracks\"\xdd\x01\n" +
	"\x13UpdateTrackSettings\x12\x1d\n" +
	"\n" +
	"track_sids\x18\x01 \x03(\tR\ttrackSids\x12\x1a\n" +
	"\bdisabled\x18\x03 \x01(\bR\bdisabled\x12/\n" +
	"\aquality\x18\x04 \x01(\x0e2\x15.livekit.VideoQualityR\aquality\x12\x14\n" +
	"\x05width\x18\x05 \x01(\rR\x05width\x12\x16\n" +
	"\x06height\x18\x06 \x01(\rR\x06height\x12\x10\n" +
	"\x03fps\x18\a \x01(\rR\x03fps\x12\x1a\n" +
	"\bpriority\x18\b \x01(\rR\bpriority\"l\n" +
	"\x15UpdateLocalAudioTrack\x12\x1b\n" +
	"\ttrack_sid\x18\x01 \x01(\tR\btrackSid\x126\n" +
	"\bfeatures\x18\x02 \x03(\x0e2\x1a.livekit.AudioTrackFeatureR\bfeatures\"b\n" +
	"\x15UpdateLocalVideoTrack\x12\x1b\n" +
	"\ttrack_sid\x18\x01 \x01(\tR\btrackSid\x12\x14\n" +
	"\x05width\x18\x02 \x01(\rR\x05width\x12\x16\n" +
	"\x06height\x18\x03 \x01(\rR\x06height\"\x84\x02\n" +
	"\fLeaveRequest\x12#\n" +
	"\rcan_reconnect\x18\x01 \x01(\bR\fcanReconnect\x121\n" +
	"\x06reason\x18\x02 \x01(\x0e2\x19.livekit.DisconnectReasonR\x06reason\x124\n" +
	"\x06action\x18\x03 \x01(\x0e2\x1c.livekit.LeaveRequest.ActionR\x06action\x121\n" +
	"\aregions\x18\x04 \x01(\v2\x17.livekit.RegionSettingsR\aregions\"3\n" +
	"\x06Action\x12\x0e\n" +
	"\n" +
	"DISCONNECT\x10\x00\x12\n" +
	"\n" +
	"\x06RESUME\x10\x01\x12\r\n" +
	"\tRECONNECT\x10\x02\"a\n" +
	"\x11UpdateVideoLayers\x12\x1b\n" +
	"\ttrack_sid\x18\x01 \x01(\tR\btrackSid\x12+\n" +
	"\x06layers\x18\x02 \x03(\v2\x13.livekit.VideoLayerR\x06layers:\x02\x18\x01\"\xfd\x01\n" +
	"\x19UpdateParticipantMetadata\x12\x1a\n" +
	"\bmetadata\x18\x01 \x01(\tR\bmetadata\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12R\n" +
	"\n" +
	"attributes\x18\x03 \x03(\v22.livekit.UpdateParticipantMetadata.AttributesEntryR\n" +
	"attributes\x12\x1d\n" +
	"\n" +
	"request_id\x18\x04 \x01(\rR\trequestId\x1a=\n" +
	"\x0fAttributesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"[\n" +
	"\tICEServer\x12\x12\n" +
	"\x04urls\x18\x01 \x03(\tR\x04urls\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12\x1e\n" +
	"\n" +
	"credential\x18\x03 \x01(\tR\n" +
	"credential\"C\n" +
	"\x0fSpeakersChanged\x120\n" +
	"\bspeakers\x18\x01 \x03(\v2\x14.livekit.SpeakerInfoR\bspeakers\"/\n" +
	"\n" +
	"RoomUpdate\x12!\n" +
	"\x04room\x18\x01 \x01(\v2\r.livekit.RoomR\x04room\"\x8c\x01\n" +
	"\x15ConnectionQualityInfo\x12'\n" +
	"\x0fparticipant_sid\x18\x01 \x01(\tR\x0eparticipantSid\x124\n" +
	"\aquality\x18\x02 \x01(\x0e2\x1a.livekit.ConnectionQualityR\aquality\x12\x14\n" +
	"\x05score\x18\x03 \x01(\x02R\x05score\"S\n" +
	"\x17ConnectionQualityUpdate\x128\n" +
	"\aupdates\x18\x01 \x03(\v2\x1e.livekit.ConnectionQualityInfoR\aupdates\"\x83\x01\n" +
	"\x0fStreamStateInfo\x12'\n" +
	"\x0fparticipant_sid\x18\x01 \x01(\tR\x0eparticipantSid\x12\x1b\n" +
	"\ttrack_sid\x18\x02 \x01(\tR\btrackSid\x12*\n" +
	"\x05state\x18\x03 \x01(\x0e2\x14.livekit.StreamStateR\x05state\"R\n" +
	"\x11StreamStateUpdate\x12=\n" +
	"\rstream_states\x18\x01 \x03(\v2\x18.livekit.StreamStateInfoR\fstreamStates\"^\n" +
	"\x11SubscribedQuality\x12/\n" +
	"\aquality\x18\x01 \x01(\x0e2\x15.livekit.VideoQualityR\aquality\x12\x18\n" +
	"\aenabled\x18\x02 \x01(\bR\aenabled\"a\n" +
	"\x0fSubscribedCodec\x12\x14\n" +
	"\x05codec\x18\x01 \x01(\tR\x05codec\x128\n" +
	"\tqualities\x18\x02 \x03(\v2\x1a.livekit.SubscribedQualityR\tqualities\"\xd0\x01\n" +
	"\x17SubscribedQualityUpdate\x12\x1b\n" +
	"\ttrack_sid\x18\x01 \x01(\tR\btrackSid\x12Q\n" +
	"\x14subscribed_qualities\x18\x02 \x03(\v2\x1a.livekit.SubscribedQualityB\x02\x18\x01R\x13subscribedQualities\x12E\n" +
	"\x11subscribed_codecs\x18\x03 \x03(\v2\x18.livekit.SubscribedCodecR\x10subscribedCodecs\"\xab\x01\n" +
	"\x0fTrackPermission\x12'\n" +
	"\x0fparticipant_sid\x18\x01 \x01(\tR\x0eparticipantSid\x12\x1d\n" +
	"\n" +
	"all_tracks\x18\x02 \x01(\bR\tallTracks\x12\x1d\n" +
	"\n" +
	"track_sids\x18\x03 \x03(\tR\ttrackSids\x121\n" +
	"\x14participant_identity\x18\x04 \x01(\tR\x13participantIdentity\"\x8a\x01\n" +
	"\x16SubscriptionPermission\x12)\n" +
	"\x10all_participants\x18\x01 \x01(\bR\x0fallParticipants\x12E\n" +
	"\x11track_permissions\x18\x02 \x03(\v2\x18.livekit.TrackPermissionR\x10trackPermissions\"~\n" +
	"\x1cSubscriptionPermissionUpdate\x12'\n" +
	"\x0fparticipant_sid\x18\x01 \x01(\tR\x0eparticipantSid\x12\x1b\n" +
	"\ttrack_sid\x18\x02 \x01(\tR\btrackSid\x12\x18\n" +
	"\aallowed\x18\x03 \x01(\bR\aallowed\"\xd1\x01\n" +
	"\x11RoomMovedResponse\x12!\n" +
	"\x04room\x18\x01 \x01(\v2\r.livekit.RoomR\x04room\x12\x14\n" +
	"\x05token\x18\x02 \x01(\tR\x05token\x12:\n" +
	"\vparticipant\x18\x03 \x01(\v2\x18.livekit.ParticipantInfoR\vparticipant\x12G\n" +
	"\x12other_participants\x18\x04 \x03(\v2\x18.livekit.ParticipantInfoR\x11otherParticipants\"\xcb\x03\n" +
	"\tSyncState\x123\n" +
	"\x06answer\x18\x01 \x01(\v2\x1b.livekit.SessionDescriptionR\x06answer\x12?\n" +
	"\fsubscription\x18\x02 \x01(\v2\x1b.livekit.UpdateSubscriptionR\fsubscription\x12F\n" +
	"\x0epublish_tracks\x18\x03 \x03(\v2\x1f.livekit.TrackPublishedResponseR\rpublishTracks\x12=\n" +
	"\rdata_channels\x18\x04 \x03(\v2\x18.livekit.DataChannelInfoR\fdataChannels\x121\n" +
	"\x05offer\x18\x05 \x01(\v2\x1b.livekit.SessionDescriptionR\x05offer\x12.\n" +
	"\x13track_sids_disabled\x18\x06 \x03(\tR\x11trackSidsDisabled\x12^\n" +
	"\x1adatachannel_receive_states\x18\a \x03(\v2 .livekit.DataChannelReceiveStateR\x18datachannelReceiveStates\"Y\n" +
	"\x17DataChannelReceiveState\x12#\n" +
	"\rpublisher_sid\x18\x01 \x01(\tR\fpublisherSid\x12\x19\n" +
	"\blast_seq\x18\x02 \x01(\rR\alastSeq\"f\n" +
	"\x0fDataChannelInfo\x12\x14\n" +
	"\x05label\x18\x01 \x01(\tR\x05label\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\rR\x02id\x12-\n" +
	"\x06target\x18\x03 \x01(\x0e2\x15.livekit.SignalTargetR\x06target\"\x9b\x04\n" +
	"\x10SimulateScenario\x12'\n" +
	"\x0espeaker_update\x18\x01 \x01(\x05H\x00R\rspeakerUpdate\x12#\n" +
	"\fnode_failure\x18\x02 \x01(\bH\x00R\vnodeFailure\x12\x1e\n" +
	"\tmigration\x18\x03 \x01(\bH\x00R\tmigration\x12#\n" +
	"\fserver_leave\x18\x04 \x01(\bH\x00R\vserverLeave\x12X\n" +
	"\x19switch_candidate_protocol\x18\x05 \x01(\x0e2\x1a.livekit.CandidateProtocolH\x00R\x17switchCandidateProtocol\x123\n" +
	"\x14subscriber_bandwidth\x18\x06 \x01(\x03H\x00R\x13subscriberBandwidth\x12?\n" +
	"\x1bdisconnect_signal_on_resume\x18\a \x01(\bH\x00R\x18disconnectSignalOnResume\x12U\n" +
	"'disconnect_signal_on_resume_no_messages\x18\b \x01(\bH\x00R\"disconnectSignalOnResumeNoMessages\x12A\n" +
	"\x1cleave_request_full_reconnect\x18\t \x01(\bH\x00R\x19leaveRequestFullReconnectB\n" +
	"\n" +
	"\bscenario\"6\n" +
	"\x04Ping\x12\x1c\n" +
	"\ttimestamp\x18\x01 \x01(\x03R\ttimestamp\x12\x10\n" +
	"\x03rtt\x18\x02 \x01(\x03R\x03rtt\"T\n" +
	"\x04Pong\x12.\n" +
	"\x13last_ping_timestamp\x18\x01 \x01(\x03R\x11lastPingTimestamp\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x03R\ttimestamp\"?\n" +
	"\x0eRegionSettings\x12-\n" +
	"\aregions\x18\x01 \x03(\v2\x13.livekit.RegionInfoR\aregions\"R\n" +
	"\n" +
	"RegionInfo\x12\x16\n" +
	"\x06region\x18\x01 \x01(\tR\x06region\x12\x10\n" +
	"\x03url\x18\x02 \x01(\tR\x03url\x12\x1a\n" +
	"\bdistance\x18\x03 \x01(\x03R\bdistance\"a\n" +
	"\x14SubscriptionResponse\x12\x1b\n" +
	"\ttrack_sid\x18\x01 \x01(\tR\btrackSid\x12,\n" +
	"\x03err\x18\x02 \x01(\x0e2\x1a.livekit.SubscriptionErrorR\x03err\"\xc9\x01\n" +
	"\x0fRequestResponse\x12\x1d\n" +
	"\n" +
	"request_id\x18\x01 \x01(\rR\trequestId\x127\n" +
	"\x06reason\x18\x02 \x01(\x0e2\x1f.livekit.RequestResponse.ReasonR\x06reason\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\"D\n" +
	"\x06Reason\x12\x06\n" +
	"\x02OK\x10\x00\x12\r\n" +
	"\tNOT_FOUND\x10\x01\x12\x0f\n" +
	"\vNOT_ALLOWED\x10\x02\x12\x12\n" +
	"\x0eLIMIT_EXCEEDED\x10\x03\".\n" +
	"\x0fTrackSubscribed\x12\x1b\n" +
	"\ttrack_sid\x18\x01 \x01(\tR\btrackSid*-\n" +
	"\fSignalTarget\x12\r\n" +
	"\tPUBLISHER\x10\x00\x12\x0e\n" +
	"\n" +
	"SUBSCRIBER\x10\x01*%\n" +
	"\vStreamState\x12\n" +
	"\n" +
	"\x06ACTIVE\x10\x00\x12\n" +
	"\n" +
	"\x06PAUSED\x10\x01*.\n" +
	"\x11CandidateProtocol\x12\a\n" +
	"\x03UDP\x10\x00\x12\a\n" +
	"\x03TCP\x10\x01\x12\a\n" +
	"\x03TLS\x10\x02BFZ#github.com/livekit/protocol/livekit\xaa\x02\rLiveKit.Proto\xea\x02\x0eLiveKit::Protob\x06proto3"

var (
	file_livekit_rtc_proto_rawDescOnce sync.Once
	file_livekit_rtc_proto_rawDescData []byte
)

func file_livekit_rtc_proto_rawDescGZIP() []byte {
	file_livekit_rtc_proto_rawDescOnce.Do(func() {
		file_livekit_rtc_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_livekit_rtc_proto_rawDesc), len(file_livekit_rtc_proto_rawDesc)))
	})
	return file_livekit_rtc_proto_rawDescData
}

var file_livekit_rtc_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_livekit_rtc_proto_msgTypes = make([]protoimpl.MessageInfo, 46)
var file_livekit_rtc_proto_goTypes = []any{
	(SignalTarget)(0),                    // 0: livekit.SignalTarget
	(StreamState)(0),                     // 1: livekit.StreamState
	(CandidateProtocol)(0),               // 2: livekit.CandidateProtocol
	(LeaveRequest_Action)(0),             // 3: livekit.LeaveRequest.Action
	(RequestResponse_Reason)(0),          // 4: livekit.RequestResponse.Reason
	(*SignalRequest)(nil),                // 5: livekit.SignalRequest
	(*SignalResponse)(nil),               // 6: livekit.SignalResponse
	(*SimulcastCodec)(nil),               // 7: livekit.SimulcastCodec
	(*AddTrackRequest)(nil),              // 8: livekit.AddTrackRequest
	(*TrickleRequest)(nil),               // 9: livekit.TrickleRequest
	(*MuteTrackRequest)(nil),             // 10: livekit.MuteTrackRequest
	(*JoinResponse)(nil),                 // 11: livekit.JoinResponse
	(*ReconnectResponse)(nil),            // 12: livekit.ReconnectResponse
	(*TrackPublishedResponse)(nil),       // 13: livekit.TrackPublishedResponse
	(*TrackUnpublishedResponse)(nil),     // 14: livekit.TrackUnpublishedResponse
	(*SessionDescription)(nil),           // 15: livekit.SessionDescription
	(*ParticipantUpdate)(nil),            // 16: livekit.ParticipantUpdate
	(*RemoteParticipantUpdate)(nil),      // 17: livekit.RemoteParticipantUpdate
	(*UpdateSubscription)(nil),           // 18: livekit.UpdateSubscription
	(*UpdateTrackSettings)(nil),          // 19: livekit.UpdateTrackSettings
	(*UpdateLocalAudioTrack)(nil),        // 20: livekit.UpdateLocalAudioTrack
	(*UpdateLocalVideoTrack)(nil),        // 21: livekit.UpdateLocalVideoTrack
	(*LeaveRequest)(nil),                 // 22: livekit.LeaveRequest
	(*UpdateVideoLayers)(nil),            // 23: livekit.UpdateVideoLayers
	(*UpdateParticipantMetadata)(nil),    // 24: livekit.UpdateParticipantMetadata
	(*ICEServer)(nil),                    // 25: livekit.ICEServer
	(*SpeakersChanged)(nil),              // 26: livekit.SpeakersChanged
	(*RoomUpdate)(nil),                   // 27: livekit.RoomUpdate
	(*ConnectionQualityInfo)(nil),        // 28: livekit.ConnectionQualityInfo
	(*ConnectionQualityUpdate)(nil),      // 29: livekit.ConnectionQualityUpdate
	(*StreamStateInfo)(nil),              // 30: livekit.StreamStateInfo
	(*StreamStateUpdate)(nil),            // 31: livekit.StreamStateUpdate
	(*SubscribedQuality)(nil),            // 32: livekit.SubscribedQuality
	(*SubscribedCodec)(nil),              // 33: livekit.SubscribedCodec
	(*SubscribedQualityUpdate)(nil),      // 34: livekit.SubscribedQualityUpdate
	(*TrackPermission)(nil),              // 35: livekit.TrackPermission
	(*SubscriptionPermission)(nil),       // 36: livekit.SubscriptionPermission
	(*SubscriptionPermissionUpdate)(nil), // 37: livekit.SubscriptionPermissionUpdate
	(*RoomMovedResponse)(nil),            // 38: livekit.RoomMovedResponse
	(*SyncState)(nil),                    // 39: livekit.SyncState
	(*DataChannelReceiveState)(nil),      // 40: livekit.DataChannelReceiveState
	(*DataChannelInfo)(nil),              // 41: livekit.DataChannelInfo
	(*SimulateScenario)(nil),             // 42: livekit.SimulateScenario
	(*Ping)(nil),                         // 43: livekit.Ping
	(*Pong)(nil),                         // 44: livekit.Pong
	(*RegionSettings)(nil),               // 45: livekit.RegionSettings
	(*RegionInfo)(nil),                   // 46: livekit.RegionInfo
	(*SubscriptionResponse)(nil),         // 47: livekit.SubscriptionResponse
	(*RequestResponse)(nil),              // 48: livekit.RequestResponse
	(*TrackSubscribed)(nil),              // 49: livekit.TrackSubscribed
	nil,                                  // 50: livekit.UpdateParticipantMetadata.AttributesEntry
	(TrackType)(0),                       // 51: livekit.TrackType
	(TrackSource)(0),                     // 52: livekit.TrackSource
	(*VideoLayer)(nil),                   // 53: livekit.VideoLayer
	(Encryption_Type)(0),                 // 54: livekit.Encryption.Type
	(BackupCodecPolicy)(0),               // 55: livekit.BackupCodecPolicy
	(AudioTrackFeature)(0),               // 56: livekit.AudioTrackFeature
	(*Room)(nil),                         // 57: livekit.Room
	(*ParticipantInfo)(nil),              // 58: livekit.ParticipantInfo
	(*ClientConfiguration)(nil),          // 59: livekit.ClientConfiguration
	(*ServerInfo)(nil),                   // 60: livekit.ServerInfo
	(*Codec)(nil),                        // 61: livekit.Codec
	(*TrackInfo)(nil),                    // 62: livekit.TrackInfo
	(*RemoteParticipantInfo)(nil),        // 63: livekit.RemoteParticipantInfo
	(*ParticipantTracks)(nil),            // 64: livekit.ParticipantTracks
	(VideoQuality)(0),                    // 65: livekit.VideoQuality
	(DisconnectReason)(0),                // 66: livekit.DisconnectReason
	(*SpeakerInfo)(nil),                  // 67: livekit.SpeakerInfo
	(ConnectionQuality)(0),               // 68: livekit.ConnectionQuality
	(SubscriptionError)(0),               // 69: livekit.SubscriptionError
}
var file_livekit_rtc_proto_depIdxs = []int32{
	15, // 0: livekit.SignalRequest.offer:type_name -> livekit.SessionDescription
	15, // 1: livekit.SignalRequest.answer:type_name -> livekit.SessionDescription
	9,  // 2: livekit.SignalRequest.trickle:type_name -> livekit.TrickleRequest
	8,  // 3: livekit.SignalRequest.add_track:type_name -> livekit.AddTrackRequest
	10, // 4: livekit.SignalRequest.mute:type_name -> livekit.MuteTrackRequest
	18, // 5: livekit.SignalRequest.subscription:type_name -> livekit.UpdateSubscription
	19, // 6: livekit.SignalRequest.track_setting:type_name -> livekit.UpdateTrackSettings
	22, // 7: livekit.SignalRequest.leave:type_name -> livekit.LeaveRequest
	23, // 8: livekit.SignalRequest.update_layers:type_name -> livekit.UpdateVideoLayers
	36, // 9: livekit.SignalRequest.subscription_permission:type_name -> livekit.SubscriptionPermission
	39, // 10: livekit.SignalRequest.sync_state:type_name -> livekit.SyncState
	42, // 11: livekit.SignalRequest.simulate:type_name -> livekit.SimulateScenario
	24, // 12: livekit.SignalRequest.update_metadata:type_name -> livekit.UpdateParticipantMetadata
	43, // 13: livekit.SignalRequest.ping_req:type_name -> livekit.Ping
	20, // 14: livekit.SignalRequest.update_audio_track:type_name -> livekit.UpdateLocalAudioTrack
	21, // 15: livekit.SignalRequest.update_video_track:type_name -> livekit.UpdateLocalVideoTrack
	11, // 16: livekit.SignalResponse.join:type_name -> livekit.JoinResponse
	15, // 17: livekit.SignalResponse.answer:type_name -> livekit.SessionDescription
	15, // 18: livekit.SignalResponse.offer:type_name -> livekit.SessionDescription
	9,  // 19: livekit.SignalResponse.trickle:type_name -> livekit.TrickleRequest
	16, // 20: livekit.SignalResponse.update:type_name -> livekit.ParticipantUpdate
	13, // 21: livekit.SignalResponse.track_published:type_name -> livekit.TrackPublishedResponse
	22, // 22: livekit.SignalResponse.leave:type_name -> livekit.LeaveRequest
	10, // 23: livekit.SignalResponse.mute:type_name -> livekit.MuteTrackRequest
	26, // 24: livekit.SignalResponse.speakers_changed:type_name -> livekit.SpeakersChanged
	27, // 25: livekit.SignalResponse.room_update:type_name -> livekit.RoomUpdate
	29, // 26: livekit.SignalResponse.connection_quality:type_name -> livekit.ConnectionQualityUpdate
	31, // 27: livekit.SignalResponse.stream_state_update:type_name -> livekit.StreamStateUpdate
	34, // 28: livekit.SignalResponse.subscribed_quality_update:type_name -> livekit.SubscribedQualityUpdate
	37, // 29: livekit.SignalResponse.subscription_permission_update:type_name -> livekit.SubscriptionPermissionUpdate
	14, // 30: livekit.SignalResponse.track_unpublished:type_name -> livekit.TrackUnpublishedResponse
	12, // 31: livekit.SignalResponse.reconnect:type_name -> livekit.ReconnectResponse
	44, // 32: livekit.SignalResponse.pong_resp:type_name -> livekit.Pong
	47, // 33: livekit.SignalResponse.subscription_response:type_name -> livekit.SubscriptionResponse
	48, // 34: livekit.SignalResponse.request_response:type_name -> livekit.RequestResponse
	49, // 35: livekit.SignalResponse.track_subscribed:type_name -> livekit.TrackSubscribed
	38, // 36: livekit.SignalResponse.room_moved:type_name -> livekit.RoomMovedResponse
	51, // 37: livekit.AddTrackRequest.type:type_name -> livekit.TrackType
	52, // 38: livekit.AddTrackRequest.source:type_name -> livekit.TrackSource
	53, // 39: livekit.AddTrackRequest.layers:type_name -> livekit.VideoLayer
	7,  // 40: livekit.AddTrackRequest.simulcast_codecs:type_name -> livekit.SimulcastCodec
	54, // 41: livekit.AddTrackRequest.encryption:type_name -> livekit.Encryption.Type
	55, // 42: livekit.AddTrackRequest.backup_codec_policy:type_name -> livekit.BackupCodecPolicy
	56, // 43: livekit.AddTrackRequest.audio_features:type_name -> livekit.AudioTrackFeature
	0,  // 44: livekit.TrickleRequest.target:type_name -> livekit.SignalTarget
	57, // 45: livekit.JoinResponse.room:type_name -> livekit.Room
	58, // 46: livekit.JoinResponse.participant:type_name -> livekit.ParticipantInfo
	58, // 47: livekit.JoinResponse.other_participants:type_name -> livekit.ParticipantInfo
	25, // 48: livekit.JoinResponse.ice_servers:type_name -> livekit.ICEServer
	59, // 49: livekit.JoinResponse.client_configuration:type_name -> livekit.ClientConfiguration
	60, // 50: livekit.JoinResponse.server_info:type_name -> livekit.ServerInfo
	61, // 51: livekit.JoinResponse.enabled_publish_codecs:type_name -> livekit.Codec
	25, // 52: livekit.ReconnectResponse.ice_servers:type_name -> livekit.ICEServer
	59, // 53: livekit.ReconnectResponse.client_configuration:type_name -> livekit.ClientConfiguration
	60, // 54: livekit.ReconnectResponse.server_info:type_name -> livekit.ServerInfo
	62, // 55: livekit.TrackPublishedResponse.track:type_name -> livekit.TrackInfo
	58, // 56: livekit.ParticipantUpdate.participants:type_name -> livekit.ParticipantInfo
	63, // 57: livekit.RemoteParticipantUpdate.participants:type_name -> livekit.RemoteParticipantInfo
	64, // 58: livekit.UpdateSubscription.participant_tracks:type_name -> livekit.ParticipantTracks
	65, // 59: livekit.UpdateTrackSettings.quality:type_name -> livekit.VideoQuality
	56, // 60: livekit.UpdateLocalAudioTrack.features:type_name -> livekit.AudioTrackFeature
	66, // 61: livekit.LeaveRequest.reason:type_name -> livekit.DisconnectReason
	3,  // 62: livekit.LeaveRequest.action:type_name -> livekit.LeaveRequest.Action
	45, // 63: livekit.LeaveRequest.regions:type_name -> livekit.RegionSettings
	53, // 64: livekit.UpdateVideoLayers.layers:type_name -> livekit.VideoLayer
	50, // 65: livekit.UpdateParticipantMetadata.attributes:type_name -> livekit.UpdateParticipantMetadata.AttributesEntry
	67, // 66: livekit.SpeakersChanged.speakers:type_name -> livekit.SpeakerInfo
	57, // 67: livekit.RoomUpdate.room:type_name -> livekit.Room
	68, // 68: livekit.ConnectionQualityInfo.quality:type_name -> livekit.ConnectionQuality
	28, // 69: livekit.ConnectionQualityUpdate.updates:type_name -> livekit.ConnectionQualityInfo
	1,  // 70: livekit.StreamStateInfo.state:type_name -> livekit.StreamState
	30, // 71: livekit.StreamStateUpdate.stream_states:type_name -> livekit.StreamStateInfo
	65, // 72: livekit.SubscribedQuality.quality:type_name -> livekit.VideoQuality
	32, // 73: livekit.SubscribedCodec.qualities:type_name -> livekit.SubscribedQuality
	32, // 74: livekit.SubscribedQualityUpdate.subscribed_qualities:type_name -> livekit.SubscribedQuality
	33, // 75: livekit.SubscribedQualityUpdate.subscribed_codecs:type_name -> livekit.SubscribedCodec
	35, // 76: livekit.SubscriptionPermission.track_permissions:type_name -> livekit.TrackPermission
	57, // 77: livekit.RoomMovedResponse.room:type_name -> livekit.Room
	58, // 78: livekit.RoomMovedResponse.participant:type_name -> livekit.ParticipantInfo
	58, // 79: livekit.RoomMovedResponse.other_participants:type_name -> livekit.ParticipantInfo
	15, // 80: livekit.SyncState.answer:type_name -> livekit.SessionDescription
	18, // 81: livekit.SyncState.subscription:type_name -> livekit.UpdateSubscription
	13, // 82: livekit.SyncState.publish_tracks:type_name -> livekit.TrackPublishedResponse
	41, // 83: livekit.SyncState.data_channels:type_name -> livekit.DataChannelInfo
	15, // 84: livekit.SyncState.offer:type_name -> livekit.SessionDescription
	40, // 85: livekit.SyncState.datachannel_receive_states:type_name -> livekit.DataChannelReceiveState
	0,  // 86: livekit.DataChannelInfo.target:type_name -> livekit.SignalTarget
	2,  // 87: livekit.SimulateScenario.switch_candidate_protocol:type_name -> livekit.CandidateProtocol
	46, // 88: livekit.RegionSettings.regions:type_name -> livekit.RegionInfo
	69, // 89: livekit.SubscriptionResponse.err:type_name -> livekit.SubscriptionError
	4,  // 90: livekit.RequestResponse.reason:type_name -> livekit.RequestResponse.Reason
	91, // [91:91] is the sub-list for method output_type
	91, // [91:91] is the sub-list for method input_type
	91, // [91:91] is the sub-list for extension type_name
	91, // [91:91] is the sub-list for extension extendee
	0,  // [0:91] is the sub-list for field type_name
}

func init() { file_livekit_rtc_proto_init() }
func file_livekit_rtc_proto_init() {
	if File_livekit_rtc_proto != nil {
		return
	}
	file_livekit_models_proto_init()
	file_livekit_rtc_proto_msgTypes[0].OneofWrappers = []any{
		(*SignalRequest_Offer)(nil),
		(*SignalRequest_Answer)(nil),
		(*SignalRequest_Trickle)(nil),
		(*SignalRequest_AddTrack)(nil),
		(*SignalRequest_Mute)(nil),
		(*SignalRequest_Subscription)(nil),
		(*SignalRequest_TrackSetting)(nil),
		(*SignalRequest_Leave)(nil),
		(*SignalRequest_UpdateLayers)(nil),
		(*SignalRequest_SubscriptionPermission)(nil),
		(*SignalRequest_SyncState)(nil),
		(*SignalRequest_Simulate)(nil),
		(*SignalRequest_Ping)(nil),
		(*SignalRequest_UpdateMetadata)(nil),
		(*SignalRequest_PingReq)(nil),
		(*SignalRequest_UpdateAudioTrack)(nil),
		(*SignalRequest_UpdateVideoTrack)(nil),
	}
	file_livekit_rtc_proto_msgTypes[1].OneofWrappers = []any{
		(*SignalResponse_Join)(nil),
		(*SignalResponse_Answer)(nil),
		(*SignalResponse_Offer)(nil),
		(*SignalResponse_Trickle)(nil),
		(*SignalResponse_Update)(nil),
		(*SignalResponse_TrackPublished)(nil),
		(*SignalResponse_Leave)(nil),
		(*SignalResponse_Mute)(nil),
		(*SignalResponse_SpeakersChanged)(nil),
		(*SignalResponse_RoomUpdate)(nil),
		(*SignalResponse_ConnectionQuality)(nil),
		(*SignalResponse_StreamStateUpdate)(nil),
		(*SignalResponse_SubscribedQualityUpdate)(nil),
		(*SignalResponse_SubscriptionPermissionUpdate)(nil),
		(*SignalResponse_RefreshToken)(nil),
		(*SignalResponse_TrackUnpublished)(nil),
		(*SignalResponse_Pong)(nil),
		(*SignalResponse_Reconnect)(nil),
		(*SignalResponse_PongResp)(nil),
		(*SignalResponse_SubscriptionResponse)(nil),
		(*SignalResponse_RequestResponse)(nil),
		(*SignalResponse_TrackSubscribed)(nil),
		(*SignalResponse_RoomMoved)(nil),
	}
	file_livekit_rtc_proto_msgTypes[37].OneofWrappers = []any{
		(*SimulateScenario_SpeakerUpdate)(nil),
		(*SimulateScenario_NodeFailure)(nil),
		(*SimulateScenario_Migration)(nil),
		(*SimulateScenario_ServerLeave)(nil),
		(*SimulateScenario_SwitchCandidateProtocol)(nil),
		(*SimulateScenario_SubscriberBandwidth)(nil),
		(*SimulateScenario_DisconnectSignalOnResume)(nil),
		(*SimulateScenario_DisconnectSignalOnResumeNoMessages)(nil),
		(*SimulateScenario_LeaveRequestFullReconnect)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_livekit_rtc_proto_rawDesc), len(file_livekit_rtc_proto_rawDesc)),
			NumEnums:      5,
			NumMessages:   46,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_livekit_rtc_proto_goTypes,
		DependencyIndexes: file_livekit_rtc_proto_depIdxs,
		EnumInfos:         file_livekit_rtc_proto_enumTypes,
		MessageInfos:      file_livekit_rtc_proto_msgTypes,
	}.Build()
	File_livekit_rtc_proto = out.File
	file_livekit_rtc_proto_goTypes = nil
	file_livekit_rtc_proto_depIdxs = nil
}
