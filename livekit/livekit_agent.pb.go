// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: livekit_agent.proto

package livekit

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type JobType int32

const (
	JobType_JT_ROOM        JobType = 0
	JobType_JT_PUBLISHER   JobType = 1
	JobType_JT_PARTICIPANT JobType = 2
)

// Enum value maps for JobType.
var (
	JobType_name = map[int32]string{
		0: "JT_ROOM",
		1: "JT_PUBLISHER",
		2: "JT_PARTICIPANT",
	}
	JobType_value = map[string]int32{
		"JT_ROOM":        0,
		"JT_PUBLISHER":   1,
		"JT_PARTICIPANT": 2,
	}
)

func (x JobType) Enum() *JobType {
	p := new(JobType)
	*p = x
	return p
}

func (x JobType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (JobType) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_agent_proto_enumTypes[0].Descriptor()
}

func (JobType) Type() protoreflect.EnumType {
	return &file_livekit_agent_proto_enumTypes[0]
}

func (x JobType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use JobType.Descriptor instead.
func (JobType) EnumDescriptor() ([]byte, []int) {
	return file_livekit_agent_proto_rawDescGZIP(), []int{0}
}

type WorkerStatus int32

const (
	WorkerStatus_WS_AVAILABLE WorkerStatus = 0
	WorkerStatus_WS_FULL      WorkerStatus = 1
)

// Enum value maps for WorkerStatus.
var (
	WorkerStatus_name = map[int32]string{
		0: "WS_AVAILABLE",
		1: "WS_FULL",
	}
	WorkerStatus_value = map[string]int32{
		"WS_AVAILABLE": 0,
		"WS_FULL":      1,
	}
)

func (x WorkerStatus) Enum() *WorkerStatus {
	p := new(WorkerStatus)
	*p = x
	return p
}

func (x WorkerStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WorkerStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_agent_proto_enumTypes[1].Descriptor()
}

func (WorkerStatus) Type() protoreflect.EnumType {
	return &file_livekit_agent_proto_enumTypes[1]
}

func (x WorkerStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WorkerStatus.Descriptor instead.
func (WorkerStatus) EnumDescriptor() ([]byte, []int) {
	return file_livekit_agent_proto_rawDescGZIP(), []int{1}
}

type JobStatus int32

const (
	JobStatus_JS_PENDING JobStatus = 0
	JobStatus_JS_RUNNING JobStatus = 1
	JobStatus_JS_SUCCESS JobStatus = 2
	JobStatus_JS_FAILED  JobStatus = 3
)

// Enum value maps for JobStatus.
var (
	JobStatus_name = map[int32]string{
		0: "JS_PENDING",
		1: "JS_RUNNING",
		2: "JS_SUCCESS",
		3: "JS_FAILED",
	}
	JobStatus_value = map[string]int32{
		"JS_PENDING": 0,
		"JS_RUNNING": 1,
		"JS_SUCCESS": 2,
		"JS_FAILED":  3,
	}
)

func (x JobStatus) Enum() *JobStatus {
	p := new(JobStatus)
	*p = x
	return p
}

func (x JobStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (JobStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_agent_proto_enumTypes[2].Descriptor()
}

func (JobStatus) Type() protoreflect.EnumType {
	return &file_livekit_agent_proto_enumTypes[2]
}

func (x JobStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use JobStatus.Descriptor instead.
func (JobStatus) EnumDescriptor() ([]byte, []int) {
	return file_livekit_agent_proto_rawDescGZIP(), []int{2}
}

type Job struct {
	state       protoimpl.MessageState `protogen:"open.v1"`
	Id          string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	DispatchId  string                 `protobuf:"bytes,9,opt,name=dispatch_id,json=dispatchId,proto3" json:"dispatch_id,omitempty"`
	Type        JobType                `protobuf:"varint,2,opt,name=type,proto3,enum=livekit.JobType" json:"type,omitempty"`
	Room        *Room                  `protobuf:"bytes,3,opt,name=room,proto3" json:"room,omitempty"`
	Participant *ParticipantInfo       `protobuf:"bytes,4,opt,name=participant,proto3,oneof" json:"participant,omitempty"`
	// Deprecated: Marked as deprecated in livekit_agent.proto.
	Namespace     string    `protobuf:"bytes,5,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Metadata      string    `protobuf:"bytes,6,opt,name=metadata,proto3" json:"metadata,omitempty"`
	AgentName     string    `protobuf:"bytes,7,opt,name=agent_name,json=agentName,proto3" json:"agent_name,omitempty"`
	State         *JobState `protobuf:"bytes,8,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Job) Reset() {
	*x = Job{}
	mi := &file_livekit_agent_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Job) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Job) ProtoMessage() {}

func (x *Job) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_agent_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Job.ProtoReflect.Descriptor instead.
func (*Job) Descriptor() ([]byte, []int) {
	return file_livekit_agent_proto_rawDescGZIP(), []int{0}
}

func (x *Job) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Job) GetDispatchId() string {
	if x != nil {
		return x.DispatchId
	}
	return ""
}

func (x *Job) GetType() JobType {
	if x != nil {
		return x.Type
	}
	return JobType_JT_ROOM
}

func (x *Job) GetRoom() *Room {
	if x != nil {
		return x.Room
	}
	return nil
}

func (x *Job) GetParticipant() *ParticipantInfo {
	if x != nil {
		return x.Participant
	}
	return nil
}

// Deprecated: Marked as deprecated in livekit_agent.proto.
func (x *Job) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *Job) GetMetadata() string {
	if x != nil {
		return x.Metadata
	}
	return ""
}

func (x *Job) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

func (x *Job) GetState() *JobState {
	if x != nil {
		return x.State
	}
	return nil
}

type JobState struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Status              JobStatus              `protobuf:"varint,1,opt,name=status,proto3,enum=livekit.JobStatus" json:"status,omitempty"`
	Error               string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	StartedAt           int64                  `protobuf:"varint,3,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	EndedAt             int64                  `protobuf:"varint,4,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`
	UpdatedAt           int64                  `protobuf:"varint,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	ParticipantIdentity string                 `protobuf:"bytes,6,opt,name=participant_identity,json=participantIdentity,proto3" json:"participant_identity,omitempty"`
	WorkerId            string                 `protobuf:"bytes,7,opt,name=worker_id,json=workerId,proto3" json:"worker_id,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *JobState) Reset() {
	*x = JobState{}
	mi := &file_livekit_agent_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobState) ProtoMessage() {}

func (x *JobState) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_agent_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobState.ProtoReflect.Descriptor instead.
func (*JobState) Descriptor() ([]byte, []int) {
	return file_livekit_agent_proto_rawDescGZIP(), []int{1}
}

func (x *JobState) GetStatus() JobStatus {
	if x != nil {
		return x.Status
	}
	return JobStatus_JS_PENDING
}

func (x *JobState) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *JobState) GetStartedAt() int64 {
	if x != nil {
		return x.StartedAt
	}
	return 0
}

func (x *JobState) GetEndedAt() int64 {
	if x != nil {
		return x.EndedAt
	}
	return 0
}

func (x *JobState) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *JobState) GetParticipantIdentity() string {
	if x != nil {
		return x.ParticipantIdentity
	}
	return ""
}

func (x *JobState) GetWorkerId() string {
	if x != nil {
		return x.WorkerId
	}
	return ""
}

// from Worker to Server
type WorkerMessage struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Message:
	//
	//	*WorkerMessage_Register
	//	*WorkerMessage_Availability
	//	*WorkerMessage_UpdateWorker
	//	*WorkerMessage_UpdateJob
	//	*WorkerMessage_Ping
	//	*WorkerMessage_SimulateJob
	//	*WorkerMessage_MigrateJob
	Message       isWorkerMessage_Message `protobuf_oneof:"message"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkerMessage) Reset() {
	*x = WorkerMessage{}
	mi := &file_livekit_agent_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkerMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkerMessage) ProtoMessage() {}

func (x *WorkerMessage) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_agent_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkerMessage.ProtoReflect.Descriptor instead.
func (*WorkerMessage) Descriptor() ([]byte, []int) {
	return file_livekit_agent_proto_rawDescGZIP(), []int{2}
}

func (x *WorkerMessage) GetMessage() isWorkerMessage_Message {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *WorkerMessage) GetRegister() *RegisterWorkerRequest {
	if x != nil {
		if x, ok := x.Message.(*WorkerMessage_Register); ok {
			return x.Register
		}
	}
	return nil
}

func (x *WorkerMessage) GetAvailability() *AvailabilityResponse {
	if x != nil {
		if x, ok := x.Message.(*WorkerMessage_Availability); ok {
			return x.Availability
		}
	}
	return nil
}

func (x *WorkerMessage) GetUpdateWorker() *UpdateWorkerStatus {
	if x != nil {
		if x, ok := x.Message.(*WorkerMessage_UpdateWorker); ok {
			return x.UpdateWorker
		}
	}
	return nil
}

func (x *WorkerMessage) GetUpdateJob() *UpdateJobStatus {
	if x != nil {
		if x, ok := x.Message.(*WorkerMessage_UpdateJob); ok {
			return x.UpdateJob
		}
	}
	return nil
}

func (x *WorkerMessage) GetPing() *WorkerPing {
	if x != nil {
		if x, ok := x.Message.(*WorkerMessage_Ping); ok {
			return x.Ping
		}
	}
	return nil
}

func (x *WorkerMessage) GetSimulateJob() *SimulateJobRequest {
	if x != nil {
		if x, ok := x.Message.(*WorkerMessage_SimulateJob); ok {
			return x.SimulateJob
		}
	}
	return nil
}

func (x *WorkerMessage) GetMigrateJob() *MigrateJobRequest {
	if x != nil {
		if x, ok := x.Message.(*WorkerMessage_MigrateJob); ok {
			return x.MigrateJob
		}
	}
	return nil
}

type isWorkerMessage_Message interface {
	isWorkerMessage_Message()
}

type WorkerMessage_Register struct {
	// agent workers need to register themselves with the server first
	Register *RegisterWorkerRequest `protobuf:"bytes,1,opt,name=register,proto3,oneof"`
}

type WorkerMessage_Availability struct {
	// worker confirms to server that it's available for a job, or declines it
	Availability *AvailabilityResponse `protobuf:"bytes,2,opt,name=availability,proto3,oneof"`
}

type WorkerMessage_UpdateWorker struct {
	// worker can update its status to the server, including taking itself out of the pool
	UpdateWorker *UpdateWorkerStatus `protobuf:"bytes,3,opt,name=update_worker,json=updateWorker,proto3,oneof"`
}

type WorkerMessage_UpdateJob struct {
	// job can send status updates to the server, useful for tracking progress
	UpdateJob *UpdateJobStatus `protobuf:"bytes,4,opt,name=update_job,json=updateJob,proto3,oneof"`
}

type WorkerMessage_Ping struct {
	Ping *WorkerPing `protobuf:"bytes,5,opt,name=ping,proto3,oneof"`
}

type WorkerMessage_SimulateJob struct {
	SimulateJob *SimulateJobRequest `protobuf:"bytes,6,opt,name=simulate_job,json=simulateJob,proto3,oneof"`
}

type WorkerMessage_MigrateJob struct {
	MigrateJob *MigrateJobRequest `protobuf:"bytes,7,opt,name=migrate_job,json=migrateJob,proto3,oneof"`
}

func (*WorkerMessage_Register) isWorkerMessage_Message() {}

func (*WorkerMessage_Availability) isWorkerMessage_Message() {}

func (*WorkerMessage_UpdateWorker) isWorkerMessage_Message() {}

func (*WorkerMessage_UpdateJob) isWorkerMessage_Message() {}

func (*WorkerMessage_Ping) isWorkerMessage_Message() {}

func (*WorkerMessage_SimulateJob) isWorkerMessage_Message() {}

func (*WorkerMessage_MigrateJob) isWorkerMessage_Message() {}

// from Server to Worker
type ServerMessage struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Message:
	//
	//	*ServerMessage_Register
	//	*ServerMessage_Availability
	//	*ServerMessage_Assignment
	//	*ServerMessage_Termination
	//	*ServerMessage_Pong
	Message       isServerMessage_Message `protobuf_oneof:"message"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServerMessage) Reset() {
	*x = ServerMessage{}
	mi := &file_livekit_agent_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerMessage) ProtoMessage() {}

func (x *ServerMessage) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_agent_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerMessage.ProtoReflect.Descriptor instead.
func (*ServerMessage) Descriptor() ([]byte, []int) {
	return file_livekit_agent_proto_rawDescGZIP(), []int{3}
}

func (x *ServerMessage) GetMessage() isServerMessage_Message {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *ServerMessage) GetRegister() *RegisterWorkerResponse {
	if x != nil {
		if x, ok := x.Message.(*ServerMessage_Register); ok {
			return x.Register
		}
	}
	return nil
}

func (x *ServerMessage) GetAvailability() *AvailabilityRequest {
	if x != nil {
		if x, ok := x.Message.(*ServerMessage_Availability); ok {
			return x.Availability
		}
	}
	return nil
}

func (x *ServerMessage) GetAssignment() *JobAssignment {
	if x != nil {
		if x, ok := x.Message.(*ServerMessage_Assignment); ok {
			return x.Assignment
		}
	}
	return nil
}

func (x *ServerMessage) GetTermination() *JobTermination {
	if x != nil {
		if x, ok := x.Message.(*ServerMessage_Termination); ok {
			return x.Termination
		}
	}
	return nil
}

func (x *ServerMessage) GetPong() *WorkerPong {
	if x != nil {
		if x, ok := x.Message.(*ServerMessage_Pong); ok {
			return x.Pong
		}
	}
	return nil
}

type isServerMessage_Message interface {
	isServerMessage_Message()
}

type ServerMessage_Register struct {
	// server confirms the registration, from this moment on, the worker is considered active
	Register *RegisterWorkerResponse `protobuf:"bytes,1,opt,name=register,proto3,oneof"`
}

type ServerMessage_Availability struct {
	// server asks worker to confirm availability for a job
	Availability *AvailabilityRequest `protobuf:"bytes,2,opt,name=availability,proto3,oneof"`
}

type ServerMessage_Assignment struct {
	Assignment *JobAssignment `protobuf:"bytes,3,opt,name=assignment,proto3,oneof"`
}

type ServerMessage_Termination struct {
	Termination *JobTermination `protobuf:"bytes,5,opt,name=termination,proto3,oneof"`
}

type ServerMessage_Pong struct {
	Pong *WorkerPong `protobuf:"bytes,4,opt,name=pong,proto3,oneof"`
}

func (*ServerMessage_Register) isServerMessage_Message() {}

func (*ServerMessage_Availability) isServerMessage_Message() {}

func (*ServerMessage_Assignment) isServerMessage_Message() {}

func (*ServerMessage_Termination) isServerMessage_Message() {}

func (*ServerMessage_Pong) isServerMessage_Message() {}

type SimulateJobRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          JobType                `protobuf:"varint,1,opt,name=type,proto3,enum=livekit.JobType" json:"type,omitempty"`
	Room          *Room                  `protobuf:"bytes,2,opt,name=room,proto3" json:"room,omitempty"`
	Participant   *ParticipantInfo       `protobuf:"bytes,3,opt,name=participant,proto3" json:"participant,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SimulateJobRequest) Reset() {
	*x = SimulateJobRequest{}
	mi := &file_livekit_agent_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SimulateJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimulateJobRequest) ProtoMessage() {}

func (x *SimulateJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_agent_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimulateJobRequest.ProtoReflect.Descriptor instead.
func (*SimulateJobRequest) Descriptor() ([]byte, []int) {
	return file_livekit_agent_proto_rawDescGZIP(), []int{4}
}

func (x *SimulateJobRequest) GetType() JobType {
	if x != nil {
		return x.Type
	}
	return JobType_JT_ROOM
}

func (x *SimulateJobRequest) GetRoom() *Room {
	if x != nil {
		return x.Room
	}
	return nil
}

func (x *SimulateJobRequest) GetParticipant() *ParticipantInfo {
	if x != nil {
		return x.Participant
	}
	return nil
}

type WorkerPing struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Timestamp     int64                  `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkerPing) Reset() {
	*x = WorkerPing{}
	mi := &file_livekit_agent_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkerPing) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkerPing) ProtoMessage() {}

func (x *WorkerPing) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_agent_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkerPing.ProtoReflect.Descriptor instead.
func (*WorkerPing) Descriptor() ([]byte, []int) {
	return file_livekit_agent_proto_rawDescGZIP(), []int{5}
}

func (x *WorkerPing) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type WorkerPong struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LastTimestamp int64                  `protobuf:"varint,1,opt,name=last_timestamp,json=lastTimestamp,proto3" json:"last_timestamp,omitempty"`
	Timestamp     int64                  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkerPong) Reset() {
	*x = WorkerPong{}
	mi := &file_livekit_agent_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkerPong) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkerPong) ProtoMessage() {}

func (x *WorkerPong) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_agent_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkerPong.ProtoReflect.Descriptor instead.
func (*WorkerPong) Descriptor() ([]byte, []int) {
	return file_livekit_agent_proto_rawDescGZIP(), []int{6}
}

func (x *WorkerPong) GetLastTimestamp() int64 {
	if x != nil {
		return x.LastTimestamp
	}
	return 0
}

func (x *WorkerPong) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type RegisterWorkerRequest struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	Type      JobType                `protobuf:"varint,1,opt,name=type,proto3,enum=livekit.JobType" json:"type,omitempty"`
	AgentName string                 `protobuf:"bytes,8,opt,name=agent_name,json=agentName,proto3" json:"agent_name,omitempty"`
	// string worker_id = 2;
	Version string `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	// string name = 4 [deprecated = true];
	PingInterval       uint32                 `protobuf:"varint,5,opt,name=ping_interval,json=pingInterval,proto3" json:"ping_interval,omitempty"`
	Namespace          *string                `protobuf:"bytes,6,opt,name=namespace,proto3,oneof" json:"namespace,omitempty"`
	AllowedPermissions *ParticipantPermission `protobuf:"bytes,7,opt,name=allowed_permissions,json=allowedPermissions,proto3" json:"allowed_permissions,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *RegisterWorkerRequest) Reset() {
	*x = RegisterWorkerRequest{}
	mi := &file_livekit_agent_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterWorkerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterWorkerRequest) ProtoMessage() {}

func (x *RegisterWorkerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_agent_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterWorkerRequest.ProtoReflect.Descriptor instead.
func (*RegisterWorkerRequest) Descriptor() ([]byte, []int) {
	return file_livekit_agent_proto_rawDescGZIP(), []int{7}
}

func (x *RegisterWorkerRequest) GetType() JobType {
	if x != nil {
		return x.Type
	}
	return JobType_JT_ROOM
}

func (x *RegisterWorkerRequest) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

func (x *RegisterWorkerRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *RegisterWorkerRequest) GetPingInterval() uint32 {
	if x != nil {
		return x.PingInterval
	}
	return 0
}

func (x *RegisterWorkerRequest) GetNamespace() string {
	if x != nil && x.Namespace != nil {
		return *x.Namespace
	}
	return ""
}

func (x *RegisterWorkerRequest) GetAllowedPermissions() *ParticipantPermission {
	if x != nil {
		return x.AllowedPermissions
	}
	return nil
}

type RegisterWorkerResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WorkerId      string                 `protobuf:"bytes,1,opt,name=worker_id,json=workerId,proto3" json:"worker_id,omitempty"`
	ServerInfo    *ServerInfo            `protobuf:"bytes,3,opt,name=server_info,json=serverInfo,proto3" json:"server_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterWorkerResponse) Reset() {
	*x = RegisterWorkerResponse{}
	mi := &file_livekit_agent_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterWorkerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterWorkerResponse) ProtoMessage() {}

func (x *RegisterWorkerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_agent_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterWorkerResponse.ProtoReflect.Descriptor instead.
func (*RegisterWorkerResponse) Descriptor() ([]byte, []int) {
	return file_livekit_agent_proto_rawDescGZIP(), []int{8}
}

func (x *RegisterWorkerResponse) GetWorkerId() string {
	if x != nil {
		return x.WorkerId
	}
	return ""
}

func (x *RegisterWorkerResponse) GetServerInfo() *ServerInfo {
	if x != nil {
		return x.ServerInfo
	}
	return nil
}

type MigrateJobRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// string job_id = 1 [deprecated = true];
	JobIds        []string `protobuf:"bytes,2,rep,name=job_ids,json=jobIds,proto3" json:"job_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MigrateJobRequest) Reset() {
	*x = MigrateJobRequest{}
	mi := &file_livekit_agent_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MigrateJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MigrateJobRequest) ProtoMessage() {}

func (x *MigrateJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_agent_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MigrateJobRequest.ProtoReflect.Descriptor instead.
func (*MigrateJobRequest) Descriptor() ([]byte, []int) {
	return file_livekit_agent_proto_rawDescGZIP(), []int{9}
}

func (x *MigrateJobRequest) GetJobIds() []string {
	if x != nil {
		return x.JobIds
	}
	return nil
}

type AvailabilityRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Job   *Job                   `protobuf:"bytes,1,opt,name=job,proto3" json:"job,omitempty"`
	// True when the job was previously assigned to another worker but has been
	// migrated due to different reasons (e.g. worker failure, job migration)
	Resuming      bool `protobuf:"varint,2,opt,name=resuming,proto3" json:"resuming,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AvailabilityRequest) Reset() {
	*x = AvailabilityRequest{}
	mi := &file_livekit_agent_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvailabilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailabilityRequest) ProtoMessage() {}

func (x *AvailabilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_agent_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailabilityRequest.ProtoReflect.Descriptor instead.
func (*AvailabilityRequest) Descriptor() ([]byte, []int) {
	return file_livekit_agent_proto_rawDescGZIP(), []int{10}
}

func (x *AvailabilityRequest) GetJob() *Job {
	if x != nil {
		return x.Job
	}
	return nil
}

func (x *AvailabilityRequest) GetResuming() bool {
	if x != nil {
		return x.Resuming
	}
	return false
}

type AvailabilityResponse struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	JobId                 string                 `protobuf:"bytes,1,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
	Available             bool                   `protobuf:"varint,2,opt,name=available,proto3" json:"available,omitempty"`
	SupportsResume        bool                   `protobuf:"varint,3,opt,name=supports_resume,json=supportsResume,proto3" json:"supports_resume,omitempty"`
	Terminate             bool                   `protobuf:"varint,8,opt,name=terminate,proto3" json:"terminate,omitempty"`
	ParticipantName       string                 `protobuf:"bytes,4,opt,name=participant_name,json=participantName,proto3" json:"participant_name,omitempty"`
	ParticipantIdentity   string                 `protobuf:"bytes,5,opt,name=participant_identity,json=participantIdentity,proto3" json:"participant_identity,omitempty"`
	ParticipantMetadata   string                 `protobuf:"bytes,6,opt,name=participant_metadata,json=participantMetadata,proto3" json:"participant_metadata,omitempty"`
	ParticipantAttributes map[string]string      `protobuf:"bytes,7,rep,name=participant_attributes,json=participantAttributes,proto3" json:"participant_attributes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // NEXT_ID: 9
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *AvailabilityResponse) Reset() {
	*x = AvailabilityResponse{}
	mi := &file_livekit_agent_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvailabilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvailabilityResponse) ProtoMessage() {}

func (x *AvailabilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_agent_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvailabilityResponse.ProtoReflect.Descriptor instead.
func (*AvailabilityResponse) Descriptor() ([]byte, []int) {
	return file_livekit_agent_proto_rawDescGZIP(), []int{11}
}

func (x *AvailabilityResponse) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *AvailabilityResponse) GetAvailable() bool {
	if x != nil {
		return x.Available
	}
	return false
}

func (x *AvailabilityResponse) GetSupportsResume() bool {
	if x != nil {
		return x.SupportsResume
	}
	return false
}

func (x *AvailabilityResponse) GetTerminate() bool {
	if x != nil {
		return x.Terminate
	}
	return false
}

func (x *AvailabilityResponse) GetParticipantName() string {
	if x != nil {
		return x.ParticipantName
	}
	return ""
}

func (x *AvailabilityResponse) GetParticipantIdentity() string {
	if x != nil {
		return x.ParticipantIdentity
	}
	return ""
}

func (x *AvailabilityResponse) GetParticipantMetadata() string {
	if x != nil {
		return x.ParticipantMetadata
	}
	return ""
}

func (x *AvailabilityResponse) GetParticipantAttributes() map[string]string {
	if x != nil {
		return x.ParticipantAttributes
	}
	return nil
}

type UpdateJobStatus struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	JobId string                 `protobuf:"bytes,1,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
	// The worker can indicate the job end by either specifying SUCCESS or FAILED
	Status JobStatus `protobuf:"varint,2,opt,name=status,proto3,enum=livekit.JobStatus" json:"status,omitempty"`
	// metadata shown on the dashboard, useful for debugging
	Error         string `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateJobStatus) Reset() {
	*x = UpdateJobStatus{}
	mi := &file_livekit_agent_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateJobStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateJobStatus) ProtoMessage() {}

func (x *UpdateJobStatus) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_agent_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateJobStatus.ProtoReflect.Descriptor instead.
func (*UpdateJobStatus) Descriptor() ([]byte, []int) {
	return file_livekit_agent_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateJobStatus) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *UpdateJobStatus) GetStatus() JobStatus {
	if x != nil {
		return x.Status
	}
	return JobStatus_JS_PENDING
}

func (x *UpdateJobStatus) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

type UpdateWorkerStatus struct {
	state  protoimpl.MessageState `protogen:"open.v1"`
	Status *WorkerStatus          `protobuf:"varint,1,opt,name=status,proto3,enum=livekit.WorkerStatus,oneof" json:"status,omitempty"`
	// optional string metadata = 2 [deprecated=true];
	Load          float32 `protobuf:"fixed32,3,opt,name=load,proto3" json:"load,omitempty"`
	JobCount      uint32  `protobuf:"varint,4,opt,name=job_count,json=jobCount,proto3" json:"job_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateWorkerStatus) Reset() {
	*x = UpdateWorkerStatus{}
	mi := &file_livekit_agent_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateWorkerStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWorkerStatus) ProtoMessage() {}

func (x *UpdateWorkerStatus) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_agent_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWorkerStatus.ProtoReflect.Descriptor instead.
func (*UpdateWorkerStatus) Descriptor() ([]byte, []int) {
	return file_livekit_agent_proto_rawDescGZIP(), []int{13}
}

func (x *UpdateWorkerStatus) GetStatus() WorkerStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return WorkerStatus_WS_AVAILABLE
}

func (x *UpdateWorkerStatus) GetLoad() float32 {
	if x != nil {
		return x.Load
	}
	return 0
}

func (x *UpdateWorkerStatus) GetJobCount() uint32 {
	if x != nil {
		return x.JobCount
	}
	return 0
}

type JobAssignment struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Job           *Job                   `protobuf:"bytes,1,opt,name=job,proto3" json:"job,omitempty"`
	Url           *string                `protobuf:"bytes,2,opt,name=url,proto3,oneof" json:"url,omitempty"`
	Token         string                 `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobAssignment) Reset() {
	*x = JobAssignment{}
	mi := &file_livekit_agent_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobAssignment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobAssignment) ProtoMessage() {}

func (x *JobAssignment) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_agent_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobAssignment.ProtoReflect.Descriptor instead.
func (*JobAssignment) Descriptor() ([]byte, []int) {
	return file_livekit_agent_proto_rawDescGZIP(), []int{14}
}

func (x *JobAssignment) GetJob() *Job {
	if x != nil {
		return x.Job
	}
	return nil
}

func (x *JobAssignment) GetUrl() string {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return ""
}

func (x *JobAssignment) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type JobTermination struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	JobId         string                 `protobuf:"bytes,1,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobTermination) Reset() {
	*x = JobTermination{}
	mi := &file_livekit_agent_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobTermination) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobTermination) ProtoMessage() {}

func (x *JobTermination) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_agent_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobTermination.ProtoReflect.Descriptor instead.
func (*JobTermination) Descriptor() ([]byte, []int) {
	return file_livekit_agent_proto_rawDescGZIP(), []int{15}
}

func (x *JobTermination) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

var File_livekit_agent_proto protoreflect.FileDescriptor

const file_livekit_agent_proto_rawDesc = "" +
	"\n" +
	"\x13livekit_agent.proto\x12\alivekit\x1a\x14livekit_models.proto\"\xd6\x02\n" +
	"\x03Job\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1f\n" +
	"\vdispatch_id\x18\t \x01(\tR\n" +
	"dispatchId\x12$\n" +
	"\x04type\x18\x02 \x01(\x0e2\x10.livekit.JobTypeR\x04type\x12!\n" +
	"\x04room\x18\x03 \x01(\v2\r.livekit.RoomR\x04room\x12?\n" +
	"\vparticipant\x18\x04 \x01(\v2\x18.livekit.ParticipantInfoH\x00R\vparticipant\x88\x01\x01\x12 \n" +
	"\tnamespace\x18\x05 \x01(\tB\x02\x18\x01R\tnamespace\x12\x1a\n" +
	"\bmetadata\x18\x06 \x01(\tR\bmetadata\x12\x1d\n" +
	"\n" +
	"agent_name\x18\a \x01(\tR\tagentName\x12'\n" +
	"\x05state\x18\b \x01(\v2\x11.livekit.JobStateR\x05stateB\x0e\n" +
	"\f_participant\"\xf5\x01\n" +
	"\bJobState\x12*\n" +
	"\x06status\x18\x01 \x01(\x0e2\x12.livekit.JobStatusR\x06status\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\x12\x1d\n" +
	"\n" +
	"started_at\x18\x03 \x01(\x03R\tstartedAt\x12\x19\n" +
	"\bended_at\x18\x04 \x01(\x03R\aendedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\x05 \x01(\x03R\tupdatedAt\x121\n" +
	"\x14participant_identity\x18\x06 \x01(\tR\x13participantIdentity\x12\x1b\n" +
	"\tworker_id\x18\a \x01(\tR\bworkerId\"\xc8\x03\n" +
	"\rWorkerMessage\x12<\n" +
	"\bregister\x18\x01 \x01(\v2\x1e.livekit.RegisterWorkerRequestH\x00R\bregister\x12C\n" +
	"\favailability\x18\x02 \x01(\v2\x1d.livekit.AvailabilityResponseH\x00R\favailability\x12B\n" +
	"\rupdate_worker\x18\x03 \x01(\v2\x1b.livekit.UpdateWorkerStatusH\x00R\fupdateWorker\x129\n" +
	"\n" +
	"update_job\x18\x04 \x01(\v2\x18.livekit.UpdateJobStatusH\x00R\tupdateJob\x12)\n" +
	"\x04ping\x18\x05 \x01(\v2\x13.livekit.WorkerPingH\x00R\x04ping\x12@\n" +
	"\fsimulate_job\x18\x06 \x01(\v2\x1b.livekit.SimulateJobRequestH\x00R\vsimulateJob\x12=\n" +
	"\vmigrate_job\x18\a \x01(\v2\x1a.livekit.MigrateJobRequestH\x00R\n" +
	"migrateJobB\t\n" +
	"\amessage\"\xbf\x02\n" +
	"\rServerMessage\x12=\n" +
	"\bregister\x18\x01 \x01(\v2\x1f.livekit.RegisterWorkerResponseH\x00R\bregister\x12B\n" +
	"\favailability\x18\x02 \x01(\v2\x1c.livekit.AvailabilityRequestH\x00R\favailability\x128\n" +
	"\n" +
	"assignment\x18\x03 \x01(\v2\x16.livekit.JobAssignmentH\x00R\n" +
	"assignment\x12;\n" +
	"\vtermination\x18\x05 \x01(\v2\x17.livekit.JobTerminationH\x00R\vtermination\x12)\n" +
	"\x04pong\x18\x04 \x01(\v2\x13.livekit.WorkerPongH\x00R\x04pongB\t\n" +
	"\amessage\"\x99\x01\n" +
	"\x12SimulateJobRequest\x12$\n" +
	"\x04type\x18\x01 \x01(\x0e2\x10.livekit.JobTypeR\x04type\x12!\n" +
	"\x04room\x18\x02 \x01(\v2\r.livekit.RoomR\x04room\x12:\n" +
	"\vparticipant\x18\x03 \x01(\v2\x18.livekit.ParticipantInfoR\vparticipant\"*\n" +
	"\n" +
	"WorkerPing\x12\x1c\n" +
	"\ttimestamp\x18\x01 \x01(\x03R\ttimestamp\"Q\n" +
	"\n" +
	"WorkerPong\x12%\n" +
	"\x0elast_timestamp\x18\x01 \x01(\x03R\rlastTimestamp\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x03R\ttimestamp\"\x9d\x02\n" +
	"\x15RegisterWorkerRequest\x12$\n" +
	"\x04type\x18\x01 \x01(\x0e2\x10.livekit.JobTypeR\x04type\x12\x1d\n" +
	"\n" +
	"agent_name\x18\b \x01(\tR\tagentName\x12\x18\n" +
	"\aversion\x18\x03 \x01(\tR\aversion\x12#\n" +
	"\rping_interval\x18\x05 \x01(\rR\fpingInterval\x12!\n" +
	"\tnamespace\x18\x06 \x01(\tH\x00R\tnamespace\x88\x01\x01\x12O\n" +
	"\x13allowed_permissions\x18\a \x01(\v2\x1e.livekit.ParticipantPermissionR\x12allowedPermissionsB\f\n" +
	"\n" +
	"_namespace\"k\n" +
	"\x16RegisterWorkerResponse\x12\x1b\n" +
	"\tworker_id\x18\x01 \x01(\tR\bworkerId\x124\n" +
	"\vserver_info\x18\x03 \x01(\v2\x13.livekit.ServerInfoR\n" +
	"serverInfo\",\n" +
	"\x11MigrateJobRequest\x12\x17\n" +
	"\ajob_ids\x18\x02 \x03(\tR\x06jobIds\"Q\n" +
	"\x13AvailabilityRequest\x12\x1e\n" +
	"\x03job\x18\x01 \x01(\v2\f.livekit.JobR\x03job\x12\x1a\n" +
	"\bresuming\x18\x02 \x01(\bR\bresuming\"\xde\x03\n" +
	"\x14AvailabilityResponse\x12\x15\n" +
	"\x06job_id\x18\x01 \x01(\tR\x05jobId\x12\x1c\n" +
	"\tavailable\x18\x02 \x01(\bR\tavailable\x12'\n" +
	"\x0fsupports_resume\x18\x03 \x01(\bR\x0esupportsResume\x12\x1c\n" +
	"\tterminate\x18\b \x01(\bR\tterminate\x12)\n" +
	"\x10participant_name\x18\x04 \x01(\tR\x0fparticipantName\x121\n" +
	"\x14participant_identity\x18\x05 \x01(\tR\x13participantIdentity\x121\n" +
	"\x14participant_metadata\x18\x06 \x01(\tR\x13participantMetadata\x12o\n" +
	"\x16participant_attributes\x18\a \x03(\v28.livekit.AvailabilityResponse.ParticipantAttributesEntryR\x15participantAttributes\x1aH\n" +
	"\x1aParticipantAttributesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"j\n" +
	"\x0fUpdateJobStatus\x12\x15\n" +
	"\x06job_id\x18\x01 \x01(\tR\x05jobId\x12*\n" +
	"\x06status\x18\x02 \x01(\x0e2\x12.livekit.JobStatusR\x06status\x12\x14\n" +
	"\x05error\x18\x03 \x01(\tR\x05error\"\x84\x01\n" +
	"\x12UpdateWorkerStatus\x122\n" +
	"\x06status\x18\x01 \x01(\x0e2\x15.livekit.WorkerStatusH\x00R\x06status\x88\x01\x01\x12\x12\n" +
	"\x04load\x18\x03 \x01(\x02R\x04load\x12\x1b\n" +
	"\tjob_count\x18\x04 \x01(\rR\bjobCountB\t\n" +
	"\a_status\"d\n" +
	"\rJobAssignment\x12\x1e\n" +
	"\x03job\x18\x01 \x01(\v2\f.livekit.JobR\x03job\x12\x15\n" +
	"\x03url\x18\x02 \x01(\tH\x00R\x03url\x88\x01\x01\x12\x14\n" +
	"\x05token\x18\x03 \x01(\tR\x05tokenB\x06\n" +
	"\x04_url\"'\n" +
	"\x0eJobTermination\x12\x15\n" +
	"\x06job_id\x18\x01 \x01(\tR\x05jobId*<\n" +
	"\aJobType\x12\v\n" +
	"\aJT_ROOM\x10\x00\x12\x10\n" +
	"\fJT_PUBLISHER\x10\x01\x12\x12\n" +
	"\x0eJT_PARTICIPANT\x10\x02*-\n" +
	"\fWorkerStatus\x12\x10\n" +
	"\fWS_AVAILABLE\x10\x00\x12\v\n" +
	"\aWS_FULL\x10\x01*J\n" +
	"\tJobStatus\x12\x0e\n" +
	"\n" +
	"JS_PENDING\x10\x00\x12\x0e\n" +
	"\n" +
	"JS_RUNNING\x10\x01\x12\x0e\n" +
	"\n" +
	"JS_SUCCESS\x10\x02\x12\r\n" +
	"\tJS_FAILED\x10\x03BFZ#github.com/livekit/protocol/livekit\xaa\x02\rLiveKit.Proto\xea\x02\x0eLiveKit::Protob\x06proto3"

var (
	file_livekit_agent_proto_rawDescOnce sync.Once
	file_livekit_agent_proto_rawDescData []byte
)

func file_livekit_agent_proto_rawDescGZIP() []byte {
	file_livekit_agent_proto_rawDescOnce.Do(func() {
		file_livekit_agent_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_livekit_agent_proto_rawDesc), len(file_livekit_agent_proto_rawDesc)))
	})
	return file_livekit_agent_proto_rawDescData
}

var file_livekit_agent_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_livekit_agent_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_livekit_agent_proto_goTypes = []any{
	(JobType)(0),                   // 0: livekit.JobType
	(WorkerStatus)(0),              // 1: livekit.WorkerStatus
	(JobStatus)(0),                 // 2: livekit.JobStatus
	(*Job)(nil),                    // 3: livekit.Job
	(*JobState)(nil),               // 4: livekit.JobState
	(*WorkerMessage)(nil),          // 5: livekit.WorkerMessage
	(*ServerMessage)(nil),          // 6: livekit.ServerMessage
	(*SimulateJobRequest)(nil),     // 7: livekit.SimulateJobRequest
	(*WorkerPing)(nil),             // 8: livekit.WorkerPing
	(*WorkerPong)(nil),             // 9: livekit.WorkerPong
	(*RegisterWorkerRequest)(nil),  // 10: livekit.RegisterWorkerRequest
	(*RegisterWorkerResponse)(nil), // 11: livekit.RegisterWorkerResponse
	(*MigrateJobRequest)(nil),      // 12: livekit.MigrateJobRequest
	(*AvailabilityRequest)(nil),    // 13: livekit.AvailabilityRequest
	(*AvailabilityResponse)(nil),   // 14: livekit.AvailabilityResponse
	(*UpdateJobStatus)(nil),        // 15: livekit.UpdateJobStatus
	(*UpdateWorkerStatus)(nil),     // 16: livekit.UpdateWorkerStatus
	(*JobAssignment)(nil),          // 17: livekit.JobAssignment
	(*JobTermination)(nil),         // 18: livekit.JobTermination
	nil,                            // 19: livekit.AvailabilityResponse.ParticipantAttributesEntry
	(*Room)(nil),                   // 20: livekit.Room
	(*ParticipantInfo)(nil),        // 21: livekit.ParticipantInfo
	(*ParticipantPermission)(nil),  // 22: livekit.ParticipantPermission
	(*ServerInfo)(nil),             // 23: livekit.ServerInfo
}
var file_livekit_agent_proto_depIdxs = []int32{
	0,  // 0: livekit.Job.type:type_name -> livekit.JobType
	20, // 1: livekit.Job.room:type_name -> livekit.Room
	21, // 2: livekit.Job.participant:type_name -> livekit.ParticipantInfo
	4,  // 3: livekit.Job.state:type_name -> livekit.JobState
	2,  // 4: livekit.JobState.status:type_name -> livekit.JobStatus
	10, // 5: livekit.WorkerMessage.register:type_name -> livekit.RegisterWorkerRequest
	14, // 6: livekit.WorkerMessage.availability:type_name -> livekit.AvailabilityResponse
	16, // 7: livekit.WorkerMessage.update_worker:type_name -> livekit.UpdateWorkerStatus
	15, // 8: livekit.WorkerMessage.update_job:type_name -> livekit.UpdateJobStatus
	8,  // 9: livekit.WorkerMessage.ping:type_name -> livekit.WorkerPing
	7,  // 10: livekit.WorkerMessage.simulate_job:type_name -> livekit.SimulateJobRequest
	12, // 11: livekit.WorkerMessage.migrate_job:type_name -> livekit.MigrateJobRequest
	11, // 12: livekit.ServerMessage.register:type_name -> livekit.RegisterWorkerResponse
	13, // 13: livekit.ServerMessage.availability:type_name -> livekit.AvailabilityRequest
	17, // 14: livekit.ServerMessage.assignment:type_name -> livekit.JobAssignment
	18, // 15: livekit.ServerMessage.termination:type_name -> livekit.JobTermination
	9,  // 16: livekit.ServerMessage.pong:type_name -> livekit.WorkerPong
	0,  // 17: livekit.SimulateJobRequest.type:type_name -> livekit.JobType
	20, // 18: livekit.SimulateJobRequest.room:type_name -> livekit.Room
	21, // 19: livekit.SimulateJobRequest.participant:type_name -> livekit.ParticipantInfo
	0,  // 20: livekit.RegisterWorkerRequest.type:type_name -> livekit.JobType
	22, // 21: livekit.RegisterWorkerRequest.allowed_permissions:type_name -> livekit.ParticipantPermission
	23, // 22: livekit.RegisterWorkerResponse.server_info:type_name -> livekit.ServerInfo
	3,  // 23: livekit.AvailabilityRequest.job:type_name -> livekit.Job
	19, // 24: livekit.AvailabilityResponse.participant_attributes:type_name -> livekit.AvailabilityResponse.ParticipantAttributesEntry
	2,  // 25: livekit.UpdateJobStatus.status:type_name -> livekit.JobStatus
	1,  // 26: livekit.UpdateWorkerStatus.status:type_name -> livekit.WorkerStatus
	3,  // 27: livekit.JobAssignment.job:type_name -> livekit.Job
	28, // [28:28] is the sub-list for method output_type
	28, // [28:28] is the sub-list for method input_type
	28, // [28:28] is the sub-list for extension type_name
	28, // [28:28] is the sub-list for extension extendee
	0,  // [0:28] is the sub-list for field type_name
}

func init() { file_livekit_agent_proto_init() }
func file_livekit_agent_proto_init() {
	if File_livekit_agent_proto != nil {
		return
	}
	file_livekit_models_proto_init()
	file_livekit_agent_proto_msgTypes[0].OneofWrappers = []any{}
	file_livekit_agent_proto_msgTypes[2].OneofWrappers = []any{
		(*WorkerMessage_Register)(nil),
		(*WorkerMessage_Availability)(nil),
		(*WorkerMessage_UpdateWorker)(nil),
		(*WorkerMessage_UpdateJob)(nil),
		(*WorkerMessage_Ping)(nil),
		(*WorkerMessage_SimulateJob)(nil),
		(*WorkerMessage_MigrateJob)(nil),
	}
	file_livekit_agent_proto_msgTypes[3].OneofWrappers = []any{
		(*ServerMessage_Register)(nil),
		(*ServerMessage_Availability)(nil),
		(*ServerMessage_Assignment)(nil),
		(*ServerMessage_Termination)(nil),
		(*ServerMessage_Pong)(nil),
	}
	file_livekit_agent_proto_msgTypes[7].OneofWrappers = []any{}
	file_livekit_agent_proto_msgTypes[13].OneofWrappers = []any{}
	file_livekit_agent_proto_msgTypes[14].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_livekit_agent_proto_rawDesc), len(file_livekit_agent_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_livekit_agent_proto_goTypes,
		DependencyIndexes: file_livekit_agent_proto_depIdxs,
		EnumInfos:         file_livekit_agent_proto_enumTypes,
		MessageInfos:      file_livekit_agent_proto_msgTypes,
	}.Build()
	File_livekit_agent_proto = out.File
	file_livekit_agent_proto_goTypes = nil
	file_livekit_agent_proto_depIdxs = nil
}
