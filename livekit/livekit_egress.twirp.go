// Code generated by protoc-gen-twirp v8.1.3, DO NOT EDIT.
// source: livekit_egress.proto

package livekit

import context "context"
import fmt "fmt"
import http "net/http"
import io "io"
import json "encoding/json"
import strconv "strconv"
import strings "strings"

import protojson "google.golang.org/protobuf/encoding/protojson"
import proto "google.golang.org/protobuf/proto"
import twirp "github.com/twitchtv/twirp"
import ctxsetters "github.com/twitchtv/twirp/ctxsetters"

// Version compatibility assertion.
// If the constant is not defined in the package, that likely means
// the package needs to be updated to work with this generated code.
// See https://twitchtv.github.io/twirp/docs/version_matrix.html
const _ = twirp.TwirpPackageMinVersion_8_1_0

// ================
// Egress Interface
// ================

type Egress interface {
	// start recording or streaming a room, participant, or tracks
	StartRoomCompositeEgress(context.Context, *RoomCompositeEgressRequest) (*EgressInfo, error)

	StartWebEgress(context.Context, *WebEgressRequest) (*EgressInfo, error)

	StartParticipantEgress(context.Context, *ParticipantEgressRequest) (*EgressInfo, error)

	StartTrackCompositeEgress(context.Context, *TrackCompositeEgressRequest) (*EgressInfo, error)

	StartTrackEgress(context.Context, *TrackEgressRequest) (*EgressInfo, error)

	// update web composite layout
	UpdateLayout(context.Context, *UpdateLayoutRequest) (*EgressInfo, error)

	// add or remove stream endpoints
	UpdateStream(context.Context, *UpdateStreamRequest) (*EgressInfo, error)

	// list available egress
	ListEgress(context.Context, *ListEgressRequest) (*ListEgressResponse, error)

	// stop a recording or stream
	StopEgress(context.Context, *StopEgressRequest) (*EgressInfo, error)
}

// ======================
// Egress Protobuf Client
// ======================

type egressProtobufClient struct {
	client      HTTPClient
	urls        [9]string
	interceptor twirp.Interceptor
	opts        twirp.ClientOptions
}

// NewEgressProtobufClient creates a Protobuf client that implements the Egress interface.
// It communicates using Protobuf and can be configured with a custom HTTPClient.
func NewEgressProtobufClient(baseURL string, client HTTPClient, opts ...twirp.ClientOption) Egress {
	if c, ok := client.(*http.Client); ok {
		client = withoutRedirects(c)
	}

	clientOpts := twirp.ClientOptions{}
	for _, o := range opts {
		o(&clientOpts)
	}

	// Using ReadOpt allows backwards and forwards compatibility with new options in the future
	literalURLs := false
	_ = clientOpts.ReadOpt("literalURLs", &literalURLs)
	var pathPrefix string
	if ok := clientOpts.ReadOpt("pathPrefix", &pathPrefix); !ok {
		pathPrefix = "/twirp" // default prefix
	}

	// Build method URLs: <baseURL>[<prefix>]/<package>.<Service>/<Method>
	serviceURL := sanitizeBaseURL(baseURL)
	serviceURL += baseServicePath(pathPrefix, "livekit", "Egress")
	urls := [9]string{
		serviceURL + "StartRoomCompositeEgress",
		serviceURL + "StartWebEgress",
		serviceURL + "StartParticipantEgress",
		serviceURL + "StartTrackCompositeEgress",
		serviceURL + "StartTrackEgress",
		serviceURL + "UpdateLayout",
		serviceURL + "UpdateStream",
		serviceURL + "ListEgress",
		serviceURL + "StopEgress",
	}

	return &egressProtobufClient{
		client:      client,
		urls:        urls,
		interceptor: twirp.ChainInterceptors(clientOpts.Interceptors...),
		opts:        clientOpts,
	}
}

func (c *egressProtobufClient) StartRoomCompositeEgress(ctx context.Context, in *RoomCompositeEgressRequest) (*EgressInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Egress")
	ctx = ctxsetters.WithMethodName(ctx, "StartRoomCompositeEgress")
	caller := c.callStartRoomCompositeEgress
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *RoomCompositeEgressRequest) (*EgressInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*RoomCompositeEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*RoomCompositeEgressRequest) when calling interceptor")
					}
					return c.callStartRoomCompositeEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *egressProtobufClient) callStartRoomCompositeEgress(ctx context.Context, in *RoomCompositeEgressRequest) (*EgressInfo, error) {
	out := new(EgressInfo)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[0], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *egressProtobufClient) StartWebEgress(ctx context.Context, in *WebEgressRequest) (*EgressInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Egress")
	ctx = ctxsetters.WithMethodName(ctx, "StartWebEgress")
	caller := c.callStartWebEgress
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *WebEgressRequest) (*EgressInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*WebEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*WebEgressRequest) when calling interceptor")
					}
					return c.callStartWebEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *egressProtobufClient) callStartWebEgress(ctx context.Context, in *WebEgressRequest) (*EgressInfo, error) {
	out := new(EgressInfo)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[1], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *egressProtobufClient) StartParticipantEgress(ctx context.Context, in *ParticipantEgressRequest) (*EgressInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Egress")
	ctx = ctxsetters.WithMethodName(ctx, "StartParticipantEgress")
	caller := c.callStartParticipantEgress
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ParticipantEgressRequest) (*EgressInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ParticipantEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ParticipantEgressRequest) when calling interceptor")
					}
					return c.callStartParticipantEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *egressProtobufClient) callStartParticipantEgress(ctx context.Context, in *ParticipantEgressRequest) (*EgressInfo, error) {
	out := new(EgressInfo)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[2], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *egressProtobufClient) StartTrackCompositeEgress(ctx context.Context, in *TrackCompositeEgressRequest) (*EgressInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Egress")
	ctx = ctxsetters.WithMethodName(ctx, "StartTrackCompositeEgress")
	caller := c.callStartTrackCompositeEgress
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *TrackCompositeEgressRequest) (*EgressInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*TrackCompositeEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*TrackCompositeEgressRequest) when calling interceptor")
					}
					return c.callStartTrackCompositeEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *egressProtobufClient) callStartTrackCompositeEgress(ctx context.Context, in *TrackCompositeEgressRequest) (*EgressInfo, error) {
	out := new(EgressInfo)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[3], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *egressProtobufClient) StartTrackEgress(ctx context.Context, in *TrackEgressRequest) (*EgressInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Egress")
	ctx = ctxsetters.WithMethodName(ctx, "StartTrackEgress")
	caller := c.callStartTrackEgress
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *TrackEgressRequest) (*EgressInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*TrackEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*TrackEgressRequest) when calling interceptor")
					}
					return c.callStartTrackEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *egressProtobufClient) callStartTrackEgress(ctx context.Context, in *TrackEgressRequest) (*EgressInfo, error) {
	out := new(EgressInfo)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[4], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *egressProtobufClient) UpdateLayout(ctx context.Context, in *UpdateLayoutRequest) (*EgressInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Egress")
	ctx = ctxsetters.WithMethodName(ctx, "UpdateLayout")
	caller := c.callUpdateLayout
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *UpdateLayoutRequest) (*EgressInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateLayoutRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateLayoutRequest) when calling interceptor")
					}
					return c.callUpdateLayout(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *egressProtobufClient) callUpdateLayout(ctx context.Context, in *UpdateLayoutRequest) (*EgressInfo, error) {
	out := new(EgressInfo)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[5], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *egressProtobufClient) UpdateStream(ctx context.Context, in *UpdateStreamRequest) (*EgressInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Egress")
	ctx = ctxsetters.WithMethodName(ctx, "UpdateStream")
	caller := c.callUpdateStream
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *UpdateStreamRequest) (*EgressInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateStreamRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateStreamRequest) when calling interceptor")
					}
					return c.callUpdateStream(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *egressProtobufClient) callUpdateStream(ctx context.Context, in *UpdateStreamRequest) (*EgressInfo, error) {
	out := new(EgressInfo)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[6], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *egressProtobufClient) ListEgress(ctx context.Context, in *ListEgressRequest) (*ListEgressResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Egress")
	ctx = ctxsetters.WithMethodName(ctx, "ListEgress")
	caller := c.callListEgress
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ListEgressRequest) (*ListEgressResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListEgressRequest) when calling interceptor")
					}
					return c.callListEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListEgressResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListEgressResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *egressProtobufClient) callListEgress(ctx context.Context, in *ListEgressRequest) (*ListEgressResponse, error) {
	out := new(ListEgressResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[7], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *egressProtobufClient) StopEgress(ctx context.Context, in *StopEgressRequest) (*EgressInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Egress")
	ctx = ctxsetters.WithMethodName(ctx, "StopEgress")
	caller := c.callStopEgress
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *StopEgressRequest) (*EgressInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*StopEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*StopEgressRequest) when calling interceptor")
					}
					return c.callStopEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *egressProtobufClient) callStopEgress(ctx context.Context, in *StopEgressRequest) (*EgressInfo, error) {
	out := new(EgressInfo)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[8], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

// ==================
// Egress JSON Client
// ==================

type egressJSONClient struct {
	client      HTTPClient
	urls        [9]string
	interceptor twirp.Interceptor
	opts        twirp.ClientOptions
}

// NewEgressJSONClient creates a JSON client that implements the Egress interface.
// It communicates using JSON and can be configured with a custom HTTPClient.
func NewEgressJSONClient(baseURL string, client HTTPClient, opts ...twirp.ClientOption) Egress {
	if c, ok := client.(*http.Client); ok {
		client = withoutRedirects(c)
	}

	clientOpts := twirp.ClientOptions{}
	for _, o := range opts {
		o(&clientOpts)
	}

	// Using ReadOpt allows backwards and forwards compatibility with new options in the future
	literalURLs := false
	_ = clientOpts.ReadOpt("literalURLs", &literalURLs)
	var pathPrefix string
	if ok := clientOpts.ReadOpt("pathPrefix", &pathPrefix); !ok {
		pathPrefix = "/twirp" // default prefix
	}

	// Build method URLs: <baseURL>[<prefix>]/<package>.<Service>/<Method>
	serviceURL := sanitizeBaseURL(baseURL)
	serviceURL += baseServicePath(pathPrefix, "livekit", "Egress")
	urls := [9]string{
		serviceURL + "StartRoomCompositeEgress",
		serviceURL + "StartWebEgress",
		serviceURL + "StartParticipantEgress",
		serviceURL + "StartTrackCompositeEgress",
		serviceURL + "StartTrackEgress",
		serviceURL + "UpdateLayout",
		serviceURL + "UpdateStream",
		serviceURL + "ListEgress",
		serviceURL + "StopEgress",
	}

	return &egressJSONClient{
		client:      client,
		urls:        urls,
		interceptor: twirp.ChainInterceptors(clientOpts.Interceptors...),
		opts:        clientOpts,
	}
}

func (c *egressJSONClient) StartRoomCompositeEgress(ctx context.Context, in *RoomCompositeEgressRequest) (*EgressInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Egress")
	ctx = ctxsetters.WithMethodName(ctx, "StartRoomCompositeEgress")
	caller := c.callStartRoomCompositeEgress
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *RoomCompositeEgressRequest) (*EgressInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*RoomCompositeEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*RoomCompositeEgressRequest) when calling interceptor")
					}
					return c.callStartRoomCompositeEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *egressJSONClient) callStartRoomCompositeEgress(ctx context.Context, in *RoomCompositeEgressRequest) (*EgressInfo, error) {
	out := new(EgressInfo)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[0], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *egressJSONClient) StartWebEgress(ctx context.Context, in *WebEgressRequest) (*EgressInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Egress")
	ctx = ctxsetters.WithMethodName(ctx, "StartWebEgress")
	caller := c.callStartWebEgress
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *WebEgressRequest) (*EgressInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*WebEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*WebEgressRequest) when calling interceptor")
					}
					return c.callStartWebEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *egressJSONClient) callStartWebEgress(ctx context.Context, in *WebEgressRequest) (*EgressInfo, error) {
	out := new(EgressInfo)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[1], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *egressJSONClient) StartParticipantEgress(ctx context.Context, in *ParticipantEgressRequest) (*EgressInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Egress")
	ctx = ctxsetters.WithMethodName(ctx, "StartParticipantEgress")
	caller := c.callStartParticipantEgress
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ParticipantEgressRequest) (*EgressInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ParticipantEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ParticipantEgressRequest) when calling interceptor")
					}
					return c.callStartParticipantEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *egressJSONClient) callStartParticipantEgress(ctx context.Context, in *ParticipantEgressRequest) (*EgressInfo, error) {
	out := new(EgressInfo)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[2], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *egressJSONClient) StartTrackCompositeEgress(ctx context.Context, in *TrackCompositeEgressRequest) (*EgressInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Egress")
	ctx = ctxsetters.WithMethodName(ctx, "StartTrackCompositeEgress")
	caller := c.callStartTrackCompositeEgress
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *TrackCompositeEgressRequest) (*EgressInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*TrackCompositeEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*TrackCompositeEgressRequest) when calling interceptor")
					}
					return c.callStartTrackCompositeEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *egressJSONClient) callStartTrackCompositeEgress(ctx context.Context, in *TrackCompositeEgressRequest) (*EgressInfo, error) {
	out := new(EgressInfo)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[3], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *egressJSONClient) StartTrackEgress(ctx context.Context, in *TrackEgressRequest) (*EgressInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Egress")
	ctx = ctxsetters.WithMethodName(ctx, "StartTrackEgress")
	caller := c.callStartTrackEgress
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *TrackEgressRequest) (*EgressInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*TrackEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*TrackEgressRequest) when calling interceptor")
					}
					return c.callStartTrackEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *egressJSONClient) callStartTrackEgress(ctx context.Context, in *TrackEgressRequest) (*EgressInfo, error) {
	out := new(EgressInfo)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[4], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *egressJSONClient) UpdateLayout(ctx context.Context, in *UpdateLayoutRequest) (*EgressInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Egress")
	ctx = ctxsetters.WithMethodName(ctx, "UpdateLayout")
	caller := c.callUpdateLayout
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *UpdateLayoutRequest) (*EgressInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateLayoutRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateLayoutRequest) when calling interceptor")
					}
					return c.callUpdateLayout(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *egressJSONClient) callUpdateLayout(ctx context.Context, in *UpdateLayoutRequest) (*EgressInfo, error) {
	out := new(EgressInfo)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[5], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *egressJSONClient) UpdateStream(ctx context.Context, in *UpdateStreamRequest) (*EgressInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Egress")
	ctx = ctxsetters.WithMethodName(ctx, "UpdateStream")
	caller := c.callUpdateStream
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *UpdateStreamRequest) (*EgressInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateStreamRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateStreamRequest) when calling interceptor")
					}
					return c.callUpdateStream(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *egressJSONClient) callUpdateStream(ctx context.Context, in *UpdateStreamRequest) (*EgressInfo, error) {
	out := new(EgressInfo)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[6], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *egressJSONClient) ListEgress(ctx context.Context, in *ListEgressRequest) (*ListEgressResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Egress")
	ctx = ctxsetters.WithMethodName(ctx, "ListEgress")
	caller := c.callListEgress
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ListEgressRequest) (*ListEgressResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListEgressRequest) when calling interceptor")
					}
					return c.callListEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListEgressResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListEgressResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *egressJSONClient) callListEgress(ctx context.Context, in *ListEgressRequest) (*ListEgressResponse, error) {
	out := new(ListEgressResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[7], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *egressJSONClient) StopEgress(ctx context.Context, in *StopEgressRequest) (*EgressInfo, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Egress")
	ctx = ctxsetters.WithMethodName(ctx, "StopEgress")
	caller := c.callStopEgress
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *StopEgressRequest) (*EgressInfo, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*StopEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*StopEgressRequest) when calling interceptor")
					}
					return c.callStopEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *egressJSONClient) callStopEgress(ctx context.Context, in *StopEgressRequest) (*EgressInfo, error) {
	out := new(EgressInfo)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[8], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

// =====================
// Egress Server Handler
// =====================

type egressServer struct {
	Egress
	interceptor      twirp.Interceptor
	hooks            *twirp.ServerHooks
	pathPrefix       string // prefix for routing
	jsonSkipDefaults bool   // do not include unpopulated fields (default values) in the response
	jsonCamelCase    bool   // JSON fields are serialized as lowerCamelCase rather than keeping the original proto names
}

// NewEgressServer builds a TwirpServer that can be used as an http.Handler to handle
// HTTP requests that are routed to the right method in the provided svc implementation.
// The opts are twirp.ServerOption modifiers, for example twirp.WithServerHooks(hooks).
func NewEgressServer(svc Egress, opts ...interface{}) TwirpServer {
	serverOpts := newServerOpts(opts)

	// Using ReadOpt allows backwards and forwards compatibility with new options in the future
	jsonSkipDefaults := false
	_ = serverOpts.ReadOpt("jsonSkipDefaults", &jsonSkipDefaults)
	jsonCamelCase := false
	_ = serverOpts.ReadOpt("jsonCamelCase", &jsonCamelCase)
	var pathPrefix string
	if ok := serverOpts.ReadOpt("pathPrefix", &pathPrefix); !ok {
		pathPrefix = "/twirp" // default prefix
	}

	return &egressServer{
		Egress:           svc,
		hooks:            serverOpts.Hooks,
		interceptor:      twirp.ChainInterceptors(serverOpts.Interceptors...),
		pathPrefix:       pathPrefix,
		jsonSkipDefaults: jsonSkipDefaults,
		jsonCamelCase:    jsonCamelCase,
	}
}

// writeError writes an HTTP response with a valid Twirp error format, and triggers hooks.
// If err is not a twirp.Error, it will get wrapped with twirp.InternalErrorWith(err)
func (s *egressServer) writeError(ctx context.Context, resp http.ResponseWriter, err error) {
	writeError(ctx, resp, err, s.hooks)
}

// handleRequestBodyError is used to handle error when the twirp server cannot read request
func (s *egressServer) handleRequestBodyError(ctx context.Context, resp http.ResponseWriter, msg string, err error) {
	if context.Canceled == ctx.Err() {
		s.writeError(ctx, resp, twirp.NewError(twirp.Canceled, "failed to read request: context canceled"))
		return
	}
	if context.DeadlineExceeded == ctx.Err() {
		s.writeError(ctx, resp, twirp.NewError(twirp.DeadlineExceeded, "failed to read request: deadline exceeded"))
		return
	}
	s.writeError(ctx, resp, twirp.WrapError(malformedRequestError(msg), err))
}

// EgressPathPrefix is a convenience constant that may identify URL paths.
// Should be used with caution, it only matches routes generated by Twirp Go clients,
// with the default "/twirp" prefix and default CamelCase service and method names.
// More info: https://twitchtv.github.io/twirp/docs/routing.html
const EgressPathPrefix = "/twirp/livekit.Egress/"

func (s *egressServer) ServeHTTP(resp http.ResponseWriter, req *http.Request) {
	ctx := req.Context()
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "Egress")
	ctx = ctxsetters.WithResponseWriter(ctx, resp)

	var err error
	ctx, err = callRequestReceived(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	if req.Method != "POST" {
		msg := fmt.Sprintf("unsupported method %q (only POST is allowed)", req.Method)
		s.writeError(ctx, resp, badRouteError(msg, req.Method, req.URL.Path))
		return
	}

	// Verify path format: [<prefix>]/<package>.<Service>/<Method>
	prefix, pkgService, method := parseTwirpPath(req.URL.Path)
	if pkgService != "livekit.Egress" {
		msg := fmt.Sprintf("no handler for path %q", req.URL.Path)
		s.writeError(ctx, resp, badRouteError(msg, req.Method, req.URL.Path))
		return
	}
	if prefix != s.pathPrefix {
		msg := fmt.Sprintf("invalid path prefix %q, expected %q, on path %q", prefix, s.pathPrefix, req.URL.Path)
		s.writeError(ctx, resp, badRouteError(msg, req.Method, req.URL.Path))
		return
	}

	switch method {
	case "StartRoomCompositeEgress":
		s.serveStartRoomCompositeEgress(ctx, resp, req)
		return
	case "StartWebEgress":
		s.serveStartWebEgress(ctx, resp, req)
		return
	case "StartParticipantEgress":
		s.serveStartParticipantEgress(ctx, resp, req)
		return
	case "StartTrackCompositeEgress":
		s.serveStartTrackCompositeEgress(ctx, resp, req)
		return
	case "StartTrackEgress":
		s.serveStartTrackEgress(ctx, resp, req)
		return
	case "UpdateLayout":
		s.serveUpdateLayout(ctx, resp, req)
		return
	case "UpdateStream":
		s.serveUpdateStream(ctx, resp, req)
		return
	case "ListEgress":
		s.serveListEgress(ctx, resp, req)
		return
	case "StopEgress":
		s.serveStopEgress(ctx, resp, req)
		return
	default:
		msg := fmt.Sprintf("no handler for path %q", req.URL.Path)
		s.writeError(ctx, resp, badRouteError(msg, req.Method, req.URL.Path))
		return
	}
}

func (s *egressServer) serveStartRoomCompositeEgress(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveStartRoomCompositeEgressJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveStartRoomCompositeEgressProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *egressServer) serveStartRoomCompositeEgressJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "StartRoomCompositeEgress")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(RoomCompositeEgressRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.Egress.StartRoomCompositeEgress
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *RoomCompositeEgressRequest) (*EgressInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*RoomCompositeEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*RoomCompositeEgressRequest) when calling interceptor")
					}
					return s.Egress.StartRoomCompositeEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *EgressInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *EgressInfo and nil error while calling StartRoomCompositeEgress. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *egressServer) serveStartRoomCompositeEgressProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "StartRoomCompositeEgress")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(RoomCompositeEgressRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.Egress.StartRoomCompositeEgress
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *RoomCompositeEgressRequest) (*EgressInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*RoomCompositeEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*RoomCompositeEgressRequest) when calling interceptor")
					}
					return s.Egress.StartRoomCompositeEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *EgressInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *EgressInfo and nil error while calling StartRoomCompositeEgress. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *egressServer) serveStartWebEgress(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveStartWebEgressJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveStartWebEgressProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *egressServer) serveStartWebEgressJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "StartWebEgress")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(WebEgressRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.Egress.StartWebEgress
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *WebEgressRequest) (*EgressInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*WebEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*WebEgressRequest) when calling interceptor")
					}
					return s.Egress.StartWebEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *EgressInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *EgressInfo and nil error while calling StartWebEgress. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *egressServer) serveStartWebEgressProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "StartWebEgress")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(WebEgressRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.Egress.StartWebEgress
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *WebEgressRequest) (*EgressInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*WebEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*WebEgressRequest) when calling interceptor")
					}
					return s.Egress.StartWebEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *EgressInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *EgressInfo and nil error while calling StartWebEgress. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *egressServer) serveStartParticipantEgress(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveStartParticipantEgressJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveStartParticipantEgressProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *egressServer) serveStartParticipantEgressJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "StartParticipantEgress")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(ParticipantEgressRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.Egress.StartParticipantEgress
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ParticipantEgressRequest) (*EgressInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ParticipantEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ParticipantEgressRequest) when calling interceptor")
					}
					return s.Egress.StartParticipantEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *EgressInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *EgressInfo and nil error while calling StartParticipantEgress. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *egressServer) serveStartParticipantEgressProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "StartParticipantEgress")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(ParticipantEgressRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.Egress.StartParticipantEgress
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ParticipantEgressRequest) (*EgressInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ParticipantEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ParticipantEgressRequest) when calling interceptor")
					}
					return s.Egress.StartParticipantEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *EgressInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *EgressInfo and nil error while calling StartParticipantEgress. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *egressServer) serveStartTrackCompositeEgress(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveStartTrackCompositeEgressJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveStartTrackCompositeEgressProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *egressServer) serveStartTrackCompositeEgressJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "StartTrackCompositeEgress")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(TrackCompositeEgressRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.Egress.StartTrackCompositeEgress
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *TrackCompositeEgressRequest) (*EgressInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*TrackCompositeEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*TrackCompositeEgressRequest) when calling interceptor")
					}
					return s.Egress.StartTrackCompositeEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *EgressInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *EgressInfo and nil error while calling StartTrackCompositeEgress. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *egressServer) serveStartTrackCompositeEgressProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "StartTrackCompositeEgress")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(TrackCompositeEgressRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.Egress.StartTrackCompositeEgress
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *TrackCompositeEgressRequest) (*EgressInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*TrackCompositeEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*TrackCompositeEgressRequest) when calling interceptor")
					}
					return s.Egress.StartTrackCompositeEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *EgressInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *EgressInfo and nil error while calling StartTrackCompositeEgress. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *egressServer) serveStartTrackEgress(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveStartTrackEgressJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveStartTrackEgressProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *egressServer) serveStartTrackEgressJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "StartTrackEgress")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(TrackEgressRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.Egress.StartTrackEgress
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *TrackEgressRequest) (*EgressInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*TrackEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*TrackEgressRequest) when calling interceptor")
					}
					return s.Egress.StartTrackEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *EgressInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *EgressInfo and nil error while calling StartTrackEgress. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *egressServer) serveStartTrackEgressProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "StartTrackEgress")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(TrackEgressRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.Egress.StartTrackEgress
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *TrackEgressRequest) (*EgressInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*TrackEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*TrackEgressRequest) when calling interceptor")
					}
					return s.Egress.StartTrackEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *EgressInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *EgressInfo and nil error while calling StartTrackEgress. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *egressServer) serveUpdateLayout(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveUpdateLayoutJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveUpdateLayoutProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *egressServer) serveUpdateLayoutJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "UpdateLayout")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(UpdateLayoutRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.Egress.UpdateLayout
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *UpdateLayoutRequest) (*EgressInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateLayoutRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateLayoutRequest) when calling interceptor")
					}
					return s.Egress.UpdateLayout(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *EgressInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *EgressInfo and nil error while calling UpdateLayout. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *egressServer) serveUpdateLayoutProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "UpdateLayout")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(UpdateLayoutRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.Egress.UpdateLayout
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *UpdateLayoutRequest) (*EgressInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateLayoutRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateLayoutRequest) when calling interceptor")
					}
					return s.Egress.UpdateLayout(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *EgressInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *EgressInfo and nil error while calling UpdateLayout. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *egressServer) serveUpdateStream(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveUpdateStreamJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveUpdateStreamProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *egressServer) serveUpdateStreamJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "UpdateStream")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(UpdateStreamRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.Egress.UpdateStream
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *UpdateStreamRequest) (*EgressInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateStreamRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateStreamRequest) when calling interceptor")
					}
					return s.Egress.UpdateStream(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *EgressInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *EgressInfo and nil error while calling UpdateStream. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *egressServer) serveUpdateStreamProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "UpdateStream")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(UpdateStreamRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.Egress.UpdateStream
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *UpdateStreamRequest) (*EgressInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateStreamRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateStreamRequest) when calling interceptor")
					}
					return s.Egress.UpdateStream(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *EgressInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *EgressInfo and nil error while calling UpdateStream. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *egressServer) serveListEgress(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveListEgressJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveListEgressProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *egressServer) serveListEgressJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "ListEgress")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(ListEgressRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.Egress.ListEgress
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ListEgressRequest) (*ListEgressResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListEgressRequest) when calling interceptor")
					}
					return s.Egress.ListEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListEgressResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListEgressResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ListEgressResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ListEgressResponse and nil error while calling ListEgress. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *egressServer) serveListEgressProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "ListEgress")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(ListEgressRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.Egress.ListEgress
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ListEgressRequest) (*ListEgressResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListEgressRequest) when calling interceptor")
					}
					return s.Egress.ListEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListEgressResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListEgressResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ListEgressResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ListEgressResponse and nil error while calling ListEgress. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *egressServer) serveStopEgress(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveStopEgressJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveStopEgressProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *egressServer) serveStopEgressJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "StopEgress")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(StopEgressRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.Egress.StopEgress
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *StopEgressRequest) (*EgressInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*StopEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*StopEgressRequest) when calling interceptor")
					}
					return s.Egress.StopEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *EgressInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *EgressInfo and nil error while calling StopEgress. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *egressServer) serveStopEgressProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "StopEgress")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(StopEgressRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.Egress.StopEgress
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *StopEgressRequest) (*EgressInfo, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*StopEgressRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*StopEgressRequest) when calling interceptor")
					}
					return s.Egress.StopEgress(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*EgressInfo)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*EgressInfo) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *EgressInfo
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *EgressInfo and nil error while calling StopEgress. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *egressServer) ServiceDescriptor() ([]byte, int) {
	return twirpFileDescriptor1, 0
}

func (s *egressServer) ProtocGenTwirpVersion() string {
	return "v8.1.3"
}

// PathPrefix returns the base service path, in the form: "/<prefix>/<package>.<Service>/"
// that is everything in a Twirp route except for the <Method>. This can be used for routing,
// for example to identify the requests that are targeted to this service in a mux.
func (s *egressServer) PathPrefix() string {
	return baseServicePath(s.pathPrefix, "livekit", "Egress")
}

var twirpFileDescriptor1 = []byte{
	// 3274 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x5a, 0xc9, 0x73, 0x1b, 0xc7,
	0xd5, 0xe7, 0x60, 0xc7, 0xc3, 0xc2, 0x61, 0x93, 0xa2, 0x20, 0xca, 0x9f, 0x25, 0x43, 0x5e, 0x64,
	0xda, 0xa6, 0xf8, 0x89, 0xb2, 0x2c, 0xcb, 0x9f, 0xfd, 0x05, 0x24, 0x87, 0x24, 0x6c, 0x90, 0x40,
	0x06, 0xa0, 0xe4, 0xe4, 0x32, 0x35, 0x04, 0x9a, 0xe4, 0x14, 0x81, 0x19, 0x78, 0xba, 0x41, 0x09,
	0xae, 0xfc, 0x01, 0x39, 0x66, 0xb9, 0xa5, 0x52, 0x95, 0x4b, 0x2a, 0x87, 0x94, 0xef, 0xf9, 0x1f,
	0x92, 0x43, 0x0e, 0xa9, 0xfc, 0x03, 0x39, 0x26, 0x95, 0xdc, 0x72, 0x4f, 0xf5, 0x32, 0x0b, 0x06,
	0x43, 0x8a, 0x8b, 0xaa, 0x72, 0x48, 0x6e, 0xd3, 0x6f, 0xc3, 0xeb, 0xee, 0x5f, 0xbf, 0xd7, 0xef,
	0x35, 0x60, 0xa1, 0x6f, 0x9d, 0xe2, 0x13, 0x8b, 0x1a, 0xf8, 0xc8, 0xc5, 0x84, 0xac, 0x0c, 0x5d,
	0x87, 0x3a, 0x28, 0x2b, 0xa9, 0x4b, 0x3e, 0x7b, 0xe0, 0xf4, 0x70, 0x5f, 0xb2, 0xab, 0x7f, 0xce,
	0xc0, 0x92, 0xee, 0x38, 0x83, 0x0d, 0x67, 0x30, 0x74, 0x88, 0x45, 0xb1, 0xc6, 0x95, 0x75, 0xfc,
	0xcd, 0x08, 0x13, 0x8a, 0x6e, 0x43, 0xde, 0x75, 0x9c, 0x81, 0x61, 0x9b, 0x03, 0x5c, 0x51, 0xee,
	0x2a, 0xf7, 0xf3, 0x7a, 0x8e, 0x11, 0xf6, 0xcc, 0x01, 0x46, 0x8b, 0x90, 0xe9, 0x9b, 0x63, 0x67,
	0x44, 0x2b, 0x09, 0xce, 0x91, 0x23, 0xf4, 0x3f, 0x00, 0xe6, 0xa8, 0x67, 0x39, 0x86, 0x63, 0xf7,
	0xc7, 0x95, 0xe4, 0x5d, 0xe5, 0x7e, 0x4e, 0xcf, 0x73, 0x4a, 0xd3, 0xee, 0x8f, 0xd1, 0x27, 0x50,
	0x14, 0xec, 0x81, 0xf5, 0xd2, 0xb2, 0x8f, 0x2a, 0xb3, 0x77, 0x95, 0xfb, 0xe5, 0x87, 0x0b, 0x2b,
	0xd2, 0xbf, 0x95, 0x1a, 0x63, 0xee, 0x72, 0x9e, 0x5e, 0x30, 0x83, 0x01, 0xb3, 0x7b, 0x6a, 0xf5,
	0xb0, 0xb4, 0x9b, 0x12, 0x76, 0x39, 0x85, 0xdb, 0x7d, 0x17, 0x66, 0xbb, 0x23, 0x42, 0x9d, 0x81,
	0x71, 0x60, 0x12, 0x6c, 0x8c, 0xdc, 0x7e, 0x25, 0xcd, 0xfd, 0x2a, 0x09, 0xf2, 0xba, 0x49, 0xf0,
	0xbe, 0xdb, 0x47, 0x8f, 0x20, 0x75, 0x68, 0xf5, 0x71, 0x25, 0x73, 0x57, 0xb9, 0x5f, 0x78, 0xb8,
	0xe4, 0xff, 0xae, 0x66, 0x77, 0x9d, 0x1e, 0xee, 0x6d, 0x59, 0x7d, 0xdc, 0x1c, 0xd1, 0xe1, 0x88,
	0xae, 0x27, 0x2a, 0xca, 0xce, 0x8c, 0xce, 0xa5, 0xd1, 0x1a, 0x64, 0x08, 0x75, 0xb1, 0x39, 0xa8,
	0x64, 0xb9, 0xde, 0x0d, 0x5f, 0xaf, 0xcd, 0xc9, 0x13, 0x2a, 0x52, 0x14, 0x7d, 0x01, 0x39, 0x82,
	0x8f, 0x06, 0xd8, 0xa6, 0xa4, 0x02, 0x5c, 0xed, 0x8d, 0x40, 0x4d, 0x30, 0x62, 0x7e, 0xd0, 0xd7,
	0x41, 0x4f, 0x20, 0x33, 0x74, 0x31, 0xc1, 0xb4, 0x92, 0xe3, 0x8b, 0xf4, 0xe6, 0xa4, 0xb3, 0x96,
	0x7d, 0xd4, 0x1c, 0x52, 0xcb, 0xb1, 0x49, 0x8b, 0x4b, 0xed, 0x28, 0xba, 0x94, 0x47, 0x8f, 0x21,
	0x67, 0xf6, 0x4e, 0x4d, 0xbb, 0x8b, 0x7b, 0x95, 0x3c, 0xff, 0xe5, 0xca, 0x59, 0xba, 0x3b, 0x8a,
	0xee, 0xcb, 0xa2, 0xcf, 0xa1, 0xc8, 0xa6, 0x6b, 0x38, 0xdc, 0x21, 0x52, 0x29, 0xdc, 0x4d, 0x9e,
	0xbf, 0x48, 0x7a, 0xe1, 0xd0, 0xff, 0x26, 0xe8, 0xff, 0xa0, 0x2c, 0xa6, 0xee, 0x1b, 0x28, 0x72,
	0x03, 0xf1, 0xab, 0xa5, 0x97, 0x48, 0x68, 0x44, 0x90, 0x06, 0xb3, 0x72, 0xea, 0xbe, 0x7a, 0x89,
	0xab, 0x9f, 0xbb, 0x6a, 0x7a, 0x59, 0x2a, 0x79, 0x66, 0x3e, 0x85, 0x92, 0x35, 0x30, 0x8f, 0x82,
	0x49, 0x94, 0xb9, 0x91, 0x00, 0x61, 0x75, 0xc6, 0x95, 0xca, 0x45, 0x2b, 0x18, 0x10, 0xf4, 0x10,
	0x72, 0x2f, 0xf0, 0xc1, 0xb1, 0xe3, 0x9c, 0x90, 0x8a, 0xca, 0xb5, 0x16, 0x7d, 0xad, 0xe7, 0x82,
	0xb1, 0xe1, 0xd8, 0x87, 0xd6, 0x91, 0xee, 0xcb, 0xad, 0xe7, 0x20, 0x23, 0x7e, 0x68, 0x3d, 0x0f,
	0x59, 0x47, 0xac, 0x69, 0xf5, 0x9f, 0x69, 0x50, 0x9f, 0xe3, 0x83, 0xc9, 0xd3, 0xa4, 0x42, 0x92,
	0xa1, 0x52, 0x9c, 0x23, 0xf6, 0x19, 0x39, 0x2a, 0x89, 0xe8, 0x51, 0x99, 0x44, 0x7c, 0x32, 0x8a,
	0xf8, 0x0f, 0x01, 0x99, 0x2f, 0x4c, 0x8b, 0x1a, 0x84, 0x9a, 0x2e, 0x35, 0x88, 0x75, 0x64, 0x9b,
	0xfd, 0x4a, 0x91, 0x8b, 0xa9, 0x9c, 0xd3, 0x66, 0x8c, 0x36, 0xa7, 0xfb, 0xb8, 0x4f, 0x5d, 0x11,
	0xf7, 0xe9, 0xab, 0xe1, 0x3e, 0x73, 0x2d, 0xdc, 0x67, 0xaf, 0x81, 0xfb, 0xdc, 0x35, 0x70, 0x9f,
	0xbf, 0x2e, 0xee, 0xe1, 0x7a, 0xb8, 0x2f, 0xbc, 0x0e, 0xdc, 0x97, 0xae, 0x84, 0xfb, 0xf2, 0x75,
	0x70, 0xff, 0x9b, 0x14, 0x54, 0x5a, 0xa6, 0x4b, 0xad, 0xae, 0x35, 0x34, 0x6d, 0x7a, 0x89, 0x6c,
	0xb2, 0x04, 0x39, 0xab, 0x87, 0x6d, 0x6a, 0xd1, 0xb1, 0xcc, 0x27, 0xfe, 0x18, 0xbd, 0x05, 0x45,
	0xd2, 0x75, 0x31, 0xb6, 0x0d, 0x72, 0x6c, 0xba, 0x58, 0x9e, 0x84, 0x82, 0xa0, 0xb5, 0x19, 0x29,
	0x04, 0x99, 0xd4, 0x85, 0x20, 0x33, 0x13, 0x0b, 0x99, 0xf4, 0x2b, 0x20, 0x33, 0x73, 0x0e, 0x64,
	0x32, 0xd7, 0x85, 0x4c, 0xf6, 0x7a, 0x90, 0xc9, 0xbd, 0x0e, 0xc8, 0xe4, 0xaf, 0x04, 0x19, 0xb8,
	0x20, 0x64, 0x42, 0x40, 0xf9, 0x5b, 0x1a, 0x6e, 0x77, 0x5c, 0xb3, 0x7b, 0x72, 0x95, 0x9b, 0xc7,
	0xdb, 0x50, 0x16, 0x61, 0x93, 0x32, 0x0b, 0x86, 0xd5, 0x93, 0x88, 0x11, 0x17, 0x0b, 0x6e, 0xb6,
	0xde, 0x63, 0x52, 0x22, 0x7a, 0xfa, 0x52, 0x49, 0x21, 0xc5, 0xa9, 0x9e, 0xd4, 0xbf, 0x29, 0x2c,
	0xe6, 0xae, 0x15, 0x16, 0x33, 0xd7, 0x08, 0x8b, 0xd9, 0xff, 0x5e, 0x07, 0xae, 0x82, 0xf1, 0xd9,
	0xeb, 0x84, 0xc5, 0xbf, 0x28, 0x80, 0x38, 0xe0, 0x2e, 0x01, 0xf2, 0x5b, 0x90, 0x8b, 0xc0, 0x3b,
	0x4b, 0x25, 0x66, 0x1f, 0x48, 0xcc, 0x26, 0xf9, 0x56, 0xde, 0xf2, 0x7d, 0xda, 0xb4, 0x5c, 0xdc,
	0xa5, 0xc1, 0x5a, 0xf8, 0x70, 0x7d, 0x07, 0x4a, 0x2f, 0xf0, 0x01, 0x71, 0xba, 0x27, 0x98, 0xf2,
	0x9b, 0x31, 0x43, 0x7b, 0x7e, 0x67, 0x46, 0x2f, 0xfa, 0x64, 0x76, 0x35, 0x0e, 0xcf, 0x37, 0x7d,
	0xd9, 0xf9, 0x56, 0xff, 0x90, 0x80, 0xb9, 0x29, 0x40, 0xa0, 0x8f, 0x21, 0xcf, 0x21, 0x44, 0xc7,
	0x43, 0x31, 0xc7, 0x72, 0x14, 0x7b, 0x42, 0xbc, 0x33, 0x1e, 0x62, 0x3d, 0x77, 0x28, 0xbf, 0x58,
	0x3a, 0x60, 0xdf, 0x43, 0x93, 0x1e, 0x7b, 0xe9, 0xc0, 0x1b, 0xa3, 0xf7, 0x41, 0xed, 0x59, 0xc4,
	0x3c, 0xe8, 0x63, 0x63, 0x60, 0xda, 0xd6, 0x21, 0x26, 0xe2, 0x44, 0xe4, 0xf4, 0x59, 0x49, 0xdf,
	0x95, 0x64, 0x74, 0x0f, 0x12, 0x64, 0x4d, 0xae, 0xd3, 0x5c, 0x00, 0x9b, 0xb5, 0xfd, 0x61, 0xdf,
	0x31, 0x7b, 0x3b, 0x33, 0x7a, 0x82, 0xac, 0xa1, 0x77, 0x21, 0x79, 0xd4, 0x1d, 0xca, 0x08, 0x80,
	0x7c, 0xa9, 0xed, 0x8d, 0x96, 0x2f, 0xc6, 0x04, 0xd0, 0x2a, 0xa4, 0xcd, 0x6f, 0x47, 0x2e, 0x9e,
	0x4a, 0x13, 0x35, 0x46, 0x5d, 0xef, 0x3b, 0x07, 0xbe, 0xbc, 0x10, 0x44, 0x0f, 0x20, 0x63, 0xf6,
	0xad, 0x66, 0xbb, 0x3d, 0x55, 0x35, 0xd4, 0x38, 0xd9, 0x97, 0x97, 0x62, 0xa1, 0xd5, 0xfc, 0x75,
	0x0a, 0xe6, 0x63, 0xe0, 0x8d, 0x9e, 0x42, 0x8e, 0x97, 0x6e, 0x5d, 0xa7, 0x2f, 0x97, 0xf3, 0xcd,
	0xf8, 0xe3, 0xd0, 0x92, 0x52, 0xba, 0x2f, 0x8f, 0xde, 0x83, 0x59, 0xb6, 0x88, 0x0c, 0x6e, 0xc6,
	0xd0, 0xc5, 0x87, 0xd6, 0x4b, 0xb9, 0xb6, 0x65, 0x8f, 0xdc, 0xe2, 0x54, 0x74, 0x0f, 0x4a, 0xc3,
	0xbe, 0x39, 0xee, 0x5b, 0x84, 0x0a, 0x70, 0xca, 0xc8, 0xe9, 0x11, 0x39, 0x40, 0x3f, 0x04, 0xc4,
	0x7e, 0xd8, 0x98, 0x94, 0x2c, 0x70, 0x49, 0x95, 0x71, 0x5a, 0x61, 0xe9, 0xf7, 0x41, 0xf5, 0x4e,
	0x73, 0x6f, 0xe4, 0x9a, 0xec, 0x5c, 0xf0, 0x15, 0x2f, 0xe9, 0xde, 0x29, 0xdf, 0x94, 0x64, 0x76,
	0xf0, 0x7d, 0x37, 0xc9, 0xe8, 0x90, 0xb9, 0x09, 0x7c, 0xa6, 0x67, 0x1c, 0xfc, 0x36, 0x97, 0x09,
	0x26, 0x21, 0xc6, 0xb1, 0x30, 0xc9, 0x9d, 0x07, 0x93, 0xf4, 0x85, 0x60, 0x92, 0xb9, 0x30, 0x4c,
	0xb2, 0x97, 0x87, 0x49, 0xfe, 0xb2, 0x30, 0xf9, 0x79, 0x02, 0xd4, 0xe8, 0xb1, 0x9f, 0x38, 0x3c,
	0xca, 0x05, 0x0e, 0x4f, 0xfa, 0xbc, 0x55, 0x49, 0x5c, 0x68, 0x55, 0x92, 0x17, 0x5e, 0x95, 0xd4,
	0xe5, 0x57, 0x25, 0x73, 0xd9, 0x55, 0xf9, 0x7b, 0x12, 0x0a, 0xa1, 0xb0, 0xce, 0x26, 0xdd, 0x35,
	0x87, 0x74, 0xe4, 0x62, 0xc3, 0xb2, 0x29, 0x76, 0x4f, 0x4d, 0x71, 0x78, 0x4a, 0xfa, 0xac, 0xa4,
	0xd7, 0x25, 0x19, 0x2d, 0x40, 0xfa, 0x85, 0xd5, 0x93, 0x51, 0x27, 0xad, 0x8b, 0x01, 0x5a, 0x84,
	0xcc, 0x31, 0xb6, 0x8e, 0x8e, 0x29, 0x9f, 0x68, 0x5a, 0x97, 0xa3, 0xb8, 0x13, 0x95, 0x8a, 0x3d,
	0x51, 0xb5, 0x69, 0x4c, 0xa7, 0x23, 0xc1, 0x90, 0x3b, 0x7c, 0x0e, 0x9e, 0x1f, 0x41, 0x41, 0x24,
	0x32, 0x16, 0x33, 0xbb, 0xf2, 0x0e, 0x30, 0x3f, 0xa9, 0xbe, 0xc1, 0x58, 0x3a, 0x58, 0xfe, 0x77,
	0xec, 0x7e, 0x67, 0xcf, 0xdb, 0xef, 0xdc, 0x85, 0xf6, 0x3b, 0x7f, 0xe1, 0xfd, 0x86, 0xcb, 0xef,
	0x77, 0xe1, 0xb2, 0xfb, 0xfd, 0x5d, 0x0a, 0x72, 0x9e, 0x9f, 0xbc, 0xa8, 0xee, 0x76, 0x31, 0x21,
	0xc6, 0x09, 0x1e, 0x4b, 0xfc, 0xe7, 0x05, 0xe5, 0x2b, 0x3c, 0x66, 0x5b, 0x49, 0x70, 0xd7, 0xc5,
	0x7e, 0xdb, 0x4a, 0x8c, 0x58, 0xcc, 0x23, 0x98, 0x10, 0xcb, 0xb1, 0x0d, 0xea, 0x9c, 0x60, 0x5b,
	0x46, 0xb2, 0xa2, 0x24, 0x76, 0x18, 0x0d, 0xbd, 0x0b, 0xb3, 0x26, 0x21, 0xa3, 0x01, 0x36, 0x5c,
	0xa7, 0x8f, 0x0d, 0xd3, 0xb5, 0x79, 0xbd, 0x9d, 0xd7, 0x4b, 0x82, 0xac, 0x3b, 0x7d, 0x5c, 0x73,
	0x6d, 0xf4, 0x31, 0xdc, 0x0c, 0xcb, 0xe1, 0x97, 0x14, 0xbb, 0xb6, 0xd9, 0x67, 0xb9, 0xbc, 0xc4,
	0xe5, 0x17, 0x02, 0x79, 0x4d, 0x32, 0xeb, 0x3d, 0xe6, 0x9b, 0x8b, 0x8f, 0x58, 0x68, 0x14, 0x01,
	0x57, 0x8e, 0xd8, 0x81, 0xc6, 0x76, 0x6f, 0xe8, 0x58, 0x36, 0x95, 0xf8, 0xf2, 0xc7, 0x4c, 0xe7,
	0x60, 0xc4, 0x32, 0xb8, 0x6c, 0x77, 0xc9, 0x11, 0xba, 0x0f, 0xea, 0xa1, 0xe3, 0x76, 0xb1, 0xc1,
	0x8e, 0xbd, 0x41, 0xe8, 0x58, 0xf6, 0xbc, 0x72, 0x7a, 0x99, 0xd3, 0x5b, 0x26, 0x3d, 0x6e, 0x33,
	0x2a, 0xfa, 0x0c, 0x72, 0x03, 0x4c, 0xcd, 0x9e, 0x49, 0x4d, 0x59, 0x84, 0xdc, 0x99, 0xda, 0xfd,
	0x95, 0x5d, 0x29, 0xa1, 0xd9, 0xd4, 0x1d, 0xeb, 0xbe, 0x02, 0xaa, 0x40, 0x96, 0x9a, 0x47, 0x47,
	0x96, 0x7d, 0xc4, 0x91, 0xc3, 0x6e, 0x29, 0x62, 0x88, 0x1e, 0xc0, 0x7c, 0xd7, 0xb1, 0x29, 0x8f,
	0xf8, 0x16, 0xe1, 0x97, 0x7c, 0x36, 0xb3, 0x3c, 0x97, 0x42, 0x92, 0xb5, 0x19, 0x70, 0xd0, 0x32,
	0xa4, 0x87, 0xae, 0xf3, 0x72, 0x2c, 0x21, 0x13, 0xdc, 0xd0, 0x5a, 0x8c, 0x2a, 0x6f, 0x1e, 0x42,
	0x64, 0xe9, 0x33, 0x28, 0x4d, 0x78, 0x84, 0x54, 0x48, 0x06, 0xdb, 0xcd, 0x3e, 0xd9, 0x49, 0x3e,
	0x35, 0xfb, 0x23, 0x2c, 0xf7, 0x59, 0x0c, 0x9e, 0x26, 0x9e, 0x28, 0xd5, 0x6f, 0x20, 0xef, 0xe3,
	0x15, 0xdd, 0x85, 0x42, 0xd7, 0xc5, 0xbc, 0xd4, 0x34, 0xfb, 0x44, 0x1a, 0x08, 0x93, 0x42, 0x2b,
	0x9c, 0x98, 0x58, 0x61, 0xdf, 0xdf, 0xe4, 0x2b, 0xfd, 0xad, 0xfe, 0x08, 0x66, 0x23, 0xc0, 0x67,
	0x55, 0xad, 0xd9, 0xed, 0x3a, 0x23, 0x9b, 0x86, 0x2f, 0x80, 0x05, 0x49, 0xe3, 0x49, 0xf3, 0x0e,
	0x78, 0x43, 0x8e, 0x65, 0xf1, 0xf3, 0x20, 0x49, 0x0c, 0xcc, 0xef, 0x40, 0x99, 0x2d, 0xa4, 0x69,
	0xd9, 0xd8, 0x0d, 0x67, 0xea, 0x92, 0x4f, 0x65, 0x76, 0xaa, 0x3f, 0x55, 0xa0, 0x18, 0x3e, 0x44,
	0x57, 0x3d, 0x23, 0xaf, 0x11, 0x9f, 0xd5, 0xe7, 0x50, 0x08, 0xad, 0x53, 0x4c, 0x73, 0x6c, 0x09,
	0x72, 0x23, 0xc2, 0x4e, 0xc6, 0xc0, 0xdb, 0x42, 0x7f, 0xcc, 0x78, 0x43, 0x93, 0x90, 0x17, 0x8e,
	0xeb, 0x55, 0x75, 0xfe, 0xb8, 0xfa, 0x1c, 0x8a, 0xe1, 0xb2, 0x02, 0xad, 0x4d, 0xdd, 0x98, 0x6e,
	0x46, 0xea, 0x8f, 0x98, 0xab, 0x12, 0x82, 0xd4, 0xc8, 0xed, 0x93, 0x4a, 0xe2, 0x6e, 0xf2, 0x7e,
	0x5e, 0xe7, 0xdf, 0xd5, 0xdf, 0x27, 0x61, 0x36, 0x52, 0x2d, 0x05, 0xe9, 0x42, 0x89, 0x4f, 0x17,
	0x89, 0x89, 0x74, 0xb1, 0x00, 0xe9, 0x1e, 0x1e, 0xd2, 0x63, 0x99, 0x45, 0xc4, 0x00, 0xbd, 0x01,
	0xf9, 0x43, 0xd7, 0x1c, 0x60, 0xd7, 0xa4, 0x22, 0x3d, 0xa6, 0xf5, 0x80, 0xc0, 0xc2, 0xbe, 0x28,
	0x76, 0x45, 0xd8, 0x4f, 0x47, 0xc2, 0x3e, 0x6f, 0x97, 0xcb, 0xb0, 0x6f, 0xfa, 0xdf, 0x2c, 0x9a,
	0x09, 0xad, 0x03, 0x8b, 0x72, 0xbb, 0x19, 0x6e, 0x57, 0x54, 0xc8, 0xeb, 0x82, 0x16, 0x08, 0x7d,
	0x33, 0x32, 0xfb, 0x16, 0x1d, 0xf3, 0x90, 0xe7, 0x09, 0x7d, 0x5f, 0xd0, 0x58, 0x8a, 0x13, 0x42,
	0x87, 0x2e, 0x2b, 0x5b, 0xec, 0xee, 0x98, 0xe7, 0x8f, 0xb4, 0x2e, 0x6a, 0xf0, 0x2d, 0x8f, 0xca,
	0x1c, 0x15, 0xf5, 0xb6, 0x70, 0x34, 0x17, 0x71, 0xf4, 0x19, 0xe3, 0x49, 0x47, 0x4f, 0xfd, 0x6f,
	0xe6, 0x83, 0xd0, 0xf2, 0x1c, 0xcd, 0x0b, 0x1f, 0x38, 0x31, 0xe4, 0xa8, 0x10, 0xf2, 0x1c, 0x2d,
	0x86, 0x84, 0x3c, 0x47, 0x3f, 0x04, 0x74, 0x82, 0xc7, 0x06, 0x5f, 0xb9, 0x20, 0xcd, 0xb3, 0x58,
	0xa2, 0xe8, 0xea, 0x09, 0x1e, 0x6f, 0x31, 0x86, 0x97, 0xe7, 0xab, 0x5f, 0xc2, 0xfc, 0xfe, 0xb0,
	0x67, 0x52, 0xdc, 0xe0, 0xaf, 0x16, 0xa1, 0x92, 0x4c, 0xbc, 0x9f, 0xb0, 0x50, 0x2d, 0xef, 0x4e,
	0x82, 0x20, 0xc2, 0x73, 0xdc, 0x8b, 0x47, 0xf5, 0xc7, 0x8a, 0x67, 0x4c, 0xe0, 0xe9, 0x42, 0xc6,
	0x58, 0x2a, 0xe9, 0xf5, 0x64, 0x55, 0x6a, 0x84, 0xc0, 0x56, 0x32, 0x7b, 0x3d, 0x01, 0xdd, 0x7d,
	0xb7, 0x4f, 0xd8, 0xb4, 0x5c, 0x3c, 0x70, 0x4e, 0xf1, 0x84, 0x68, 0x92, 0x8b, 0xaa, 0x82, 0x13,
	0x48, 0x57, 0x31, 0xcc, 0x35, 0x2c, 0x72, 0x99, 0xc6, 0xdb, 0x84, 0x93, 0x89, 0xe9, 0x19, 0x9b,
	0x5d, 0x6a, 0x9d, 0x7a, 0x3d, 0x37, 0x39, 0xaa, 0xfe, 0x3f, 0xa0, 0xf0, 0xcf, 0x90, 0xa1, 0x63,
	0x13, 0x76, 0xc7, 0x4f, 0x5b, 0x14, 0x0f, 0x58, 0x10, 0x65, 0x59, 0x24, 0xd8, 0x7b, 0x21, 0x57,
	0xb7, 0x0f, 0x1d, 0x5d, 0x48, 0x54, 0x57, 0x61, 0xae, 0x4d, 0x9d, 0xe1, 0x94, 0x9f, 0x67, 0xae,
	0x57, 0xf5, 0x77, 0x79, 0x80, 0xc0, 0xce, 0xf9, 0x6b, 0x7b, 0x13, 0xb2, 0x7c, 0xc2, 0xfe, 0x8c,
	0x32, 0x6c, 0x58, 0xef, 0x4d, 0xae, 0x44, 0x29, 0xb2, 0x12, 0x4f, 0xa1, 0x40, 0x9c, 0x11, 0x4b,
	0x99, 0xbc, 0x58, 0x5d, 0xe2, 0x00, 0xbe, 0x15, 0x99, 0x44, 0x9b, 0x4b, 0xf0, 0x6a, 0x15, 0x88,
	0xff, 0x8d, 0x3e, 0x82, 0x0c, 0xa1, 0x26, 0x1d, 0x11, 0xbe, 0x50, 0xe5, 0xd0, 0xe5, 0x45, 0xaa,
	0x71, 0xa6, 0x2e, 0x85, 0x58, 0xfc, 0xe5, 0x4d, 0x7b, 0xdc, 0x33, 0x4c, 0xca, 0x31, 0x9a, 0xd4,
	0xf3, 0x92, 0x52, 0xa3, 0xac, 0xf6, 0xc7, 0x76, 0x4f, 0x30, 0x0b, 0x9c, 0x99, 0xe5, 0xe3, 0x1a,
	0x7f, 0x5d, 0x1b, 0x71, 0xa8, 0x71, 0x26, 0x12, 0x9a, 0x92, 0x52, 0xa3, 0x2c, 0x1d, 0xf7, 0x30,
	0x35, 0xad, 0x3e, 0xa9, 0xdc, 0x10, 0xe9, 0x58, 0x0e, 0x59, 0xec, 0xc1, 0xae, 0xeb, 0xb8, 0x32,
	0x01, 0x8b, 0x01, 0x33, 0xc7, 0x3f, 0xf8, 0xa1, 0xad, 0x2c, 0x8a, 0xe0, 0xc3, 0x29, 0xec, 0x78,
	0xa2, 0x06, 0x94, 0xf9, 0x7a, 0x75, 0xbd, 0x2e, 0x9d, 0xbc, 0xbe, 0xdf, 0xf3, 0xa7, 0x77, 0xf6,
	0xeb, 0xe1, 0xce, 0x8c, 0x5e, 0x72, 0xc3, 0x5c, 0xf4, 0x11, 0x24, 0x5f, 0xe0, 0x83, 0x4a, 0x39,
	0xd2, 0xb6, 0x88, 0x3e, 0x94, 0xb0, 0x2b, 0xe4, 0x0b, 0x7c, 0x80, 0x34, 0x28, 0x0c, 0x83, 0x5e,
	0x72, 0x65, 0x9e, 0xab, 0xbd, 0x15, 0x64, 0xd9, 0x33, 0xfa, 0xcc, 0x3b, 0x33, 0x7a, 0x58, 0x0f,
	0x35, 0x61, 0x56, 0x34, 0x52, 0x82, 0x49, 0x88, 0x4a, 0xef, 0x6d, 0xdf, 0xd4, 0x39, 0x9d, 0xc8,
	0x9d, 0x19, 0xbd, 0x4c, 0x27, 0xd8, 0x68, 0x0d, 0xd2, 0x9c, 0x22, 0xeb, 0x92, 0xdb, 0x93, 0x66,
	0xa2, 0xda, 0x42, 0x16, 0x7d, 0x1c, 0x79, 0x40, 0x8c, 0xe6, 0x20, 0x06, 0x6a, 0x76, 0xa4, 0x78,
	0xd7, 0x4f, 0xf1, 0x7b, 0x86, 0x1f, 0xc8, 0x56, 0x4f, 0xf4, 0x56, 0xce, 0xea, 0x04, 0xa6, 0x22,
	0xc5, 0x45, 0x9b, 0xe7, 0x93, 0x50, 0x83, 0xb1, 0x18, 0xed, 0x4b, 0x4a, 0x46, 0x48, 0x29, 0xe8,
	0x2c, 0x3e, 0xf5, 0x1b, 0x75, 0x2e, 0x26, 0xa3, 0x3e, 0xf5, 0xda, 0x5d, 0xf3, 0x31, 0x4e, 0x7a,
	0x6d, 0x3a, 0x5d, 0x48, 0xa2, 0x47, 0xb2, 0x47, 0xe8, 0x69, 0x8a, 0x77, 0xb3, 0x69, 0x4f, 0x45,
	0x6b, 0xd0, 0xd3, 0xfa, 0x22, 0x68, 0xee, 0x79, 0x8a, 0x73, 0xd1, 0xde, 0x60, 0xc8, 0x63, 0xbf,
	0xab, 0xe7, 0xe9, 0x3f, 0xf1, 0xba, 0x7a, 0x9e, 0xf6, 0x42, 0xc4, 0x61, 0x5e, 0x0e, 0x09, 0x5d,
	0xd1, 0xd4, 0xf3, 0x34, 0x3f, 0x80, 0x39, 0xaf, 0x10, 0x32, 0xfa, 0x4e, 0x57, 0x74, 0x22, 0x6e,
	0x8a, 0xae, 0x85, 0xc7, 0x68, 0x48, 0x3a, 0x5a, 0x81, 0xf9, 0x03, 0xb3, 0x7b, 0x32, 0x1a, 0x1a,
	0x84, 0x3a, 0x2e, 0xfb, 0xbd, 0x11, 0xc1, 0xbd, 0xca, 0x2d, 0x1e, 0x0c, 0xe7, 0x04, 0xab, 0x2d,
	0x38, 0xfb, 0x04, 0xf7, 0xd6, 0xf3, 0x90, 0x75, 0xc5, 0xce, 0xb3, 0xea, 0x44, 0xf8, 0x56, 0xfd,
	0x1c, 0xca, 0x93, 0x7b, 0x8c, 0xde, 0x83, 0x94, 0x65, 0x1f, 0x3a, 0x53, 0x71, 0x32, 0xb4, 0xca,
	0x5c, 0xe0, 0x69, 0xa2, 0xa2, 0x54, 0xff, 0xa1, 0x00, 0x04, 0x8c, 0xf8, 0x57, 0xc4, 0x50, 0x30,
	0x49, 0x9c, 0x17, 0x4c, 0x92, 0x93, 0xc1, 0x64, 0x09, 0x72, 0x13, 0xcd, 0x98, 0xa4, 0xee, 0x8f,
	0xd1, 0x43, 0x3f, 0xa2, 0x89, 0x2b, 0xc7, 0x52, 0x8c, 0x97, 0x2b, 0x91, 0xb0, 0xe6, 0xc7, 0x98,
	0x4c, 0x28, 0xc6, 0x54, 0x57, 0x20, 0x23, 0xe4, 0x10, 0x40, 0xa6, 0xb6, 0xd1, 0xa9, 0x3f, 0xd3,
	0xd4, 0x19, 0x54, 0x84, 0xdc, 0x56, 0x7d, 0xaf, 0xde, 0xde, 0xd1, 0x36, 0x55, 0x85, 0x71, 0xb6,
	0x6a, 0xf5, 0x86, 0xb6, 0xa9, 0x26, 0xaa, 0xdf, 0x29, 0x90, 0xf3, 0x50, 0xe3, 0xf5, 0x32, 0xc2,
	0xa9, 0xcb, 0x1b, 0xbf, 0xa6, 0x89, 0x67, 0x22, 0x13, 0x47, 0x90, 0x22, 0xd6, 0xb7, 0x58, 0x2e,
	0x08, 0xff, 0x66, 0xf2, 0x3e, 0x56, 0xc4, 0x35, 0xd6, 0x1f, 0x57, 0xff, 0x94, 0x80, 0x62, 0x18,
	0xab, 0xd3, 0xdd, 0x33, 0xe5, 0xc2, 0xdd, 0xb3, 0xdc, 0x19, 0xdd, 0xb3, 0xb0, 0xbf, 0x89, 0x33,
	0xfc, 0x4d, 0x86, 0xfc, 0xfd, 0x00, 0xe6, 0x7c, 0xc3, 0xbe, 0xe3, 0xe2, 0x66, 0xae, 0x7a, 0x0c,
	0x1f, 0xe4, 0x8f, 0x60, 0x71, 0xd2, 0x15, 0x5f, 0x43, 0xa4, 0x8a, 0x85, 0xb0, 0x3b, 0xbe, 0x16,
	0xaf, 0x97, 0xc5, 0x09, 0xe6, 0xe5, 0x08, 0x5f, 0x97, 0x24, 0xab, 0x97, 0x39, 0x71, 0x83, 0xd1,
	0x22, 0x3b, 0x94, 0x39, 0x6f, 0x87, 0xb2, 0x13, 0x3b, 0x54, 0xfd, 0x89, 0x02, 0x10, 0x9c, 0xe1,
	0x8b, 0x37, 0x5a, 0xee, 0x04, 0x5d, 0x12, 0xe6, 0x94, 0xc2, 0xad, 0x7a, 0x0d, 0x91, 0x69, 0x97,
	0x2e, 0x01, 0x9a, 0xea, 0x2f, 0x12, 0x70, 0xa3, 0x36, 0xa2, 0xce, 0x54, 0xe2, 0x09, 0xbd, 0xcc,
	0x28, 0xd7, 0x78, 0x7d, 0x4c, 0x5c, 0xe3, 0xf5, 0x31, 0x79, 0xb9, 0x97, 0x99, 0x98, 0xb7, 0x95,
	0xd4, 0xe5, 0xdf, 0x56, 0xc2, 0x4f, 0x1c, 0x3f, 0x4b, 0xc0, 0x2c, 0x5b, 0x9c, 0x50, 0x0e, 0xfc,
	0x8f, 0xef, 0x43, 0x2e, 0x7f, 0x2a, 0x0b, 0xc6, 0xe0, 0x89, 0x03, 0x2d, 0x80, 0xba, 0xa9, 0x6d,
	0xd5, 0xf6, 0x1b, 0x1d, 0x63, 0xab, 0xde, 0xd0, 0x3a, 0x3f, 0x68, 0xb1, 0x60, 0x98, 0x85, 0xe4,
	0x6e, 0xeb, 0x91, 0xaa, 0xb0, 0x8f, 0xe6, 0xf6, 0xb6, 0x9a, 0x58, 0xde, 0x83, 0x1b, 0xb1, 0xed,
	0x7c, 0x74, 0x0f, 0xee, 0x78, 0x06, 0xda, 0xda, 0xf6, 0xae, 0xb6, 0xd7, 0xd1, 0x36, 0xb9, 0x29,
	0xa3, 0xa5, 0x37, 0x3b, 0xcd, 0x8d, 0x66, 0x43, 0x9d, 0x41, 0x2a, 0x14, 0x77, 0x1a, 0xed, 0x80,
	0xa2, 0x2c, 0x3f, 0x88, 0x3c, 0x27, 0xc8, 0xa6, 0x62, 0x1e, 0xd2, 0xf5, 0xbd, 0x4d, 0xed, 0x6b,
	0x75, 0x06, 0x95, 0x20, 0xdf, 0xa9, 0xef, 0x6a, 0xed, 0x4e, 0x6d, 0xb7, 0xa5, 0x2a, 0xcb, 0x87,
	0x30, 0x1b, 0xe9, 0x48, 0xa2, 0x45, 0x40, 0xf5, 0xdd, 0xda, 0xb6, 0x66, 0xb4, 0xf7, 0xb7, 0xb6,
	0xea, 0x5f, 0x1b, 0x9e, 0xe6, 0x12, 0x2c, 0x4e, 0xd0, 0x43, 0x66, 0xd0, 0x1d, 0xb8, 0x3d, 0xc1,
	0xdb, 0x6b, 0xee, 0x69, 0x46, 0xf3, 0x99, 0xa6, 0x3f, 0xd7, 0xeb, 0x1d, 0x4d, 0x4d, 0x2c, 0x7f,
	0xea, 0x65, 0x47, 0x7f, 0x86, 0xa1, 0x25, 0x0a, 0x4d, 0x29, 0x07, 0x29, 0xbd, 0xc3, 0x4d, 0x66,
	0x21, 0xd9, 0xd6, 0x3b, 0x6a, 0x62, 0x79, 0x1f, 0x0a, 0xa1, 0x7f, 0x8b, 0x21, 0x04, 0x65, 0x4f,
	0x6f, 0xb7, 0xfe, 0x75, 0x7d, 0x6f, 0x5b, 0x9d, 0x61, 0x2e, 0x6f, 0xee, 0xd7, 0x1a, 0xc6, 0xc6,
	0x4e, 0x6d, 0x6f, 0x4f, 0x6b, 0x18, 0xb5, 0x6d, 0x6d, 0xaf, 0xa3, 0x2a, 0xcc, 0xe5, 0x49, 0x7a,
	0xa3, 0xa3, 0xe9, 0x7b, 0x35, 0xee, 0xd1, 0x1f, 0x15, 0xb8, 0x11, 0x7b, 0x6e, 0xf9, 0xb2, 0x3e,
	0x7c, 0xfc, 0xc8, 0xf8, 0xe4, 0xe1, 0x6a, 0xcb, 0x58, 0x5b, 0x95, 0x0b, 0xed, 0x53, 0x1e, 0xaf,
	0xaa, 0x0a, 0x9a, 0x83, 0x12, 0xa7, 0xfc, 0xef, 0xea, 0x13, 0x21, 0x94, 0x88, 0x90, 0x1e, 0xaf,
	0xaa, 0x49, 0x74, 0x0b, 0x6e, 0xb4, 0x9a, 0x7a, 0x47, 0xaf, 0xd5, 0x3b, 0xc6, 0x84, 0xc9, 0xd4,
	0x19, 0xac, 0xc7, 0xab, 0x6a, 0x9a, 0x79, 0x3d, 0xc9, 0xf2, 0x7f, 0x24, 0x73, 0x16, 0xef, 0xf1,
	0xaa, 0x9a, 0x5d, 0xfe, 0x95, 0x02, 0xc5, 0x70, 0x1d, 0x82, 0xe6, 0x61, 0x56, 0xdb, 0xd6, 0xb5,
	0x76, 0xdb, 0x68, 0x77, 0x6a, 0x7a, 0x47, 0xac, 0xd5, 0x1c, 0x94, 0x24, 0x51, 0x26, 0x69, 0x25,
	0x44, 0xd2, 0xf6, 0x36, 0x99, 0x54, 0x22, 0xa4, 0xba, 0xd1, 0xdc, 0x6d, 0x35, 0xb4, 0x8e, 0xa6,
	0x26, 0x43, 0x72, 0x32, 0x8b, 0xa7, 0xd8, 0x6e, 0x78, 0xd6, 0xd6, 0x9b, 0x7a, 0x47, 0xdb, 0x54,
	0xd3, 0xa8, 0x02, 0x0b, 0x92, 0xd6, 0xa8, 0xef, 0xd6, 0x3b, 0x86, 0xae, 0xd5, 0x36, 0x58, 0xfe,
	0xcf, 0x2c, 0x7f, 0x09, 0x6a, 0xb4, 0xbe, 0x62, 0x33, 0xf2, 0x9c, 0x6c, 0xee, 0xeb, 0x1b, 0x9a,
	0xc1, 0x0e, 0x8b, 0xf1, 0x5c, 0x5b, 0x17, 0x90, 0x8b, 0xe1, 0xb5, 0x37, 0xbf, 0x52, 0x95, 0x87,
	0xbf, 0x4c, 0x43, 0x46, 0x46, 0xa0, 0x0e, 0x54, 0xf8, 0x7f, 0xa0, 0x62, 0xaa, 0x14, 0x74, 0x91,
	0x1a, 0x66, 0x29, 0xae, 0x86, 0x45, 0xdf, 0x63, 0x90, 0x35, 0x5d, 0xea, 0x17, 0x2e, 0xe8, 0xec,
	0x62, 0x26, 0xde, 0x42, 0x0b, 0x16, 0xb9, 0x85, 0xe9, 0x54, 0xf2, 0xea, 0xfa, 0x26, 0xde, 0xe2,
	0x33, 0xb8, 0xc5, 0x2d, 0xc6, 0x95, 0x32, 0xe8, 0x42, 0x95, 0x4e, 0xbc, 0xdd, 0x4d, 0x50, 0x03,
	0xbb, 0xd2, 0xdc, 0x79, 0x15, 0x4f, 0xbc, 0x95, 0x1a, 0x14, 0xc3, 0xdd, 0x16, 0x14, 0xa4, 0x99,
	0x98, 0x26, 0xcc, 0x2b, 0x4c, 0x88, 0x68, 0x31, 0x65, 0x62, 0xa2, 0xf5, 0x12, 0x6f, 0x42, 0x03,
	0x08, 0xba, 0x16, 0x28, 0x48, 0x96, 0x53, 0x1d, 0x93, 0xa5, 0xdb, 0xb1, 0x3c, 0xd9, 0xe6, 0xf8,
	0x9c, 0xdd, 0xc7, 0xbd, 0xde, 0x05, 0x0a, 0xdf, 0x8b, 0x23, 0x0d, 0x8d, 0x58, 0x2f, 0xd6, 0xb7,
	0x7e, 0x78, 0xef, 0xc8, 0xa2, 0xc7, 0xa3, 0x83, 0x95, 0xae, 0x33, 0x78, 0x20, 0x05, 0x1e, 0x78,
	0x9d, 0x47, 0x8f, 0xf0, 0xdb, 0x44, 0xa9, 0x61, 0x9d, 0xe2, 0xaf, 0x44, 0x3b, 0x99, 0x3a, 0x7f,
	0x4d, 0x94, 0xe5, 0xf8, 0xe9, 0x53, 0x4e, 0x38, 0xc8, 0x70, 0x95, 0xb5, 0x7f, 0x05, 0x00, 0x00,
	0xff, 0xff, 0x89, 0xb9, 0x85, 0xdc, 0xf9, 0x2b, 0x00, 0x00,
}
