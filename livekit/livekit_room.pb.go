// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: livekit_room.proto

package livekit

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateRoomRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// name of the room
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// configuration to use for this room parameters. Setting parameters below override the config defaults.
	RoomPreset string `protobuf:"bytes,12,opt,name=room_preset,json=roomPreset,proto3" json:"room_preset,omitempty"`
	// number of seconds to keep the room open if no one joins
	EmptyTimeout uint32 `protobuf:"varint,2,opt,name=empty_timeout,json=emptyTimeout,proto3" json:"empty_timeout,omitempty"`
	// number of seconds to keep the room open after everyone leaves
	DepartureTimeout uint32 `protobuf:"varint,10,opt,name=departure_timeout,json=departureTimeout,proto3" json:"departure_timeout,omitempty"`
	// limit number of participants that can be in a room
	MaxParticipants uint32 `protobuf:"varint,3,opt,name=max_participants,json=maxParticipants,proto3" json:"max_participants,omitempty"`
	// override the node room is allocated to, for debugging
	NodeId string `protobuf:"bytes,4,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	// metadata of room
	Metadata string `protobuf:"bytes,5,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// auto-egress configurations
	Egress *RoomEgress `protobuf:"bytes,6,opt,name=egress,proto3" json:"egress,omitempty"`
	// playout delay of subscriber
	MinPlayoutDelay uint32 `protobuf:"varint,7,opt,name=min_playout_delay,json=minPlayoutDelay,proto3" json:"min_playout_delay,omitempty"`
	MaxPlayoutDelay uint32 `protobuf:"varint,8,opt,name=max_playout_delay,json=maxPlayoutDelay,proto3" json:"max_playout_delay,omitempty"`
	// improves A/V sync when playout_delay set to a value larger than 200ms. It will disables transceiver re-use
	// so not recommended for rooms with frequent subscription changes
	SyncStreams bool `protobuf:"varint,9,opt,name=sync_streams,json=syncStreams,proto3" json:"sync_streams,omitempty"`
	// replay
	ReplayEnabled bool `protobuf:"varint,13,opt,name=replay_enabled,json=replayEnabled,proto3" json:"replay_enabled,omitempty"`
	// Define agents that should be dispatched to this room
	Agents        []*RoomAgentDispatch `protobuf:"bytes,14,rep,name=agents,proto3" json:"agents,omitempty"` // NEXT-ID: 15
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateRoomRequest) Reset() {
	*x = CreateRoomRequest{}
	mi := &file_livekit_room_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateRoomRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRoomRequest) ProtoMessage() {}

func (x *CreateRoomRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_room_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRoomRequest.ProtoReflect.Descriptor instead.
func (*CreateRoomRequest) Descriptor() ([]byte, []int) {
	return file_livekit_room_proto_rawDescGZIP(), []int{0}
}

func (x *CreateRoomRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateRoomRequest) GetRoomPreset() string {
	if x != nil {
		return x.RoomPreset
	}
	return ""
}

func (x *CreateRoomRequest) GetEmptyTimeout() uint32 {
	if x != nil {
		return x.EmptyTimeout
	}
	return 0
}

func (x *CreateRoomRequest) GetDepartureTimeout() uint32 {
	if x != nil {
		return x.DepartureTimeout
	}
	return 0
}

func (x *CreateRoomRequest) GetMaxParticipants() uint32 {
	if x != nil {
		return x.MaxParticipants
	}
	return 0
}

func (x *CreateRoomRequest) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *CreateRoomRequest) GetMetadata() string {
	if x != nil {
		return x.Metadata
	}
	return ""
}

func (x *CreateRoomRequest) GetEgress() *RoomEgress {
	if x != nil {
		return x.Egress
	}
	return nil
}

func (x *CreateRoomRequest) GetMinPlayoutDelay() uint32 {
	if x != nil {
		return x.MinPlayoutDelay
	}
	return 0
}

func (x *CreateRoomRequest) GetMaxPlayoutDelay() uint32 {
	if x != nil {
		return x.MaxPlayoutDelay
	}
	return 0
}

func (x *CreateRoomRequest) GetSyncStreams() bool {
	if x != nil {
		return x.SyncStreams
	}
	return false
}

func (x *CreateRoomRequest) GetReplayEnabled() bool {
	if x != nil {
		return x.ReplayEnabled
	}
	return false
}

func (x *CreateRoomRequest) GetAgents() []*RoomAgentDispatch {
	if x != nil {
		return x.Agents
	}
	return nil
}

type RoomEgress struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Room          *RoomCompositeEgressRequest `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	Participant   *AutoParticipantEgress      `protobuf:"bytes,3,opt,name=participant,proto3" json:"participant,omitempty"`
	Tracks        *AutoTrackEgress            `protobuf:"bytes,2,opt,name=tracks,proto3" json:"tracks,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RoomEgress) Reset() {
	*x = RoomEgress{}
	mi := &file_livekit_room_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoomEgress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomEgress) ProtoMessage() {}

func (x *RoomEgress) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_room_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomEgress.ProtoReflect.Descriptor instead.
func (*RoomEgress) Descriptor() ([]byte, []int) {
	return file_livekit_room_proto_rawDescGZIP(), []int{1}
}

func (x *RoomEgress) GetRoom() *RoomCompositeEgressRequest {
	if x != nil {
		return x.Room
	}
	return nil
}

func (x *RoomEgress) GetParticipant() *AutoParticipantEgress {
	if x != nil {
		return x.Participant
	}
	return nil
}

func (x *RoomEgress) GetTracks() *AutoTrackEgress {
	if x != nil {
		return x.Tracks
	}
	return nil
}

type RoomAgent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Dispatches    []*RoomAgentDispatch   `protobuf:"bytes,1,rep,name=dispatches,proto3" json:"dispatches,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RoomAgent) Reset() {
	*x = RoomAgent{}
	mi := &file_livekit_room_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoomAgent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomAgent) ProtoMessage() {}

func (x *RoomAgent) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_room_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomAgent.ProtoReflect.Descriptor instead.
func (*RoomAgent) Descriptor() ([]byte, []int) {
	return file_livekit_room_proto_rawDescGZIP(), []int{2}
}

func (x *RoomAgent) GetDispatches() []*RoomAgentDispatch {
	if x != nil {
		return x.Dispatches
	}
	return nil
}

type ListRoomsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// when set, will only return rooms with name match
	Names         []string `protobuf:"bytes,1,rep,name=names,proto3" json:"names,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListRoomsRequest) Reset() {
	*x = ListRoomsRequest{}
	mi := &file_livekit_room_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListRoomsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRoomsRequest) ProtoMessage() {}

func (x *ListRoomsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_room_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRoomsRequest.ProtoReflect.Descriptor instead.
func (*ListRoomsRequest) Descriptor() ([]byte, []int) {
	return file_livekit_room_proto_rawDescGZIP(), []int{3}
}

func (x *ListRoomsRequest) GetNames() []string {
	if x != nil {
		return x.Names
	}
	return nil
}

type ListRoomsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Rooms         []*Room                `protobuf:"bytes,1,rep,name=rooms,proto3" json:"rooms,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListRoomsResponse) Reset() {
	*x = ListRoomsResponse{}
	mi := &file_livekit_room_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListRoomsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRoomsResponse) ProtoMessage() {}

func (x *ListRoomsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_room_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRoomsResponse.ProtoReflect.Descriptor instead.
func (*ListRoomsResponse) Descriptor() ([]byte, []int) {
	return file_livekit_room_proto_rawDescGZIP(), []int{4}
}

func (x *ListRoomsResponse) GetRooms() []*Room {
	if x != nil {
		return x.Rooms
	}
	return nil
}

type DeleteRoomRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// name of the room
	Room          string `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteRoomRequest) Reset() {
	*x = DeleteRoomRequest{}
	mi := &file_livekit_room_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteRoomRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRoomRequest) ProtoMessage() {}

func (x *DeleteRoomRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_room_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRoomRequest.ProtoReflect.Descriptor instead.
func (*DeleteRoomRequest) Descriptor() ([]byte, []int) {
	return file_livekit_room_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteRoomRequest) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

type DeleteRoomResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteRoomResponse) Reset() {
	*x = DeleteRoomResponse{}
	mi := &file_livekit_room_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteRoomResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRoomResponse) ProtoMessage() {}

func (x *DeleteRoomResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_room_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRoomResponse.ProtoReflect.Descriptor instead.
func (*DeleteRoomResponse) Descriptor() ([]byte, []int) {
	return file_livekit_room_proto_rawDescGZIP(), []int{6}
}

type ListParticipantsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// name of the room
	Room          string `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListParticipantsRequest) Reset() {
	*x = ListParticipantsRequest{}
	mi := &file_livekit_room_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListParticipantsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListParticipantsRequest) ProtoMessage() {}

func (x *ListParticipantsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_room_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListParticipantsRequest.ProtoReflect.Descriptor instead.
func (*ListParticipantsRequest) Descriptor() ([]byte, []int) {
	return file_livekit_room_proto_rawDescGZIP(), []int{7}
}

func (x *ListParticipantsRequest) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

type ListParticipantsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Participants  []*ParticipantInfo     `protobuf:"bytes,1,rep,name=participants,proto3" json:"participants,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListParticipantsResponse) Reset() {
	*x = ListParticipantsResponse{}
	mi := &file_livekit_room_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListParticipantsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListParticipantsResponse) ProtoMessage() {}

func (x *ListParticipantsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_room_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListParticipantsResponse.ProtoReflect.Descriptor instead.
func (*ListParticipantsResponse) Descriptor() ([]byte, []int) {
	return file_livekit_room_proto_rawDescGZIP(), []int{8}
}

func (x *ListParticipantsResponse) GetParticipants() []*ParticipantInfo {
	if x != nil {
		return x.Participants
	}
	return nil
}

type RoomParticipantIdentity struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// name of the room
	Room string `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	// identity of the participant
	Identity      string `protobuf:"bytes,2,opt,name=identity,proto3" json:"identity,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RoomParticipantIdentity) Reset() {
	*x = RoomParticipantIdentity{}
	mi := &file_livekit_room_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoomParticipantIdentity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomParticipantIdentity) ProtoMessage() {}

func (x *RoomParticipantIdentity) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_room_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomParticipantIdentity.ProtoReflect.Descriptor instead.
func (*RoomParticipantIdentity) Descriptor() ([]byte, []int) {
	return file_livekit_room_proto_rawDescGZIP(), []int{9}
}

func (x *RoomParticipantIdentity) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

func (x *RoomParticipantIdentity) GetIdentity() string {
	if x != nil {
		return x.Identity
	}
	return ""
}

type RemoveParticipantResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveParticipantResponse) Reset() {
	*x = RemoveParticipantResponse{}
	mi := &file_livekit_room_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveParticipantResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveParticipantResponse) ProtoMessage() {}

func (x *RemoveParticipantResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_room_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveParticipantResponse.ProtoReflect.Descriptor instead.
func (*RemoveParticipantResponse) Descriptor() ([]byte, []int) {
	return file_livekit_room_proto_rawDescGZIP(), []int{10}
}

type MuteRoomTrackRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// name of the room
	Room     string `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	Identity string `protobuf:"bytes,2,opt,name=identity,proto3" json:"identity,omitempty"`
	// sid of the track to mute
	TrackSid string `protobuf:"bytes,3,opt,name=track_sid,json=trackSid,proto3" json:"track_sid,omitempty"`
	// set to true to mute, false to unmute
	Muted         bool `protobuf:"varint,4,opt,name=muted,proto3" json:"muted,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MuteRoomTrackRequest) Reset() {
	*x = MuteRoomTrackRequest{}
	mi := &file_livekit_room_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MuteRoomTrackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MuteRoomTrackRequest) ProtoMessage() {}

func (x *MuteRoomTrackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_room_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MuteRoomTrackRequest.ProtoReflect.Descriptor instead.
func (*MuteRoomTrackRequest) Descriptor() ([]byte, []int) {
	return file_livekit_room_proto_rawDescGZIP(), []int{11}
}

func (x *MuteRoomTrackRequest) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

func (x *MuteRoomTrackRequest) GetIdentity() string {
	if x != nil {
		return x.Identity
	}
	return ""
}

func (x *MuteRoomTrackRequest) GetTrackSid() string {
	if x != nil {
		return x.TrackSid
	}
	return ""
}

func (x *MuteRoomTrackRequest) GetMuted() bool {
	if x != nil {
		return x.Muted
	}
	return false
}

type MuteRoomTrackResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Track         *TrackInfo             `protobuf:"bytes,1,opt,name=track,proto3" json:"track,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MuteRoomTrackResponse) Reset() {
	*x = MuteRoomTrackResponse{}
	mi := &file_livekit_room_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MuteRoomTrackResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MuteRoomTrackResponse) ProtoMessage() {}

func (x *MuteRoomTrackResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_room_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MuteRoomTrackResponse.ProtoReflect.Descriptor instead.
func (*MuteRoomTrackResponse) Descriptor() ([]byte, []int) {
	return file_livekit_room_proto_rawDescGZIP(), []int{12}
}

func (x *MuteRoomTrackResponse) GetTrack() *TrackInfo {
	if x != nil {
		return x.Track
	}
	return nil
}

type UpdateParticipantRequest struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	Room     string                 `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	Identity string                 `protobuf:"bytes,2,opt,name=identity,proto3" json:"identity,omitempty"`
	// metadata to update. skipping updates if left empty
	Metadata string `protobuf:"bytes,3,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// set to update the participant's permissions
	Permission *ParticipantPermission `protobuf:"bytes,4,opt,name=permission,proto3" json:"permission,omitempty"`
	// display name to update
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	// attributes to update. it only updates attributes that have been set
	// to delete attributes, set the value to an empty string
	Attributes    map[string]string `protobuf:"bytes,6,rep,name=attributes,proto3" json:"attributes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateParticipantRequest) Reset() {
	*x = UpdateParticipantRequest{}
	mi := &file_livekit_room_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateParticipantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateParticipantRequest) ProtoMessage() {}

func (x *UpdateParticipantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_room_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateParticipantRequest.ProtoReflect.Descriptor instead.
func (*UpdateParticipantRequest) Descriptor() ([]byte, []int) {
	return file_livekit_room_proto_rawDescGZIP(), []int{13}
}

func (x *UpdateParticipantRequest) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

func (x *UpdateParticipantRequest) GetIdentity() string {
	if x != nil {
		return x.Identity
	}
	return ""
}

func (x *UpdateParticipantRequest) GetMetadata() string {
	if x != nil {
		return x.Metadata
	}
	return ""
}

func (x *UpdateParticipantRequest) GetPermission() *ParticipantPermission {
	if x != nil {
		return x.Permission
	}
	return nil
}

func (x *UpdateParticipantRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateParticipantRequest) GetAttributes() map[string]string {
	if x != nil {
		return x.Attributes
	}
	return nil
}

type UpdateSubscriptionsRequest struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	Room     string                 `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	Identity string                 `protobuf:"bytes,2,opt,name=identity,proto3" json:"identity,omitempty"`
	// list of sids of tracks
	TrackSids []string `protobuf:"bytes,3,rep,name=track_sids,json=trackSids,proto3" json:"track_sids,omitempty"`
	// set to true to subscribe, false to unsubscribe from tracks
	Subscribe bool `protobuf:"varint,4,opt,name=subscribe,proto3" json:"subscribe,omitempty"`
	// list of participants and their tracks
	ParticipantTracks []*ParticipantTracks `protobuf:"bytes,5,rep,name=participant_tracks,json=participantTracks,proto3" json:"participant_tracks,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *UpdateSubscriptionsRequest) Reset() {
	*x = UpdateSubscriptionsRequest{}
	mi := &file_livekit_room_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateSubscriptionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSubscriptionsRequest) ProtoMessage() {}

func (x *UpdateSubscriptionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_room_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSubscriptionsRequest.ProtoReflect.Descriptor instead.
func (*UpdateSubscriptionsRequest) Descriptor() ([]byte, []int) {
	return file_livekit_room_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateSubscriptionsRequest) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

func (x *UpdateSubscriptionsRequest) GetIdentity() string {
	if x != nil {
		return x.Identity
	}
	return ""
}

func (x *UpdateSubscriptionsRequest) GetTrackSids() []string {
	if x != nil {
		return x.TrackSids
	}
	return nil
}

func (x *UpdateSubscriptionsRequest) GetSubscribe() bool {
	if x != nil {
		return x.Subscribe
	}
	return false
}

func (x *UpdateSubscriptionsRequest) GetParticipantTracks() []*ParticipantTracks {
	if x != nil {
		return x.ParticipantTracks
	}
	return nil
}

type UpdateSubscriptionsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateSubscriptionsResponse) Reset() {
	*x = UpdateSubscriptionsResponse{}
	mi := &file_livekit_room_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateSubscriptionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSubscriptionsResponse) ProtoMessage() {}

func (x *UpdateSubscriptionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_room_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSubscriptionsResponse.ProtoReflect.Descriptor instead.
func (*UpdateSubscriptionsResponse) Descriptor() ([]byte, []int) {
	return file_livekit_room_proto_rawDescGZIP(), []int{15}
}

type SendDataRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Room  string                 `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	Data  []byte                 `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	Kind  DataPacket_Kind        `protobuf:"varint,3,opt,name=kind,proto3,enum=livekit.DataPacket_Kind" json:"kind,omitempty"`
	// mark deprecated
	//
	// Deprecated: Marked as deprecated in livekit_room.proto.
	DestinationSids []string `protobuf:"bytes,4,rep,name=destination_sids,json=destinationSids,proto3" json:"destination_sids,omitempty"`
	// when set, only forward to these identities
	DestinationIdentities []string `protobuf:"bytes,6,rep,name=destination_identities,json=destinationIdentities,proto3" json:"destination_identities,omitempty"`
	Topic                 *string  `protobuf:"bytes,5,opt,name=topic,proto3,oneof" json:"topic,omitempty"`
	// added by SDK to enable de-duping of messages, for INTERNAL USE ONLY
	Nonce         []byte `protobuf:"bytes,7,opt,name=nonce,proto3" json:"nonce,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendDataRequest) Reset() {
	*x = SendDataRequest{}
	mi := &file_livekit_room_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendDataRequest) ProtoMessage() {}

func (x *SendDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_room_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendDataRequest.ProtoReflect.Descriptor instead.
func (*SendDataRequest) Descriptor() ([]byte, []int) {
	return file_livekit_room_proto_rawDescGZIP(), []int{16}
}

func (x *SendDataRequest) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

func (x *SendDataRequest) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *SendDataRequest) GetKind() DataPacket_Kind {
	if x != nil {
		return x.Kind
	}
	return DataPacket_RELIABLE
}

// Deprecated: Marked as deprecated in livekit_room.proto.
func (x *SendDataRequest) GetDestinationSids() []string {
	if x != nil {
		return x.DestinationSids
	}
	return nil
}

func (x *SendDataRequest) GetDestinationIdentities() []string {
	if x != nil {
		return x.DestinationIdentities
	}
	return nil
}

func (x *SendDataRequest) GetTopic() string {
	if x != nil && x.Topic != nil {
		return *x.Topic
	}
	return ""
}

func (x *SendDataRequest) GetNonce() []byte {
	if x != nil {
		return x.Nonce
	}
	return nil
}

type SendDataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendDataResponse) Reset() {
	*x = SendDataResponse{}
	mi := &file_livekit_room_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendDataResponse) ProtoMessage() {}

func (x *SendDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_room_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendDataResponse.ProtoReflect.Descriptor instead.
func (*SendDataResponse) Descriptor() ([]byte, []int) {
	return file_livekit_room_proto_rawDescGZIP(), []int{17}
}

type UpdateRoomMetadataRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Room  string                 `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	// metadata to update. skipping updates if left empty
	Metadata      string `protobuf:"bytes,2,opt,name=metadata,proto3" json:"metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRoomMetadataRequest) Reset() {
	*x = UpdateRoomMetadataRequest{}
	mi := &file_livekit_room_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRoomMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRoomMetadataRequest) ProtoMessage() {}

func (x *UpdateRoomMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_room_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRoomMetadataRequest.ProtoReflect.Descriptor instead.
func (*UpdateRoomMetadataRequest) Descriptor() ([]byte, []int) {
	return file_livekit_room_proto_rawDescGZIP(), []int{18}
}

func (x *UpdateRoomMetadataRequest) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

func (x *UpdateRoomMetadataRequest) GetMetadata() string {
	if x != nil {
		return x.Metadata
	}
	return ""
}

type RoomConfiguration struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Name  string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"` // Used as ID, must be unique
	// number of seconds to keep the room open if no one joins
	EmptyTimeout uint32 `protobuf:"varint,2,opt,name=empty_timeout,json=emptyTimeout,proto3" json:"empty_timeout,omitempty"`
	// number of seconds to keep the room open after everyone leaves
	DepartureTimeout uint32 `protobuf:"varint,3,opt,name=departure_timeout,json=departureTimeout,proto3" json:"departure_timeout,omitempty"`
	// limit number of participants that can be in a room, excluding Egress and Ingress participants
	MaxParticipants uint32 `protobuf:"varint,4,opt,name=max_participants,json=maxParticipants,proto3" json:"max_participants,omitempty"`
	// egress
	Egress *RoomEgress `protobuf:"bytes,5,opt,name=egress,proto3" json:"egress,omitempty"`
	// playout delay of subscriber
	MinPlayoutDelay uint32 `protobuf:"varint,7,opt,name=min_playout_delay,json=minPlayoutDelay,proto3" json:"min_playout_delay,omitempty"`
	MaxPlayoutDelay uint32 `protobuf:"varint,8,opt,name=max_playout_delay,json=maxPlayoutDelay,proto3" json:"max_playout_delay,omitempty"`
	// improves A/V sync when playout_delay set to a value larger than 200ms. It will disables transceiver re-use
	// so not recommended for rooms with frequent subscription changes
	SyncStreams bool `protobuf:"varint,9,opt,name=sync_streams,json=syncStreams,proto3" json:"sync_streams,omitempty"`
	// Define agents that should be dispatched to this room
	Agents        []*RoomAgentDispatch `protobuf:"bytes,10,rep,name=agents,proto3" json:"agents,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RoomConfiguration) Reset() {
	*x = RoomConfiguration{}
	mi := &file_livekit_room_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoomConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomConfiguration) ProtoMessage() {}

func (x *RoomConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_room_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomConfiguration.ProtoReflect.Descriptor instead.
func (*RoomConfiguration) Descriptor() ([]byte, []int) {
	return file_livekit_room_proto_rawDescGZIP(), []int{19}
}

func (x *RoomConfiguration) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RoomConfiguration) GetEmptyTimeout() uint32 {
	if x != nil {
		return x.EmptyTimeout
	}
	return 0
}

func (x *RoomConfiguration) GetDepartureTimeout() uint32 {
	if x != nil {
		return x.DepartureTimeout
	}
	return 0
}

func (x *RoomConfiguration) GetMaxParticipants() uint32 {
	if x != nil {
		return x.MaxParticipants
	}
	return 0
}

func (x *RoomConfiguration) GetEgress() *RoomEgress {
	if x != nil {
		return x.Egress
	}
	return nil
}

func (x *RoomConfiguration) GetMinPlayoutDelay() uint32 {
	if x != nil {
		return x.MinPlayoutDelay
	}
	return 0
}

func (x *RoomConfiguration) GetMaxPlayoutDelay() uint32 {
	if x != nil {
		return x.MaxPlayoutDelay
	}
	return 0
}

func (x *RoomConfiguration) GetSyncStreams() bool {
	if x != nil {
		return x.SyncStreams
	}
	return false
}

func (x *RoomConfiguration) GetAgents() []*RoomAgentDispatch {
	if x != nil {
		return x.Agents
	}
	return nil
}

type ForwardParticipantRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// room to forward participant from
	Room string `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	// identity of the participant to forward
	Identity string `protobuf:"bytes,2,opt,name=identity,proto3" json:"identity,omitempty"`
	// room to forward participant to
	DestinationRoom string `protobuf:"bytes,3,opt,name=destination_room,json=destinationRoom,proto3" json:"destination_room,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ForwardParticipantRequest) Reset() {
	*x = ForwardParticipantRequest{}
	mi := &file_livekit_room_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ForwardParticipantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForwardParticipantRequest) ProtoMessage() {}

func (x *ForwardParticipantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_room_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForwardParticipantRequest.ProtoReflect.Descriptor instead.
func (*ForwardParticipantRequest) Descriptor() ([]byte, []int) {
	return file_livekit_room_proto_rawDescGZIP(), []int{20}
}

func (x *ForwardParticipantRequest) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

func (x *ForwardParticipantRequest) GetIdentity() string {
	if x != nil {
		return x.Identity
	}
	return ""
}

func (x *ForwardParticipantRequest) GetDestinationRoom() string {
	if x != nil {
		return x.DestinationRoom
	}
	return ""
}

type ForwardParticipantResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ForwardParticipantResponse) Reset() {
	*x = ForwardParticipantResponse{}
	mi := &file_livekit_room_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ForwardParticipantResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForwardParticipantResponse) ProtoMessage() {}

func (x *ForwardParticipantResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_room_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForwardParticipantResponse.ProtoReflect.Descriptor instead.
func (*ForwardParticipantResponse) Descriptor() ([]byte, []int) {
	return file_livekit_room_proto_rawDescGZIP(), []int{21}
}

type MoveParticipantRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// room to move participant from
	Room string `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	// identity of the participant to move to
	Identity string `protobuf:"bytes,2,opt,name=identity,proto3" json:"identity,omitempty"`
	// room to move participant to
	DestinationRoom string `protobuf:"bytes,3,opt,name=destination_room,json=destinationRoom,proto3" json:"destination_room,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *MoveParticipantRequest) Reset() {
	*x = MoveParticipantRequest{}
	mi := &file_livekit_room_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MoveParticipantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MoveParticipantRequest) ProtoMessage() {}

func (x *MoveParticipantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_room_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MoveParticipantRequest.ProtoReflect.Descriptor instead.
func (*MoveParticipantRequest) Descriptor() ([]byte, []int) {
	return file_livekit_room_proto_rawDescGZIP(), []int{22}
}

func (x *MoveParticipantRequest) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

func (x *MoveParticipantRequest) GetIdentity() string {
	if x != nil {
		return x.Identity
	}
	return ""
}

func (x *MoveParticipantRequest) GetDestinationRoom() string {
	if x != nil {
		return x.DestinationRoom
	}
	return ""
}

type MoveParticipantResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MoveParticipantResponse) Reset() {
	*x = MoveParticipantResponse{}
	mi := &file_livekit_room_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MoveParticipantResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MoveParticipantResponse) ProtoMessage() {}

func (x *MoveParticipantResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_room_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MoveParticipantResponse.ProtoReflect.Descriptor instead.
func (*MoveParticipantResponse) Descriptor() ([]byte, []int) {
	return file_livekit_room_proto_rawDescGZIP(), []int{23}
}

var File_livekit_room_proto protoreflect.FileDescriptor

const file_livekit_room_proto_rawDesc = "" +
	"\n" +
	"\x12livekit_room.proto\x12\alivekit\x1a\x14livekit_models.proto\x1a\x14livekit_egress.proto\x1a\x1clivekit_agent_dispatch.proto\"\xfd\x03\n" +
	"\x11CreateRoomRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x1f\n" +
	"\vroom_preset\x18\f \x01(\tR\n" +
	"roomPreset\x12#\n" +
	"\rempty_timeout\x18\x02 \x01(\rR\femptyTimeout\x12+\n" +
	"\x11departure_timeout\x18\n" +
	" \x01(\rR\x10departureTimeout\x12)\n" +
	"\x10max_participants\x18\x03 \x01(\rR\x0fmaxParticipants\x12\x17\n" +
	"\anode_id\x18\x04 \x01(\tR\x06nodeId\x12\x1a\n" +
	"\bmetadata\x18\x05 \x01(\tR\bmetadata\x12+\n" +
	"\x06egress\x18\x06 \x01(\v2\x13.livekit.RoomEgressR\x06egress\x12*\n" +
	"\x11min_playout_delay\x18\a \x01(\rR\x0fminPlayoutDelay\x12*\n" +
	"\x11max_playout_delay\x18\b \x01(\rR\x0fmaxPlayoutDelay\x12!\n" +
	"\fsync_streams\x18\t \x01(\bR\vsyncStreams\x12%\n" +
	"\x0ereplay_enabled\x18\r \x01(\bR\rreplayEnabled\x122\n" +
	"\x06agents\x18\x0e \x03(\v2\x1a.livekit.RoomAgentDispatchR\x06agents\"\xb9\x01\n" +
	"\n" +
	"RoomEgress\x127\n" +
	"\x04room\x18\x01 \x01(\v2#.livekit.RoomCompositeEgressRequestR\x04room\x12@\n" +
	"\vparticipant\x18\x03 \x01(\v2\x1e.livekit.AutoParticipantEgressR\vparticipant\x120\n" +
	"\x06tracks\x18\x02 \x01(\v2\x18.livekit.AutoTrackEgressR\x06tracks\"G\n" +
	"\tRoomAgent\x12:\n" +
	"\n" +
	"dispatches\x18\x01 \x03(\v2\x1a.livekit.RoomAgentDispatchR\n" +
	"dispatches\"(\n" +
	"\x10ListRoomsRequest\x12\x14\n" +
	"\x05names\x18\x01 \x03(\tR\x05names\"8\n" +
	"\x11ListRoomsResponse\x12#\n" +
	"\x05rooms\x18\x01 \x03(\v2\r.livekit.RoomR\x05rooms\"'\n" +
	"\x11DeleteRoomRequest\x12\x12\n" +
	"\x04room\x18\x01 \x01(\tR\x04room\"\x14\n" +
	"\x12DeleteRoomResponse\"-\n" +
	"\x17ListParticipantsRequest\x12\x12\n" +
	"\x04room\x18\x01 \x01(\tR\x04room\"X\n" +
	"\x18ListParticipantsResponse\x12<\n" +
	"\fparticipants\x18\x01 \x03(\v2\x18.livekit.ParticipantInfoR\fparticipants\"I\n" +
	"\x17RoomParticipantIdentity\x12\x12\n" +
	"\x04room\x18\x01 \x01(\tR\x04room\x12\x1a\n" +
	"\bidentity\x18\x02 \x01(\tR\bidentity\"\x1b\n" +
	"\x19RemoveParticipantResponse\"y\n" +
	"\x14MuteRoomTrackRequest\x12\x12\n" +
	"\x04room\x18\x01 \x01(\tR\x04room\x12\x1a\n" +
	"\bidentity\x18\x02 \x01(\tR\bidentity\x12\x1b\n" +
	"\ttrack_sid\x18\x03 \x01(\tR\btrackSid\x12\x14\n" +
	"\x05muted\x18\x04 \x01(\bR\x05muted\"A\n" +
	"\x15MuteRoomTrackResponse\x12(\n" +
	"\x05track\x18\x01 \x01(\v2\x12.livekit.TrackInfoR\x05track\"\xcc\x02\n" +
	"\x18UpdateParticipantRequest\x12\x12\n" +
	"\x04room\x18\x01 \x01(\tR\x04room\x12\x1a\n" +
	"\bidentity\x18\x02 \x01(\tR\bidentity\x12\x1a\n" +
	"\bmetadata\x18\x03 \x01(\tR\bmetadata\x12>\n" +
	"\n" +
	"permission\x18\x04 \x01(\v2\x1e.livekit.ParticipantPermissionR\n" +
	"permission\x12\x12\n" +
	"\x04name\x18\x05 \x01(\tR\x04name\x12Q\n" +
	"\n" +
	"attributes\x18\x06 \x03(\v21.livekit.UpdateParticipantRequest.AttributesEntryR\n" +
	"attributes\x1a=\n" +
	"\x0fAttributesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xd4\x01\n" +
	"\x1aUpdateSubscriptionsRequest\x12\x12\n" +
	"\x04room\x18\x01 \x01(\tR\x04room\x12\x1a\n" +
	"\bidentity\x18\x02 \x01(\tR\bidentity\x12\x1d\n" +
	"\n" +
	"track_sids\x18\x03 \x03(\tR\ttrackSids\x12\x1c\n" +
	"\tsubscribe\x18\x04 \x01(\bR\tsubscribe\x12I\n" +
	"\x12participant_tracks\x18\x05 \x03(\v2\x1a.livekit.ParticipantTracksR\x11participantTracks\"\x1d\n" +
	"\x1bUpdateSubscriptionsResponse\"\x88\x02\n" +
	"\x0fSendDataRequest\x12\x12\n" +
	"\x04room\x18\x01 \x01(\tR\x04room\x12\x12\n" +
	"\x04data\x18\x02 \x01(\fR\x04data\x12,\n" +
	"\x04kind\x18\x03 \x01(\x0e2\x18.livekit.DataPacket.KindR\x04kind\x12-\n" +
	"\x10destination_sids\x18\x04 \x03(\tB\x02\x18\x01R\x0fdestinationSids\x125\n" +
	"\x16destination_identities\x18\x06 \x03(\tR\x15destinationIdentities\x12\x19\n" +
	"\x05topic\x18\x05 \x01(\tH\x00R\x05topic\x88\x01\x01\x12\x14\n" +
	"\x05nonce\x18\a \x01(\fR\x05nonceB\b\n" +
	"\x06_topic\"\x12\n" +
	"\x10SendDataResponse\"K\n" +
	"\x19UpdateRoomMetadataRequest\x12\x12\n" +
	"\x04room\x18\x01 \x01(\tR\x04room\x12\x1a\n" +
	"\bmetadata\x18\x02 \x01(\tR\bmetadata\"\x80\x03\n" +
	"\x11RoomConfiguration\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12#\n" +
	"\rempty_timeout\x18\x02 \x01(\rR\femptyTimeout\x12+\n" +
	"\x11departure_timeout\x18\x03 \x01(\rR\x10departureTimeout\x12)\n" +
	"\x10max_participants\x18\x04 \x01(\rR\x0fmaxParticipants\x12+\n" +
	"\x06egress\x18\x05 \x01(\v2\x13.livekit.RoomEgressR\x06egress\x12*\n" +
	"\x11min_playout_delay\x18\a \x01(\rR\x0fminPlayoutDelay\x12*\n" +
	"\x11max_playout_delay\x18\b \x01(\rR\x0fmaxPlayoutDelay\x12!\n" +
	"\fsync_streams\x18\t \x01(\bR\vsyncStreams\x122\n" +
	"\x06agents\x18\n" +
	" \x03(\v2\x1a.livekit.RoomAgentDispatchR\x06agents\"v\n" +
	"\x19ForwardParticipantRequest\x12\x12\n" +
	"\x04room\x18\x01 \x01(\tR\x04room\x12\x1a\n" +
	"\bidentity\x18\x02 \x01(\tR\bidentity\x12)\n" +
	"\x10destination_room\x18\x03 \x01(\tR\x0fdestinationRoom\"\x1c\n" +
	"\x1aForwardParticipantResponse\"s\n" +
	"\x16MoveParticipantRequest\x12\x12\n" +
	"\x04room\x18\x01 \x01(\tR\x04room\x12\x1a\n" +
	"\bidentity\x18\x02 \x01(\tR\bidentity\x12)\n" +
	"\x10destination_room\x18\x03 \x01(\tR\x0fdestinationRoom\"\x19\n" +
	"\x17MoveParticipantResponse2\x9b\b\n" +
	"\vRoomService\x127\n" +
	"\n" +
	"CreateRoom\x12\x1a.livekit.CreateRoomRequest\x1a\r.livekit.Room\x12B\n" +
	"\tListRooms\x12\x19.livekit.ListRoomsRequest\x1a\x1a.livekit.ListRoomsResponse\x12E\n" +
	"\n" +
	"DeleteRoom\x12\x1a.livekit.DeleteRoomRequest\x1a\x1b.livekit.DeleteRoomResponse\x12W\n" +
	"\x10ListParticipants\x12 .livekit.ListParticipantsRequest\x1a!.livekit.ListParticipantsResponse\x12L\n" +
	"\x0eGetParticipant\x12 .livekit.RoomParticipantIdentity\x1a\x18.livekit.ParticipantInfo\x12Y\n" +
	"\x11RemoveParticipant\x12 .livekit.RoomParticipantIdentity\x1a\".livekit.RemoveParticipantResponse\x12S\n" +
	"\x12MutePublishedTrack\x12\x1d.livekit.MuteRoomTrackRequest\x1a\x1e.livekit.MuteRoomTrackResponse\x12P\n" +
	"\x11UpdateParticipant\x12!.livekit.UpdateParticipantRequest\x1a\x18.livekit.ParticipantInfo\x12`\n" +
	"\x13UpdateSubscriptions\x12#.livekit.UpdateSubscriptionsRequest\x1a$.livekit.UpdateSubscriptionsResponse\x12?\n" +
	"\bSendData\x12\x18.livekit.SendDataRequest\x1a\x19.livekit.SendDataResponse\x12G\n" +
	"\x12UpdateRoomMetadata\x12\".livekit.UpdateRoomMetadataRequest\x1a\r.livekit.Room\x12]\n" +
	"\x12ForwardParticipant\x12\".livekit.ForwardParticipantRequest\x1a#.livekit.ForwardParticipantResponse\x12T\n" +
	"\x0fMoveParticipant\x12\x1f.livekit.MoveParticipantRequest\x1a .livekit.MoveParticipantResponseBFZ#github.com/livekit/protocol/livekit\xaa\x02\rLiveKit.Proto\xea\x02\x0eLiveKit::Protob\x06proto3"

var (
	file_livekit_room_proto_rawDescOnce sync.Once
	file_livekit_room_proto_rawDescData []byte
)

func file_livekit_room_proto_rawDescGZIP() []byte {
	file_livekit_room_proto_rawDescOnce.Do(func() {
		file_livekit_room_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_livekit_room_proto_rawDesc), len(file_livekit_room_proto_rawDesc)))
	})
	return file_livekit_room_proto_rawDescData
}

var file_livekit_room_proto_msgTypes = make([]protoimpl.MessageInfo, 25)
var file_livekit_room_proto_goTypes = []any{
	(*CreateRoomRequest)(nil),           // 0: livekit.CreateRoomRequest
	(*RoomEgress)(nil),                  // 1: livekit.RoomEgress
	(*RoomAgent)(nil),                   // 2: livekit.RoomAgent
	(*ListRoomsRequest)(nil),            // 3: livekit.ListRoomsRequest
	(*ListRoomsResponse)(nil),           // 4: livekit.ListRoomsResponse
	(*DeleteRoomRequest)(nil),           // 5: livekit.DeleteRoomRequest
	(*DeleteRoomResponse)(nil),          // 6: livekit.DeleteRoomResponse
	(*ListParticipantsRequest)(nil),     // 7: livekit.ListParticipantsRequest
	(*ListParticipantsResponse)(nil),    // 8: livekit.ListParticipantsResponse
	(*RoomParticipantIdentity)(nil),     // 9: livekit.RoomParticipantIdentity
	(*RemoveParticipantResponse)(nil),   // 10: livekit.RemoveParticipantResponse
	(*MuteRoomTrackRequest)(nil),        // 11: livekit.MuteRoomTrackRequest
	(*MuteRoomTrackResponse)(nil),       // 12: livekit.MuteRoomTrackResponse
	(*UpdateParticipantRequest)(nil),    // 13: livekit.UpdateParticipantRequest
	(*UpdateSubscriptionsRequest)(nil),  // 14: livekit.UpdateSubscriptionsRequest
	(*UpdateSubscriptionsResponse)(nil), // 15: livekit.UpdateSubscriptionsResponse
	(*SendDataRequest)(nil),             // 16: livekit.SendDataRequest
	(*SendDataResponse)(nil),            // 17: livekit.SendDataResponse
	(*UpdateRoomMetadataRequest)(nil),   // 18: livekit.UpdateRoomMetadataRequest
	(*RoomConfiguration)(nil),           // 19: livekit.RoomConfiguration
	(*ForwardParticipantRequest)(nil),   // 20: livekit.ForwardParticipantRequest
	(*ForwardParticipantResponse)(nil),  // 21: livekit.ForwardParticipantResponse
	(*MoveParticipantRequest)(nil),      // 22: livekit.MoveParticipantRequest
	(*MoveParticipantResponse)(nil),     // 23: livekit.MoveParticipantResponse
	nil,                                 // 24: livekit.UpdateParticipantRequest.AttributesEntry
	(*RoomAgentDispatch)(nil),           // 25: livekit.RoomAgentDispatch
	(*RoomCompositeEgressRequest)(nil),  // 26: livekit.RoomCompositeEgressRequest
	(*AutoParticipantEgress)(nil),       // 27: livekit.AutoParticipantEgress
	(*AutoTrackEgress)(nil),             // 28: livekit.AutoTrackEgress
	(*Room)(nil),                        // 29: livekit.Room
	(*ParticipantInfo)(nil),             // 30: livekit.ParticipantInfo
	(*TrackInfo)(nil),                   // 31: livekit.TrackInfo
	(*ParticipantPermission)(nil),       // 32: livekit.ParticipantPermission
	(*ParticipantTracks)(nil),           // 33: livekit.ParticipantTracks
	(DataPacket_Kind)(0),                // 34: livekit.DataPacket.Kind
}
var file_livekit_room_proto_depIdxs = []int32{
	1,  // 0: livekit.CreateRoomRequest.egress:type_name -> livekit.RoomEgress
	25, // 1: livekit.CreateRoomRequest.agents:type_name -> livekit.RoomAgentDispatch
	26, // 2: livekit.RoomEgress.room:type_name -> livekit.RoomCompositeEgressRequest
	27, // 3: livekit.RoomEgress.participant:type_name -> livekit.AutoParticipantEgress
	28, // 4: livekit.RoomEgress.tracks:type_name -> livekit.AutoTrackEgress
	25, // 5: livekit.RoomAgent.dispatches:type_name -> livekit.RoomAgentDispatch
	29, // 6: livekit.ListRoomsResponse.rooms:type_name -> livekit.Room
	30, // 7: livekit.ListParticipantsResponse.participants:type_name -> livekit.ParticipantInfo
	31, // 8: livekit.MuteRoomTrackResponse.track:type_name -> livekit.TrackInfo
	32, // 9: livekit.UpdateParticipantRequest.permission:type_name -> livekit.ParticipantPermission
	24, // 10: livekit.UpdateParticipantRequest.attributes:type_name -> livekit.UpdateParticipantRequest.AttributesEntry
	33, // 11: livekit.UpdateSubscriptionsRequest.participant_tracks:type_name -> livekit.ParticipantTracks
	34, // 12: livekit.SendDataRequest.kind:type_name -> livekit.DataPacket.Kind
	1,  // 13: livekit.RoomConfiguration.egress:type_name -> livekit.RoomEgress
	25, // 14: livekit.RoomConfiguration.agents:type_name -> livekit.RoomAgentDispatch
	0,  // 15: livekit.RoomService.CreateRoom:input_type -> livekit.CreateRoomRequest
	3,  // 16: livekit.RoomService.ListRooms:input_type -> livekit.ListRoomsRequest
	5,  // 17: livekit.RoomService.DeleteRoom:input_type -> livekit.DeleteRoomRequest
	7,  // 18: livekit.RoomService.ListParticipants:input_type -> livekit.ListParticipantsRequest
	9,  // 19: livekit.RoomService.GetParticipant:input_type -> livekit.RoomParticipantIdentity
	9,  // 20: livekit.RoomService.RemoveParticipant:input_type -> livekit.RoomParticipantIdentity
	11, // 21: livekit.RoomService.MutePublishedTrack:input_type -> livekit.MuteRoomTrackRequest
	13, // 22: livekit.RoomService.UpdateParticipant:input_type -> livekit.UpdateParticipantRequest
	14, // 23: livekit.RoomService.UpdateSubscriptions:input_type -> livekit.UpdateSubscriptionsRequest
	16, // 24: livekit.RoomService.SendData:input_type -> livekit.SendDataRequest
	18, // 25: livekit.RoomService.UpdateRoomMetadata:input_type -> livekit.UpdateRoomMetadataRequest
	20, // 26: livekit.RoomService.ForwardParticipant:input_type -> livekit.ForwardParticipantRequest
	22, // 27: livekit.RoomService.MoveParticipant:input_type -> livekit.MoveParticipantRequest
	29, // 28: livekit.RoomService.CreateRoom:output_type -> livekit.Room
	4,  // 29: livekit.RoomService.ListRooms:output_type -> livekit.ListRoomsResponse
	6,  // 30: livekit.RoomService.DeleteRoom:output_type -> livekit.DeleteRoomResponse
	8,  // 31: livekit.RoomService.ListParticipants:output_type -> livekit.ListParticipantsResponse
	30, // 32: livekit.RoomService.GetParticipant:output_type -> livekit.ParticipantInfo
	10, // 33: livekit.RoomService.RemoveParticipant:output_type -> livekit.RemoveParticipantResponse
	12, // 34: livekit.RoomService.MutePublishedTrack:output_type -> livekit.MuteRoomTrackResponse
	30, // 35: livekit.RoomService.UpdateParticipant:output_type -> livekit.ParticipantInfo
	15, // 36: livekit.RoomService.UpdateSubscriptions:output_type -> livekit.UpdateSubscriptionsResponse
	17, // 37: livekit.RoomService.SendData:output_type -> livekit.SendDataResponse
	29, // 38: livekit.RoomService.UpdateRoomMetadata:output_type -> livekit.Room
	21, // 39: livekit.RoomService.ForwardParticipant:output_type -> livekit.ForwardParticipantResponse
	23, // 40: livekit.RoomService.MoveParticipant:output_type -> livekit.MoveParticipantResponse
	28, // [28:41] is the sub-list for method output_type
	15, // [15:28] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_livekit_room_proto_init() }
func file_livekit_room_proto_init() {
	if File_livekit_room_proto != nil {
		return
	}
	file_livekit_models_proto_init()
	file_livekit_egress_proto_init()
	file_livekit_agent_dispatch_proto_init()
	file_livekit_room_proto_msgTypes[16].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_livekit_room_proto_rawDesc), len(file_livekit_room_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   25,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_livekit_room_proto_goTypes,
		DependencyIndexes: file_livekit_room_proto_depIdxs,
		MessageInfos:      file_livekit_room_proto_msgTypes,
	}.Build()
	File_livekit_room_proto = out.File
	file_livekit_room_proto_goTypes = nil
	file_livekit_room_proto_depIdxs = nil
}
