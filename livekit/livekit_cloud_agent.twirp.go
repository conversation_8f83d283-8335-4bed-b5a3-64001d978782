// Code generated by protoc-gen-twirp v8.1.3, DO NOT EDIT.
// source: livekit_cloud_agent.proto

package livekit

import context "context"
import fmt "fmt"
import http "net/http"
import io "io"
import json "encoding/json"
import strconv "strconv"
import strings "strings"

import protojson "google.golang.org/protobuf/encoding/protojson"
import proto "google.golang.org/protobuf/proto"
import twirp "github.com/twitchtv/twirp"
import ctxsetters "github.com/twitchtv/twirp/ctxsetters"

// Version compatibility assertion.
// If the constant is not defined in the package, that likely means
// the package needs to be updated to work with this generated code.
// See https://twitchtv.github.io/twirp/docs/version_matrix.html
const _ = twirp.TwirpPackageMinVersion_8_1_0

// ====================
// CloudAgent Interface
// ====================

type CloudAgent interface {
	CreateAgent(context.Context, *CreateAgentRequest) (*CreateAgentResponse, error)

	ListAgents(context.Context, *ListAgentsRequest) (*ListAgentsResponse, error)

	ListAgentVersions(context.Context, *ListAgentVersionsRequest) (*ListAgentVersionsResponse, error)

	ListAgentSecrets(context.Context, *ListAgentSecretsRequest) (*ListAgentSecretsResponse, error)

	UpdateAgent(context.Context, *UpdateAgentRequest) (*UpdateAgentResponse, error)

	DeployAgent(context.Context, *DeployAgentRequest) (*DeployAgentResponse, error)

	UpdateAgentSecrets(context.Context, *UpdateAgentSecretsRequest) (*UpdateAgentSecretsResponse, error)

	RollbackAgent(context.Context, *RollbackAgentRequest) (*RollbackAgentResponse, error)

	DeleteAgent(context.Context, *DeleteAgentRequest) (*DeleteAgentResponse, error)

	GetClientSettings(context.Context, *ClientSettingsRequest) (*ClientSettingsResponse, error)
}

// ==========================
// CloudAgent Protobuf Client
// ==========================

type cloudAgentProtobufClient struct {
	client      HTTPClient
	urls        [10]string
	interceptor twirp.Interceptor
	opts        twirp.ClientOptions
}

// NewCloudAgentProtobufClient creates a Protobuf client that implements the CloudAgent interface.
// It communicates using Protobuf and can be configured with a custom HTTPClient.
func NewCloudAgentProtobufClient(baseURL string, client HTTPClient, opts ...twirp.ClientOption) CloudAgent {
	if c, ok := client.(*http.Client); ok {
		client = withoutRedirects(c)
	}

	clientOpts := twirp.ClientOptions{}
	for _, o := range opts {
		o(&clientOpts)
	}

	// Using ReadOpt allows backwards and forwards compatibility with new options in the future
	literalURLs := false
	_ = clientOpts.ReadOpt("literalURLs", &literalURLs)
	var pathPrefix string
	if ok := clientOpts.ReadOpt("pathPrefix", &pathPrefix); !ok {
		pathPrefix = "/twirp" // default prefix
	}

	// Build method URLs: <baseURL>[<prefix>]/<package>.<Service>/<Method>
	serviceURL := sanitizeBaseURL(baseURL)
	serviceURL += baseServicePath(pathPrefix, "livekit", "CloudAgent")
	urls := [10]string{
		serviceURL + "CreateAgent",
		serviceURL + "ListAgents",
		serviceURL + "ListAgentVersions",
		serviceURL + "ListAgentSecrets",
		serviceURL + "UpdateAgent",
		serviceURL + "DeployAgent",
		serviceURL + "UpdateAgentSecrets",
		serviceURL + "RollbackAgent",
		serviceURL + "DeleteAgent",
		serviceURL + "GetClientSettings",
	}

	return &cloudAgentProtobufClient{
		client:      client,
		urls:        urls,
		interceptor: twirp.ChainInterceptors(clientOpts.Interceptors...),
		opts:        clientOpts,
	}
}

func (c *cloudAgentProtobufClient) CreateAgent(ctx context.Context, in *CreateAgentRequest) (*CreateAgentResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "CloudAgent")
	ctx = ctxsetters.WithMethodName(ctx, "CreateAgent")
	caller := c.callCreateAgent
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *CreateAgentRequest) (*CreateAgentResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateAgentRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateAgentRequest) when calling interceptor")
					}
					return c.callCreateAgent(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*CreateAgentResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*CreateAgentResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *cloudAgentProtobufClient) callCreateAgent(ctx context.Context, in *CreateAgentRequest) (*CreateAgentResponse, error) {
	out := new(CreateAgentResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[0], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *cloudAgentProtobufClient) ListAgents(ctx context.Context, in *ListAgentsRequest) (*ListAgentsResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "CloudAgent")
	ctx = ctxsetters.WithMethodName(ctx, "ListAgents")
	caller := c.callListAgents
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ListAgentsRequest) (*ListAgentsResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListAgentsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListAgentsRequest) when calling interceptor")
					}
					return c.callListAgents(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListAgentsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListAgentsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *cloudAgentProtobufClient) callListAgents(ctx context.Context, in *ListAgentsRequest) (*ListAgentsResponse, error) {
	out := new(ListAgentsResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[1], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *cloudAgentProtobufClient) ListAgentVersions(ctx context.Context, in *ListAgentVersionsRequest) (*ListAgentVersionsResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "CloudAgent")
	ctx = ctxsetters.WithMethodName(ctx, "ListAgentVersions")
	caller := c.callListAgentVersions
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ListAgentVersionsRequest) (*ListAgentVersionsResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListAgentVersionsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListAgentVersionsRequest) when calling interceptor")
					}
					return c.callListAgentVersions(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListAgentVersionsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListAgentVersionsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *cloudAgentProtobufClient) callListAgentVersions(ctx context.Context, in *ListAgentVersionsRequest) (*ListAgentVersionsResponse, error) {
	out := new(ListAgentVersionsResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[2], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *cloudAgentProtobufClient) ListAgentSecrets(ctx context.Context, in *ListAgentSecretsRequest) (*ListAgentSecretsResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "CloudAgent")
	ctx = ctxsetters.WithMethodName(ctx, "ListAgentSecrets")
	caller := c.callListAgentSecrets
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ListAgentSecretsRequest) (*ListAgentSecretsResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListAgentSecretsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListAgentSecretsRequest) when calling interceptor")
					}
					return c.callListAgentSecrets(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListAgentSecretsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListAgentSecretsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *cloudAgentProtobufClient) callListAgentSecrets(ctx context.Context, in *ListAgentSecretsRequest) (*ListAgentSecretsResponse, error) {
	out := new(ListAgentSecretsResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[3], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *cloudAgentProtobufClient) UpdateAgent(ctx context.Context, in *UpdateAgentRequest) (*UpdateAgentResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "CloudAgent")
	ctx = ctxsetters.WithMethodName(ctx, "UpdateAgent")
	caller := c.callUpdateAgent
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *UpdateAgentRequest) (*UpdateAgentResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateAgentRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateAgentRequest) when calling interceptor")
					}
					return c.callUpdateAgent(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*UpdateAgentResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*UpdateAgentResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *cloudAgentProtobufClient) callUpdateAgent(ctx context.Context, in *UpdateAgentRequest) (*UpdateAgentResponse, error) {
	out := new(UpdateAgentResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[4], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *cloudAgentProtobufClient) DeployAgent(ctx context.Context, in *DeployAgentRequest) (*DeployAgentResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "CloudAgent")
	ctx = ctxsetters.WithMethodName(ctx, "DeployAgent")
	caller := c.callDeployAgent
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *DeployAgentRequest) (*DeployAgentResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*DeployAgentRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*DeployAgentRequest) when calling interceptor")
					}
					return c.callDeployAgent(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*DeployAgentResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*DeployAgentResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *cloudAgentProtobufClient) callDeployAgent(ctx context.Context, in *DeployAgentRequest) (*DeployAgentResponse, error) {
	out := new(DeployAgentResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[5], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *cloudAgentProtobufClient) UpdateAgentSecrets(ctx context.Context, in *UpdateAgentSecretsRequest) (*UpdateAgentSecretsResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "CloudAgent")
	ctx = ctxsetters.WithMethodName(ctx, "UpdateAgentSecrets")
	caller := c.callUpdateAgentSecrets
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *UpdateAgentSecretsRequest) (*UpdateAgentSecretsResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateAgentSecretsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateAgentSecretsRequest) when calling interceptor")
					}
					return c.callUpdateAgentSecrets(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*UpdateAgentSecretsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*UpdateAgentSecretsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *cloudAgentProtobufClient) callUpdateAgentSecrets(ctx context.Context, in *UpdateAgentSecretsRequest) (*UpdateAgentSecretsResponse, error) {
	out := new(UpdateAgentSecretsResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[6], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *cloudAgentProtobufClient) RollbackAgent(ctx context.Context, in *RollbackAgentRequest) (*RollbackAgentResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "CloudAgent")
	ctx = ctxsetters.WithMethodName(ctx, "RollbackAgent")
	caller := c.callRollbackAgent
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *RollbackAgentRequest) (*RollbackAgentResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*RollbackAgentRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*RollbackAgentRequest) when calling interceptor")
					}
					return c.callRollbackAgent(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*RollbackAgentResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*RollbackAgentResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *cloudAgentProtobufClient) callRollbackAgent(ctx context.Context, in *RollbackAgentRequest) (*RollbackAgentResponse, error) {
	out := new(RollbackAgentResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[7], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *cloudAgentProtobufClient) DeleteAgent(ctx context.Context, in *DeleteAgentRequest) (*DeleteAgentResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "CloudAgent")
	ctx = ctxsetters.WithMethodName(ctx, "DeleteAgent")
	caller := c.callDeleteAgent
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *DeleteAgentRequest) (*DeleteAgentResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*DeleteAgentRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*DeleteAgentRequest) when calling interceptor")
					}
					return c.callDeleteAgent(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*DeleteAgentResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*DeleteAgentResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *cloudAgentProtobufClient) callDeleteAgent(ctx context.Context, in *DeleteAgentRequest) (*DeleteAgentResponse, error) {
	out := new(DeleteAgentResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[8], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *cloudAgentProtobufClient) GetClientSettings(ctx context.Context, in *ClientSettingsRequest) (*ClientSettingsResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "CloudAgent")
	ctx = ctxsetters.WithMethodName(ctx, "GetClientSettings")
	caller := c.callGetClientSettings
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ClientSettingsRequest) (*ClientSettingsResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ClientSettingsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ClientSettingsRequest) when calling interceptor")
					}
					return c.callGetClientSettings(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ClientSettingsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ClientSettingsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *cloudAgentProtobufClient) callGetClientSettings(ctx context.Context, in *ClientSettingsRequest) (*ClientSettingsResponse, error) {
	out := new(ClientSettingsResponse)
	ctx, err := doProtobufRequest(ctx, c.client, c.opts.Hooks, c.urls[9], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

// ======================
// CloudAgent JSON Client
// ======================

type cloudAgentJSONClient struct {
	client      HTTPClient
	urls        [10]string
	interceptor twirp.Interceptor
	opts        twirp.ClientOptions
}

// NewCloudAgentJSONClient creates a JSON client that implements the CloudAgent interface.
// It communicates using JSON and can be configured with a custom HTTPClient.
func NewCloudAgentJSONClient(baseURL string, client HTTPClient, opts ...twirp.ClientOption) CloudAgent {
	if c, ok := client.(*http.Client); ok {
		client = withoutRedirects(c)
	}

	clientOpts := twirp.ClientOptions{}
	for _, o := range opts {
		o(&clientOpts)
	}

	// Using ReadOpt allows backwards and forwards compatibility with new options in the future
	literalURLs := false
	_ = clientOpts.ReadOpt("literalURLs", &literalURLs)
	var pathPrefix string
	if ok := clientOpts.ReadOpt("pathPrefix", &pathPrefix); !ok {
		pathPrefix = "/twirp" // default prefix
	}

	// Build method URLs: <baseURL>[<prefix>]/<package>.<Service>/<Method>
	serviceURL := sanitizeBaseURL(baseURL)
	serviceURL += baseServicePath(pathPrefix, "livekit", "CloudAgent")
	urls := [10]string{
		serviceURL + "CreateAgent",
		serviceURL + "ListAgents",
		serviceURL + "ListAgentVersions",
		serviceURL + "ListAgentSecrets",
		serviceURL + "UpdateAgent",
		serviceURL + "DeployAgent",
		serviceURL + "UpdateAgentSecrets",
		serviceURL + "RollbackAgent",
		serviceURL + "DeleteAgent",
		serviceURL + "GetClientSettings",
	}

	return &cloudAgentJSONClient{
		client:      client,
		urls:        urls,
		interceptor: twirp.ChainInterceptors(clientOpts.Interceptors...),
		opts:        clientOpts,
	}
}

func (c *cloudAgentJSONClient) CreateAgent(ctx context.Context, in *CreateAgentRequest) (*CreateAgentResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "CloudAgent")
	ctx = ctxsetters.WithMethodName(ctx, "CreateAgent")
	caller := c.callCreateAgent
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *CreateAgentRequest) (*CreateAgentResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateAgentRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateAgentRequest) when calling interceptor")
					}
					return c.callCreateAgent(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*CreateAgentResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*CreateAgentResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *cloudAgentJSONClient) callCreateAgent(ctx context.Context, in *CreateAgentRequest) (*CreateAgentResponse, error) {
	out := new(CreateAgentResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[0], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *cloudAgentJSONClient) ListAgents(ctx context.Context, in *ListAgentsRequest) (*ListAgentsResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "CloudAgent")
	ctx = ctxsetters.WithMethodName(ctx, "ListAgents")
	caller := c.callListAgents
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ListAgentsRequest) (*ListAgentsResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListAgentsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListAgentsRequest) when calling interceptor")
					}
					return c.callListAgents(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListAgentsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListAgentsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *cloudAgentJSONClient) callListAgents(ctx context.Context, in *ListAgentsRequest) (*ListAgentsResponse, error) {
	out := new(ListAgentsResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[1], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *cloudAgentJSONClient) ListAgentVersions(ctx context.Context, in *ListAgentVersionsRequest) (*ListAgentVersionsResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "CloudAgent")
	ctx = ctxsetters.WithMethodName(ctx, "ListAgentVersions")
	caller := c.callListAgentVersions
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ListAgentVersionsRequest) (*ListAgentVersionsResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListAgentVersionsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListAgentVersionsRequest) when calling interceptor")
					}
					return c.callListAgentVersions(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListAgentVersionsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListAgentVersionsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *cloudAgentJSONClient) callListAgentVersions(ctx context.Context, in *ListAgentVersionsRequest) (*ListAgentVersionsResponse, error) {
	out := new(ListAgentVersionsResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[2], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *cloudAgentJSONClient) ListAgentSecrets(ctx context.Context, in *ListAgentSecretsRequest) (*ListAgentSecretsResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "CloudAgent")
	ctx = ctxsetters.WithMethodName(ctx, "ListAgentSecrets")
	caller := c.callListAgentSecrets
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ListAgentSecretsRequest) (*ListAgentSecretsResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListAgentSecretsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListAgentSecretsRequest) when calling interceptor")
					}
					return c.callListAgentSecrets(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListAgentSecretsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListAgentSecretsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *cloudAgentJSONClient) callListAgentSecrets(ctx context.Context, in *ListAgentSecretsRequest) (*ListAgentSecretsResponse, error) {
	out := new(ListAgentSecretsResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[3], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *cloudAgentJSONClient) UpdateAgent(ctx context.Context, in *UpdateAgentRequest) (*UpdateAgentResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "CloudAgent")
	ctx = ctxsetters.WithMethodName(ctx, "UpdateAgent")
	caller := c.callUpdateAgent
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *UpdateAgentRequest) (*UpdateAgentResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateAgentRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateAgentRequest) when calling interceptor")
					}
					return c.callUpdateAgent(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*UpdateAgentResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*UpdateAgentResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *cloudAgentJSONClient) callUpdateAgent(ctx context.Context, in *UpdateAgentRequest) (*UpdateAgentResponse, error) {
	out := new(UpdateAgentResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[4], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *cloudAgentJSONClient) DeployAgent(ctx context.Context, in *DeployAgentRequest) (*DeployAgentResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "CloudAgent")
	ctx = ctxsetters.WithMethodName(ctx, "DeployAgent")
	caller := c.callDeployAgent
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *DeployAgentRequest) (*DeployAgentResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*DeployAgentRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*DeployAgentRequest) when calling interceptor")
					}
					return c.callDeployAgent(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*DeployAgentResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*DeployAgentResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *cloudAgentJSONClient) callDeployAgent(ctx context.Context, in *DeployAgentRequest) (*DeployAgentResponse, error) {
	out := new(DeployAgentResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[5], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *cloudAgentJSONClient) UpdateAgentSecrets(ctx context.Context, in *UpdateAgentSecretsRequest) (*UpdateAgentSecretsResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "CloudAgent")
	ctx = ctxsetters.WithMethodName(ctx, "UpdateAgentSecrets")
	caller := c.callUpdateAgentSecrets
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *UpdateAgentSecretsRequest) (*UpdateAgentSecretsResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateAgentSecretsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateAgentSecretsRequest) when calling interceptor")
					}
					return c.callUpdateAgentSecrets(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*UpdateAgentSecretsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*UpdateAgentSecretsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *cloudAgentJSONClient) callUpdateAgentSecrets(ctx context.Context, in *UpdateAgentSecretsRequest) (*UpdateAgentSecretsResponse, error) {
	out := new(UpdateAgentSecretsResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[6], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *cloudAgentJSONClient) RollbackAgent(ctx context.Context, in *RollbackAgentRequest) (*RollbackAgentResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "CloudAgent")
	ctx = ctxsetters.WithMethodName(ctx, "RollbackAgent")
	caller := c.callRollbackAgent
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *RollbackAgentRequest) (*RollbackAgentResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*RollbackAgentRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*RollbackAgentRequest) when calling interceptor")
					}
					return c.callRollbackAgent(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*RollbackAgentResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*RollbackAgentResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *cloudAgentJSONClient) callRollbackAgent(ctx context.Context, in *RollbackAgentRequest) (*RollbackAgentResponse, error) {
	out := new(RollbackAgentResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[7], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *cloudAgentJSONClient) DeleteAgent(ctx context.Context, in *DeleteAgentRequest) (*DeleteAgentResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "CloudAgent")
	ctx = ctxsetters.WithMethodName(ctx, "DeleteAgent")
	caller := c.callDeleteAgent
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *DeleteAgentRequest) (*DeleteAgentResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*DeleteAgentRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*DeleteAgentRequest) when calling interceptor")
					}
					return c.callDeleteAgent(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*DeleteAgentResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*DeleteAgentResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *cloudAgentJSONClient) callDeleteAgent(ctx context.Context, in *DeleteAgentRequest) (*DeleteAgentResponse, error) {
	out := new(DeleteAgentResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[8], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

func (c *cloudAgentJSONClient) GetClientSettings(ctx context.Context, in *ClientSettingsRequest) (*ClientSettingsResponse, error) {
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "CloudAgent")
	ctx = ctxsetters.WithMethodName(ctx, "GetClientSettings")
	caller := c.callGetClientSettings
	if c.interceptor != nil {
		caller = func(ctx context.Context, req *ClientSettingsRequest) (*ClientSettingsResponse, error) {
			resp, err := c.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ClientSettingsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ClientSettingsRequest) when calling interceptor")
					}
					return c.callGetClientSettings(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ClientSettingsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ClientSettingsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}
	return caller(ctx, in)
}

func (c *cloudAgentJSONClient) callGetClientSettings(ctx context.Context, in *ClientSettingsRequest) (*ClientSettingsResponse, error) {
	out := new(ClientSettingsResponse)
	ctx, err := doJSONRequest(ctx, c.client, c.opts.Hooks, c.urls[9], in, out)
	if err != nil {
		twerr, ok := err.(twirp.Error)
		if !ok {
			twerr = twirp.InternalErrorWith(err)
		}
		callClientError(ctx, c.opts.Hooks, twerr)
		return nil, err
	}

	callClientResponseReceived(ctx, c.opts.Hooks)

	return out, nil
}

// =========================
// CloudAgent Server Handler
// =========================

type cloudAgentServer struct {
	CloudAgent
	interceptor      twirp.Interceptor
	hooks            *twirp.ServerHooks
	pathPrefix       string // prefix for routing
	jsonSkipDefaults bool   // do not include unpopulated fields (default values) in the response
	jsonCamelCase    bool   // JSON fields are serialized as lowerCamelCase rather than keeping the original proto names
}

// NewCloudAgentServer builds a TwirpServer that can be used as an http.Handler to handle
// HTTP requests that are routed to the right method in the provided svc implementation.
// The opts are twirp.ServerOption modifiers, for example twirp.WithServerHooks(hooks).
func NewCloudAgentServer(svc CloudAgent, opts ...interface{}) TwirpServer {
	serverOpts := newServerOpts(opts)

	// Using ReadOpt allows backwards and forwards compatibility with new options in the future
	jsonSkipDefaults := false
	_ = serverOpts.ReadOpt("jsonSkipDefaults", &jsonSkipDefaults)
	jsonCamelCase := false
	_ = serverOpts.ReadOpt("jsonCamelCase", &jsonCamelCase)
	var pathPrefix string
	if ok := serverOpts.ReadOpt("pathPrefix", &pathPrefix); !ok {
		pathPrefix = "/twirp" // default prefix
	}

	return &cloudAgentServer{
		CloudAgent:       svc,
		hooks:            serverOpts.Hooks,
		interceptor:      twirp.ChainInterceptors(serverOpts.Interceptors...),
		pathPrefix:       pathPrefix,
		jsonSkipDefaults: jsonSkipDefaults,
		jsonCamelCase:    jsonCamelCase,
	}
}

// writeError writes an HTTP response with a valid Twirp error format, and triggers hooks.
// If err is not a twirp.Error, it will get wrapped with twirp.InternalErrorWith(err)
func (s *cloudAgentServer) writeError(ctx context.Context, resp http.ResponseWriter, err error) {
	writeError(ctx, resp, err, s.hooks)
}

// handleRequestBodyError is used to handle error when the twirp server cannot read request
func (s *cloudAgentServer) handleRequestBodyError(ctx context.Context, resp http.ResponseWriter, msg string, err error) {
	if context.Canceled == ctx.Err() {
		s.writeError(ctx, resp, twirp.NewError(twirp.Canceled, "failed to read request: context canceled"))
		return
	}
	if context.DeadlineExceeded == ctx.Err() {
		s.writeError(ctx, resp, twirp.NewError(twirp.DeadlineExceeded, "failed to read request: deadline exceeded"))
		return
	}
	s.writeError(ctx, resp, twirp.WrapError(malformedRequestError(msg), err))
}

// CloudAgentPathPrefix is a convenience constant that may identify URL paths.
// Should be used with caution, it only matches routes generated by Twirp Go clients,
// with the default "/twirp" prefix and default CamelCase service and method names.
// More info: https://twitchtv.github.io/twirp/docs/routing.html
const CloudAgentPathPrefix = "/twirp/livekit.CloudAgent/"

func (s *cloudAgentServer) ServeHTTP(resp http.ResponseWriter, req *http.Request) {
	ctx := req.Context()
	ctx = ctxsetters.WithPackageName(ctx, "livekit")
	ctx = ctxsetters.WithServiceName(ctx, "CloudAgent")
	ctx = ctxsetters.WithResponseWriter(ctx, resp)

	var err error
	ctx, err = callRequestReceived(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	if req.Method != "POST" {
		msg := fmt.Sprintf("unsupported method %q (only POST is allowed)", req.Method)
		s.writeError(ctx, resp, badRouteError(msg, req.Method, req.URL.Path))
		return
	}

	// Verify path format: [<prefix>]/<package>.<Service>/<Method>
	prefix, pkgService, method := parseTwirpPath(req.URL.Path)
	if pkgService != "livekit.CloudAgent" {
		msg := fmt.Sprintf("no handler for path %q", req.URL.Path)
		s.writeError(ctx, resp, badRouteError(msg, req.Method, req.URL.Path))
		return
	}
	if prefix != s.pathPrefix {
		msg := fmt.Sprintf("invalid path prefix %q, expected %q, on path %q", prefix, s.pathPrefix, req.URL.Path)
		s.writeError(ctx, resp, badRouteError(msg, req.Method, req.URL.Path))
		return
	}

	switch method {
	case "CreateAgent":
		s.serveCreateAgent(ctx, resp, req)
		return
	case "ListAgents":
		s.serveListAgents(ctx, resp, req)
		return
	case "ListAgentVersions":
		s.serveListAgentVersions(ctx, resp, req)
		return
	case "ListAgentSecrets":
		s.serveListAgentSecrets(ctx, resp, req)
		return
	case "UpdateAgent":
		s.serveUpdateAgent(ctx, resp, req)
		return
	case "DeployAgent":
		s.serveDeployAgent(ctx, resp, req)
		return
	case "UpdateAgentSecrets":
		s.serveUpdateAgentSecrets(ctx, resp, req)
		return
	case "RollbackAgent":
		s.serveRollbackAgent(ctx, resp, req)
		return
	case "DeleteAgent":
		s.serveDeleteAgent(ctx, resp, req)
		return
	case "GetClientSettings":
		s.serveGetClientSettings(ctx, resp, req)
		return
	default:
		msg := fmt.Sprintf("no handler for path %q", req.URL.Path)
		s.writeError(ctx, resp, badRouteError(msg, req.Method, req.URL.Path))
		return
	}
}

func (s *cloudAgentServer) serveCreateAgent(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveCreateAgentJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveCreateAgentProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *cloudAgentServer) serveCreateAgentJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "CreateAgent")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(CreateAgentRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.CloudAgent.CreateAgent
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *CreateAgentRequest) (*CreateAgentResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateAgentRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateAgentRequest) when calling interceptor")
					}
					return s.CloudAgent.CreateAgent(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*CreateAgentResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*CreateAgentResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *CreateAgentResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *CreateAgentResponse and nil error while calling CreateAgent. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *cloudAgentServer) serveCreateAgentProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "CreateAgent")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(CreateAgentRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.CloudAgent.CreateAgent
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *CreateAgentRequest) (*CreateAgentResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*CreateAgentRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*CreateAgentRequest) when calling interceptor")
					}
					return s.CloudAgent.CreateAgent(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*CreateAgentResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*CreateAgentResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *CreateAgentResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *CreateAgentResponse and nil error while calling CreateAgent. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *cloudAgentServer) serveListAgents(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveListAgentsJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveListAgentsProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *cloudAgentServer) serveListAgentsJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "ListAgents")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(ListAgentsRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.CloudAgent.ListAgents
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ListAgentsRequest) (*ListAgentsResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListAgentsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListAgentsRequest) when calling interceptor")
					}
					return s.CloudAgent.ListAgents(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListAgentsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListAgentsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ListAgentsResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ListAgentsResponse and nil error while calling ListAgents. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *cloudAgentServer) serveListAgentsProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "ListAgents")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(ListAgentsRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.CloudAgent.ListAgents
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ListAgentsRequest) (*ListAgentsResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListAgentsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListAgentsRequest) when calling interceptor")
					}
					return s.CloudAgent.ListAgents(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListAgentsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListAgentsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ListAgentsResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ListAgentsResponse and nil error while calling ListAgents. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *cloudAgentServer) serveListAgentVersions(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveListAgentVersionsJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveListAgentVersionsProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *cloudAgentServer) serveListAgentVersionsJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "ListAgentVersions")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(ListAgentVersionsRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.CloudAgent.ListAgentVersions
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ListAgentVersionsRequest) (*ListAgentVersionsResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListAgentVersionsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListAgentVersionsRequest) when calling interceptor")
					}
					return s.CloudAgent.ListAgentVersions(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListAgentVersionsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListAgentVersionsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ListAgentVersionsResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ListAgentVersionsResponse and nil error while calling ListAgentVersions. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *cloudAgentServer) serveListAgentVersionsProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "ListAgentVersions")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(ListAgentVersionsRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.CloudAgent.ListAgentVersions
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ListAgentVersionsRequest) (*ListAgentVersionsResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListAgentVersionsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListAgentVersionsRequest) when calling interceptor")
					}
					return s.CloudAgent.ListAgentVersions(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListAgentVersionsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListAgentVersionsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ListAgentVersionsResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ListAgentVersionsResponse and nil error while calling ListAgentVersions. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *cloudAgentServer) serveListAgentSecrets(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveListAgentSecretsJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveListAgentSecretsProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *cloudAgentServer) serveListAgentSecretsJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "ListAgentSecrets")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(ListAgentSecretsRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.CloudAgent.ListAgentSecrets
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ListAgentSecretsRequest) (*ListAgentSecretsResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListAgentSecretsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListAgentSecretsRequest) when calling interceptor")
					}
					return s.CloudAgent.ListAgentSecrets(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListAgentSecretsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListAgentSecretsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ListAgentSecretsResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ListAgentSecretsResponse and nil error while calling ListAgentSecrets. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *cloudAgentServer) serveListAgentSecretsProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "ListAgentSecrets")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(ListAgentSecretsRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.CloudAgent.ListAgentSecrets
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ListAgentSecretsRequest) (*ListAgentSecretsResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ListAgentSecretsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ListAgentSecretsRequest) when calling interceptor")
					}
					return s.CloudAgent.ListAgentSecrets(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ListAgentSecretsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ListAgentSecretsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ListAgentSecretsResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ListAgentSecretsResponse and nil error while calling ListAgentSecrets. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *cloudAgentServer) serveUpdateAgent(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveUpdateAgentJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveUpdateAgentProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *cloudAgentServer) serveUpdateAgentJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "UpdateAgent")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(UpdateAgentRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.CloudAgent.UpdateAgent
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *UpdateAgentRequest) (*UpdateAgentResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateAgentRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateAgentRequest) when calling interceptor")
					}
					return s.CloudAgent.UpdateAgent(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*UpdateAgentResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*UpdateAgentResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *UpdateAgentResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *UpdateAgentResponse and nil error while calling UpdateAgent. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *cloudAgentServer) serveUpdateAgentProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "UpdateAgent")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(UpdateAgentRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.CloudAgent.UpdateAgent
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *UpdateAgentRequest) (*UpdateAgentResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateAgentRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateAgentRequest) when calling interceptor")
					}
					return s.CloudAgent.UpdateAgent(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*UpdateAgentResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*UpdateAgentResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *UpdateAgentResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *UpdateAgentResponse and nil error while calling UpdateAgent. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *cloudAgentServer) serveDeployAgent(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveDeployAgentJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveDeployAgentProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *cloudAgentServer) serveDeployAgentJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "DeployAgent")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(DeployAgentRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.CloudAgent.DeployAgent
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *DeployAgentRequest) (*DeployAgentResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*DeployAgentRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*DeployAgentRequest) when calling interceptor")
					}
					return s.CloudAgent.DeployAgent(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*DeployAgentResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*DeployAgentResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *DeployAgentResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *DeployAgentResponse and nil error while calling DeployAgent. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *cloudAgentServer) serveDeployAgentProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "DeployAgent")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(DeployAgentRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.CloudAgent.DeployAgent
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *DeployAgentRequest) (*DeployAgentResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*DeployAgentRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*DeployAgentRequest) when calling interceptor")
					}
					return s.CloudAgent.DeployAgent(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*DeployAgentResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*DeployAgentResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *DeployAgentResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *DeployAgentResponse and nil error while calling DeployAgent. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *cloudAgentServer) serveUpdateAgentSecrets(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveUpdateAgentSecretsJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveUpdateAgentSecretsProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *cloudAgentServer) serveUpdateAgentSecretsJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "UpdateAgentSecrets")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(UpdateAgentSecretsRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.CloudAgent.UpdateAgentSecrets
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *UpdateAgentSecretsRequest) (*UpdateAgentSecretsResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateAgentSecretsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateAgentSecretsRequest) when calling interceptor")
					}
					return s.CloudAgent.UpdateAgentSecrets(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*UpdateAgentSecretsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*UpdateAgentSecretsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *UpdateAgentSecretsResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *UpdateAgentSecretsResponse and nil error while calling UpdateAgentSecrets. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *cloudAgentServer) serveUpdateAgentSecretsProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "UpdateAgentSecrets")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(UpdateAgentSecretsRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.CloudAgent.UpdateAgentSecrets
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *UpdateAgentSecretsRequest) (*UpdateAgentSecretsResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*UpdateAgentSecretsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*UpdateAgentSecretsRequest) when calling interceptor")
					}
					return s.CloudAgent.UpdateAgentSecrets(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*UpdateAgentSecretsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*UpdateAgentSecretsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *UpdateAgentSecretsResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *UpdateAgentSecretsResponse and nil error while calling UpdateAgentSecrets. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *cloudAgentServer) serveRollbackAgent(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveRollbackAgentJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveRollbackAgentProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *cloudAgentServer) serveRollbackAgentJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "RollbackAgent")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(RollbackAgentRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.CloudAgent.RollbackAgent
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *RollbackAgentRequest) (*RollbackAgentResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*RollbackAgentRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*RollbackAgentRequest) when calling interceptor")
					}
					return s.CloudAgent.RollbackAgent(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*RollbackAgentResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*RollbackAgentResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *RollbackAgentResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *RollbackAgentResponse and nil error while calling RollbackAgent. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *cloudAgentServer) serveRollbackAgentProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "RollbackAgent")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(RollbackAgentRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.CloudAgent.RollbackAgent
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *RollbackAgentRequest) (*RollbackAgentResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*RollbackAgentRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*RollbackAgentRequest) when calling interceptor")
					}
					return s.CloudAgent.RollbackAgent(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*RollbackAgentResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*RollbackAgentResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *RollbackAgentResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *RollbackAgentResponse and nil error while calling RollbackAgent. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *cloudAgentServer) serveDeleteAgent(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveDeleteAgentJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveDeleteAgentProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *cloudAgentServer) serveDeleteAgentJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "DeleteAgent")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(DeleteAgentRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.CloudAgent.DeleteAgent
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *DeleteAgentRequest) (*DeleteAgentResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*DeleteAgentRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*DeleteAgentRequest) when calling interceptor")
					}
					return s.CloudAgent.DeleteAgent(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*DeleteAgentResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*DeleteAgentResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *DeleteAgentResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *DeleteAgentResponse and nil error while calling DeleteAgent. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *cloudAgentServer) serveDeleteAgentProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "DeleteAgent")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(DeleteAgentRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.CloudAgent.DeleteAgent
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *DeleteAgentRequest) (*DeleteAgentResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*DeleteAgentRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*DeleteAgentRequest) when calling interceptor")
					}
					return s.CloudAgent.DeleteAgent(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*DeleteAgentResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*DeleteAgentResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *DeleteAgentResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *DeleteAgentResponse and nil error while calling DeleteAgent. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *cloudAgentServer) serveGetClientSettings(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	header := req.Header.Get("Content-Type")
	i := strings.Index(header, ";")
	if i == -1 {
		i = len(header)
	}
	switch strings.TrimSpace(strings.ToLower(header[:i])) {
	case "application/json":
		s.serveGetClientSettingsJSON(ctx, resp, req)
	case "application/protobuf":
		s.serveGetClientSettingsProtobuf(ctx, resp, req)
	default:
		msg := fmt.Sprintf("unexpected Content-Type: %q", req.Header.Get("Content-Type"))
		twerr := badRouteError(msg, req.Method, req.URL.Path)
		s.writeError(ctx, resp, twerr)
	}
}

func (s *cloudAgentServer) serveGetClientSettingsJSON(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "GetClientSettings")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	d := json.NewDecoder(req.Body)
	rawReqBody := json.RawMessage{}
	if err := d.Decode(&rawReqBody); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}
	reqContent := new(ClientSettingsRequest)
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	if err = unmarshaler.Unmarshal(rawReqBody, reqContent); err != nil {
		s.handleRequestBodyError(ctx, resp, "the json request could not be decoded", err)
		return
	}

	handler := s.CloudAgent.GetClientSettings
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ClientSettingsRequest) (*ClientSettingsResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ClientSettingsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ClientSettingsRequest) when calling interceptor")
					}
					return s.CloudAgent.GetClientSettings(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ClientSettingsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ClientSettingsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ClientSettingsResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ClientSettingsResponse and nil error while calling GetClientSettings. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	marshaler := &protojson.MarshalOptions{UseProtoNames: !s.jsonCamelCase, EmitUnpopulated: !s.jsonSkipDefaults}
	respBytes, err := marshaler.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal json response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/json")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)

	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *cloudAgentServer) serveGetClientSettingsProtobuf(ctx context.Context, resp http.ResponseWriter, req *http.Request) {
	var err error
	ctx = ctxsetters.WithMethodName(ctx, "GetClientSettings")
	ctx, err = callRequestRouted(ctx, s.hooks)
	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}

	buf, err := io.ReadAll(req.Body)
	if err != nil {
		s.handleRequestBodyError(ctx, resp, "failed to read request body", err)
		return
	}
	reqContent := new(ClientSettingsRequest)
	if err = proto.Unmarshal(buf, reqContent); err != nil {
		s.writeError(ctx, resp, malformedRequestError("the protobuf request could not be decoded"))
		return
	}

	handler := s.CloudAgent.GetClientSettings
	if s.interceptor != nil {
		handler = func(ctx context.Context, req *ClientSettingsRequest) (*ClientSettingsResponse, error) {
			resp, err := s.interceptor(
				func(ctx context.Context, req interface{}) (interface{}, error) {
					typedReq, ok := req.(*ClientSettingsRequest)
					if !ok {
						return nil, twirp.InternalError("failed type assertion req.(*ClientSettingsRequest) when calling interceptor")
					}
					return s.CloudAgent.GetClientSettings(ctx, typedReq)
				},
			)(ctx, req)
			if resp != nil {
				typedResp, ok := resp.(*ClientSettingsResponse)
				if !ok {
					return nil, twirp.InternalError("failed type assertion resp.(*ClientSettingsResponse) when calling interceptor")
				}
				return typedResp, err
			}
			return nil, err
		}
	}

	// Call service method
	var respContent *ClientSettingsResponse
	func() {
		defer ensurePanicResponses(ctx, resp, s.hooks)
		respContent, err = handler(ctx, reqContent)
	}()

	if err != nil {
		s.writeError(ctx, resp, err)
		return
	}
	if respContent == nil {
		s.writeError(ctx, resp, twirp.InternalError("received a nil *ClientSettingsResponse and nil error while calling GetClientSettings. nil responses are not supported"))
		return
	}

	ctx = callResponsePrepared(ctx, s.hooks)

	respBytes, err := proto.Marshal(respContent)
	if err != nil {
		s.writeError(ctx, resp, wrapInternal(err, "failed to marshal proto response"))
		return
	}

	ctx = ctxsetters.WithStatusCode(ctx, http.StatusOK)
	resp.Header().Set("Content-Type", "application/protobuf")
	resp.Header().Set("Content-Length", strconv.Itoa(len(respBytes)))
	resp.WriteHeader(http.StatusOK)
	if n, err := resp.Write(respBytes); err != nil {
		msg := fmt.Sprintf("failed to write response, %d of %d bytes written: %s", n, len(respBytes), err.Error())
		twerr := twirp.NewError(twirp.Unknown, msg)
		ctx = callError(ctx, s.hooks, twerr)
	}
	callResponseSent(ctx, s.hooks)
}

func (s *cloudAgentServer) ServiceDescriptor() ([]byte, int) {
	return twirpFileDescriptor5, 0
}

func (s *cloudAgentServer) ProtocGenTwirpVersion() string {
	return "v8.1.3"
}

// PathPrefix returns the base service path, in the form: "/<prefix>/<package>.<Service>/"
// that is everything in a Twirp route except for the <Method>. This can be used for routing,
// for example to identify the requests that are targeted to this service in a mux.
func (s *cloudAgentServer) PathPrefix() string {
	return baseServicePath(s.pathPrefix, "livekit", "CloudAgent")
}

var twirpFileDescriptor5 = []byte{
	// 1192 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x58, 0x4d, 0x6f, 0x1c, 0x45,
	0x13, 0xf6, 0xec, 0xf7, 0xd4, 0xae, 0xdf, 0x37, 0x6e, 0x7f, 0x8d, 0xc7, 0x71, 0xb2, 0x6e, 0x0b,
	0xc9, 0xe2, 0xb0, 0x16, 0xe6, 0x94, 0x70, 0xc1, 0x31, 0x10, 0xec, 0x38, 0x96, 0x35, 0x4e, 0x22,
	0x81, 0x90, 0x56, 0xe3, 0xd9, 0xce, 0x32, 0x64, 0xbe, 0x3c, 0xdd, 0x63, 0xc2, 0x01, 0x89, 0x2b,
	0x67, 0x7e, 0x02, 0x12, 0x07, 0x24, 0x7e, 0x08, 0xff, 0x81, 0x2b, 0x1c, 0xf8, 0x15, 0xa8, 0x7b,
	0x7a, 0x66, 0xbb, 0x77, 0xd7, 0xf6, 0xe2, 0x8d, 0xc4, 0xcd, 0xd5, 0x55, 0x5d, 0xfd, 0x3c, 0x4f,
	0x55, 0x57, 0xcf, 0x1a, 0x36, 0x02, 0xff, 0x8a, 0xbc, 0xf1, 0x59, 0xdf, 0x0b, 0xe2, 0x6c, 0xd0,
	0x77, 0x87, 0x24, 0x62, 0xbd, 0x24, 0x8d, 0x59, 0x8c, 0x9a, 0xd2, 0x65, 0x3f, 0x1c, 0xc6, 0xf1,
	0x30, 0x20, 0x7b, 0x62, 0xf9, 0x22, 0x7b, 0xbd, 0xc7, 0xfc, 0x90, 0x50, 0xe6, 0x86, 0x49, 0x1e,
	0x89, 0x7f, 0x33, 0xa0, 0x7d, 0xc0, 0x77, 0x9e, 0x13, 0x2f, 0x25, 0x0c, 0x21, 0xa8, 0x45, 0x6e,
	0x48, 0x2c, 0xa3, 0x6b, 0xec, 0x9a, 0x8e, 0xf8, 0x1b, 0xad, 0x40, 0xfd, 0xca, 0x0d, 0x32, 0x62,
	0x55, 0xba, 0xc6, 0x6e, 0xc7, 0xc9, 0x0d, 0xf4, 0x08, 0xc0, 0x4b, 0x89, 0xcb, 0xc8, 0xa0, 0xef,
	0x32, 0xab, 0xda, 0x35, 0x76, 0xdb, 0xfb, 0x76, 0x2f, 0x3f, 0xaf, 0x57, 0x9c, 0xd7, 0x7b, 0x51,
	0x9c, 0xe7, 0x98, 0x32, 0xfa, 0x80, 0xf1, 0xad, 0x59, 0x32, 0x28, 0xb6, 0xd6, 0x6e, 0xdf, 0x2a,
	0xa3, 0x0f, 0x18, 0xfe, 0xd3, 0x00, 0x74, 0x28, 0x12, 0x09, 0xd4, 0x0e, 0xb9, 0xcc, 0x08, 0x65,
	0x68, 0x1b, 0x40, 0xf0, 0xef, 0x8f, 0xc0, 0x3f, 0xa9, 0x58, 0x86, 0x63, 0x8a, 0xd5, 0x53, 0xce,
	0xa2, 0x07, 0x4d, 0x2a, 0x38, 0x52, 0xab, 0xd2, 0xad, 0xee, 0xb6, 0xf7, 0x57, 0x7a, 0x52, 0xa5,
	0x9e, 0x22, 0x80, 0x53, 0x04, 0xa1, 0x07, 0xd0, 0x4a, 0x49, 0x12, 0xf8, 0x9e, 0x4b, 0x05, 0xbb,
	0xba, 0x48, 0x58, 0xae, 0xa1, 0xf7, 0xa0, 0x13, 0xba, 0x6f, 0xfb, 0x65, 0x4c, 0xad, 0x8c, 0x69,
	0x87, 0xee, 0x5b, 0xa7, 0x08, 0xdb, 0x84, 0xa6, 0x97, 0x64, 0xfd, 0x94, 0x5c, 0x5a, 0xf5, 0x12,
	0x56, 0xc3, 0x4b, 0x32, 0x87, 0x5c, 0x22, 0x0b, 0x9a, 0x29, 0x19, 0xfa, 0x71, 0x44, 0xad, 0x46,
	0xb7, 0xba, 0x6b, 0x3a, 0x85, 0x89, 0x7f, 0x31, 0x60, 0x59, 0xe3, 0x49, 0x93, 0x38, 0xa2, 0x04,
	0x6d, 0x40, 0x2b, 0x27, 0xea, 0x0f, 0x64, 0x8d, 0x9a, 0xc2, 0x3e, 0x1a, 0xa0, 0x2d, 0x4d, 0x83,
	0x8a, 0x70, 0x2a, 0xfc, 0xd7, 0xa0, 0x41, 0x99, 0xcb, 0xb2, 0x9c, 0x8d, 0xe9, 0x48, 0x8b, 0x63,
	0xb8, 0x22, 0x29, 0xf5, 0xe3, 0x48, 0x50, 0x30, 0x9d, 0xc2, 0x44, 0x3b, 0xb0, 0x98, 0xa4, 0x84,
	0xfa, 0xc3, 0x88, 0x0c, 0xfa, 0x59, 0x1a, 0xe4, 0x04, 0x9c, 0x4e, 0xb9, 0xf8, 0x32, 0x0d, 0xf0,
	0x1f, 0x15, 0xf8, 0xbf, 0x80, 0xf8, 0x09, 0x49, 0x82, 0xf8, 0xbb, 0x90, 0x44, 0x8c, 0x1f, 0x95,
	0xf3, 0x90, 0x10, 0xa5, 0xa5, 0x81, 0xaf, 0xe8, 0xe0, 0xaf, 0x43, 0x67, 0x2b, 0x55, 0x10, 0x0a,
	0x2b, 0x15, 0xd8, 0x86, 0x4e, 0xe8, 0x47, 0xa3, 0x0a, 0xd4, 0x85, 0xbf, 0x1d, 0xfa, 0x91, 0xa3,
	0x86, 0xa8, 0x45, 0x6a, 0xc8, 0x10, 0xa5, 0x40, 0xeb, 0xa3, 0x02, 0x35, 0xf3, 0xa3, 0x65, 0x71,
	0xb8, 0x23, 0x4b, 0xfb, 0x5e, 0x92, 0x59, 0x2d, 0xe9, 0xc8, 0xd2, 0xc3, 0x24, 0x2b, 0x1c, 0x21,
	0x09, 0x2d, 0xb3, 0x74, 0x3c, 0x27, 0x21, 0x77, 0x84, 0x24, 0x14, 0xa9, 0x20, 0x77, 0x84, 0x24,
	0xe4, 0xa9, 0x36, 0xc1, 0xe4, 0x8e, 0xc0, 0x0f, 0x7d, 0x66, 0xb5, 0x85, 0xab, 0x15, 0x92, 0xf0,
	0x84, 0xdb, 0xdc, 0xc9, 0x01, 0xe4, 0xce, 0x4e, 0xee, 0xf4, 0x92, 0x4c, 0x38, 0xf1, 0x4f, 0x15,
	0x30, 0x85, 0xbc, 0x47, 0xd1, 0xeb, 0x78, 0x8e, 0xea, 0x2b, 0x55, 0xae, 0xea, 0x55, 0xfe, 0x14,
	0x96, 0xf2, 0x8d, 0x83, 0xb2, 0x80, 0x5c, 0x6a, 0x7e, 0x43, 0x2c, 0xfd, 0x86, 0x8c, 0x2a, 0xec,
	0xdc, 0x73, 0xf5, 0x05, 0xaa, 0x5e, 0xaf, 0xfa, 0x2c, 0xd7, 0xeb, 0x23, 0x68, 0xe7, 0x07, 0xe6,
	0x43, 0xa0, 0x71, 0xeb, 0x10, 0x80, 0x22, 0xfc, 0x80, 0xe1, 0xe7, 0xb0, 0x74, 0xe2, 0x53, 0x26,
	0x12, 0xd3, 0x62, 0x06, 0x6c, 0x4d, 0xce, 0x00, 0x55, 0x81, 0xeb, 0x9b, 0x0f, 0x7f, 0x0c, 0x48,
	0x4d, 0x27, 0xaf, 0xda, 0xfb, 0xd0, 0x10, 0x01, 0xd4, 0x32, 0x04, 0x21, 0xa4, 0x13, 0xe2, 0x05,
	0x71, 0x64, 0x04, 0xfe, 0x1e, 0x3a, 0x62, 0xf1, 0x95, 0x14, 0x55, 0x91, 0xdb, 0xd0, 0xe5, 0xb6,
	0x44, 0xf3, 0xa4, 0x24, 0x62, 0x02, 0x45, 0xcb, 0x29, 0xcc, 0x39, 0x06, 0x2a, 0x7e, 0x01, 0x56,
	0x49, 0x40, 0x42, 0x28, 0x65, 0xb9, 0x73, 0xcf, 0xe0, 0x53, 0xd8, 0x98, 0x92, 0x55, 0xaa, 0xf3,
	0x01, 0xb4, 0x24, 0xa5, 0x42, 0x9f, 0x55, 0x5d, 0x1f, 0xb9, 0xc3, 0x29, 0xc3, 0xf0, 0x0f, 0x15,
	0x40, 0x2f, 0xc5, 0x24, 0xd7, 0x66, 0xf7, 0x0d, 0x00, 0xb7, 0x27, 0x01, 0x8e, 0x8f, 0xf5, 0xff,
	0x74, 0x4c, 0xab, 0x5d, 0xdf, 0x9c, 0xa1, 0xeb, 0xf1, 0x11, 0x2c, 0x6b, 0x0a, 0x48, 0x31, 0x2d,
	0x68, 0xd2, 0xcc, 0xf3, 0x08, 0xa5, 0x42, 0x81, 0x96, 0x53, 0x98, 0xdc, 0x13, 0x12, 0x4a, 0xdd,
	0x61, 0x51, 0x9f, 0xc2, 0xc4, 0x7f, 0x19, 0x80, 0xf2, 0x0b, 0xf8, 0x0e, 0xd5, 0x54, 0xf8, 0x54,
	0xff, 0xed, 0x23, 0x59, 0x9b, 0x41, 0xfd, 0xfa, 0xad, 0xea, 0x37, 0xc6, 0xd5, 0xc7, 0x3f, 0x1a,
	0xb0, 0xac, 0x11, 0xbd, 0xbb, 0x68, 0x9a, 0x3a, 0x55, 0x5d, 0x9d, 0x89, 0xd7, 0xae, 0x36, 0xe5,
	0xb5, 0xfb, 0xd9, 0x80, 0x0d, 0xa5, 0x80, 0xb9, 0x1c, 0xf3, 0x5f, 0x35, 0x74, 0x1f, 0xcc, 0xf8,
	0x8a, 0xa4, 0xdf, 0xa6, 0x3e, 0x23, 0x02, 0x58, 0xcb, 0x19, 0x2d, 0xa8, 0x55, 0xa9, 0xcd, 0xd2,
	0x65, 0x67, 0x60, 0x4f, 0x03, 0x39, 0x47, 0xb3, 0x7d, 0x03, 0x2b, 0x4e, 0x1c, 0x04, 0x17, 0xae,
	0xf7, 0x66, 0xd6, 0x6e, 0xbb, 0xeb, 0x83, 0x84, 0x9f, 0xc1, 0xea, 0xd8, 0x59, 0x73, 0x00, 0x3f,
	0xe5, 0x97, 0x24, 0x20, 0xb3, 0x8f, 0x9c, 0x5b, 0x66, 0xe2, 0x11, 0xef, 0x45, 0x25, 0xdf, 0x1c,
	0xd0, 0xce, 0x61, 0xbd, 0x1c, 0xaf, 0xef, 0xaa, 0x91, 0xf0, 0xb1, 0xf2, 0x12, 0x8c, 0x17, 0x5e,
	0x69, 0x23, 0x63, 0x96, 0x36, 0x7a, 0x04, 0x8b, 0xe7, 0x84, 0x31, 0x3f, 0x1a, 0xd2, 0x33, 0x37,
	0x75, 0xc3, 0xdb, 0x7f, 0x1c, 0x98, 0xf2, 0xc7, 0x01, 0xfe, 0x1c, 0xd6, 0x0e, 0x03, 0x5f, 0xe4,
	0xcc, 0x13, 0x28, 0x20, 0x1a, 0x09, 0x4f, 0x56, 0x60, 0x58, 0x2b, 0x31, 0x68, 0x67, 0x39, 0x32,
	0x0a, 0xaf, 0xc3, 0xea, 0x78, 0x26, 0xa1, 0xd1, 0xfe, 0xef, 0x0d, 0x80, 0x43, 0xfe, 0xcb, 0x47,
	0x60, 0x47, 0xc7, 0xd0, 0x56, 0xbe, 0x97, 0xd1, 0x66, 0x99, 0x76, 0xf2, 0xd7, 0x82, 0x7d, 0x7f,
	0xba, 0x33, 0x47, 0x88, 0x17, 0xd0, 0x53, 0x80, 0xd1, 0xf7, 0x00, 0xb2, 0xcb, 0xe8, 0x89, 0x6f,
	0x0e, 0x7b, 0x73, 0xaa, 0xaf, 0x4c, 0xf4, 0x95, 0xf2, 0x9d, 0x52, 0xbc, 0xa0, 0x68, 0x7b, 0x72,
	0xcf, 0xd8, 0x9b, 0x6d, 0xe3, 0x9b, 0x42, 0xca, 0xec, 0x5f, 0xc0, 0xbd, 0xf1, 0x5a, 0xa3, 0xee,
	0xe4, 0x4e, 0xbd, 0xb7, 0xec, 0xed, 0x1b, 0x22, 0xca, 0xd4, 0xc7, 0xd0, 0x56, 0x26, 0x88, 0xa2,
	0xe6, 0xe4, 0xfb, 0xad, 0xa8, 0x39, 0xe5, 0x69, 0xcb, 0x73, 0x29, 0xe3, 0x5b, 0xc9, 0x35, 0xf9,
	0x7a, 0x29, 0xb9, 0xa6, 0x4c, 0x7c, 0xbc, 0x80, 0xfa, 0xda, 0x17, 0x44, 0x41, 0x1a, 0x4f, 0x43,
	0x30, 0x46, 0x7b, 0xe7, 0xc6, 0x98, 0xf2, 0x80, 0x33, 0x58, 0xd4, 0x86, 0x0f, 0xda, 0x2a, 0xf7,
	0x4d, 0x1b, 0x80, 0xf6, 0x83, 0xeb, 0xdc, 0x3a, 0xfd, 0x72, 0x62, 0x68, 0xf4, 0xc7, 0xe7, 0x92,
	0x46, 0x7f, 0x62, 0xc8, 0xe0, 0x05, 0xf4, 0x0a, 0x96, 0x9e, 0x12, 0xa6, 0xdf, 0x07, 0x34, 0x82,
	0x30, 0xf5, 0xa2, 0xd8, 0x0f, 0xaf, 0xf5, 0x17, 0x79, 0x9f, 0x7c, 0xf6, 0xe5, 0xce, 0xd0, 0x67,
	0x5f, 0x67, 0x17, 0x3d, 0x2f, 0x0e, 0xf7, 0x64, 0x78, 0xfe, 0x4f, 0x03, 0x2f, 0x0e, 0x8a, 0x85,
	0x5f, 0x2b, 0x8b, 0x27, 0xfe, 0x15, 0x79, 0xe6, 0xb3, 0xde, 0x19, 0x77, 0xfd, 0x5d, 0xf9, 0x9f,
	0xb4, 0x1f, 0x3f, 0x16, 0x0b, 0x17, 0x0d, 0xb1, 0xe5, 0xc3, 0x7f, 0x02, 0x00, 0x00, 0xff, 0xff,
	0x57, 0x85, 0x32, 0x0c, 0x9b, 0x10, 0x00, 0x00,
}
