// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: livekit_analytics.proto

package livekit

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type StreamType int32

const (
	StreamType_UPSTREAM   StreamType = 0
	StreamType_DOWNSTREAM StreamType = 1
)

// Enum value maps for StreamType.
var (
	StreamType_name = map[int32]string{
		0: "UPSTREAM",
		1: "DOWNSTREAM",
	}
	StreamType_value = map[string]int32{
		"UPSTREAM":   0,
		"DOWNSTREAM": 1,
	}
)

func (x StreamType) Enum() *StreamType {
	p := new(StreamType)
	*p = x
	return p
}

func (x StreamType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StreamType) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_analytics_proto_enumTypes[0].Descriptor()
}

func (StreamType) Type() protoreflect.EnumType {
	return &file_livekit_analytics_proto_enumTypes[0]
}

func (x StreamType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StreamType.Descriptor instead.
func (StreamType) EnumDescriptor() ([]byte, []int) {
	return file_livekit_analytics_proto_rawDescGZIP(), []int{0}
}

type AnalyticsEventType int32

const (
	AnalyticsEventType_ROOM_CREATED                       AnalyticsEventType = 0
	AnalyticsEventType_ROOM_ENDED                         AnalyticsEventType = 1
	AnalyticsEventType_PARTICIPANT_JOINED                 AnalyticsEventType = 2
	AnalyticsEventType_PARTICIPANT_LEFT                   AnalyticsEventType = 3
	AnalyticsEventType_TRACK_PUBLISHED                    AnalyticsEventType = 4
	AnalyticsEventType_TRACK_PUBLISH_REQUESTED            AnalyticsEventType = 20
	AnalyticsEventType_TRACK_UNPUBLISHED                  AnalyticsEventType = 5
	AnalyticsEventType_TRACK_SUBSCRIBED                   AnalyticsEventType = 6
	AnalyticsEventType_TRACK_SUBSCRIBE_REQUESTED          AnalyticsEventType = 21
	AnalyticsEventType_TRACK_SUBSCRIBE_FAILED             AnalyticsEventType = 25
	AnalyticsEventType_TRACK_UNSUBSCRIBED                 AnalyticsEventType = 7
	AnalyticsEventType_TRACK_PUBLISHED_UPDATE             AnalyticsEventType = 10
	AnalyticsEventType_TRACK_MUTED                        AnalyticsEventType = 23
	AnalyticsEventType_TRACK_UNMUTED                      AnalyticsEventType = 24
	AnalyticsEventType_TRACK_PUBLISH_STATS                AnalyticsEventType = 26
	AnalyticsEventType_TRACK_SUBSCRIBE_STATS              AnalyticsEventType = 27
	AnalyticsEventType_PARTICIPANT_ACTIVE                 AnalyticsEventType = 11
	AnalyticsEventType_PARTICIPANT_RESUMED                AnalyticsEventType = 22
	AnalyticsEventType_EGRESS_STARTED                     AnalyticsEventType = 12
	AnalyticsEventType_EGRESS_ENDED                       AnalyticsEventType = 13
	AnalyticsEventType_EGRESS_UPDATED                     AnalyticsEventType = 28
	AnalyticsEventType_TRACK_MAX_SUBSCRIBED_VIDEO_QUALITY AnalyticsEventType = 14
	AnalyticsEventType_RECONNECTED                        AnalyticsEventType = 15
	AnalyticsEventType_INGRESS_CREATED                    AnalyticsEventType = 18
	AnalyticsEventType_INGRESS_DELETED                    AnalyticsEventType = 19
	AnalyticsEventType_INGRESS_STARTED                    AnalyticsEventType = 16
	AnalyticsEventType_INGRESS_ENDED                      AnalyticsEventType = 17
	AnalyticsEventType_INGRESS_UPDATED                    AnalyticsEventType = 29
	AnalyticsEventType_SIP_INBOUND_TRUNK_CREATED          AnalyticsEventType = 30
	AnalyticsEventType_SIP_INBOUND_TRUNK_DELETED          AnalyticsEventType = 31
	AnalyticsEventType_SIP_OUTBOUND_TRUNK_CREATED         AnalyticsEventType = 32
	AnalyticsEventType_SIP_OUTBOUND_TRUNK_DELETED         AnalyticsEventType = 33
	AnalyticsEventType_SIP_DISPATCH_RULE_CREATED          AnalyticsEventType = 34
	AnalyticsEventType_SIP_DISPATCH_RULE_DELETED          AnalyticsEventType = 35
	AnalyticsEventType_SIP_PARTICIPANT_CREATED            AnalyticsEventType = 36
	AnalyticsEventType_SIP_CALL_INCOMING                  AnalyticsEventType = 37
	AnalyticsEventType_SIP_CALL_STARTED                   AnalyticsEventType = 38
	AnalyticsEventType_SIP_CALL_ENDED                     AnalyticsEventType = 39
	AnalyticsEventType_SIP_TRANSFER_REQUESTED             AnalyticsEventType = 43
	AnalyticsEventType_SIP_TRANSFER_COMPLETE              AnalyticsEventType = 44
	AnalyticsEventType_REPORT                             AnalyticsEventType = 40
	AnalyticsEventType_API_CALL                           AnalyticsEventType = 41
	AnalyticsEventType_WEBHOOK                            AnalyticsEventType = 42
)

// Enum value maps for AnalyticsEventType.
var (
	AnalyticsEventType_name = map[int32]string{
		0:  "ROOM_CREATED",
		1:  "ROOM_ENDED",
		2:  "PARTICIPANT_JOINED",
		3:  "PARTICIPANT_LEFT",
		4:  "TRACK_PUBLISHED",
		20: "TRACK_PUBLISH_REQUESTED",
		5:  "TRACK_UNPUBLISHED",
		6:  "TRACK_SUBSCRIBED",
		21: "TRACK_SUBSCRIBE_REQUESTED",
		25: "TRACK_SUBSCRIBE_FAILED",
		7:  "TRACK_UNSUBSCRIBED",
		10: "TRACK_PUBLISHED_UPDATE",
		23: "TRACK_MUTED",
		24: "TRACK_UNMUTED",
		26: "TRACK_PUBLISH_STATS",
		27: "TRACK_SUBSCRIBE_STATS",
		11: "PARTICIPANT_ACTIVE",
		22: "PARTICIPANT_RESUMED",
		12: "EGRESS_STARTED",
		13: "EGRESS_ENDED",
		28: "EGRESS_UPDATED",
		14: "TRACK_MAX_SUBSCRIBED_VIDEO_QUALITY",
		15: "RECONNECTED",
		18: "INGRESS_CREATED",
		19: "INGRESS_DELETED",
		16: "INGRESS_STARTED",
		17: "INGRESS_ENDED",
		29: "INGRESS_UPDATED",
		30: "SIP_INBOUND_TRUNK_CREATED",
		31: "SIP_INBOUND_TRUNK_DELETED",
		32: "SIP_OUTBOUND_TRUNK_CREATED",
		33: "SIP_OUTBOUND_TRUNK_DELETED",
		34: "SIP_DISPATCH_RULE_CREATED",
		35: "SIP_DISPATCH_RULE_DELETED",
		36: "SIP_PARTICIPANT_CREATED",
		37: "SIP_CALL_INCOMING",
		38: "SIP_CALL_STARTED",
		39: "SIP_CALL_ENDED",
		43: "SIP_TRANSFER_REQUESTED",
		44: "SIP_TRANSFER_COMPLETE",
		40: "REPORT",
		41: "API_CALL",
		42: "WEBHOOK",
	}
	AnalyticsEventType_value = map[string]int32{
		"ROOM_CREATED":                       0,
		"ROOM_ENDED":                         1,
		"PARTICIPANT_JOINED":                 2,
		"PARTICIPANT_LEFT":                   3,
		"TRACK_PUBLISHED":                    4,
		"TRACK_PUBLISH_REQUESTED":            20,
		"TRACK_UNPUBLISHED":                  5,
		"TRACK_SUBSCRIBED":                   6,
		"TRACK_SUBSCRIBE_REQUESTED":          21,
		"TRACK_SUBSCRIBE_FAILED":             25,
		"TRACK_UNSUBSCRIBED":                 7,
		"TRACK_PUBLISHED_UPDATE":             10,
		"TRACK_MUTED":                        23,
		"TRACK_UNMUTED":                      24,
		"TRACK_PUBLISH_STATS":                26,
		"TRACK_SUBSCRIBE_STATS":              27,
		"PARTICIPANT_ACTIVE":                 11,
		"PARTICIPANT_RESUMED":                22,
		"EGRESS_STARTED":                     12,
		"EGRESS_ENDED":                       13,
		"EGRESS_UPDATED":                     28,
		"TRACK_MAX_SUBSCRIBED_VIDEO_QUALITY": 14,
		"RECONNECTED":                        15,
		"INGRESS_CREATED":                    18,
		"INGRESS_DELETED":                    19,
		"INGRESS_STARTED":                    16,
		"INGRESS_ENDED":                      17,
		"INGRESS_UPDATED":                    29,
		"SIP_INBOUND_TRUNK_CREATED":          30,
		"SIP_INBOUND_TRUNK_DELETED":          31,
		"SIP_OUTBOUND_TRUNK_CREATED":         32,
		"SIP_OUTBOUND_TRUNK_DELETED":         33,
		"SIP_DISPATCH_RULE_CREATED":          34,
		"SIP_DISPATCH_RULE_DELETED":          35,
		"SIP_PARTICIPANT_CREATED":            36,
		"SIP_CALL_INCOMING":                  37,
		"SIP_CALL_STARTED":                   38,
		"SIP_CALL_ENDED":                     39,
		"SIP_TRANSFER_REQUESTED":             43,
		"SIP_TRANSFER_COMPLETE":              44,
		"REPORT":                             40,
		"API_CALL":                           41,
		"WEBHOOK":                            42,
	}
)

func (x AnalyticsEventType) Enum() *AnalyticsEventType {
	p := new(AnalyticsEventType)
	*p = x
	return p
}

func (x AnalyticsEventType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AnalyticsEventType) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_analytics_proto_enumTypes[1].Descriptor()
}

func (AnalyticsEventType) Type() protoreflect.EnumType {
	return &file_livekit_analytics_proto_enumTypes[1]
}

func (x AnalyticsEventType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AnalyticsEventType.Descriptor instead.
func (AnalyticsEventType) EnumDescriptor() ([]byte, []int) {
	return file_livekit_analytics_proto_rawDescGZIP(), []int{1}
}

type FeatureUsageInfo_Feature int32

const (
	FeatureUsageInfo_KRISP_NOISE_CANCELLATION            FeatureUsageInfo_Feature = 0
	FeatureUsageInfo_KRISP_BACKGROUND_VOICE_CANCELLATION FeatureUsageInfo_Feature = 1
)

// Enum value maps for FeatureUsageInfo_Feature.
var (
	FeatureUsageInfo_Feature_name = map[int32]string{
		0: "KRISP_NOISE_CANCELLATION",
		1: "KRISP_BACKGROUND_VOICE_CANCELLATION",
	}
	FeatureUsageInfo_Feature_value = map[string]int32{
		"KRISP_NOISE_CANCELLATION":            0,
		"KRISP_BACKGROUND_VOICE_CANCELLATION": 1,
	}
)

func (x FeatureUsageInfo_Feature) Enum() *FeatureUsageInfo_Feature {
	p := new(FeatureUsageInfo_Feature)
	*p = x
	return p
}

func (x FeatureUsageInfo_Feature) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FeatureUsageInfo_Feature) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_analytics_proto_enumTypes[2].Descriptor()
}

func (FeatureUsageInfo_Feature) Type() protoreflect.EnumType {
	return &file_livekit_analytics_proto_enumTypes[2]
}

func (x FeatureUsageInfo_Feature) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FeatureUsageInfo_Feature.Descriptor instead.
func (FeatureUsageInfo_Feature) EnumDescriptor() ([]byte, []int) {
	return file_livekit_analytics_proto_rawDescGZIP(), []int{12, 0}
}

type AnalyticsVideoLayer struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Layer         int32                  `protobuf:"varint,1,opt,name=layer,proto3" json:"layer,omitempty"`
	Packets       uint32                 `protobuf:"varint,2,opt,name=packets,proto3" json:"packets,omitempty"`
	Bytes         uint64                 `protobuf:"varint,3,opt,name=bytes,proto3" json:"bytes,omitempty"`
	Frames        uint32                 `protobuf:"varint,4,opt,name=frames,proto3" json:"frames,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnalyticsVideoLayer) Reset() {
	*x = AnalyticsVideoLayer{}
	mi := &file_livekit_analytics_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnalyticsVideoLayer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyticsVideoLayer) ProtoMessage() {}

func (x *AnalyticsVideoLayer) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_analytics_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyticsVideoLayer.ProtoReflect.Descriptor instead.
func (*AnalyticsVideoLayer) Descriptor() ([]byte, []int) {
	return file_livekit_analytics_proto_rawDescGZIP(), []int{0}
}

func (x *AnalyticsVideoLayer) GetLayer() int32 {
	if x != nil {
		return x.Layer
	}
	return 0
}

func (x *AnalyticsVideoLayer) GetPackets() uint32 {
	if x != nil {
		return x.Packets
	}
	return 0
}

func (x *AnalyticsVideoLayer) GetBytes() uint64 {
	if x != nil {
		return x.Bytes
	}
	return 0
}

func (x *AnalyticsVideoLayer) GetFrames() uint32 {
	if x != nil {
		return x.Frames
	}
	return 0
}

type AnalyticsStream struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Ssrc              uint32                 `protobuf:"varint,1,opt,name=ssrc,proto3" json:"ssrc,omitempty"`
	PrimaryPackets    uint32                 `protobuf:"varint,2,opt,name=primary_packets,json=primaryPackets,proto3" json:"primary_packets,omitempty"`
	PrimaryBytes      uint64                 `protobuf:"varint,3,opt,name=primary_bytes,json=primaryBytes,proto3" json:"primary_bytes,omitempty"`
	RetransmitPackets uint32                 `protobuf:"varint,4,opt,name=retransmit_packets,json=retransmitPackets,proto3" json:"retransmit_packets,omitempty"`
	RetransmitBytes   uint64                 `protobuf:"varint,5,opt,name=retransmit_bytes,json=retransmitBytes,proto3" json:"retransmit_bytes,omitempty"`
	PaddingPackets    uint32                 `protobuf:"varint,6,opt,name=padding_packets,json=paddingPackets,proto3" json:"padding_packets,omitempty"`
	PaddingBytes      uint64                 `protobuf:"varint,7,opt,name=padding_bytes,json=paddingBytes,proto3" json:"padding_bytes,omitempty"`
	PacketsLost       uint32                 `protobuf:"varint,8,opt,name=packets_lost,json=packetsLost,proto3" json:"packets_lost,omitempty"`
	Frames            uint32                 `protobuf:"varint,9,opt,name=frames,proto3" json:"frames,omitempty"`
	Rtt               uint32                 `protobuf:"varint,10,opt,name=rtt,proto3" json:"rtt,omitempty"`
	Jitter            uint32                 `protobuf:"varint,11,opt,name=jitter,proto3" json:"jitter,omitempty"`
	Nacks             uint32                 `protobuf:"varint,12,opt,name=nacks,proto3" json:"nacks,omitempty"`
	Plis              uint32                 `protobuf:"varint,13,opt,name=plis,proto3" json:"plis,omitempty"`
	Firs              uint32                 `protobuf:"varint,14,opt,name=firs,proto3" json:"firs,omitempty"`
	VideoLayers       []*AnalyticsVideoLayer `protobuf:"bytes,15,rep,name=video_layers,json=videoLayers,proto3" json:"video_layers,omitempty"`
	StartTime         *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime           *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	PacketsOutOfOrder uint32                 `protobuf:"varint,19,opt,name=packets_out_of_order,json=packetsOutOfOrder,proto3" json:"packets_out_of_order,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *AnalyticsStream) Reset() {
	*x = AnalyticsStream{}
	mi := &file_livekit_analytics_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnalyticsStream) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyticsStream) ProtoMessage() {}

func (x *AnalyticsStream) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_analytics_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyticsStream.ProtoReflect.Descriptor instead.
func (*AnalyticsStream) Descriptor() ([]byte, []int) {
	return file_livekit_analytics_proto_rawDescGZIP(), []int{1}
}

func (x *AnalyticsStream) GetSsrc() uint32 {
	if x != nil {
		return x.Ssrc
	}
	return 0
}

func (x *AnalyticsStream) GetPrimaryPackets() uint32 {
	if x != nil {
		return x.PrimaryPackets
	}
	return 0
}

func (x *AnalyticsStream) GetPrimaryBytes() uint64 {
	if x != nil {
		return x.PrimaryBytes
	}
	return 0
}

func (x *AnalyticsStream) GetRetransmitPackets() uint32 {
	if x != nil {
		return x.RetransmitPackets
	}
	return 0
}

func (x *AnalyticsStream) GetRetransmitBytes() uint64 {
	if x != nil {
		return x.RetransmitBytes
	}
	return 0
}

func (x *AnalyticsStream) GetPaddingPackets() uint32 {
	if x != nil {
		return x.PaddingPackets
	}
	return 0
}

func (x *AnalyticsStream) GetPaddingBytes() uint64 {
	if x != nil {
		return x.PaddingBytes
	}
	return 0
}

func (x *AnalyticsStream) GetPacketsLost() uint32 {
	if x != nil {
		return x.PacketsLost
	}
	return 0
}

func (x *AnalyticsStream) GetFrames() uint32 {
	if x != nil {
		return x.Frames
	}
	return 0
}

func (x *AnalyticsStream) GetRtt() uint32 {
	if x != nil {
		return x.Rtt
	}
	return 0
}

func (x *AnalyticsStream) GetJitter() uint32 {
	if x != nil {
		return x.Jitter
	}
	return 0
}

func (x *AnalyticsStream) GetNacks() uint32 {
	if x != nil {
		return x.Nacks
	}
	return 0
}

func (x *AnalyticsStream) GetPlis() uint32 {
	if x != nil {
		return x.Plis
	}
	return 0
}

func (x *AnalyticsStream) GetFirs() uint32 {
	if x != nil {
		return x.Firs
	}
	return 0
}

func (x *AnalyticsStream) GetVideoLayers() []*AnalyticsVideoLayer {
	if x != nil {
		return x.VideoLayers
	}
	return nil
}

func (x *AnalyticsStream) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *AnalyticsStream) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *AnalyticsStream) GetPacketsOutOfOrder() uint32 {
	if x != nil {
		return x.PacketsOutOfOrder
	}
	return 0
}

type AnalyticsStat struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// unique id for this stat
	Id            string                 `protobuf:"bytes,14,opt,name=id,proto3" json:"id,omitempty"`
	AnalyticsKey  string                 `protobuf:"bytes,1,opt,name=analytics_key,json=analyticsKey,proto3" json:"analytics_key,omitempty"`
	Kind          StreamType             `protobuf:"varint,2,opt,name=kind,proto3,enum=livekit.StreamType" json:"kind,omitempty"`
	TimeStamp     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=time_stamp,json=timeStamp,proto3" json:"time_stamp,omitempty"`
	Node          string                 `protobuf:"bytes,4,opt,name=node,proto3" json:"node,omitempty"`
	RoomId        string                 `protobuf:"bytes,5,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	RoomName      string                 `protobuf:"bytes,6,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`
	ParticipantId string                 `protobuf:"bytes,7,opt,name=participant_id,json=participantId,proto3" json:"participant_id,omitempty"`
	TrackId       string                 `protobuf:"bytes,8,opt,name=track_id,json=trackId,proto3" json:"track_id,omitempty"`
	Score         float32                `protobuf:"fixed32,9,opt,name=score,proto3" json:"score,omitempty"` // average score
	Streams       []*AnalyticsStream     `protobuf:"bytes,10,rep,name=streams,proto3" json:"streams,omitempty"`
	Mime          string                 `protobuf:"bytes,11,opt,name=mime,proto3" json:"mime,omitempty"`
	MinScore      float32                `protobuf:"fixed32,12,opt,name=min_score,json=minScore,proto3" json:"min_score,omitempty"`
	MedianScore   float32                `protobuf:"fixed32,13,opt,name=median_score,json=medianScore,proto3" json:"median_score,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnalyticsStat) Reset() {
	*x = AnalyticsStat{}
	mi := &file_livekit_analytics_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnalyticsStat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyticsStat) ProtoMessage() {}

func (x *AnalyticsStat) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_analytics_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyticsStat.ProtoReflect.Descriptor instead.
func (*AnalyticsStat) Descriptor() ([]byte, []int) {
	return file_livekit_analytics_proto_rawDescGZIP(), []int{2}
}

func (x *AnalyticsStat) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AnalyticsStat) GetAnalyticsKey() string {
	if x != nil {
		return x.AnalyticsKey
	}
	return ""
}

func (x *AnalyticsStat) GetKind() StreamType {
	if x != nil {
		return x.Kind
	}
	return StreamType_UPSTREAM
}

func (x *AnalyticsStat) GetTimeStamp() *timestamppb.Timestamp {
	if x != nil {
		return x.TimeStamp
	}
	return nil
}

func (x *AnalyticsStat) GetNode() string {
	if x != nil {
		return x.Node
	}
	return ""
}

func (x *AnalyticsStat) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *AnalyticsStat) GetRoomName() string {
	if x != nil {
		return x.RoomName
	}
	return ""
}

func (x *AnalyticsStat) GetParticipantId() string {
	if x != nil {
		return x.ParticipantId
	}
	return ""
}

func (x *AnalyticsStat) GetTrackId() string {
	if x != nil {
		return x.TrackId
	}
	return ""
}

func (x *AnalyticsStat) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *AnalyticsStat) GetStreams() []*AnalyticsStream {
	if x != nil {
		return x.Streams
	}
	return nil
}

func (x *AnalyticsStat) GetMime() string {
	if x != nil {
		return x.Mime
	}
	return ""
}

func (x *AnalyticsStat) GetMinScore() float32 {
	if x != nil {
		return x.MinScore
	}
	return 0
}

func (x *AnalyticsStat) GetMedianScore() float32 {
	if x != nil {
		return x.MedianScore
	}
	return 0
}

type AnalyticsStats struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Stats         []*AnalyticsStat       `protobuf:"bytes,1,rep,name=stats,proto3" json:"stats,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnalyticsStats) Reset() {
	*x = AnalyticsStats{}
	mi := &file_livekit_analytics_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnalyticsStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyticsStats) ProtoMessage() {}

func (x *AnalyticsStats) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_analytics_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyticsStats.ProtoReflect.Descriptor instead.
func (*AnalyticsStats) Descriptor() ([]byte, []int) {
	return file_livekit_analytics_proto_rawDescGZIP(), []int{3}
}

func (x *AnalyticsStats) GetStats() []*AnalyticsStat {
	if x != nil {
		return x.Stats
	}
	return nil
}

type AnalyticsClientMeta struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Region            string                 `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	Node              string                 `protobuf:"bytes,2,opt,name=node,proto3" json:"node,omitempty"`
	ClientAddr        string                 `protobuf:"bytes,3,opt,name=client_addr,json=clientAddr,proto3" json:"client_addr,omitempty"`
	ClientConnectTime uint32                 `protobuf:"varint,4,opt,name=client_connect_time,json=clientConnectTime,proto3" json:"client_connect_time,omitempty"`
	// udp, tcp, turn
	ConnectionType  string          `protobuf:"bytes,5,opt,name=connection_type,json=connectionType,proto3" json:"connection_type,omitempty"`
	ReconnectReason ReconnectReason `protobuf:"varint,6,opt,name=reconnect_reason,json=reconnectReason,proto3,enum=livekit.ReconnectReason" json:"reconnect_reason,omitempty"`
	GeoHash         *string         `protobuf:"bytes,7,opt,name=geo_hash,json=geoHash,proto3,oneof" json:"geo_hash,omitempty"`
	Country         *string         `protobuf:"bytes,8,opt,name=country,proto3,oneof" json:"country,omitempty"`
	IspAsn          *uint32         `protobuf:"varint,9,opt,name=isp_asn,json=ispAsn,proto3,oneof" json:"isp_asn,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *AnalyticsClientMeta) Reset() {
	*x = AnalyticsClientMeta{}
	mi := &file_livekit_analytics_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnalyticsClientMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyticsClientMeta) ProtoMessage() {}

func (x *AnalyticsClientMeta) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_analytics_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyticsClientMeta.ProtoReflect.Descriptor instead.
func (*AnalyticsClientMeta) Descriptor() ([]byte, []int) {
	return file_livekit_analytics_proto_rawDescGZIP(), []int{4}
}

func (x *AnalyticsClientMeta) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *AnalyticsClientMeta) GetNode() string {
	if x != nil {
		return x.Node
	}
	return ""
}

func (x *AnalyticsClientMeta) GetClientAddr() string {
	if x != nil {
		return x.ClientAddr
	}
	return ""
}

func (x *AnalyticsClientMeta) GetClientConnectTime() uint32 {
	if x != nil {
		return x.ClientConnectTime
	}
	return 0
}

func (x *AnalyticsClientMeta) GetConnectionType() string {
	if x != nil {
		return x.ConnectionType
	}
	return ""
}

func (x *AnalyticsClientMeta) GetReconnectReason() ReconnectReason {
	if x != nil {
		return x.ReconnectReason
	}
	return ReconnectReason_RR_UNKNOWN
}

func (x *AnalyticsClientMeta) GetGeoHash() string {
	if x != nil && x.GeoHash != nil {
		return *x.GeoHash
	}
	return ""
}

func (x *AnalyticsClientMeta) GetCountry() string {
	if x != nil && x.Country != nil {
		return *x.Country
	}
	return ""
}

func (x *AnalyticsClientMeta) GetIspAsn() uint32 {
	if x != nil && x.IspAsn != nil {
		return *x.IspAsn
	}
	return 0
}

type AnalyticsEvent struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// unique id for this event
	Id                        string                 `protobuf:"bytes,25,opt,name=id,proto3" json:"id,omitempty"`
	Type                      AnalyticsEventType     `protobuf:"varint,1,opt,name=type,proto3,enum=livekit.AnalyticsEventType" json:"type,omitempty"`
	Timestamp                 *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	RoomId                    string                 `protobuf:"bytes,3,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	Room                      *Room                  `protobuf:"bytes,4,opt,name=room,proto3" json:"room,omitempty"`
	ParticipantId             string                 `protobuf:"bytes,5,opt,name=participant_id,json=participantId,proto3" json:"participant_id,omitempty"`
	Participant               *ParticipantInfo       `protobuf:"bytes,6,opt,name=participant,proto3" json:"participant,omitempty"`
	TrackId                   string                 `protobuf:"bytes,7,opt,name=track_id,json=trackId,proto3" json:"track_id,omitempty"`
	Track                     *TrackInfo             `protobuf:"bytes,8,opt,name=track,proto3" json:"track,omitempty"`
	AnalyticsKey              string                 `protobuf:"bytes,10,opt,name=analytics_key,json=analyticsKey,proto3" json:"analytics_key,omitempty"`
	ClientInfo                *ClientInfo            `protobuf:"bytes,11,opt,name=client_info,json=clientInfo,proto3" json:"client_info,omitempty"`
	ClientMeta                *AnalyticsClientMeta   `protobuf:"bytes,12,opt,name=client_meta,json=clientMeta,proto3" json:"client_meta,omitempty"`
	EgressId                  string                 `protobuf:"bytes,13,opt,name=egress_id,json=egressId,proto3" json:"egress_id,omitempty"`
	IngressId                 string                 `protobuf:"bytes,19,opt,name=ingress_id,json=ingressId,proto3" json:"ingress_id,omitempty"`
	MaxSubscribedVideoQuality VideoQuality           `protobuf:"varint,14,opt,name=max_subscribed_video_quality,json=maxSubscribedVideoQuality,proto3,enum=livekit.VideoQuality" json:"max_subscribed_video_quality,omitempty"`
	Publisher                 *ParticipantInfo       `protobuf:"bytes,15,opt,name=publisher,proto3" json:"publisher,omitempty"`
	Mime                      string                 `protobuf:"bytes,16,opt,name=mime,proto3" json:"mime,omitempty"`
	Egress                    *EgressInfo            `protobuf:"bytes,17,opt,name=egress,proto3" json:"egress,omitempty"`
	Ingress                   *IngressInfo           `protobuf:"bytes,18,opt,name=ingress,proto3" json:"ingress,omitempty"`
	Error                     string                 `protobuf:"bytes,20,opt,name=error,proto3" json:"error,omitempty"`
	RtpStats                  *RTPStats              `protobuf:"bytes,21,opt,name=rtp_stats,json=rtpStats,proto3" json:"rtp_stats,omitempty"`
	VideoLayer                int32                  `protobuf:"varint,22,opt,name=video_layer,json=videoLayer,proto3" json:"video_layer,omitempty"`
	NodeId                    string                 `protobuf:"bytes,24,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	SipCallId                 string                 `protobuf:"bytes,26,opt,name=sip_call_id,json=sipCallId,proto3" json:"sip_call_id,omitempty"`
	SipCall                   *SIPCallInfo           `protobuf:"bytes,27,opt,name=sip_call,json=sipCall,proto3" json:"sip_call,omitempty"`
	SipTrunkId                string                 `protobuf:"bytes,28,opt,name=sip_trunk_id,json=sipTrunkId,proto3" json:"sip_trunk_id,omitempty"`
	SipInboundTrunk           *SIPInboundTrunkInfo   `protobuf:"bytes,29,opt,name=sip_inbound_trunk,json=sipInboundTrunk,proto3" json:"sip_inbound_trunk,omitempty"`
	SipOutboundTrunk          *SIPOutboundTrunkInfo  `protobuf:"bytes,30,opt,name=sip_outbound_trunk,json=sipOutboundTrunk,proto3" json:"sip_outbound_trunk,omitempty"`
	SipDispatchRuleId         string                 `protobuf:"bytes,31,opt,name=sip_dispatch_rule_id,json=sipDispatchRuleId,proto3" json:"sip_dispatch_rule_id,omitempty"`
	SipDispatchRule           *SIPDispatchRuleInfo   `protobuf:"bytes,32,opt,name=sip_dispatch_rule,json=sipDispatchRule,proto3" json:"sip_dispatch_rule,omitempty"`
	SipTransfer               *SIPTransferInfo       `protobuf:"bytes,36,opt,name=sip_transfer,json=sipTransfer,proto3" json:"sip_transfer,omitempty"`
	Report                    *ReportInfo            `protobuf:"bytes,33,opt,name=report,proto3" json:"report,omitempty"`
	ApiCall                   *APICallInfo           `protobuf:"bytes,34,opt,name=api_call,json=apiCall,proto3" json:"api_call,omitempty"`
	Webhook                   *WebhookInfo           `protobuf:"bytes,35,opt,name=webhook,proto3" json:"webhook,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *AnalyticsEvent) Reset() {
	*x = AnalyticsEvent{}
	mi := &file_livekit_analytics_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnalyticsEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyticsEvent) ProtoMessage() {}

func (x *AnalyticsEvent) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_analytics_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyticsEvent.ProtoReflect.Descriptor instead.
func (*AnalyticsEvent) Descriptor() ([]byte, []int) {
	return file_livekit_analytics_proto_rawDescGZIP(), []int{5}
}

func (x *AnalyticsEvent) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AnalyticsEvent) GetType() AnalyticsEventType {
	if x != nil {
		return x.Type
	}
	return AnalyticsEventType_ROOM_CREATED
}

func (x *AnalyticsEvent) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *AnalyticsEvent) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *AnalyticsEvent) GetRoom() *Room {
	if x != nil {
		return x.Room
	}
	return nil
}

func (x *AnalyticsEvent) GetParticipantId() string {
	if x != nil {
		return x.ParticipantId
	}
	return ""
}

func (x *AnalyticsEvent) GetParticipant() *ParticipantInfo {
	if x != nil {
		return x.Participant
	}
	return nil
}

func (x *AnalyticsEvent) GetTrackId() string {
	if x != nil {
		return x.TrackId
	}
	return ""
}

func (x *AnalyticsEvent) GetTrack() *TrackInfo {
	if x != nil {
		return x.Track
	}
	return nil
}

func (x *AnalyticsEvent) GetAnalyticsKey() string {
	if x != nil {
		return x.AnalyticsKey
	}
	return ""
}

func (x *AnalyticsEvent) GetClientInfo() *ClientInfo {
	if x != nil {
		return x.ClientInfo
	}
	return nil
}

func (x *AnalyticsEvent) GetClientMeta() *AnalyticsClientMeta {
	if x != nil {
		return x.ClientMeta
	}
	return nil
}

func (x *AnalyticsEvent) GetEgressId() string {
	if x != nil {
		return x.EgressId
	}
	return ""
}

func (x *AnalyticsEvent) GetIngressId() string {
	if x != nil {
		return x.IngressId
	}
	return ""
}

func (x *AnalyticsEvent) GetMaxSubscribedVideoQuality() VideoQuality {
	if x != nil {
		return x.MaxSubscribedVideoQuality
	}
	return VideoQuality_LOW
}

func (x *AnalyticsEvent) GetPublisher() *ParticipantInfo {
	if x != nil {
		return x.Publisher
	}
	return nil
}

func (x *AnalyticsEvent) GetMime() string {
	if x != nil {
		return x.Mime
	}
	return ""
}

func (x *AnalyticsEvent) GetEgress() *EgressInfo {
	if x != nil {
		return x.Egress
	}
	return nil
}

func (x *AnalyticsEvent) GetIngress() *IngressInfo {
	if x != nil {
		return x.Ingress
	}
	return nil
}

func (x *AnalyticsEvent) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *AnalyticsEvent) GetRtpStats() *RTPStats {
	if x != nil {
		return x.RtpStats
	}
	return nil
}

func (x *AnalyticsEvent) GetVideoLayer() int32 {
	if x != nil {
		return x.VideoLayer
	}
	return 0
}

func (x *AnalyticsEvent) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *AnalyticsEvent) GetSipCallId() string {
	if x != nil {
		return x.SipCallId
	}
	return ""
}

func (x *AnalyticsEvent) GetSipCall() *SIPCallInfo {
	if x != nil {
		return x.SipCall
	}
	return nil
}

func (x *AnalyticsEvent) GetSipTrunkId() string {
	if x != nil {
		return x.SipTrunkId
	}
	return ""
}

func (x *AnalyticsEvent) GetSipInboundTrunk() *SIPInboundTrunkInfo {
	if x != nil {
		return x.SipInboundTrunk
	}
	return nil
}

func (x *AnalyticsEvent) GetSipOutboundTrunk() *SIPOutboundTrunkInfo {
	if x != nil {
		return x.SipOutboundTrunk
	}
	return nil
}

func (x *AnalyticsEvent) GetSipDispatchRuleId() string {
	if x != nil {
		return x.SipDispatchRuleId
	}
	return ""
}

func (x *AnalyticsEvent) GetSipDispatchRule() *SIPDispatchRuleInfo {
	if x != nil {
		return x.SipDispatchRule
	}
	return nil
}

func (x *AnalyticsEvent) GetSipTransfer() *SIPTransferInfo {
	if x != nil {
		return x.SipTransfer
	}
	return nil
}

func (x *AnalyticsEvent) GetReport() *ReportInfo {
	if x != nil {
		return x.Report
	}
	return nil
}

func (x *AnalyticsEvent) GetApiCall() *APICallInfo {
	if x != nil {
		return x.ApiCall
	}
	return nil
}

func (x *AnalyticsEvent) GetWebhook() *WebhookInfo {
	if x != nil {
		return x.Webhook
	}
	return nil
}

type AnalyticsEvents struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Events        []*AnalyticsEvent      `protobuf:"bytes,1,rep,name=events,proto3" json:"events,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnalyticsEvents) Reset() {
	*x = AnalyticsEvents{}
	mi := &file_livekit_analytics_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnalyticsEvents) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyticsEvents) ProtoMessage() {}

func (x *AnalyticsEvents) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_analytics_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyticsEvents.ProtoReflect.Descriptor instead.
func (*AnalyticsEvents) Descriptor() ([]byte, []int) {
	return file_livekit_analytics_proto_rawDescGZIP(), []int{6}
}

func (x *AnalyticsEvents) GetEvents() []*AnalyticsEvent {
	if x != nil {
		return x.Events
	}
	return nil
}

type AnalyticsRoomParticipant struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Identity      string                 `protobuf:"bytes,2,opt,name=identity,proto3" json:"identity,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	State         ParticipantInfo_State  `protobuf:"varint,4,opt,name=state,proto3,enum=livekit.ParticipantInfo_State" json:"state,omitempty"`
	JoinedAt      *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=joined_at,json=joinedAt,proto3" json:"joined_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnalyticsRoomParticipant) Reset() {
	*x = AnalyticsRoomParticipant{}
	mi := &file_livekit_analytics_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnalyticsRoomParticipant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyticsRoomParticipant) ProtoMessage() {}

func (x *AnalyticsRoomParticipant) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_analytics_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyticsRoomParticipant.ProtoReflect.Descriptor instead.
func (*AnalyticsRoomParticipant) Descriptor() ([]byte, []int) {
	return file_livekit_analytics_proto_rawDescGZIP(), []int{7}
}

func (x *AnalyticsRoomParticipant) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AnalyticsRoomParticipant) GetIdentity() string {
	if x != nil {
		return x.Identity
	}
	return ""
}

func (x *AnalyticsRoomParticipant) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AnalyticsRoomParticipant) GetState() ParticipantInfo_State {
	if x != nil {
		return x.State
	}
	return ParticipantInfo_JOINING
}

func (x *AnalyticsRoomParticipant) GetJoinedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.JoinedAt
	}
	return nil
}

type AnalyticsRoom struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Id            string                      `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                      `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	ProjectId     string                      `protobuf:"bytes,5,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	CreatedAt     *timestamppb.Timestamp      `protobuf:"bytes,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	Participants  []*AnalyticsRoomParticipant `protobuf:"bytes,4,rep,name=participants,proto3" json:"participants,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnalyticsRoom) Reset() {
	*x = AnalyticsRoom{}
	mi := &file_livekit_analytics_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnalyticsRoom) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyticsRoom) ProtoMessage() {}

func (x *AnalyticsRoom) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_analytics_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyticsRoom.ProtoReflect.Descriptor instead.
func (*AnalyticsRoom) Descriptor() ([]byte, []int) {
	return file_livekit_analytics_proto_rawDescGZIP(), []int{8}
}

func (x *AnalyticsRoom) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AnalyticsRoom) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AnalyticsRoom) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *AnalyticsRoom) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AnalyticsRoom) GetParticipants() []*AnalyticsRoomParticipant {
	if x != nil {
		return x.Participants
	}
	return nil
}

type AnalyticsNodeRooms struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	NodeId         string                 `protobuf:"bytes,1,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	SequenceNumber uint64                 `protobuf:"varint,2,opt,name=sequence_number,json=sequenceNumber,proto3" json:"sequence_number,omitempty"`
	Timestamp      *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Rooms          []*AnalyticsRoom       `protobuf:"bytes,4,rep,name=rooms,proto3" json:"rooms,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AnalyticsNodeRooms) Reset() {
	*x = AnalyticsNodeRooms{}
	mi := &file_livekit_analytics_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnalyticsNodeRooms) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyticsNodeRooms) ProtoMessage() {}

func (x *AnalyticsNodeRooms) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_analytics_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyticsNodeRooms.ProtoReflect.Descriptor instead.
func (*AnalyticsNodeRooms) Descriptor() ([]byte, []int) {
	return file_livekit_analytics_proto_rawDescGZIP(), []int{9}
}

func (x *AnalyticsNodeRooms) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *AnalyticsNodeRooms) GetSequenceNumber() uint64 {
	if x != nil {
		return x.SequenceNumber
	}
	return 0
}

func (x *AnalyticsNodeRooms) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *AnalyticsNodeRooms) GetRooms() []*AnalyticsRoom {
	if x != nil {
		return x.Rooms
	}
	return nil
}

type ReportInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Message:
	//
	//	*ReportInfo_FeatureUsage
	Message       isReportInfo_Message `protobuf_oneof:"message"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReportInfo) Reset() {
	*x = ReportInfo{}
	mi := &file_livekit_analytics_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportInfo) ProtoMessage() {}

func (x *ReportInfo) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_analytics_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportInfo.ProtoReflect.Descriptor instead.
func (*ReportInfo) Descriptor() ([]byte, []int) {
	return file_livekit_analytics_proto_rawDescGZIP(), []int{10}
}

func (x *ReportInfo) GetMessage() isReportInfo_Message {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *ReportInfo) GetFeatureUsage() *FeatureUsageInfo {
	if x != nil {
		if x, ok := x.Message.(*ReportInfo_FeatureUsage); ok {
			return x.FeatureUsage
		}
	}
	return nil
}

type isReportInfo_Message interface {
	isReportInfo_Message()
}

type ReportInfo_FeatureUsage struct {
	FeatureUsage *FeatureUsageInfo `protobuf:"bytes,1,opt,name=feature_usage,json=featureUsage,proto3,oneof"`
}

func (*ReportInfo_FeatureUsage) isReportInfo_Message() {}

type TimeRange struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StartedAt     *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	EndedAt       *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TimeRange) Reset() {
	*x = TimeRange{}
	mi := &file_livekit_analytics_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TimeRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeRange) ProtoMessage() {}

func (x *TimeRange) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_analytics_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeRange.ProtoReflect.Descriptor instead.
func (*TimeRange) Descriptor() ([]byte, []int) {
	return file_livekit_analytics_proto_rawDescGZIP(), []int{11}
}

func (x *TimeRange) GetStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

func (x *TimeRange) GetEndedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.EndedAt
	}
	return nil
}

type FeatureUsageInfo struct {
	state               protoimpl.MessageState   `protogen:"open.v1"`
	Feature             FeatureUsageInfo_Feature `protobuf:"varint,1,opt,name=feature,proto3,enum=livekit.FeatureUsageInfo_Feature" json:"feature,omitempty"`
	ProjectId           string                   `protobuf:"bytes,2,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	RoomName            string                   `protobuf:"bytes,3,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`
	RoomId              string                   `protobuf:"bytes,4,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	ParticipantIdentity string                   `protobuf:"bytes,5,opt,name=participant_identity,json=participantIdentity,proto3" json:"participant_identity,omitempty"`
	ParticipantId       string                   `protobuf:"bytes,6,opt,name=participant_id,json=participantId,proto3" json:"participant_id,omitempty"`
	TrackId             string                   `protobuf:"bytes,7,opt,name=track_id,json=trackId,proto3" json:"track_id,omitempty"`
	// time ranges during which the feature was enabled.
	// for e. g., noise cancellation may not be applied when a media track is paused/muted,
	// this allows reporting only periods during which a feature is avtive.
	TimeRanges    []*TimeRange `protobuf:"bytes,8,rep,name=time_ranges,json=timeRanges,proto3" json:"time_ranges,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FeatureUsageInfo) Reset() {
	*x = FeatureUsageInfo{}
	mi := &file_livekit_analytics_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FeatureUsageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureUsageInfo) ProtoMessage() {}

func (x *FeatureUsageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_analytics_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureUsageInfo.ProtoReflect.Descriptor instead.
func (*FeatureUsageInfo) Descriptor() ([]byte, []int) {
	return file_livekit_analytics_proto_rawDescGZIP(), []int{12}
}

func (x *FeatureUsageInfo) GetFeature() FeatureUsageInfo_Feature {
	if x != nil {
		return x.Feature
	}
	return FeatureUsageInfo_KRISP_NOISE_CANCELLATION
}

func (x *FeatureUsageInfo) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *FeatureUsageInfo) GetRoomName() string {
	if x != nil {
		return x.RoomName
	}
	return ""
}

func (x *FeatureUsageInfo) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *FeatureUsageInfo) GetParticipantIdentity() string {
	if x != nil {
		return x.ParticipantIdentity
	}
	return ""
}

func (x *FeatureUsageInfo) GetParticipantId() string {
	if x != nil {
		return x.ParticipantId
	}
	return ""
}

func (x *FeatureUsageInfo) GetTrackId() string {
	if x != nil {
		return x.TrackId
	}
	return ""
}

func (x *FeatureUsageInfo) GetTimeRanges() []*TimeRange {
	if x != nil {
		return x.TimeRanges
	}
	return nil
}

type APICallRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Message:
	//
	//	*APICallRequest_CreateRoomRequest
	//	*APICallRequest_ListRoomsRequest
	//	*APICallRequest_DeleteRoomRequest
	//	*APICallRequest_ListParticipantsRequest
	//	*APICallRequest_RoomParticipantIdentity
	//	*APICallRequest_MuteRoomTrackRequest
	//	*APICallRequest_UpdateParticipantRequest
	//	*APICallRequest_UpdateSubscriptionsRequest
	//	*APICallRequest_SendDataRequest
	//	*APICallRequest_UpdateRoomMetadataRequest
	Message       isAPICallRequest_Message `protobuf_oneof:"message"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *APICallRequest) Reset() {
	*x = APICallRequest{}
	mi := &file_livekit_analytics_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *APICallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*APICallRequest) ProtoMessage() {}

func (x *APICallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_analytics_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use APICallRequest.ProtoReflect.Descriptor instead.
func (*APICallRequest) Descriptor() ([]byte, []int) {
	return file_livekit_analytics_proto_rawDescGZIP(), []int{13}
}

func (x *APICallRequest) GetMessage() isAPICallRequest_Message {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *APICallRequest) GetCreateRoomRequest() *CreateRoomRequest {
	if x != nil {
		if x, ok := x.Message.(*APICallRequest_CreateRoomRequest); ok {
			return x.CreateRoomRequest
		}
	}
	return nil
}

func (x *APICallRequest) GetListRoomsRequest() *ListRoomsRequest {
	if x != nil {
		if x, ok := x.Message.(*APICallRequest_ListRoomsRequest); ok {
			return x.ListRoomsRequest
		}
	}
	return nil
}

func (x *APICallRequest) GetDeleteRoomRequest() *DeleteRoomRequest {
	if x != nil {
		if x, ok := x.Message.(*APICallRequest_DeleteRoomRequest); ok {
			return x.DeleteRoomRequest
		}
	}
	return nil
}

func (x *APICallRequest) GetListParticipantsRequest() *ListParticipantsRequest {
	if x != nil {
		if x, ok := x.Message.(*APICallRequest_ListParticipantsRequest); ok {
			return x.ListParticipantsRequest
		}
	}
	return nil
}

func (x *APICallRequest) GetRoomParticipantIdentity() *RoomParticipantIdentity {
	if x != nil {
		if x, ok := x.Message.(*APICallRequest_RoomParticipantIdentity); ok {
			return x.RoomParticipantIdentity
		}
	}
	return nil
}

func (x *APICallRequest) GetMuteRoomTrackRequest() *MuteRoomTrackRequest {
	if x != nil {
		if x, ok := x.Message.(*APICallRequest_MuteRoomTrackRequest); ok {
			return x.MuteRoomTrackRequest
		}
	}
	return nil
}

func (x *APICallRequest) GetUpdateParticipantRequest() *UpdateParticipantRequest {
	if x != nil {
		if x, ok := x.Message.(*APICallRequest_UpdateParticipantRequest); ok {
			return x.UpdateParticipantRequest
		}
	}
	return nil
}

func (x *APICallRequest) GetUpdateSubscriptionsRequest() *UpdateSubscriptionsRequest {
	if x != nil {
		if x, ok := x.Message.(*APICallRequest_UpdateSubscriptionsRequest); ok {
			return x.UpdateSubscriptionsRequest
		}
	}
	return nil
}

func (x *APICallRequest) GetSendDataRequest() *SendDataRequest {
	if x != nil {
		if x, ok := x.Message.(*APICallRequest_SendDataRequest); ok {
			return x.SendDataRequest
		}
	}
	return nil
}

func (x *APICallRequest) GetUpdateRoomMetadataRequest() *UpdateRoomMetadataRequest {
	if x != nil {
		if x, ok := x.Message.(*APICallRequest_UpdateRoomMetadataRequest); ok {
			return x.UpdateRoomMetadataRequest
		}
	}
	return nil
}

type isAPICallRequest_Message interface {
	isAPICallRequest_Message()
}

type APICallRequest_CreateRoomRequest struct {
	CreateRoomRequest *CreateRoomRequest `protobuf:"bytes,1,opt,name=create_room_request,json=createRoomRequest,proto3,oneof"`
}

type APICallRequest_ListRoomsRequest struct {
	ListRoomsRequest *ListRoomsRequest `protobuf:"bytes,2,opt,name=list_rooms_request,json=listRoomsRequest,proto3,oneof"`
}

type APICallRequest_DeleteRoomRequest struct {
	DeleteRoomRequest *DeleteRoomRequest `protobuf:"bytes,3,opt,name=delete_room_request,json=deleteRoomRequest,proto3,oneof"`
}

type APICallRequest_ListParticipantsRequest struct {
	ListParticipantsRequest *ListParticipantsRequest `protobuf:"bytes,4,opt,name=list_participants_request,json=listParticipantsRequest,proto3,oneof"`
}

type APICallRequest_RoomParticipantIdentity struct {
	RoomParticipantIdentity *RoomParticipantIdentity `protobuf:"bytes,5,opt,name=room_participant_identity,json=roomParticipantIdentity,proto3,oneof"`
}

type APICallRequest_MuteRoomTrackRequest struct {
	MuteRoomTrackRequest *MuteRoomTrackRequest `protobuf:"bytes,6,opt,name=mute_room_track_request,json=muteRoomTrackRequest,proto3,oneof"`
}

type APICallRequest_UpdateParticipantRequest struct {
	UpdateParticipantRequest *UpdateParticipantRequest `protobuf:"bytes,7,opt,name=update_participant_request,json=updateParticipantRequest,proto3,oneof"`
}

type APICallRequest_UpdateSubscriptionsRequest struct {
	UpdateSubscriptionsRequest *UpdateSubscriptionsRequest `protobuf:"bytes,8,opt,name=update_subscriptions_request,json=updateSubscriptionsRequest,proto3,oneof"`
}

type APICallRequest_SendDataRequest struct {
	SendDataRequest *SendDataRequest `protobuf:"bytes,9,opt,name=send_data_request,json=sendDataRequest,proto3,oneof"`
}

type APICallRequest_UpdateRoomMetadataRequest struct {
	UpdateRoomMetadataRequest *UpdateRoomMetadataRequest `protobuf:"bytes,10,opt,name=update_room_metadata_request,json=updateRoomMetadataRequest,proto3,oneof"`
}

func (*APICallRequest_CreateRoomRequest) isAPICallRequest_Message() {}

func (*APICallRequest_ListRoomsRequest) isAPICallRequest_Message() {}

func (*APICallRequest_DeleteRoomRequest) isAPICallRequest_Message() {}

func (*APICallRequest_ListParticipantsRequest) isAPICallRequest_Message() {}

func (*APICallRequest_RoomParticipantIdentity) isAPICallRequest_Message() {}

func (*APICallRequest_MuteRoomTrackRequest) isAPICallRequest_Message() {}

func (*APICallRequest_UpdateParticipantRequest) isAPICallRequest_Message() {}

func (*APICallRequest_UpdateSubscriptionsRequest) isAPICallRequest_Message() {}

func (*APICallRequest_SendDataRequest) isAPICallRequest_Message() {}

func (*APICallRequest_UpdateRoomMetadataRequest) isAPICallRequest_Message() {}

type APICallInfo struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	ProjectId           string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	Request             *APICallRequest        `protobuf:"bytes,2,opt,name=request,proto3" json:"request,omitempty"`
	Service             string                 `protobuf:"bytes,3,opt,name=service,proto3" json:"service,omitempty"`
	Method              string                 `protobuf:"bytes,4,opt,name=method,proto3" json:"method,omitempty"`
	NodeId              string                 `protobuf:"bytes,5,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	Status              int32                  `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`
	TwirpErrorCode      string                 `protobuf:"bytes,7,opt,name=twirp_error_code,json=twirpErrorCode,proto3" json:"twirp_error_code,omitempty"`
	TwirpErrorMessage   string                 `protobuf:"bytes,8,opt,name=twirp_error_message,json=twirpErrorMessage,proto3" json:"twirp_error_message,omitempty"`
	RoomName            string                 `protobuf:"bytes,9,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`
	RoomId              string                 `protobuf:"bytes,10,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	ParticipantIdentity string                 `protobuf:"bytes,11,opt,name=participant_identity,json=participantIdentity,proto3" json:"participant_identity,omitempty"`
	ParticipantId       string                 `protobuf:"bytes,12,opt,name=participant_id,json=participantId,proto3" json:"participant_id,omitempty"`
	TrackId             string                 `protobuf:"bytes,13,opt,name=track_id,json=trackId,proto3" json:"track_id,omitempty"`
	StartedAt           *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	DurationNs          int64                  `protobuf:"varint,15,opt,name=duration_ns,json=durationNs,proto3" json:"duration_ns,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *APICallInfo) Reset() {
	*x = APICallInfo{}
	mi := &file_livekit_analytics_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *APICallInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*APICallInfo) ProtoMessage() {}

func (x *APICallInfo) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_analytics_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use APICallInfo.ProtoReflect.Descriptor instead.
func (*APICallInfo) Descriptor() ([]byte, []int) {
	return file_livekit_analytics_proto_rawDescGZIP(), []int{14}
}

func (x *APICallInfo) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *APICallInfo) GetRequest() *APICallRequest {
	if x != nil {
		return x.Request
	}
	return nil
}

func (x *APICallInfo) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

func (x *APICallInfo) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *APICallInfo) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *APICallInfo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *APICallInfo) GetTwirpErrorCode() string {
	if x != nil {
		return x.TwirpErrorCode
	}
	return ""
}

func (x *APICallInfo) GetTwirpErrorMessage() string {
	if x != nil {
		return x.TwirpErrorMessage
	}
	return ""
}

func (x *APICallInfo) GetRoomName() string {
	if x != nil {
		return x.RoomName
	}
	return ""
}

func (x *APICallInfo) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *APICallInfo) GetParticipantIdentity() string {
	if x != nil {
		return x.ParticipantIdentity
	}
	return ""
}

func (x *APICallInfo) GetParticipantId() string {
	if x != nil {
		return x.ParticipantId
	}
	return ""
}

func (x *APICallInfo) GetTrackId() string {
	if x != nil {
		return x.TrackId
	}
	return ""
}

func (x *APICallInfo) GetStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

func (x *APICallInfo) GetDurationNs() int64 {
	if x != nil {
		return x.DurationNs
	}
	return 0
}

type WebhookInfo struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	EventId             string                 `protobuf:"bytes,1,opt,name=event_id,json=eventId,proto3" json:"event_id,omitempty"`
	Event               string                 `protobuf:"bytes,2,opt,name=event,proto3" json:"event,omitempty"`
	ProjectId           string                 `protobuf:"bytes,3,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	RoomName            string                 `protobuf:"bytes,4,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`
	RoomId              string                 `protobuf:"bytes,5,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	ParticipantIdentity string                 `protobuf:"bytes,6,opt,name=participant_identity,json=participantIdentity,proto3" json:"participant_identity,omitempty"`
	ParticipantId       string                 `protobuf:"bytes,7,opt,name=participant_id,json=participantId,proto3" json:"participant_id,omitempty"`
	TrackId             string                 `protobuf:"bytes,8,opt,name=track_id,json=trackId,proto3" json:"track_id,omitempty"`
	EgressId            string                 `protobuf:"bytes,9,opt,name=egress_id,json=egressId,proto3" json:"egress_id,omitempty"`
	IngressId           string                 `protobuf:"bytes,10,opt,name=ingress_id,json=ingressId,proto3" json:"ingress_id,omitempty"`
	CreatedAt           *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	QueuedAt            *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=queued_at,json=queuedAt,proto3" json:"queued_at,omitempty"`
	QueueDurationNs     int64                  `protobuf:"varint,13,opt,name=queue_duration_ns,json=queueDurationNs,proto3" json:"queue_duration_ns,omitempty"`
	SentAt              *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=sent_at,json=sentAt,proto3" json:"sent_at,omitempty"`
	SendDurationNs      int64                  `protobuf:"varint,15,opt,name=send_duration_ns,json=sendDurationNs,proto3" json:"send_duration_ns,omitempty"`
	Url                 string                 `protobuf:"bytes,16,opt,name=url,proto3" json:"url,omitempty"`
	NumDropped          int32                  `protobuf:"varint,17,opt,name=num_dropped,json=numDropped,proto3" json:"num_dropped,omitempty"`
	IsDropped           bool                   `protobuf:"varint,18,opt,name=is_dropped,json=isDropped,proto3" json:"is_dropped,omitempty"`
	ServiceStatus       string                 `protobuf:"bytes,19,opt,name=service_status,json=serviceStatus,proto3" json:"service_status,omitempty"`
	ServiceErrorCode    int32                  `protobuf:"varint,20,opt,name=service_error_code,json=serviceErrorCode,proto3" json:"service_error_code,omitempty"`
	ServiceError        string                 `protobuf:"bytes,21,opt,name=service_error,json=serviceError,proto3" json:"service_error,omitempty"`
	SendError           string                 `protobuf:"bytes,22,opt,name=send_error,json=sendError,proto3" json:"send_error,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *WebhookInfo) Reset() {
	*x = WebhookInfo{}
	mi := &file_livekit_analytics_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WebhookInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebhookInfo) ProtoMessage() {}

func (x *WebhookInfo) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_analytics_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebhookInfo.ProtoReflect.Descriptor instead.
func (*WebhookInfo) Descriptor() ([]byte, []int) {
	return file_livekit_analytics_proto_rawDescGZIP(), []int{15}
}

func (x *WebhookInfo) GetEventId() string {
	if x != nil {
		return x.EventId
	}
	return ""
}

func (x *WebhookInfo) GetEvent() string {
	if x != nil {
		return x.Event
	}
	return ""
}

func (x *WebhookInfo) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *WebhookInfo) GetRoomName() string {
	if x != nil {
		return x.RoomName
	}
	return ""
}

func (x *WebhookInfo) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *WebhookInfo) GetParticipantIdentity() string {
	if x != nil {
		return x.ParticipantIdentity
	}
	return ""
}

func (x *WebhookInfo) GetParticipantId() string {
	if x != nil {
		return x.ParticipantId
	}
	return ""
}

func (x *WebhookInfo) GetTrackId() string {
	if x != nil {
		return x.TrackId
	}
	return ""
}

func (x *WebhookInfo) GetEgressId() string {
	if x != nil {
		return x.EgressId
	}
	return ""
}

func (x *WebhookInfo) GetIngressId() string {
	if x != nil {
		return x.IngressId
	}
	return ""
}

func (x *WebhookInfo) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *WebhookInfo) GetQueuedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.QueuedAt
	}
	return nil
}

func (x *WebhookInfo) GetQueueDurationNs() int64 {
	if x != nil {
		return x.QueueDurationNs
	}
	return 0
}

func (x *WebhookInfo) GetSentAt() *timestamppb.Timestamp {
	if x != nil {
		return x.SentAt
	}
	return nil
}

func (x *WebhookInfo) GetSendDurationNs() int64 {
	if x != nil {
		return x.SendDurationNs
	}
	return 0
}

func (x *WebhookInfo) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *WebhookInfo) GetNumDropped() int32 {
	if x != nil {
		return x.NumDropped
	}
	return 0
}

func (x *WebhookInfo) GetIsDropped() bool {
	if x != nil {
		return x.IsDropped
	}
	return false
}

func (x *WebhookInfo) GetServiceStatus() string {
	if x != nil {
		return x.ServiceStatus
	}
	return ""
}

func (x *WebhookInfo) GetServiceErrorCode() int32 {
	if x != nil {
		return x.ServiceErrorCode
	}
	return 0
}

func (x *WebhookInfo) GetServiceError() string {
	if x != nil {
		return x.ServiceError
	}
	return ""
}

func (x *WebhookInfo) GetSendError() string {
	if x != nil {
		return x.SendError
	}
	return ""
}

var File_livekit_analytics_proto protoreflect.FileDescriptor

const file_livekit_analytics_proto_rawDesc = "" +
	"\n" +
	"\x17livekit_analytics.proto\x12\alivekit\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x14livekit_models.proto\x1a\x14livekit_egress.proto\x1a\x15livekit_ingress.proto\x1a\x11livekit_sip.proto\x1a\x12livekit_room.proto\"s\n" +
	"\x13AnalyticsVideoLayer\x12\x14\n" +
	"\x05layer\x18\x01 \x01(\x05R\x05layer\x12\x18\n" +
	"\apackets\x18\x02 \x01(\rR\apackets\x12\x14\n" +
	"\x05bytes\x18\x03 \x01(\x04R\x05bytes\x12\x16\n" +
	"\x06frames\x18\x04 \x01(\rR\x06frames\"\xa2\x05\n" +
	"\x0fAnalyticsStream\x12\x12\n" +
	"\x04ssrc\x18\x01 \x01(\rR\x04ssrc\x12'\n" +
	"\x0fprimary_packets\x18\x02 \x01(\rR\x0eprimaryPackets\x12#\n" +
	"\rprimary_bytes\x18\x03 \x01(\x04R\fprimaryBytes\x12-\n" +
	"\x12retransmit_packets\x18\x04 \x01(\rR\x11retransmitPackets\x12)\n" +
	"\x10retransmit_bytes\x18\x05 \x01(\x04R\x0fretransmitBytes\x12'\n" +
	"\x0fpadding_packets\x18\x06 \x01(\rR\x0epaddingPackets\x12#\n" +
	"\rpadding_bytes\x18\a \x01(\x04R\fpaddingBytes\x12!\n" +
	"\fpackets_lost\x18\b \x01(\rR\vpacketsLost\x12\x16\n" +
	"\x06frames\x18\t \x01(\rR\x06frames\x12\x10\n" +
	"\x03rtt\x18\n" +
	" \x01(\rR\x03rtt\x12\x16\n" +
	"\x06jitter\x18\v \x01(\rR\x06jitter\x12\x14\n" +
	"\x05nacks\x18\f \x01(\rR\x05nacks\x12\x12\n" +
	"\x04plis\x18\r \x01(\rR\x04plis\x12\x12\n" +
	"\x04firs\x18\x0e \x01(\rR\x04firs\x12?\n" +
	"\fvideo_layers\x18\x0f \x03(\v2\x1c.livekit.AnalyticsVideoLayerR\vvideoLayers\x129\n" +
	"\n" +
	"start_time\x18\x11 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x12 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12/\n" +
	"\x14packets_out_of_order\x18\x13 \x01(\rR\x11packetsOutOfOrder\"\xd2\x03\n" +
	"\rAnalyticsStat\x12\x0e\n" +
	"\x02id\x18\x0e \x01(\tR\x02id\x12#\n" +
	"\ranalytics_key\x18\x01 \x01(\tR\fanalyticsKey\x12'\n" +
	"\x04kind\x18\x02 \x01(\x0e2\x13.livekit.StreamTypeR\x04kind\x129\n" +
	"\n" +
	"time_stamp\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\ttimeStamp\x12\x12\n" +
	"\x04node\x18\x04 \x01(\tR\x04node\x12\x17\n" +
	"\aroom_id\x18\x05 \x01(\tR\x06roomId\x12\x1b\n" +
	"\troom_name\x18\x06 \x01(\tR\broomName\x12%\n" +
	"\x0eparticipant_id\x18\a \x01(\tR\rparticipantId\x12\x19\n" +
	"\btrack_id\x18\b \x01(\tR\atrackId\x12\x14\n" +
	"\x05score\x18\t \x01(\x02R\x05score\x122\n" +
	"\astreams\x18\n" +
	" \x03(\v2\x18.livekit.AnalyticsStreamR\astreams\x12\x12\n" +
	"\x04mime\x18\v \x01(\tR\x04mime\x12\x1b\n" +
	"\tmin_score\x18\f \x01(\x02R\bminScore\x12!\n" +
	"\fmedian_score\x18\r \x01(\x02R\vmedianScore\">\n" +
	"\x0eAnalyticsStats\x12,\n" +
	"\x05stats\x18\x01 \x03(\v2\x16.livekit.AnalyticsStatR\x05stats\"\x82\x03\n" +
	"\x13AnalyticsClientMeta\x12\x16\n" +
	"\x06region\x18\x01 \x01(\tR\x06region\x12\x12\n" +
	"\x04node\x18\x02 \x01(\tR\x04node\x12\x1f\n" +
	"\vclient_addr\x18\x03 \x01(\tR\n" +
	"clientAddr\x12.\n" +
	"\x13client_connect_time\x18\x04 \x01(\rR\x11clientConnectTime\x12'\n" +
	"\x0fconnection_type\x18\x05 \x01(\tR\x0econnectionType\x12C\n" +
	"\x10reconnect_reason\x18\x06 \x01(\x0e2\x18.livekit.ReconnectReasonR\x0freconnectReason\x12\x1e\n" +
	"\bgeo_hash\x18\a \x01(\tH\x00R\ageoHash\x88\x01\x01\x12\x1d\n" +
	"\acountry\x18\b \x01(\tH\x01R\acountry\x88\x01\x01\x12\x1c\n" +
	"\aisp_asn\x18\t \x01(\rH\x02R\x06ispAsn\x88\x01\x01B\v\n" +
	"\t_geo_hashB\n" +
	"\n" +
	"\b_countryB\n" +
	"\n" +
	"\b_isp_asn\"\x96\f\n" +
	"\x0eAnalyticsEvent\x12\x0e\n" +
	"\x02id\x18\x19 \x01(\tR\x02id\x12/\n" +
	"\x04type\x18\x01 \x01(\x0e2\x1b.livekit.AnalyticsEventTypeR\x04type\x128\n" +
	"\ttimestamp\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\ttimestamp\x12\x17\n" +
	"\aroom_id\x18\x03 \x01(\tR\x06roomId\x12!\n" +
	"\x04room\x18\x04 \x01(\v2\r.livekit.RoomR\x04room\x12%\n" +
	"\x0eparticipant_id\x18\x05 \x01(\tR\rparticipantId\x12:\n" +
	"\vparticipant\x18\x06 \x01(\v2\x18.livekit.ParticipantInfoR\vparticipant\x12\x19\n" +
	"\btrack_id\x18\a \x01(\tR\atrackId\x12(\n" +
	"\x05track\x18\b \x01(\v2\x12.livekit.TrackInfoR\x05track\x12#\n" +
	"\ranalytics_key\x18\n" +
	" \x01(\tR\fanalyticsKey\x124\n" +
	"\vclient_info\x18\v \x01(\v2\x13.livekit.ClientInfoR\n" +
	"clientInfo\x12=\n" +
	"\vclient_meta\x18\f \x01(\v2\x1c.livekit.AnalyticsClientMetaR\n" +
	"clientMeta\x12\x1b\n" +
	"\tegress_id\x18\r \x01(\tR\begressId\x12\x1d\n" +
	"\n" +
	"ingress_id\x18\x13 \x01(\tR\tingressId\x12V\n" +
	"\x1cmax_subscribed_video_quality\x18\x0e \x01(\x0e2\x15.livekit.VideoQualityR\x19maxSubscribedVideoQuality\x126\n" +
	"\tpublisher\x18\x0f \x01(\v2\x18.livekit.ParticipantInfoR\tpublisher\x12\x12\n" +
	"\x04mime\x18\x10 \x01(\tR\x04mime\x12+\n" +
	"\x06egress\x18\x11 \x01(\v2\x13.livekit.EgressInfoR\x06egress\x12.\n" +
	"\aingress\x18\x12 \x01(\v2\x14.livekit.IngressInfoR\aingress\x12\x14\n" +
	"\x05error\x18\x14 \x01(\tR\x05error\x12.\n" +
	"\trtp_stats\x18\x15 \x01(\v2\x11.livekit.RTPStatsR\brtpStats\x12\x1f\n" +
	"\vvideo_layer\x18\x16 \x01(\x05R\n" +
	"videoLayer\x12\x17\n" +
	"\anode_id\x18\x18 \x01(\tR\x06nodeId\x12\x1e\n" +
	"\vsip_call_id\x18\x1a \x01(\tR\tsipCallId\x12/\n" +
	"\bsip_call\x18\x1b \x01(\v2\x14.livekit.SIPCallInfoR\asipCall\x12 \n" +
	"\fsip_trunk_id\x18\x1c \x01(\tR\n" +
	"sipTrunkId\x12H\n" +
	"\x11sip_inbound_trunk\x18\x1d \x01(\v2\x1c.livekit.SIPInboundTrunkInfoR\x0fsipInboundTrunk\x12K\n" +
	"\x12sip_outbound_trunk\x18\x1e \x01(\v2\x1d.livekit.SIPOutboundTrunkInfoR\x10sipOutboundTrunk\x12/\n" +
	"\x14sip_dispatch_rule_id\x18\x1f \x01(\tR\x11sipDispatchRuleId\x12H\n" +
	"\x11sip_dispatch_rule\x18  \x01(\v2\x1c.livekit.SIPDispatchRuleInfoR\x0fsipDispatchRule\x12;\n" +
	"\fsip_transfer\x18$ \x01(\v2\x18.livekit.SIPTransferInfoR\vsipTransfer\x12+\n" +
	"\x06report\x18! \x01(\v2\x13.livekit.ReportInfoR\x06report\x12/\n" +
	"\bapi_call\x18\" \x01(\v2\x14.livekit.APICallInfoR\aapiCall\x12.\n" +
	"\awebhook\x18# \x01(\v2\x14.livekit.WebhookInfoR\awebhook\"B\n" +
	"\x0fAnalyticsEvents\x12/\n" +
	"\x06events\x18\x01 \x03(\v2\x17.livekit.AnalyticsEventR\x06events\"\xc9\x01\n" +
	"\x18AnalyticsRoomParticipant\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1a\n" +
	"\bidentity\x18\x02 \x01(\tR\bidentity\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x124\n" +
	"\x05state\x18\x04 \x01(\x0e2\x1e.livekit.ParticipantInfo.StateR\x05state\x127\n" +
	"\tjoined_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\bjoinedAt\"\xd4\x01\n" +
	"\rAnalyticsRoom\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1d\n" +
	"\n" +
	"project_id\x18\x05 \x01(\tR\tprojectId\x129\n" +
	"\n" +
	"created_at\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12E\n" +
	"\fparticipants\x18\x04 \x03(\v2!.livekit.AnalyticsRoomParticipantR\fparticipants\"\xbe\x01\n" +
	"\x12AnalyticsNodeRooms\x12\x17\n" +
	"\anode_id\x18\x01 \x01(\tR\x06nodeId\x12'\n" +
	"\x0fsequence_number\x18\x02 \x01(\x04R\x0esequenceNumber\x128\n" +
	"\ttimestamp\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\ttimestamp\x12,\n" +
	"\x05rooms\x18\x04 \x03(\v2\x16.livekit.AnalyticsRoomR\x05rooms\"Y\n" +
	"\n" +
	"ReportInfo\x12@\n" +
	"\rfeature_usage\x18\x01 \x01(\v2\x19.livekit.FeatureUsageInfoH\x00R\ffeatureUsageB\t\n" +
	"\amessage\"}\n" +
	"\tTimeRange\x129\n" +
	"\n" +
	"started_at\x18\x01 \x01(\v2\x1a.google.protobuf.TimestampR\tstartedAt\x125\n" +
	"\bended_at\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\aendedAt\"\xa0\x03\n" +
	"\x10FeatureUsageInfo\x12;\n" +
	"\afeature\x18\x01 \x01(\x0e2!.livekit.FeatureUsageInfo.FeatureR\afeature\x12\x1d\n" +
	"\n" +
	"project_id\x18\x02 \x01(\tR\tprojectId\x12\x1b\n" +
	"\troom_name\x18\x03 \x01(\tR\broomName\x12\x17\n" +
	"\aroom_id\x18\x04 \x01(\tR\x06roomId\x121\n" +
	"\x14participant_identity\x18\x05 \x01(\tR\x13participantIdentity\x12%\n" +
	"\x0eparticipant_id\x18\x06 \x01(\tR\rparticipantId\x12\x19\n" +
	"\btrack_id\x18\a \x01(\tR\atrackId\x123\n" +
	"\vtime_ranges\x18\b \x03(\v2\x12.livekit.TimeRangeR\n" +
	"timeRanges\"P\n" +
	"\aFeature\x12\x1c\n" +
	"\x18KRISP_NOISE_CANCELLATION\x10\x00\x12'\n" +
	"#KRISP_BACKGROUND_VOICE_CANCELLATION\x10\x01\"\x95\a\n" +
	"\x0eAPICallRequest\x12L\n" +
	"\x13create_room_request\x18\x01 \x01(\v2\x1a.livekit.CreateRoomRequestH\x00R\x11createRoomRequest\x12I\n" +
	"\x12list_rooms_request\x18\x02 \x01(\v2\x19.livekit.ListRoomsRequestH\x00R\x10listRoomsRequest\x12L\n" +
	"\x13delete_room_request\x18\x03 \x01(\v2\x1a.livekit.DeleteRoomRequestH\x00R\x11deleteRoomRequest\x12^\n" +
	"\x19list_participants_request\x18\x04 \x01(\v2 .livekit.ListParticipantsRequestH\x00R\x17listParticipantsRequest\x12^\n" +
	"\x19room_participant_identity\x18\x05 \x01(\v2 .livekit.RoomParticipantIdentityH\x00R\x17roomParticipantIdentity\x12V\n" +
	"\x17mute_room_track_request\x18\x06 \x01(\v2\x1d.livekit.MuteRoomTrackRequestH\x00R\x14muteRoomTrackRequest\x12a\n" +
	"\x1aupdate_participant_request\x18\a \x01(\v2!.livekit.UpdateParticipantRequestH\x00R\x18updateParticipantRequest\x12g\n" +
	"\x1cupdate_subscriptions_request\x18\b \x01(\v2#.livekit.UpdateSubscriptionsRequestH\x00R\x1aupdateSubscriptionsRequest\x12F\n" +
	"\x11send_data_request\x18\t \x01(\v2\x18.livekit.SendDataRequestH\x00R\x0fsendDataRequest\x12e\n" +
	"\x1cupdate_room_metadata_request\x18\n" +
	" \x01(\v2\".livekit.UpdateRoomMetadataRequestH\x00R\x19updateRoomMetadataRequestB\t\n" +
	"\amessage\"\xa3\x04\n" +
	"\vAPICallInfo\x12\x1d\n" +
	"\n" +
	"project_id\x18\x01 \x01(\tR\tprojectId\x121\n" +
	"\arequest\x18\x02 \x01(\v2\x17.livekit.APICallRequestR\arequest\x12\x18\n" +
	"\aservice\x18\x03 \x01(\tR\aservice\x12\x16\n" +
	"\x06method\x18\x04 \x01(\tR\x06method\x12\x17\n" +
	"\anode_id\x18\x05 \x01(\tR\x06nodeId\x12\x16\n" +
	"\x06status\x18\x06 \x01(\x05R\x06status\x12(\n" +
	"\x10twirp_error_code\x18\a \x01(\tR\x0etwirpErrorCode\x12.\n" +
	"\x13twirp_error_message\x18\b \x01(\tR\x11twirpErrorMessage\x12\x1b\n" +
	"\troom_name\x18\t \x01(\tR\broomName\x12\x17\n" +
	"\aroom_id\x18\n" +
	" \x01(\tR\x06roomId\x121\n" +
	"\x14participant_identity\x18\v \x01(\tR\x13participantIdentity\x12%\n" +
	"\x0eparticipant_id\x18\f \x01(\tR\rparticipantId\x12\x19\n" +
	"\btrack_id\x18\r \x01(\tR\atrackId\x129\n" +
	"\n" +
	"started_at\x18\x0e \x01(\v2\x1a.google.protobuf.TimestampR\tstartedAt\x12\x1f\n" +
	"\vduration_ns\x18\x0f \x01(\x03R\n" +
	"durationNs\"\xae\x06\n" +
	"\vWebhookInfo\x12\x19\n" +
	"\bevent_id\x18\x01 \x01(\tR\aeventId\x12\x14\n" +
	"\x05event\x18\x02 \x01(\tR\x05event\x12\x1d\n" +
	"\n" +
	"project_id\x18\x03 \x01(\tR\tprojectId\x12\x1b\n" +
	"\troom_name\x18\x04 \x01(\tR\broomName\x12\x17\n" +
	"\aroom_id\x18\x05 \x01(\tR\x06roomId\x121\n" +
	"\x14participant_identity\x18\x06 \x01(\tR\x13participantIdentity\x12%\n" +
	"\x0eparticipant_id\x18\a \x01(\tR\rparticipantId\x12\x19\n" +
	"\btrack_id\x18\b \x01(\tR\atrackId\x12\x1b\n" +
	"\tegress_id\x18\t \x01(\tR\begressId\x12\x1d\n" +
	"\n" +
	"ingress_id\x18\n" +
	" \x01(\tR\tingressId\x129\n" +
	"\n" +
	"created_at\x18\v \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x127\n" +
	"\tqueued_at\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\bqueuedAt\x12*\n" +
	"\x11queue_duration_ns\x18\r \x01(\x03R\x0fqueueDurationNs\x123\n" +
	"\asent_at\x18\x0e \x01(\v2\x1a.google.protobuf.TimestampR\x06sentAt\x12(\n" +
	"\x10send_duration_ns\x18\x0f \x01(\x03R\x0esendDurationNs\x12\x10\n" +
	"\x03url\x18\x10 \x01(\tR\x03url\x12\x1f\n" +
	"\vnum_dropped\x18\x11 \x01(\x05R\n" +
	"numDropped\x12\x1d\n" +
	"\n" +
	"is_dropped\x18\x12 \x01(\bR\tisDropped\x12%\n" +
	"\x0eservice_status\x18\x13 \x01(\tR\rserviceStatus\x12,\n" +
	"\x12service_error_code\x18\x14 \x01(\x05R\x10serviceErrorCode\x12#\n" +
	"\rservice_error\x18\x15 \x01(\tR\fserviceError\x12\x1d\n" +
	"\n" +
	"send_error\x18\x16 \x01(\tR\tsendError**\n" +
	"\n" +
	"StreamType\x12\f\n" +
	"\bUPSTREAM\x10\x00\x12\x0e\n" +
	"\n" +
	"DOWNSTREAM\x10\x01*\x8d\b\n" +
	"\x12AnalyticsEventType\x12\x10\n" +
	"\fROOM_CREATED\x10\x00\x12\x0e\n" +
	"\n" +
	"ROOM_ENDED\x10\x01\x12\x16\n" +
	"\x12PARTICIPANT_JOINED\x10\x02\x12\x14\n" +
	"\x10PARTICIPANT_LEFT\x10\x03\x12\x13\n" +
	"\x0fTRACK_PUBLISHED\x10\x04\x12\x1b\n" +
	"\x17TRACK_PUBLISH_REQUESTED\x10\x14\x12\x15\n" +
	"\x11TRACK_UNPUBLISHED\x10\x05\x12\x14\n" +
	"\x10TRACK_SUBSCRIBED\x10\x06\x12\x1d\n" +
	"\x19TRACK_SUBSCRIBE_REQUESTED\x10\x15\x12\x1a\n" +
	"\x16TRACK_SUBSCRIBE_FAILED\x10\x19\x12\x16\n" +
	"\x12TRACK_UNSUBSCRIBED\x10\a\x12\x1a\n" +
	"\x16TRACK_PUBLISHED_UPDATE\x10\n" +
	"\x12\x0f\n" +
	"\vTRACK_MUTED\x10\x17\x12\x11\n" +
	"\rTRACK_UNMUTED\x10\x18\x12\x17\n" +
	"\x13TRACK_PUBLISH_STATS\x10\x1a\x12\x19\n" +
	"\x15TRACK_SUBSCRIBE_STATS\x10\x1b\x12\x16\n" +
	"\x12PARTICIPANT_ACTIVE\x10\v\x12\x17\n" +
	"\x13PARTICIPANT_RESUMED\x10\x16\x12\x12\n" +
	"\x0eEGRESS_STARTED\x10\f\x12\x10\n" +
	"\fEGRESS_ENDED\x10\r\x12\x12\n" +
	"\x0eEGRESS_UPDATED\x10\x1c\x12&\n" +
	"\"TRACK_MAX_SUBSCRIBED_VIDEO_QUALITY\x10\x0e\x12\x0f\n" +
	"\vRECONNECTED\x10\x0f\x12\x13\n" +
	"\x0fINGRESS_CREATED\x10\x12\x12\x13\n" +
	"\x0fINGRESS_DELETED\x10\x13\x12\x13\n" +
	"\x0fINGRESS_STARTED\x10\x10\x12\x11\n" +
	"\rINGRESS_ENDED\x10\x11\x12\x13\n" +
	"\x0fINGRESS_UPDATED\x10\x1d\x12\x1d\n" +
	"\x19SIP_INBOUND_TRUNK_CREATED\x10\x1e\x12\x1d\n" +
	"\x19SIP_INBOUND_TRUNK_DELETED\x10\x1f\x12\x1e\n" +
	"\x1aSIP_OUTBOUND_TRUNK_CREATED\x10 \x12\x1e\n" +
	"\x1aSIP_OUTBOUND_TRUNK_DELETED\x10!\x12\x1d\n" +
	"\x19SIP_DISPATCH_RULE_CREATED\x10\"\x12\x1d\n" +
	"\x19SIP_DISPATCH_RULE_DELETED\x10#\x12\x1b\n" +
	"\x17SIP_PARTICIPANT_CREATED\x10$\x12\x15\n" +
	"\x11SIP_CALL_INCOMING\x10%\x12\x14\n" +
	"\x10SIP_CALL_STARTED\x10&\x12\x12\n" +
	"\x0eSIP_CALL_ENDED\x10'\x12\x1a\n" +
	"\x16SIP_TRANSFER_REQUESTED\x10+\x12\x19\n" +
	"\x15SIP_TRANSFER_COMPLETE\x10,\x12\n" +
	"\n" +
	"\x06REPORT\x10(\x12\f\n" +
	"\bAPI_CALL\x10)\x12\v\n" +
	"\aWEBHOOK\x10*BFZ#github.com/livekit/protocol/livekit\xaa\x02\rLiveKit.Proto\xea\x02\x0eLiveKit::Protob\x06proto3"

var (
	file_livekit_analytics_proto_rawDescOnce sync.Once
	file_livekit_analytics_proto_rawDescData []byte
)

func file_livekit_analytics_proto_rawDescGZIP() []byte {
	file_livekit_analytics_proto_rawDescOnce.Do(func() {
		file_livekit_analytics_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_livekit_analytics_proto_rawDesc), len(file_livekit_analytics_proto_rawDesc)))
	})
	return file_livekit_analytics_proto_rawDescData
}

var file_livekit_analytics_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_livekit_analytics_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_livekit_analytics_proto_goTypes = []any{
	(StreamType)(0),                    // 0: livekit.StreamType
	(AnalyticsEventType)(0),            // 1: livekit.AnalyticsEventType
	(FeatureUsageInfo_Feature)(0),      // 2: livekit.FeatureUsageInfo.Feature
	(*AnalyticsVideoLayer)(nil),        // 3: livekit.AnalyticsVideoLayer
	(*AnalyticsStream)(nil),            // 4: livekit.AnalyticsStream
	(*AnalyticsStat)(nil),              // 5: livekit.AnalyticsStat
	(*AnalyticsStats)(nil),             // 6: livekit.AnalyticsStats
	(*AnalyticsClientMeta)(nil),        // 7: livekit.AnalyticsClientMeta
	(*AnalyticsEvent)(nil),             // 8: livekit.AnalyticsEvent
	(*AnalyticsEvents)(nil),            // 9: livekit.AnalyticsEvents
	(*AnalyticsRoomParticipant)(nil),   // 10: livekit.AnalyticsRoomParticipant
	(*AnalyticsRoom)(nil),              // 11: livekit.AnalyticsRoom
	(*AnalyticsNodeRooms)(nil),         // 12: livekit.AnalyticsNodeRooms
	(*ReportInfo)(nil),                 // 13: livekit.ReportInfo
	(*TimeRange)(nil),                  // 14: livekit.TimeRange
	(*FeatureUsageInfo)(nil),           // 15: livekit.FeatureUsageInfo
	(*APICallRequest)(nil),             // 16: livekit.APICallRequest
	(*APICallInfo)(nil),                // 17: livekit.APICallInfo
	(*WebhookInfo)(nil),                // 18: livekit.WebhookInfo
	(*timestamppb.Timestamp)(nil),      // 19: google.protobuf.Timestamp
	(ReconnectReason)(0),               // 20: livekit.ReconnectReason
	(*Room)(nil),                       // 21: livekit.Room
	(*ParticipantInfo)(nil),            // 22: livekit.ParticipantInfo
	(*TrackInfo)(nil),                  // 23: livekit.TrackInfo
	(*ClientInfo)(nil),                 // 24: livekit.ClientInfo
	(VideoQuality)(0),                  // 25: livekit.VideoQuality
	(*EgressInfo)(nil),                 // 26: livekit.EgressInfo
	(*IngressInfo)(nil),                // 27: livekit.IngressInfo
	(*RTPStats)(nil),                   // 28: livekit.RTPStats
	(*SIPCallInfo)(nil),                // 29: livekit.SIPCallInfo
	(*SIPInboundTrunkInfo)(nil),        // 30: livekit.SIPInboundTrunkInfo
	(*SIPOutboundTrunkInfo)(nil),       // 31: livekit.SIPOutboundTrunkInfo
	(*SIPDispatchRuleInfo)(nil),        // 32: livekit.SIPDispatchRuleInfo
	(*SIPTransferInfo)(nil),            // 33: livekit.SIPTransferInfo
	(ParticipantInfo_State)(0),         // 34: livekit.ParticipantInfo.State
	(*CreateRoomRequest)(nil),          // 35: livekit.CreateRoomRequest
	(*ListRoomsRequest)(nil),           // 36: livekit.ListRoomsRequest
	(*DeleteRoomRequest)(nil),          // 37: livekit.DeleteRoomRequest
	(*ListParticipantsRequest)(nil),    // 38: livekit.ListParticipantsRequest
	(*RoomParticipantIdentity)(nil),    // 39: livekit.RoomParticipantIdentity
	(*MuteRoomTrackRequest)(nil),       // 40: livekit.MuteRoomTrackRequest
	(*UpdateParticipantRequest)(nil),   // 41: livekit.UpdateParticipantRequest
	(*UpdateSubscriptionsRequest)(nil), // 42: livekit.UpdateSubscriptionsRequest
	(*SendDataRequest)(nil),            // 43: livekit.SendDataRequest
	(*UpdateRoomMetadataRequest)(nil),  // 44: livekit.UpdateRoomMetadataRequest
}
var file_livekit_analytics_proto_depIdxs = []int32{
	3,  // 0: livekit.AnalyticsStream.video_layers:type_name -> livekit.AnalyticsVideoLayer
	19, // 1: livekit.AnalyticsStream.start_time:type_name -> google.protobuf.Timestamp
	19, // 2: livekit.AnalyticsStream.end_time:type_name -> google.protobuf.Timestamp
	0,  // 3: livekit.AnalyticsStat.kind:type_name -> livekit.StreamType
	19, // 4: livekit.AnalyticsStat.time_stamp:type_name -> google.protobuf.Timestamp
	4,  // 5: livekit.AnalyticsStat.streams:type_name -> livekit.AnalyticsStream
	5,  // 6: livekit.AnalyticsStats.stats:type_name -> livekit.AnalyticsStat
	20, // 7: livekit.AnalyticsClientMeta.reconnect_reason:type_name -> livekit.ReconnectReason
	1,  // 8: livekit.AnalyticsEvent.type:type_name -> livekit.AnalyticsEventType
	19, // 9: livekit.AnalyticsEvent.timestamp:type_name -> google.protobuf.Timestamp
	21, // 10: livekit.AnalyticsEvent.room:type_name -> livekit.Room
	22, // 11: livekit.AnalyticsEvent.participant:type_name -> livekit.ParticipantInfo
	23, // 12: livekit.AnalyticsEvent.track:type_name -> livekit.TrackInfo
	24, // 13: livekit.AnalyticsEvent.client_info:type_name -> livekit.ClientInfo
	7,  // 14: livekit.AnalyticsEvent.client_meta:type_name -> livekit.AnalyticsClientMeta
	25, // 15: livekit.AnalyticsEvent.max_subscribed_video_quality:type_name -> livekit.VideoQuality
	22, // 16: livekit.AnalyticsEvent.publisher:type_name -> livekit.ParticipantInfo
	26, // 17: livekit.AnalyticsEvent.egress:type_name -> livekit.EgressInfo
	27, // 18: livekit.AnalyticsEvent.ingress:type_name -> livekit.IngressInfo
	28, // 19: livekit.AnalyticsEvent.rtp_stats:type_name -> livekit.RTPStats
	29, // 20: livekit.AnalyticsEvent.sip_call:type_name -> livekit.SIPCallInfo
	30, // 21: livekit.AnalyticsEvent.sip_inbound_trunk:type_name -> livekit.SIPInboundTrunkInfo
	31, // 22: livekit.AnalyticsEvent.sip_outbound_trunk:type_name -> livekit.SIPOutboundTrunkInfo
	32, // 23: livekit.AnalyticsEvent.sip_dispatch_rule:type_name -> livekit.SIPDispatchRuleInfo
	33, // 24: livekit.AnalyticsEvent.sip_transfer:type_name -> livekit.SIPTransferInfo
	13, // 25: livekit.AnalyticsEvent.report:type_name -> livekit.ReportInfo
	17, // 26: livekit.AnalyticsEvent.api_call:type_name -> livekit.APICallInfo
	18, // 27: livekit.AnalyticsEvent.webhook:type_name -> livekit.WebhookInfo
	8,  // 28: livekit.AnalyticsEvents.events:type_name -> livekit.AnalyticsEvent
	34, // 29: livekit.AnalyticsRoomParticipant.state:type_name -> livekit.ParticipantInfo.State
	19, // 30: livekit.AnalyticsRoomParticipant.joined_at:type_name -> google.protobuf.Timestamp
	19, // 31: livekit.AnalyticsRoom.created_at:type_name -> google.protobuf.Timestamp
	10, // 32: livekit.AnalyticsRoom.participants:type_name -> livekit.AnalyticsRoomParticipant
	19, // 33: livekit.AnalyticsNodeRooms.timestamp:type_name -> google.protobuf.Timestamp
	11, // 34: livekit.AnalyticsNodeRooms.rooms:type_name -> livekit.AnalyticsRoom
	15, // 35: livekit.ReportInfo.feature_usage:type_name -> livekit.FeatureUsageInfo
	19, // 36: livekit.TimeRange.started_at:type_name -> google.protobuf.Timestamp
	19, // 37: livekit.TimeRange.ended_at:type_name -> google.protobuf.Timestamp
	2,  // 38: livekit.FeatureUsageInfo.feature:type_name -> livekit.FeatureUsageInfo.Feature
	14, // 39: livekit.FeatureUsageInfo.time_ranges:type_name -> livekit.TimeRange
	35, // 40: livekit.APICallRequest.create_room_request:type_name -> livekit.CreateRoomRequest
	36, // 41: livekit.APICallRequest.list_rooms_request:type_name -> livekit.ListRoomsRequest
	37, // 42: livekit.APICallRequest.delete_room_request:type_name -> livekit.DeleteRoomRequest
	38, // 43: livekit.APICallRequest.list_participants_request:type_name -> livekit.ListParticipantsRequest
	39, // 44: livekit.APICallRequest.room_participant_identity:type_name -> livekit.RoomParticipantIdentity
	40, // 45: livekit.APICallRequest.mute_room_track_request:type_name -> livekit.MuteRoomTrackRequest
	41, // 46: livekit.APICallRequest.update_participant_request:type_name -> livekit.UpdateParticipantRequest
	42, // 47: livekit.APICallRequest.update_subscriptions_request:type_name -> livekit.UpdateSubscriptionsRequest
	43, // 48: livekit.APICallRequest.send_data_request:type_name -> livekit.SendDataRequest
	44, // 49: livekit.APICallRequest.update_room_metadata_request:type_name -> livekit.UpdateRoomMetadataRequest
	16, // 50: livekit.APICallInfo.request:type_name -> livekit.APICallRequest
	19, // 51: livekit.APICallInfo.started_at:type_name -> google.protobuf.Timestamp
	19, // 52: livekit.WebhookInfo.created_at:type_name -> google.protobuf.Timestamp
	19, // 53: livekit.WebhookInfo.queued_at:type_name -> google.protobuf.Timestamp
	19, // 54: livekit.WebhookInfo.sent_at:type_name -> google.protobuf.Timestamp
	55, // [55:55] is the sub-list for method output_type
	55, // [55:55] is the sub-list for method input_type
	55, // [55:55] is the sub-list for extension type_name
	55, // [55:55] is the sub-list for extension extendee
	0,  // [0:55] is the sub-list for field type_name
}

func init() { file_livekit_analytics_proto_init() }
func file_livekit_analytics_proto_init() {
	if File_livekit_analytics_proto != nil {
		return
	}
	file_livekit_models_proto_init()
	file_livekit_egress_proto_init()
	file_livekit_ingress_proto_init()
	file_livekit_sip_proto_init()
	file_livekit_room_proto_init()
	file_livekit_analytics_proto_msgTypes[4].OneofWrappers = []any{}
	file_livekit_analytics_proto_msgTypes[10].OneofWrappers = []any{
		(*ReportInfo_FeatureUsage)(nil),
	}
	file_livekit_analytics_proto_msgTypes[13].OneofWrappers = []any{
		(*APICallRequest_CreateRoomRequest)(nil),
		(*APICallRequest_ListRoomsRequest)(nil),
		(*APICallRequest_DeleteRoomRequest)(nil),
		(*APICallRequest_ListParticipantsRequest)(nil),
		(*APICallRequest_RoomParticipantIdentity)(nil),
		(*APICallRequest_MuteRoomTrackRequest)(nil),
		(*APICallRequest_UpdateParticipantRequest)(nil),
		(*APICallRequest_UpdateSubscriptionsRequest)(nil),
		(*APICallRequest_SendDataRequest)(nil),
		(*APICallRequest_UpdateRoomMetadataRequest)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_livekit_analytics_proto_rawDesc), len(file_livekit_analytics_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_livekit_analytics_proto_goTypes,
		DependencyIndexes: file_livekit_analytics_proto_depIdxs,
		EnumInfos:         file_livekit_analytics_proto_enumTypes,
		MessageInfos:      file_livekit_analytics_proto_msgTypes,
	}.Build()
	File_livekit_analytics_proto = out.File
	file_livekit_analytics_proto_goTypes = nil
	file_livekit_analytics_proto_depIdxs = nil
}
