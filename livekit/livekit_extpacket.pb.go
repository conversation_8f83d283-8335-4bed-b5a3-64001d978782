// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: livekit_extpacket.proto

package livekit

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ExtPacket struct {
	state                protoimpl.MessageState   `protogen:"open.v1"`
	VideoLayer           *VideoLayerSVC           `protobuf:"bytes,1,opt,name=video_layer,json=videoLayer,proto3" json:"video_layer,omitempty"`
	Arrival              int64                    `protobuf:"varint,2,opt,name=arrival,proto3" json:"arrival,omitempty"`
	ExtSequenceNumber    uint64                   `protobuf:"varint,3,opt,name=ext_sequence_number,json=extSequenceNumber,proto3" json:"ext_sequence_number,omitempty"`
	ExtTimestamp         uint64                   `protobuf:"varint,4,opt,name=ext_timestamp,json=extTimestamp,proto3" json:"ext_timestamp,omitempty"`
	KeyFrame             bool                     `protobuf:"varint,5,opt,name=key_frame,json=keyFrame,proto3" json:"key_frame,omitempty"`
	MimeType             int32                    `protobuf:"varint,6,opt,name=mime_type,json=mimeType,proto3" json:"mime_type,omitempty"`
	RawPacket            []byte                   `protobuf:"bytes,7,opt,name=raw_packet,json=rawPacket,proto3" json:"raw_packet,omitempty"`
	DependencyDescriptor *ExtDependencyDescriptor `protobuf:"bytes,8,opt,name=dependency_descriptor,json=dependencyDescriptor,proto3,oneof" json:"dependency_descriptor,omitempty"`
	AbsCaptureTimeExt    []byte                   `protobuf:"bytes,9,opt,name=abs_capture_time_ext,json=absCaptureTimeExt,proto3,oneof" json:"abs_capture_time_ext,omitempty"`
	IsOutOfOrder         bool                     `protobuf:"varint,10,opt,name=is_out_of_order,json=isOutOfOrder,proto3" json:"is_out_of_order,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *ExtPacket) Reset() {
	*x = ExtPacket{}
	mi := &file_livekit_extpacket_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExtPacket) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtPacket) ProtoMessage() {}

func (x *ExtPacket) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_extpacket_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtPacket.ProtoReflect.Descriptor instead.
func (*ExtPacket) Descriptor() ([]byte, []int) {
	return file_livekit_extpacket_proto_rawDescGZIP(), []int{0}
}

func (x *ExtPacket) GetVideoLayer() *VideoLayerSVC {
	if x != nil {
		return x.VideoLayer
	}
	return nil
}

func (x *ExtPacket) GetArrival() int64 {
	if x != nil {
		return x.Arrival
	}
	return 0
}

func (x *ExtPacket) GetExtSequenceNumber() uint64 {
	if x != nil {
		return x.ExtSequenceNumber
	}
	return 0
}

func (x *ExtPacket) GetExtTimestamp() uint64 {
	if x != nil {
		return x.ExtTimestamp
	}
	return 0
}

func (x *ExtPacket) GetKeyFrame() bool {
	if x != nil {
		return x.KeyFrame
	}
	return false
}

func (x *ExtPacket) GetMimeType() int32 {
	if x != nil {
		return x.MimeType
	}
	return 0
}

func (x *ExtPacket) GetRawPacket() []byte {
	if x != nil {
		return x.RawPacket
	}
	return nil
}

func (x *ExtPacket) GetDependencyDescriptor() *ExtDependencyDescriptor {
	if x != nil {
		return x.DependencyDescriptor
	}
	return nil
}

func (x *ExtPacket) GetAbsCaptureTimeExt() []byte {
	if x != nil {
		return x.AbsCaptureTimeExt
	}
	return nil
}

func (x *ExtPacket) GetIsOutOfOrder() bool {
	if x != nil {
		return x.IsOutOfOrder
	}
	return false
}

type VideoLayerSVC struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Spatial       int32                  `protobuf:"varint,1,opt,name=spatial,proto3" json:"spatial,omitempty"`
	Temporal      int32                  `protobuf:"varint,2,opt,name=temporal,proto3" json:"temporal,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoLayerSVC) Reset() {
	*x = VideoLayerSVC{}
	mi := &file_livekit_extpacket_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoLayerSVC) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoLayerSVC) ProtoMessage() {}

func (x *VideoLayerSVC) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_extpacket_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoLayerSVC.ProtoReflect.Descriptor instead.
func (*VideoLayerSVC) Descriptor() ([]byte, []int) {
	return file_livekit_extpacket_proto_rawDescGZIP(), []int{1}
}

func (x *VideoLayerSVC) GetSpatial() int32 {
	if x != nil {
		return x.Spatial
	}
	return 0
}

func (x *VideoLayerSVC) GetTemporal() int32 {
	if x != nil {
		return x.Temporal
	}
	return 0
}

type ExtDependencyDescriptor struct {
	state                      protoimpl.MessageState              `protogen:"open.v1"`
	Descriptor_                *DependencyDescriptor               `protobuf:"bytes,1,opt,name=descriptor,proto3,oneof" json:"descriptor,omitempty"`
	DecodeTargets              []*DependencyDescriptorDecodeTarget `protobuf:"bytes,2,rep,name=decode_targets,json=decodeTargets,proto3" json:"decode_targets,omitempty"`
	StructureUpdated           bool                                `protobuf:"varint,3,opt,name=structure_updated,json=structureUpdated,proto3" json:"structure_updated,omitempty"`
	ActiveDecodeTargetsUpdated bool                                `protobuf:"varint,4,opt,name=active_decode_targets_updated,json=activeDecodeTargetsUpdated,proto3" json:"active_decode_targets_updated,omitempty"`
	Integrity                  bool                                `protobuf:"varint,5,opt,name=integrity,proto3" json:"integrity,omitempty"`
	ExtFrameNum                uint64                              `protobuf:"varint,6,opt,name=ext_frame_num,json=extFrameNum,proto3" json:"ext_frame_num,omitempty"`
	ExtKeyFrameNum             uint64                              `protobuf:"varint,7,opt,name=ext_key_frame_num,json=extKeyFrameNum,proto3" json:"ext_key_frame_num,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *ExtDependencyDescriptor) Reset() {
	*x = ExtDependencyDescriptor{}
	mi := &file_livekit_extpacket_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExtDependencyDescriptor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtDependencyDescriptor) ProtoMessage() {}

func (x *ExtDependencyDescriptor) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_extpacket_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtDependencyDescriptor.ProtoReflect.Descriptor instead.
func (*ExtDependencyDescriptor) Descriptor() ([]byte, []int) {
	return file_livekit_extpacket_proto_rawDescGZIP(), []int{2}
}

func (x *ExtDependencyDescriptor) GetDescriptor_() *DependencyDescriptor {
	if x != nil {
		return x.Descriptor_
	}
	return nil
}

func (x *ExtDependencyDescriptor) GetDecodeTargets() []*DependencyDescriptorDecodeTarget {
	if x != nil {
		return x.DecodeTargets
	}
	return nil
}

func (x *ExtDependencyDescriptor) GetStructureUpdated() bool {
	if x != nil {
		return x.StructureUpdated
	}
	return false
}

func (x *ExtDependencyDescriptor) GetActiveDecodeTargetsUpdated() bool {
	if x != nil {
		return x.ActiveDecodeTargetsUpdated
	}
	return false
}

func (x *ExtDependencyDescriptor) GetIntegrity() bool {
	if x != nil {
		return x.Integrity
	}
	return false
}

func (x *ExtDependencyDescriptor) GetExtFrameNum() uint64 {
	if x != nil {
		return x.ExtFrameNum
	}
	return 0
}

func (x *ExtDependencyDescriptor) GetExtKeyFrameNum() uint64 {
	if x != nil {
		return x.ExtKeyFrameNum
	}
	return 0
}

type DependencyDescriptorDecodeTarget struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Target        int32                  `protobuf:"varint,1,opt,name=target,proto3" json:"target,omitempty"`
	Layer         *VideoLayerSVC         `protobuf:"bytes,2,opt,name=layer,proto3" json:"layer,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DependencyDescriptorDecodeTarget) Reset() {
	*x = DependencyDescriptorDecodeTarget{}
	mi := &file_livekit_extpacket_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DependencyDescriptorDecodeTarget) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DependencyDescriptorDecodeTarget) ProtoMessage() {}

func (x *DependencyDescriptorDecodeTarget) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_extpacket_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DependencyDescriptorDecodeTarget.ProtoReflect.Descriptor instead.
func (*DependencyDescriptorDecodeTarget) Descriptor() ([]byte, []int) {
	return file_livekit_extpacket_proto_rawDescGZIP(), []int{3}
}

func (x *DependencyDescriptorDecodeTarget) GetTarget() int32 {
	if x != nil {
		return x.Target
	}
	return 0
}

func (x *DependencyDescriptorDecodeTarget) GetLayer() *VideoLayerSVC {
	if x != nil {
		return x.Layer
	}
	return nil
}

type DependencyDescriptor struct {
	state                      protoimpl.MessageState    `protogen:"open.v1"`
	FirstPacketInFrame         bool                      `protobuf:"varint,1,opt,name=first_packet_in_frame,json=firstPacketInFrame,proto3" json:"first_packet_in_frame,omitempty"`
	LastPacketInFrame          bool                      `protobuf:"varint,2,opt,name=last_packet_in_frame,json=lastPacketInFrame,proto3" json:"last_packet_in_frame,omitempty"`
	FrameNumber                uint32                    `protobuf:"varint,3,opt,name=frame_number,json=frameNumber,proto3" json:"frame_number,omitempty"`
	FrameDependencies          *FrameDependencyTemplate  `protobuf:"bytes,4,opt,name=frame_dependencies,json=frameDependencies,proto3,oneof" json:"frame_dependencies,omitempty"`
	Resolution                 *RenderResolution         `protobuf:"bytes,5,opt,name=resolution,proto3,oneof" json:"resolution,omitempty"`
	ActiveDecodeTargetsBitmask *uint32                   `protobuf:"varint,6,opt,name=active_decode_targets_bitmask,json=activeDecodeTargetsBitmask,proto3,oneof" json:"active_decode_targets_bitmask,omitempty"`
	AttachedStructure          *FrameDependencyStructure `protobuf:"bytes,7,opt,name=attached_structure,json=attachedStructure,proto3,oneof" json:"attached_structure,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *DependencyDescriptor) Reset() {
	*x = DependencyDescriptor{}
	mi := &file_livekit_extpacket_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DependencyDescriptor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DependencyDescriptor) ProtoMessage() {}

func (x *DependencyDescriptor) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_extpacket_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DependencyDescriptor.ProtoReflect.Descriptor instead.
func (*DependencyDescriptor) Descriptor() ([]byte, []int) {
	return file_livekit_extpacket_proto_rawDescGZIP(), []int{4}
}

func (x *DependencyDescriptor) GetFirstPacketInFrame() bool {
	if x != nil {
		return x.FirstPacketInFrame
	}
	return false
}

func (x *DependencyDescriptor) GetLastPacketInFrame() bool {
	if x != nil {
		return x.LastPacketInFrame
	}
	return false
}

func (x *DependencyDescriptor) GetFrameNumber() uint32 {
	if x != nil {
		return x.FrameNumber
	}
	return 0
}

func (x *DependencyDescriptor) GetFrameDependencies() *FrameDependencyTemplate {
	if x != nil {
		return x.FrameDependencies
	}
	return nil
}

func (x *DependencyDescriptor) GetResolution() *RenderResolution {
	if x != nil {
		return x.Resolution
	}
	return nil
}

func (x *DependencyDescriptor) GetActiveDecodeTargetsBitmask() uint32 {
	if x != nil && x.ActiveDecodeTargetsBitmask != nil {
		return *x.ActiveDecodeTargetsBitmask
	}
	return 0
}

func (x *DependencyDescriptor) GetAttachedStructure() *FrameDependencyStructure {
	if x != nil {
		return x.AttachedStructure
	}
	return nil
}

type FrameDependencyTemplate struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	SpatialId               int32                  `protobuf:"varint,1,opt,name=spatial_id,json=spatialId,proto3" json:"spatial_id,omitempty"`
	TemporalId              int32                  `protobuf:"varint,2,opt,name=temporal_id,json=temporalId,proto3" json:"temporal_id,omitempty"`
	DecodeTargetIndications []int32                `protobuf:"varint,3,rep,packed,name=decode_target_indications,json=decodeTargetIndications,proto3" json:"decode_target_indications,omitempty"`
	FrameDiffs              []int32                `protobuf:"varint,4,rep,packed,name=frame_diffs,json=frameDiffs,proto3" json:"frame_diffs,omitempty"`
	ChainDiffs              []int32                `protobuf:"varint,5,rep,packed,name=chain_diffs,json=chainDiffs,proto3" json:"chain_diffs,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *FrameDependencyTemplate) Reset() {
	*x = FrameDependencyTemplate{}
	mi := &file_livekit_extpacket_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FrameDependencyTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FrameDependencyTemplate) ProtoMessage() {}

func (x *FrameDependencyTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_extpacket_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FrameDependencyTemplate.ProtoReflect.Descriptor instead.
func (*FrameDependencyTemplate) Descriptor() ([]byte, []int) {
	return file_livekit_extpacket_proto_rawDescGZIP(), []int{5}
}

func (x *FrameDependencyTemplate) GetSpatialId() int32 {
	if x != nil {
		return x.SpatialId
	}
	return 0
}

func (x *FrameDependencyTemplate) GetTemporalId() int32 {
	if x != nil {
		return x.TemporalId
	}
	return 0
}

func (x *FrameDependencyTemplate) GetDecodeTargetIndications() []int32 {
	if x != nil {
		return x.DecodeTargetIndications
	}
	return nil
}

func (x *FrameDependencyTemplate) GetFrameDiffs() []int32 {
	if x != nil {
		return x.FrameDiffs
	}
	return nil
}

func (x *FrameDependencyTemplate) GetChainDiffs() []int32 {
	if x != nil {
		return x.ChainDiffs
	}
	return nil
}

type RenderResolution struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Width         int32                  `protobuf:"varint,1,opt,name=width,proto3" json:"width,omitempty"`
	Height        int32                  `protobuf:"varint,2,opt,name=height,proto3" json:"height,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RenderResolution) Reset() {
	*x = RenderResolution{}
	mi := &file_livekit_extpacket_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RenderResolution) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RenderResolution) ProtoMessage() {}

func (x *RenderResolution) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_extpacket_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RenderResolution.ProtoReflect.Descriptor instead.
func (*RenderResolution) Descriptor() ([]byte, []int) {
	return file_livekit_extpacket_proto_rawDescGZIP(), []int{6}
}

func (x *RenderResolution) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *RenderResolution) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

type FrameDependencyStructure struct {
	state                        protoimpl.MessageState     `protogen:"open.v1"`
	StructureId                  int32                      `protobuf:"varint,1,opt,name=structure_id,json=structureId,proto3" json:"structure_id,omitempty"`
	NumDecodeTargets             int32                      `protobuf:"varint,2,opt,name=num_decode_targets,json=numDecodeTargets,proto3" json:"num_decode_targets,omitempty"`
	NumChains                    int32                      `protobuf:"varint,3,opt,name=num_chains,json=numChains,proto3" json:"num_chains,omitempty"`
	DecodeTargetProtectedByChain []int32                    `protobuf:"varint,4,rep,packed,name=decode_target_protected_by_chain,json=decodeTargetProtectedByChain,proto3" json:"decode_target_protected_by_chain,omitempty"`
	Resolutions                  []*RenderResolution        `protobuf:"bytes,5,rep,name=resolutions,proto3" json:"resolutions,omitempty"`
	Templates                    []*FrameDependencyTemplate `protobuf:"bytes,6,rep,name=templates,proto3" json:"templates,omitempty"`
	unknownFields                protoimpl.UnknownFields
	sizeCache                    protoimpl.SizeCache
}

func (x *FrameDependencyStructure) Reset() {
	*x = FrameDependencyStructure{}
	mi := &file_livekit_extpacket_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FrameDependencyStructure) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FrameDependencyStructure) ProtoMessage() {}

func (x *FrameDependencyStructure) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_extpacket_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FrameDependencyStructure.ProtoReflect.Descriptor instead.
func (*FrameDependencyStructure) Descriptor() ([]byte, []int) {
	return file_livekit_extpacket_proto_rawDescGZIP(), []int{7}
}

func (x *FrameDependencyStructure) GetStructureId() int32 {
	if x != nil {
		return x.StructureId
	}
	return 0
}

func (x *FrameDependencyStructure) GetNumDecodeTargets() int32 {
	if x != nil {
		return x.NumDecodeTargets
	}
	return 0
}

func (x *FrameDependencyStructure) GetNumChains() int32 {
	if x != nil {
		return x.NumChains
	}
	return 0
}

func (x *FrameDependencyStructure) GetDecodeTargetProtectedByChain() []int32 {
	if x != nil {
		return x.DecodeTargetProtectedByChain
	}
	return nil
}

func (x *FrameDependencyStructure) GetResolutions() []*RenderResolution {
	if x != nil {
		return x.Resolutions
	}
	return nil
}

func (x *FrameDependencyStructure) GetTemplates() []*FrameDependencyTemplate {
	if x != nil {
		return x.Templates
	}
	return nil
}

var File_livekit_extpacket_proto protoreflect.FileDescriptor

const file_livekit_extpacket_proto_rawDesc = "" +
	"\n" +
	"\x17livekit_extpacket.proto\x12\alivekit\"\xf8\x03\n" +
	"\tExtPacket\x127\n" +
	"\vvideo_layer\x18\x01 \x01(\v2\x16.livekit.VideoLayerSVCR\n" +
	"videoLayer\x12\x18\n" +
	"\aarrival\x18\x02 \x01(\x03R\aarrival\x12.\n" +
	"\x13ext_sequence_number\x18\x03 \x01(\x04R\x11extSequenceNumber\x12#\n" +
	"\rext_timestamp\x18\x04 \x01(\x04R\fextTimestamp\x12\x1b\n" +
	"\tkey_frame\x18\x05 \x01(\bR\bkeyFrame\x12\x1b\n" +
	"\tmime_type\x18\x06 \x01(\x05R\bmimeType\x12\x1d\n" +
	"\n" +
	"raw_packet\x18\a \x01(\fR\trawPacket\x12Z\n" +
	"\x15dependency_descriptor\x18\b \x01(\v2 .livekit.ExtDependencyDescriptorH\x00R\x14dependencyDescriptor\x88\x01\x01\x124\n" +
	"\x14abs_capture_time_ext\x18\t \x01(\fH\x01R\x11absCaptureTimeExt\x88\x01\x01\x12%\n" +
	"\x0fis_out_of_order\x18\n" +
	" \x01(\bR\fisOutOfOrderB\x18\n" +
	"\x16_dependency_descriptorB\x17\n" +
	"\x15_abs_capture_time_ext\"E\n" +
	"\rVideoLayerSVC\x12\x18\n" +
	"\aspatial\x18\x01 \x01(\x05R\aspatial\x12\x1a\n" +
	"\btemporal\x18\x02 \x01(\x05R\btemporal\"\x9b\x03\n" +
	"\x17ExtDependencyDescriptor\x12B\n" +
	"\n" +
	"descriptor\x18\x01 \x01(\v2\x1d.livekit.DependencyDescriptorH\x00R\n" +
	"descriptor\x88\x01\x01\x12P\n" +
	"\x0edecode_targets\x18\x02 \x03(\v2).livekit.DependencyDescriptorDecodeTargetR\rdecodeTargets\x12+\n" +
	"\x11structure_updated\x18\x03 \x01(\bR\x10structureUpdated\x12A\n" +
	"\x1dactive_decode_targets_updated\x18\x04 \x01(\bR\x1aactiveDecodeTargetsUpdated\x12\x1c\n" +
	"\tintegrity\x18\x05 \x01(\bR\tintegrity\x12\"\n" +
	"\rext_frame_num\x18\x06 \x01(\x04R\vextFrameNum\x12)\n" +
	"\x11ext_key_frame_num\x18\a \x01(\x04R\x0eextKeyFrameNumB\r\n" +
	"\v_descriptor\"h\n" +
	" DependencyDescriptorDecodeTarget\x12\x16\n" +
	"\x06target\x18\x01 \x01(\x05R\x06target\x12,\n" +
	"\x05layer\x18\x02 \x01(\v2\x16.livekit.VideoLayerSVCR\x05layer\"\xb1\x04\n" +
	"\x14DependencyDescriptor\x121\n" +
	"\x15first_packet_in_frame\x18\x01 \x01(\bR\x12firstPacketInFrame\x12/\n" +
	"\x14last_packet_in_frame\x18\x02 \x01(\bR\x11lastPacketInFrame\x12!\n" +
	"\fframe_number\x18\x03 \x01(\rR\vframeNumber\x12T\n" +
	"\x12frame_dependencies\x18\x04 \x01(\v2 .livekit.FrameDependencyTemplateH\x00R\x11frameDependencies\x88\x01\x01\x12>\n" +
	"\n" +
	"resolution\x18\x05 \x01(\v2\x19.livekit.RenderResolutionH\x01R\n" +
	"resolution\x88\x01\x01\x12F\n" +
	"\x1dactive_decode_targets_bitmask\x18\x06 \x01(\rH\x02R\x1aactiveDecodeTargetsBitmask\x88\x01\x01\x12U\n" +
	"\x12attached_structure\x18\a \x01(\v2!.livekit.FrameDependencyStructureH\x03R\x11attachedStructure\x88\x01\x01B\x15\n" +
	"\x13_frame_dependenciesB\r\n" +
	"\v_resolutionB \n" +
	"\x1e_active_decode_targets_bitmaskB\x15\n" +
	"\x13_attached_structure\"\xd7\x01\n" +
	"\x17FrameDependencyTemplate\x12\x1d\n" +
	"\n" +
	"spatial_id\x18\x01 \x01(\x05R\tspatialId\x12\x1f\n" +
	"\vtemporal_id\x18\x02 \x01(\x05R\n" +
	"temporalId\x12:\n" +
	"\x19decode_target_indications\x18\x03 \x03(\x05R\x17decodeTargetIndications\x12\x1f\n" +
	"\vframe_diffs\x18\x04 \x03(\x05R\n" +
	"frameDiffs\x12\x1f\n" +
	"\vchain_diffs\x18\x05 \x03(\x05R\n" +
	"chainDiffs\"@\n" +
	"\x10RenderResolution\x12\x14\n" +
	"\x05width\x18\x01 \x01(\x05R\x05width\x12\x16\n" +
	"\x06height\x18\x02 \x01(\x05R\x06height\"\xcf\x02\n" +
	"\x18FrameDependencyStructure\x12!\n" +
	"\fstructure_id\x18\x01 \x01(\x05R\vstructureId\x12,\n" +
	"\x12num_decode_targets\x18\x02 \x01(\x05R\x10numDecodeTargets\x12\x1d\n" +
	"\n" +
	"num_chains\x18\x03 \x01(\x05R\tnumChains\x12F\n" +
	" decode_target_protected_by_chain\x18\x04 \x03(\x05R\x1cdecodeTargetProtectedByChain\x12;\n" +
	"\vresolutions\x18\x05 \x03(\v2\x19.livekit.RenderResolutionR\vresolutions\x12>\n" +
	"\ttemplates\x18\x06 \x03(\v2 .livekit.FrameDependencyTemplateR\ttemplatesB%Z#github.com/livekit/protocol/livekitb\x06proto3"

var (
	file_livekit_extpacket_proto_rawDescOnce sync.Once
	file_livekit_extpacket_proto_rawDescData []byte
)

func file_livekit_extpacket_proto_rawDescGZIP() []byte {
	file_livekit_extpacket_proto_rawDescOnce.Do(func() {
		file_livekit_extpacket_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_livekit_extpacket_proto_rawDesc), len(file_livekit_extpacket_proto_rawDesc)))
	})
	return file_livekit_extpacket_proto_rawDescData
}

var file_livekit_extpacket_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_livekit_extpacket_proto_goTypes = []any{
	(*ExtPacket)(nil),                        // 0: livekit.ExtPacket
	(*VideoLayerSVC)(nil),                    // 1: livekit.VideoLayerSVC
	(*ExtDependencyDescriptor)(nil),          // 2: livekit.ExtDependencyDescriptor
	(*DependencyDescriptorDecodeTarget)(nil), // 3: livekit.DependencyDescriptorDecodeTarget
	(*DependencyDescriptor)(nil),             // 4: livekit.DependencyDescriptor
	(*FrameDependencyTemplate)(nil),          // 5: livekit.FrameDependencyTemplate
	(*RenderResolution)(nil),                 // 6: livekit.RenderResolution
	(*FrameDependencyStructure)(nil),         // 7: livekit.FrameDependencyStructure
}
var file_livekit_extpacket_proto_depIdxs = []int32{
	1,  // 0: livekit.ExtPacket.video_layer:type_name -> livekit.VideoLayerSVC
	2,  // 1: livekit.ExtPacket.dependency_descriptor:type_name -> livekit.ExtDependencyDescriptor
	4,  // 2: livekit.ExtDependencyDescriptor.descriptor:type_name -> livekit.DependencyDescriptor
	3,  // 3: livekit.ExtDependencyDescriptor.decode_targets:type_name -> livekit.DependencyDescriptorDecodeTarget
	1,  // 4: livekit.DependencyDescriptorDecodeTarget.layer:type_name -> livekit.VideoLayerSVC
	5,  // 5: livekit.DependencyDescriptor.frame_dependencies:type_name -> livekit.FrameDependencyTemplate
	6,  // 6: livekit.DependencyDescriptor.resolution:type_name -> livekit.RenderResolution
	7,  // 7: livekit.DependencyDescriptor.attached_structure:type_name -> livekit.FrameDependencyStructure
	6,  // 8: livekit.FrameDependencyStructure.resolutions:type_name -> livekit.RenderResolution
	5,  // 9: livekit.FrameDependencyStructure.templates:type_name -> livekit.FrameDependencyTemplate
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_livekit_extpacket_proto_init() }
func file_livekit_extpacket_proto_init() {
	if File_livekit_extpacket_proto != nil {
		return
	}
	file_livekit_extpacket_proto_msgTypes[0].OneofWrappers = []any{}
	file_livekit_extpacket_proto_msgTypes[2].OneofWrappers = []any{}
	file_livekit_extpacket_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_livekit_extpacket_proto_rawDesc), len(file_livekit_extpacket_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_livekit_extpacket_proto_goTypes,
		DependencyIndexes: file_livekit_extpacket_proto_depIdxs,
		MessageInfos:      file_livekit_extpacket_proto_msgTypes,
	}.Build()
	File_livekit_extpacket_proto = out.File
	file_livekit_extpacket_proto_goTypes = nil
	file_livekit_extpacket_proto_depIdxs = nil
}
