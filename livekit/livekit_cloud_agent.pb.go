// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: livekit_cloud_agent.proto

package livekit

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AgentSecret struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Value         []byte                 `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AgentSecret) Reset() {
	*x = AgentSecret{}
	mi := &file_livekit_cloud_agent_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgentSecret) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentSecret) ProtoMessage() {}

func (x *AgentSecret) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_cloud_agent_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentSecret.ProtoReflect.Descriptor instead.
func (*AgentSecret) Descriptor() ([]byte, []int) {
	return file_livekit_cloud_agent_proto_rawDescGZIP(), []int{0}
}

func (x *AgentSecret) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AgentSecret) GetValue() []byte {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *AgentSecret) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AgentSecret) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type CreateAgentRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Deprecated: Marked as deprecated in livekit_cloud_agent.proto.
	AgentName string         `protobuf:"bytes,1,opt,name=agent_name,json=agentName,proto3" json:"agent_name,omitempty"`
	Secrets   []*AgentSecret `protobuf:"bytes,2,rep,name=secrets,proto3" json:"secrets,omitempty"`
	// Deprecated: Marked as deprecated in livekit_cloud_agent.proto.
	Replicas int32 `protobuf:"varint,3,opt,name=replicas,proto3" json:"replicas,omitempty"`
	// Deprecated: Marked as deprecated in livekit_cloud_agent.proto.
	MaxReplicas int32 `protobuf:"varint,4,opt,name=max_replicas,json=maxReplicas,proto3" json:"max_replicas,omitempty"`
	// Deprecated: Marked as deprecated in livekit_cloud_agent.proto.
	CpuReq        string   `protobuf:"bytes,5,opt,name=cpu_req,json=cpuReq,proto3" json:"cpu_req,omitempty"`
	Regions       []string `protobuf:"bytes,6,rep,name=regions,proto3" json:"regions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAgentRequest) Reset() {
	*x = CreateAgentRequest{}
	mi := &file_livekit_cloud_agent_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAgentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAgentRequest) ProtoMessage() {}

func (x *CreateAgentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_cloud_agent_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAgentRequest.ProtoReflect.Descriptor instead.
func (*CreateAgentRequest) Descriptor() ([]byte, []int) {
	return file_livekit_cloud_agent_proto_rawDescGZIP(), []int{1}
}

// Deprecated: Marked as deprecated in livekit_cloud_agent.proto.
func (x *CreateAgentRequest) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

func (x *CreateAgentRequest) GetSecrets() []*AgentSecret {
	if x != nil {
		return x.Secrets
	}
	return nil
}

// Deprecated: Marked as deprecated in livekit_cloud_agent.proto.
func (x *CreateAgentRequest) GetReplicas() int32 {
	if x != nil {
		return x.Replicas
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_cloud_agent.proto.
func (x *CreateAgentRequest) GetMaxReplicas() int32 {
	if x != nil {
		return x.MaxReplicas
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_cloud_agent.proto.
func (x *CreateAgentRequest) GetCpuReq() string {
	if x != nil {
		return x.CpuReq
	}
	return ""
}

func (x *CreateAgentRequest) GetRegions() []string {
	if x != nil {
		return x.Regions
	}
	return nil
}

type CreateAgentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AgentId       string                 `protobuf:"bytes,1,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	AgentName     string                 `protobuf:"bytes,2,opt,name=agent_name,json=agentName,proto3" json:"agent_name,omitempty"`
	Status        string                 `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	Version       string                 `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`
	PresignedUrl  string                 `protobuf:"bytes,5,opt,name=presigned_url,json=presignedUrl,proto3" json:"presigned_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAgentResponse) Reset() {
	*x = CreateAgentResponse{}
	mi := &file_livekit_cloud_agent_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAgentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAgentResponse) ProtoMessage() {}

func (x *CreateAgentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_cloud_agent_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAgentResponse.ProtoReflect.Descriptor instead.
func (*CreateAgentResponse) Descriptor() ([]byte, []int) {
	return file_livekit_cloud_agent_proto_rawDescGZIP(), []int{2}
}

func (x *CreateAgentResponse) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *CreateAgentResponse) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

func (x *CreateAgentResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *CreateAgentResponse) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *CreateAgentResponse) GetPresignedUrl() string {
	if x != nil {
		return x.PresignedUrl
	}
	return ""
}

type AgentDeployment struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Region        string                 `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	AgentId       string                 `protobuf:"bytes,2,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	Status        string                 `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	Replicas      int32                  `protobuf:"varint,4,opt,name=replicas,proto3" json:"replicas,omitempty"`
	MinReplicas   int32                  `protobuf:"varint,5,opt,name=min_replicas,json=minReplicas,proto3" json:"min_replicas,omitempty"`
	MaxReplicas   int32                  `protobuf:"varint,6,opt,name=max_replicas,json=maxReplicas,proto3" json:"max_replicas,omitempty"`
	CpuReq        string                 `protobuf:"bytes,7,opt,name=cpu_req,json=cpuReq,proto3" json:"cpu_req,omitempty"`
	CurCpu        string                 `protobuf:"bytes,8,opt,name=cur_cpu,json=curCpu,proto3" json:"cur_cpu,omitempty"`
	CurMem        string                 `protobuf:"bytes,9,opt,name=cur_mem,json=curMem,proto3" json:"cur_mem,omitempty"`
	MemReq        string                 `protobuf:"bytes,10,opt,name=mem_req,json=memReq,proto3" json:"mem_req,omitempty"`
	MemLimit      string                 `protobuf:"bytes,11,opt,name=mem_limit,json=memLimit,proto3" json:"mem_limit,omitempty"`
	CpuLimit      string                 `protobuf:"bytes,12,opt,name=cpu_limit,json=cpuLimit,proto3" json:"cpu_limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AgentDeployment) Reset() {
	*x = AgentDeployment{}
	mi := &file_livekit_cloud_agent_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgentDeployment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentDeployment) ProtoMessage() {}

func (x *AgentDeployment) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_cloud_agent_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentDeployment.ProtoReflect.Descriptor instead.
func (*AgentDeployment) Descriptor() ([]byte, []int) {
	return file_livekit_cloud_agent_proto_rawDescGZIP(), []int{3}
}

func (x *AgentDeployment) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *AgentDeployment) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *AgentDeployment) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *AgentDeployment) GetReplicas() int32 {
	if x != nil {
		return x.Replicas
	}
	return 0
}

func (x *AgentDeployment) GetMinReplicas() int32 {
	if x != nil {
		return x.MinReplicas
	}
	return 0
}

func (x *AgentDeployment) GetMaxReplicas() int32 {
	if x != nil {
		return x.MaxReplicas
	}
	return 0
}

func (x *AgentDeployment) GetCpuReq() string {
	if x != nil {
		return x.CpuReq
	}
	return ""
}

func (x *AgentDeployment) GetCurCpu() string {
	if x != nil {
		return x.CurCpu
	}
	return ""
}

func (x *AgentDeployment) GetCurMem() string {
	if x != nil {
		return x.CurMem
	}
	return ""
}

func (x *AgentDeployment) GetMemReq() string {
	if x != nil {
		return x.MemReq
	}
	return ""
}

func (x *AgentDeployment) GetMemLimit() string {
	if x != nil {
		return x.MemLimit
	}
	return ""
}

func (x *AgentDeployment) GetCpuLimit() string {
	if x != nil {
		return x.CpuLimit
	}
	return ""
}

type AgentInfo struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	AgentId          string                 `protobuf:"bytes,1,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	AgentName        string                 `protobuf:"bytes,2,opt,name=agent_name,json=agentName,proto3" json:"agent_name,omitempty"`
	Version          string                 `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	AgentDeployments []*AgentDeployment     `protobuf:"bytes,4,rep,name=agent_deployments,json=agentDeployments,proto3" json:"agent_deployments,omitempty"`
	Secrets          []*AgentSecret         `protobuf:"bytes,5,rep,name=secrets,proto3" json:"secrets,omitempty"`
	DeployedAt       *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=deployed_at,json=deployedAt,proto3" json:"deployed_at,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *AgentInfo) Reset() {
	*x = AgentInfo{}
	mi := &file_livekit_cloud_agent_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentInfo) ProtoMessage() {}

func (x *AgentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_cloud_agent_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentInfo.ProtoReflect.Descriptor instead.
func (*AgentInfo) Descriptor() ([]byte, []int) {
	return file_livekit_cloud_agent_proto_rawDescGZIP(), []int{4}
}

func (x *AgentInfo) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *AgentInfo) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

func (x *AgentInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *AgentInfo) GetAgentDeployments() []*AgentDeployment {
	if x != nil {
		return x.AgentDeployments
	}
	return nil
}

func (x *AgentInfo) GetSecrets() []*AgentSecret {
	if x != nil {
		return x.Secrets
	}
	return nil
}

func (x *AgentInfo) GetDeployedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeployedAt
	}
	return nil
}

type ListAgentsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AgentName     string                 `protobuf:"bytes,1,opt,name=agent_name,json=agentName,proto3" json:"agent_name,omitempty"`
	AgentId       string                 `protobuf:"bytes,2,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAgentsRequest) Reset() {
	*x = ListAgentsRequest{}
	mi := &file_livekit_cloud_agent_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAgentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAgentsRequest) ProtoMessage() {}

func (x *ListAgentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_cloud_agent_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAgentsRequest.ProtoReflect.Descriptor instead.
func (*ListAgentsRequest) Descriptor() ([]byte, []int) {
	return file_livekit_cloud_agent_proto_rawDescGZIP(), []int{5}
}

func (x *ListAgentsRequest) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

func (x *ListAgentsRequest) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

type ListAgentsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Agents        []*AgentInfo           `protobuf:"bytes,1,rep,name=agents,proto3" json:"agents,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAgentsResponse) Reset() {
	*x = ListAgentsResponse{}
	mi := &file_livekit_cloud_agent_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAgentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAgentsResponse) ProtoMessage() {}

func (x *ListAgentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_cloud_agent_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAgentsResponse.ProtoReflect.Descriptor instead.
func (*ListAgentsResponse) Descriptor() ([]byte, []int) {
	return file_livekit_cloud_agent_proto_rawDescGZIP(), []int{6}
}

func (x *ListAgentsResponse) GetAgents() []*AgentInfo {
	if x != nil {
		return x.Agents
	}
	return nil
}

type AgentVersion struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Version       string                 `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	Current       bool                   `protobuf:"varint,2,opt,name=current,proto3" json:"current,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AgentVersion) Reset() {
	*x = AgentVersion{}
	mi := &file_livekit_cloud_agent_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgentVersion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentVersion) ProtoMessage() {}

func (x *AgentVersion) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_cloud_agent_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentVersion.ProtoReflect.Descriptor instead.
func (*AgentVersion) Descriptor() ([]byte, []int) {
	return file_livekit_cloud_agent_proto_rawDescGZIP(), []int{7}
}

func (x *AgentVersion) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *AgentVersion) GetCurrent() bool {
	if x != nil {
		return x.Current
	}
	return false
}

func (x *AgentVersion) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type ListAgentVersionsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AgentId       string                 `protobuf:"bytes,1,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	AgentName     string                 `protobuf:"bytes,2,opt,name=agent_name,json=agentName,proto3" json:"agent_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAgentVersionsRequest) Reset() {
	*x = ListAgentVersionsRequest{}
	mi := &file_livekit_cloud_agent_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAgentVersionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAgentVersionsRequest) ProtoMessage() {}

func (x *ListAgentVersionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_cloud_agent_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAgentVersionsRequest.ProtoReflect.Descriptor instead.
func (*ListAgentVersionsRequest) Descriptor() ([]byte, []int) {
	return file_livekit_cloud_agent_proto_rawDescGZIP(), []int{8}
}

func (x *ListAgentVersionsRequest) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *ListAgentVersionsRequest) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

type ListAgentVersionsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Versions      []*AgentVersion        `protobuf:"bytes,1,rep,name=versions,proto3" json:"versions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAgentVersionsResponse) Reset() {
	*x = ListAgentVersionsResponse{}
	mi := &file_livekit_cloud_agent_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAgentVersionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAgentVersionsResponse) ProtoMessage() {}

func (x *ListAgentVersionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_cloud_agent_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAgentVersionsResponse.ProtoReflect.Descriptor instead.
func (*ListAgentVersionsResponse) Descriptor() ([]byte, []int) {
	return file_livekit_cloud_agent_proto_rawDescGZIP(), []int{9}
}

func (x *ListAgentVersionsResponse) GetVersions() []*AgentVersion {
	if x != nil {
		return x.Versions
	}
	return nil
}

type UpdateAgentRequest struct {
	state   protoimpl.MessageState `protogen:"open.v1"`
	AgentId string                 `protobuf:"bytes,1,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	// Deprecated: Marked as deprecated in livekit_cloud_agent.proto.
	AgentName string `protobuf:"bytes,2,opt,name=agent_name,json=agentName,proto3" json:"agent_name,omitempty"`
	// Deprecated: Marked as deprecated in livekit_cloud_agent.proto.
	Replicas int32 `protobuf:"varint,3,opt,name=replicas,proto3" json:"replicas,omitempty"`
	// Deprecated: Marked as deprecated in livekit_cloud_agent.proto.
	MaxReplicas int32 `protobuf:"varint,4,opt,name=max_replicas,json=maxReplicas,proto3" json:"max_replicas,omitempty"`
	// Deprecated: Marked as deprecated in livekit_cloud_agent.proto.
	CpuReq        string         `protobuf:"bytes,5,opt,name=cpu_req,json=cpuReq,proto3" json:"cpu_req,omitempty"`
	Regions       []string       `protobuf:"bytes,6,rep,name=regions,proto3" json:"regions,omitempty"`
	Secrets       []*AgentSecret `protobuf:"bytes,7,rep,name=secrets,proto3" json:"secrets,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAgentRequest) Reset() {
	*x = UpdateAgentRequest{}
	mi := &file_livekit_cloud_agent_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAgentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAgentRequest) ProtoMessage() {}

func (x *UpdateAgentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_cloud_agent_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAgentRequest.ProtoReflect.Descriptor instead.
func (*UpdateAgentRequest) Descriptor() ([]byte, []int) {
	return file_livekit_cloud_agent_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateAgentRequest) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

// Deprecated: Marked as deprecated in livekit_cloud_agent.proto.
func (x *UpdateAgentRequest) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

// Deprecated: Marked as deprecated in livekit_cloud_agent.proto.
func (x *UpdateAgentRequest) GetReplicas() int32 {
	if x != nil {
		return x.Replicas
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_cloud_agent.proto.
func (x *UpdateAgentRequest) GetMaxReplicas() int32 {
	if x != nil {
		return x.MaxReplicas
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_cloud_agent.proto.
func (x *UpdateAgentRequest) GetCpuReq() string {
	if x != nil {
		return x.CpuReq
	}
	return ""
}

func (x *UpdateAgentRequest) GetRegions() []string {
	if x != nil {
		return x.Regions
	}
	return nil
}

func (x *UpdateAgentRequest) GetSecrets() []*AgentSecret {
	if x != nil {
		return x.Secrets
	}
	return nil
}

type UpdateAgentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAgentResponse) Reset() {
	*x = UpdateAgentResponse{}
	mi := &file_livekit_cloud_agent_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAgentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAgentResponse) ProtoMessage() {}

func (x *UpdateAgentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_cloud_agent_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAgentResponse.ProtoReflect.Descriptor instead.
func (*UpdateAgentResponse) Descriptor() ([]byte, []int) {
	return file_livekit_cloud_agent_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateAgentResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateAgentResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DeployAgentRequest struct {
	state   protoimpl.MessageState `protogen:"open.v1"`
	AgentId string                 `protobuf:"bytes,1,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	// Deprecated: Marked as deprecated in livekit_cloud_agent.proto.
	AgentName string         `protobuf:"bytes,2,opt,name=agent_name,json=agentName,proto3" json:"agent_name,omitempty"`
	Secrets   []*AgentSecret `protobuf:"bytes,3,rep,name=secrets,proto3" json:"secrets,omitempty"`
	// Deprecated: Marked as deprecated in livekit_cloud_agent.proto.
	Replicas int32 `protobuf:"varint,4,opt,name=replicas,proto3" json:"replicas,omitempty"`
	// Deprecated: Marked as deprecated in livekit_cloud_agent.proto.
	MaxReplicas int32 `protobuf:"varint,5,opt,name=max_replicas,json=maxReplicas,proto3" json:"max_replicas,omitempty"`
	// Deprecated: Marked as deprecated in livekit_cloud_agent.proto.
	CpuReq        string `protobuf:"bytes,6,opt,name=cpu_req,json=cpuReq,proto3" json:"cpu_req,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeployAgentRequest) Reset() {
	*x = DeployAgentRequest{}
	mi := &file_livekit_cloud_agent_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeployAgentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeployAgentRequest) ProtoMessage() {}

func (x *DeployAgentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_cloud_agent_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeployAgentRequest.ProtoReflect.Descriptor instead.
func (*DeployAgentRequest) Descriptor() ([]byte, []int) {
	return file_livekit_cloud_agent_proto_rawDescGZIP(), []int{12}
}

func (x *DeployAgentRequest) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

// Deprecated: Marked as deprecated in livekit_cloud_agent.proto.
func (x *DeployAgentRequest) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

func (x *DeployAgentRequest) GetSecrets() []*AgentSecret {
	if x != nil {
		return x.Secrets
	}
	return nil
}

// Deprecated: Marked as deprecated in livekit_cloud_agent.proto.
func (x *DeployAgentRequest) GetReplicas() int32 {
	if x != nil {
		return x.Replicas
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_cloud_agent.proto.
func (x *DeployAgentRequest) GetMaxReplicas() int32 {
	if x != nil {
		return x.MaxReplicas
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_cloud_agent.proto.
func (x *DeployAgentRequest) GetCpuReq() string {
	if x != nil {
		return x.CpuReq
	}
	return ""
}

type DeployAgentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	AgentId       string                 `protobuf:"bytes,3,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	PresignedUrl  string                 `protobuf:"bytes,4,opt,name=presigned_url,json=presignedUrl,proto3" json:"presigned_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeployAgentResponse) Reset() {
	*x = DeployAgentResponse{}
	mi := &file_livekit_cloud_agent_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeployAgentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeployAgentResponse) ProtoMessage() {}

func (x *DeployAgentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_cloud_agent_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeployAgentResponse.ProtoReflect.Descriptor instead.
func (*DeployAgentResponse) Descriptor() ([]byte, []int) {
	return file_livekit_cloud_agent_proto_rawDescGZIP(), []int{13}
}

func (x *DeployAgentResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeployAgentResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *DeployAgentResponse) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *DeployAgentResponse) GetPresignedUrl() string {
	if x != nil {
		return x.PresignedUrl
	}
	return ""
}

type UpdateAgentSecretsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AgentId       string                 `protobuf:"bytes,1,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	AgentName     string                 `protobuf:"bytes,2,opt,name=agent_name,json=agentName,proto3" json:"agent_name,omitempty"`
	Overwrite     bool                   `protobuf:"varint,3,opt,name=overwrite,proto3" json:"overwrite,omitempty"`
	Secrets       []*AgentSecret         `protobuf:"bytes,4,rep,name=secrets,proto3" json:"secrets,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAgentSecretsRequest) Reset() {
	*x = UpdateAgentSecretsRequest{}
	mi := &file_livekit_cloud_agent_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAgentSecretsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAgentSecretsRequest) ProtoMessage() {}

func (x *UpdateAgentSecretsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_cloud_agent_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAgentSecretsRequest.ProtoReflect.Descriptor instead.
func (*UpdateAgentSecretsRequest) Descriptor() ([]byte, []int) {
	return file_livekit_cloud_agent_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateAgentSecretsRequest) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *UpdateAgentSecretsRequest) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

func (x *UpdateAgentSecretsRequest) GetOverwrite() bool {
	if x != nil {
		return x.Overwrite
	}
	return false
}

func (x *UpdateAgentSecretsRequest) GetSecrets() []*AgentSecret {
	if x != nil {
		return x.Secrets
	}
	return nil
}

type UpdateAgentSecretsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAgentSecretsResponse) Reset() {
	*x = UpdateAgentSecretsResponse{}
	mi := &file_livekit_cloud_agent_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAgentSecretsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAgentSecretsResponse) ProtoMessage() {}

func (x *UpdateAgentSecretsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_cloud_agent_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAgentSecretsResponse.ProtoReflect.Descriptor instead.
func (*UpdateAgentSecretsResponse) Descriptor() ([]byte, []int) {
	return file_livekit_cloud_agent_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateAgentSecretsResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateAgentSecretsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type RollbackAgentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AgentId       string                 `protobuf:"bytes,1,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	AgentName     string                 `protobuf:"bytes,2,opt,name=agent_name,json=agentName,proto3" json:"agent_name,omitempty"`
	Version       string                 `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RollbackAgentRequest) Reset() {
	*x = RollbackAgentRequest{}
	mi := &file_livekit_cloud_agent_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RollbackAgentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RollbackAgentRequest) ProtoMessage() {}

func (x *RollbackAgentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_cloud_agent_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RollbackAgentRequest.ProtoReflect.Descriptor instead.
func (*RollbackAgentRequest) Descriptor() ([]byte, []int) {
	return file_livekit_cloud_agent_proto_rawDescGZIP(), []int{16}
}

func (x *RollbackAgentRequest) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *RollbackAgentRequest) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

func (x *RollbackAgentRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type RollbackAgentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RollbackAgentResponse) Reset() {
	*x = RollbackAgentResponse{}
	mi := &file_livekit_cloud_agent_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RollbackAgentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RollbackAgentResponse) ProtoMessage() {}

func (x *RollbackAgentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_cloud_agent_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RollbackAgentResponse.ProtoReflect.Descriptor instead.
func (*RollbackAgentResponse) Descriptor() ([]byte, []int) {
	return file_livekit_cloud_agent_proto_rawDescGZIP(), []int{17}
}

func (x *RollbackAgentResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RollbackAgentResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DeleteAgentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AgentId       string                 `protobuf:"bytes,1,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	AgentName     string                 `protobuf:"bytes,2,opt,name=agent_name,json=agentName,proto3" json:"agent_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteAgentRequest) Reset() {
	*x = DeleteAgentRequest{}
	mi := &file_livekit_cloud_agent_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAgentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAgentRequest) ProtoMessage() {}

func (x *DeleteAgentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_cloud_agent_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAgentRequest.ProtoReflect.Descriptor instead.
func (*DeleteAgentRequest) Descriptor() ([]byte, []int) {
	return file_livekit_cloud_agent_proto_rawDescGZIP(), []int{18}
}

func (x *DeleteAgentRequest) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *DeleteAgentRequest) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

type DeleteAgentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteAgentResponse) Reset() {
	*x = DeleteAgentResponse{}
	mi := &file_livekit_cloud_agent_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAgentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAgentResponse) ProtoMessage() {}

func (x *DeleteAgentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_cloud_agent_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAgentResponse.ProtoReflect.Descriptor instead.
func (*DeleteAgentResponse) Descriptor() ([]byte, []int) {
	return file_livekit_cloud_agent_proto_rawDescGZIP(), []int{19}
}

func (x *DeleteAgentResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeleteAgentResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ListAgentSecretsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AgentId       string                 `protobuf:"bytes,1,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	AgentName     string                 `protobuf:"bytes,2,opt,name=agent_name,json=agentName,proto3" json:"agent_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAgentSecretsRequest) Reset() {
	*x = ListAgentSecretsRequest{}
	mi := &file_livekit_cloud_agent_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAgentSecretsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAgentSecretsRequest) ProtoMessage() {}

func (x *ListAgentSecretsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_cloud_agent_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAgentSecretsRequest.ProtoReflect.Descriptor instead.
func (*ListAgentSecretsRequest) Descriptor() ([]byte, []int) {
	return file_livekit_cloud_agent_proto_rawDescGZIP(), []int{20}
}

func (x *ListAgentSecretsRequest) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *ListAgentSecretsRequest) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

type ListAgentSecretsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Secrets       []*AgentSecret         `protobuf:"bytes,1,rep,name=secrets,proto3" json:"secrets,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAgentSecretsResponse) Reset() {
	*x = ListAgentSecretsResponse{}
	mi := &file_livekit_cloud_agent_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAgentSecretsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAgentSecretsResponse) ProtoMessage() {}

func (x *ListAgentSecretsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_cloud_agent_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAgentSecretsResponse.ProtoReflect.Descriptor instead.
func (*ListAgentSecretsResponse) Descriptor() ([]byte, []int) {
	return file_livekit_cloud_agent_proto_rawDescGZIP(), []int{21}
}

func (x *ListAgentSecretsResponse) GetSecrets() []*AgentSecret {
	if x != nil {
		return x.Secrets
	}
	return nil
}

type SettingsParam struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Value         string                 `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SettingsParam) Reset() {
	*x = SettingsParam{}
	mi := &file_livekit_cloud_agent_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SettingsParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SettingsParam) ProtoMessage() {}

func (x *SettingsParam) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_cloud_agent_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SettingsParam.ProtoReflect.Descriptor instead.
func (*SettingsParam) Descriptor() ([]byte, []int) {
	return file_livekit_cloud_agent_proto_rawDescGZIP(), []int{22}
}

func (x *SettingsParam) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SettingsParam) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type ClientSettingsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Params        []*SettingsParam       `protobuf:"bytes,1,rep,name=params,proto3" json:"params,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientSettingsResponse) Reset() {
	*x = ClientSettingsResponse{}
	mi := &file_livekit_cloud_agent_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientSettingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientSettingsResponse) ProtoMessage() {}

func (x *ClientSettingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_cloud_agent_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientSettingsResponse.ProtoReflect.Descriptor instead.
func (*ClientSettingsResponse) Descriptor() ([]byte, []int) {
	return file_livekit_cloud_agent_proto_rawDescGZIP(), []int{23}
}

func (x *ClientSettingsResponse) GetParams() []*SettingsParam {
	if x != nil {
		return x.Params
	}
	return nil
}

type ClientSettingsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientSettingsRequest) Reset() {
	*x = ClientSettingsRequest{}
	mi := &file_livekit_cloud_agent_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientSettingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientSettingsRequest) ProtoMessage() {}

func (x *ClientSettingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_cloud_agent_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientSettingsRequest.ProtoReflect.Descriptor instead.
func (*ClientSettingsRequest) Descriptor() ([]byte, []int) {
	return file_livekit_cloud_agent_proto_rawDescGZIP(), []int{24}
}

var File_livekit_cloud_agent_proto protoreflect.FileDescriptor

const file_livekit_cloud_agent_proto_rawDesc = "" +
	"\n" +
	"\x19livekit_cloud_agent.proto\x12\alivekit\x1a\x1fgoogle/protobuf/timestamp.proto\"\xad\x01\n" +
	"\vAgentSecret\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x14\n" +
	"\x05value\x18\x02 \x01(\fR\x05value\x129\n" +
	"\n" +
	"created_at\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\xe5\x01\n" +
	"\x12CreateAgentRequest\x12!\n" +
	"\n" +
	"agent_name\x18\x01 \x01(\tB\x02\x18\x01R\tagentName\x12.\n" +
	"\asecrets\x18\x02 \x03(\v2\x14.livekit.AgentSecretR\asecrets\x12\x1e\n" +
	"\breplicas\x18\x03 \x01(\x05B\x02\x18\x01R\breplicas\x12%\n" +
	"\fmax_replicas\x18\x04 \x01(\x05B\x02\x18\x01R\vmaxReplicas\x12\x1b\n" +
	"\acpu_req\x18\x05 \x01(\tB\x02\x18\x01R\x06cpuReq\x12\x18\n" +
	"\aregions\x18\x06 \x03(\tR\aregions\"\xa6\x01\n" +
	"\x13CreateAgentResponse\x12\x19\n" +
	"\bagent_id\x18\x01 \x01(\tR\aagentId\x12\x1d\n" +
	"\n" +
	"agent_name\x18\x02 \x01(\tR\tagentName\x12\x16\n" +
	"\x06status\x18\x03 \x01(\tR\x06status\x12\x18\n" +
	"\aversion\x18\x04 \x01(\tR\aversion\x12#\n" +
	"\rpresigned_url\x18\x05 \x01(\tR\fpresignedUrl\"\xdc\x02\n" +
	"\x0fAgentDeployment\x12\x16\n" +
	"\x06region\x18\x01 \x01(\tR\x06region\x12\x19\n" +
	"\bagent_id\x18\x02 \x01(\tR\aagentId\x12\x16\n" +
	"\x06status\x18\x03 \x01(\tR\x06status\x12\x1a\n" +
	"\breplicas\x18\x04 \x01(\x05R\breplicas\x12!\n" +
	"\fmin_replicas\x18\x05 \x01(\x05R\vminReplicas\x12!\n" +
	"\fmax_replicas\x18\x06 \x01(\x05R\vmaxReplicas\x12\x17\n" +
	"\acpu_req\x18\a \x01(\tR\x06cpuReq\x12\x17\n" +
	"\acur_cpu\x18\b \x01(\tR\x06curCpu\x12\x17\n" +
	"\acur_mem\x18\t \x01(\tR\x06curMem\x12\x17\n" +
	"\amem_req\x18\n" +
	" \x01(\tR\x06memReq\x12\x1b\n" +
	"\tmem_limit\x18\v \x01(\tR\bmemLimit\x12\x1b\n" +
	"\tcpu_limit\x18\f \x01(\tR\bcpuLimit\"\x93\x02\n" +
	"\tAgentInfo\x12\x19\n" +
	"\bagent_id\x18\x01 \x01(\tR\aagentId\x12\x1d\n" +
	"\n" +
	"agent_name\x18\x02 \x01(\tR\tagentName\x12\x18\n" +
	"\aversion\x18\x03 \x01(\tR\aversion\x12E\n" +
	"\x11agent_deployments\x18\x04 \x03(\v2\x18.livekit.AgentDeploymentR\x10agentDeployments\x12.\n" +
	"\asecrets\x18\x05 \x03(\v2\x14.livekit.AgentSecretR\asecrets\x12;\n" +
	"\vdeployed_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"deployedAt\"M\n" +
	"\x11ListAgentsRequest\x12\x1d\n" +
	"\n" +
	"agent_name\x18\x01 \x01(\tR\tagentName\x12\x19\n" +
	"\bagent_id\x18\x02 \x01(\tR\aagentId\"@\n" +
	"\x12ListAgentsResponse\x12*\n" +
	"\x06agents\x18\x01 \x03(\v2\x12.livekit.AgentInfoR\x06agents\"}\n" +
	"\fAgentVersion\x12\x18\n" +
	"\aversion\x18\x01 \x01(\tR\aversion\x12\x18\n" +
	"\acurrent\x18\x02 \x01(\bR\acurrent\x129\n" +
	"\n" +
	"created_at\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\"T\n" +
	"\x18ListAgentVersionsRequest\x12\x19\n" +
	"\bagent_id\x18\x01 \x01(\tR\aagentId\x12\x1d\n" +
	"\n" +
	"agent_name\x18\x02 \x01(\tR\tagentName\"N\n" +
	"\x19ListAgentVersionsResponse\x121\n" +
	"\bversions\x18\x01 \x03(\v2\x15.livekit.AgentVersionR\bversions\"\x80\x02\n" +
	"\x12UpdateAgentRequest\x12\x19\n" +
	"\bagent_id\x18\x01 \x01(\tR\aagentId\x12!\n" +
	"\n" +
	"agent_name\x18\x02 \x01(\tB\x02\x18\x01R\tagentName\x12\x1e\n" +
	"\breplicas\x18\x03 \x01(\x05B\x02\x18\x01R\breplicas\x12%\n" +
	"\fmax_replicas\x18\x04 \x01(\x05B\x02\x18\x01R\vmaxReplicas\x12\x1b\n" +
	"\acpu_req\x18\x05 \x01(\tB\x02\x18\x01R\x06cpuReq\x12\x18\n" +
	"\aregions\x18\x06 \x03(\tR\aregions\x12.\n" +
	"\asecrets\x18\a \x03(\v2\x14.livekit.AgentSecretR\asecrets\"I\n" +
	"\x13UpdateAgentResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\xe6\x01\n" +
	"\x12DeployAgentRequest\x12\x19\n" +
	"\bagent_id\x18\x01 \x01(\tR\aagentId\x12!\n" +
	"\n" +
	"agent_name\x18\x02 \x01(\tB\x02\x18\x01R\tagentName\x12.\n" +
	"\asecrets\x18\x03 \x03(\v2\x14.livekit.AgentSecretR\asecrets\x12\x1e\n" +
	"\breplicas\x18\x04 \x01(\x05B\x02\x18\x01R\breplicas\x12%\n" +
	"\fmax_replicas\x18\x05 \x01(\x05B\x02\x18\x01R\vmaxReplicas\x12\x1b\n" +
	"\acpu_req\x18\x06 \x01(\tB\x02\x18\x01R\x06cpuReq\"\x89\x01\n" +
	"\x13DeployAgentResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x19\n" +
	"\bagent_id\x18\x03 \x01(\tR\aagentId\x12#\n" +
	"\rpresigned_url\x18\x04 \x01(\tR\fpresignedUrl\"\xa3\x01\n" +
	"\x19UpdateAgentSecretsRequest\x12\x19\n" +
	"\bagent_id\x18\x01 \x01(\tR\aagentId\x12\x1d\n" +
	"\n" +
	"agent_name\x18\x02 \x01(\tR\tagentName\x12\x1c\n" +
	"\toverwrite\x18\x03 \x01(\bR\toverwrite\x12.\n" +
	"\asecrets\x18\x04 \x03(\v2\x14.livekit.AgentSecretR\asecrets\"P\n" +
	"\x1aUpdateAgentSecretsResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"j\n" +
	"\x14RollbackAgentRequest\x12\x19\n" +
	"\bagent_id\x18\x01 \x01(\tR\aagentId\x12\x1d\n" +
	"\n" +
	"agent_name\x18\x02 \x01(\tR\tagentName\x12\x18\n" +
	"\aversion\x18\x03 \x01(\tR\aversion\"K\n" +
	"\x15RollbackAgentResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"N\n" +
	"\x12DeleteAgentRequest\x12\x19\n" +
	"\bagent_id\x18\x01 \x01(\tR\aagentId\x12\x1d\n" +
	"\n" +
	"agent_name\x18\x02 \x01(\tR\tagentName\"I\n" +
	"\x13DeleteAgentResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"S\n" +
	"\x17ListAgentSecretsRequest\x12\x19\n" +
	"\bagent_id\x18\x01 \x01(\tR\aagentId\x12\x1d\n" +
	"\n" +
	"agent_name\x18\x02 \x01(\tR\tagentName\"J\n" +
	"\x18ListAgentSecretsResponse\x12.\n" +
	"\asecrets\x18\x01 \x03(\v2\x14.livekit.AgentSecretR\asecrets\"9\n" +
	"\rSettingsParam\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value\"H\n" +
	"\x16ClientSettingsResponse\x12.\n" +
	"\x06params\x18\x01 \x03(\v2\x16.livekit.SettingsParamR\x06params\"\x17\n" +
	"\x15ClientSettingsRequest2\xc9\x06\n" +
	"\n" +
	"CloudAgent\x12J\n" +
	"\vCreateAgent\x12\x1b.livekit.CreateAgentRequest\x1a\x1c.livekit.CreateAgentResponse\"\x00\x12G\n" +
	"\n" +
	"ListAgents\x12\x1a.livekit.ListAgentsRequest\x1a\x1b.livekit.ListAgentsResponse\"\x00\x12\\\n" +
	"\x11ListAgentVersions\x12!.livekit.ListAgentVersionsRequest\x1a\".livekit.ListAgentVersionsResponse\"\x00\x12Y\n" +
	"\x10ListAgentSecrets\x12 .livekit.ListAgentSecretsRequest\x1a!.livekit.ListAgentSecretsResponse\"\x00\x12J\n" +
	"\vUpdateAgent\x12\x1b.livekit.UpdateAgentRequest\x1a\x1c.livekit.UpdateAgentResponse\"\x00\x12J\n" +
	"\vDeployAgent\x12\x1b.livekit.DeployAgentRequest\x1a\x1c.livekit.DeployAgentResponse\"\x00\x12_\n" +
	"\x12UpdateAgentSecrets\x12\".livekit.UpdateAgentSecretsRequest\x1a#.livekit.UpdateAgentSecretsResponse\"\x00\x12P\n" +
	"\rRollbackAgent\x12\x1d.livekit.RollbackAgentRequest\x1a\x1e.livekit.RollbackAgentResponse\"\x00\x12J\n" +
	"\vDeleteAgent\x12\x1b.livekit.DeleteAgentRequest\x1a\x1c.livekit.DeleteAgentResponse\"\x00\x12V\n" +
	"\x11GetClientSettings\x12\x1e.livekit.ClientSettingsRequest\x1a\x1f.livekit.ClientSettingsResponse\"\x00BFZ#github.com/livekit/protocol/livekit\xaa\x02\rLiveKit.Proto\xea\x02\x0eLiveKit::Protob\x06proto3"

var (
	file_livekit_cloud_agent_proto_rawDescOnce sync.Once
	file_livekit_cloud_agent_proto_rawDescData []byte
)

func file_livekit_cloud_agent_proto_rawDescGZIP() []byte {
	file_livekit_cloud_agent_proto_rawDescOnce.Do(func() {
		file_livekit_cloud_agent_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_livekit_cloud_agent_proto_rawDesc), len(file_livekit_cloud_agent_proto_rawDesc)))
	})
	return file_livekit_cloud_agent_proto_rawDescData
}

var file_livekit_cloud_agent_proto_msgTypes = make([]protoimpl.MessageInfo, 25)
var file_livekit_cloud_agent_proto_goTypes = []any{
	(*AgentSecret)(nil),                // 0: livekit.AgentSecret
	(*CreateAgentRequest)(nil),         // 1: livekit.CreateAgentRequest
	(*CreateAgentResponse)(nil),        // 2: livekit.CreateAgentResponse
	(*AgentDeployment)(nil),            // 3: livekit.AgentDeployment
	(*AgentInfo)(nil),                  // 4: livekit.AgentInfo
	(*ListAgentsRequest)(nil),          // 5: livekit.ListAgentsRequest
	(*ListAgentsResponse)(nil),         // 6: livekit.ListAgentsResponse
	(*AgentVersion)(nil),               // 7: livekit.AgentVersion
	(*ListAgentVersionsRequest)(nil),   // 8: livekit.ListAgentVersionsRequest
	(*ListAgentVersionsResponse)(nil),  // 9: livekit.ListAgentVersionsResponse
	(*UpdateAgentRequest)(nil),         // 10: livekit.UpdateAgentRequest
	(*UpdateAgentResponse)(nil),        // 11: livekit.UpdateAgentResponse
	(*DeployAgentRequest)(nil),         // 12: livekit.DeployAgentRequest
	(*DeployAgentResponse)(nil),        // 13: livekit.DeployAgentResponse
	(*UpdateAgentSecretsRequest)(nil),  // 14: livekit.UpdateAgentSecretsRequest
	(*UpdateAgentSecretsResponse)(nil), // 15: livekit.UpdateAgentSecretsResponse
	(*RollbackAgentRequest)(nil),       // 16: livekit.RollbackAgentRequest
	(*RollbackAgentResponse)(nil),      // 17: livekit.RollbackAgentResponse
	(*DeleteAgentRequest)(nil),         // 18: livekit.DeleteAgentRequest
	(*DeleteAgentResponse)(nil),        // 19: livekit.DeleteAgentResponse
	(*ListAgentSecretsRequest)(nil),    // 20: livekit.ListAgentSecretsRequest
	(*ListAgentSecretsResponse)(nil),   // 21: livekit.ListAgentSecretsResponse
	(*SettingsParam)(nil),              // 22: livekit.SettingsParam
	(*ClientSettingsResponse)(nil),     // 23: livekit.ClientSettingsResponse
	(*ClientSettingsRequest)(nil),      // 24: livekit.ClientSettingsRequest
	(*timestamppb.Timestamp)(nil),      // 25: google.protobuf.Timestamp
}
var file_livekit_cloud_agent_proto_depIdxs = []int32{
	25, // 0: livekit.AgentSecret.created_at:type_name -> google.protobuf.Timestamp
	25, // 1: livekit.AgentSecret.updated_at:type_name -> google.protobuf.Timestamp
	0,  // 2: livekit.CreateAgentRequest.secrets:type_name -> livekit.AgentSecret
	3,  // 3: livekit.AgentInfo.agent_deployments:type_name -> livekit.AgentDeployment
	0,  // 4: livekit.AgentInfo.secrets:type_name -> livekit.AgentSecret
	25, // 5: livekit.AgentInfo.deployed_at:type_name -> google.protobuf.Timestamp
	4,  // 6: livekit.ListAgentsResponse.agents:type_name -> livekit.AgentInfo
	25, // 7: livekit.AgentVersion.created_at:type_name -> google.protobuf.Timestamp
	7,  // 8: livekit.ListAgentVersionsResponse.versions:type_name -> livekit.AgentVersion
	0,  // 9: livekit.UpdateAgentRequest.secrets:type_name -> livekit.AgentSecret
	0,  // 10: livekit.DeployAgentRequest.secrets:type_name -> livekit.AgentSecret
	0,  // 11: livekit.UpdateAgentSecretsRequest.secrets:type_name -> livekit.AgentSecret
	0,  // 12: livekit.ListAgentSecretsResponse.secrets:type_name -> livekit.AgentSecret
	22, // 13: livekit.ClientSettingsResponse.params:type_name -> livekit.SettingsParam
	1,  // 14: livekit.CloudAgent.CreateAgent:input_type -> livekit.CreateAgentRequest
	5,  // 15: livekit.CloudAgent.ListAgents:input_type -> livekit.ListAgentsRequest
	8,  // 16: livekit.CloudAgent.ListAgentVersions:input_type -> livekit.ListAgentVersionsRequest
	20, // 17: livekit.CloudAgent.ListAgentSecrets:input_type -> livekit.ListAgentSecretsRequest
	10, // 18: livekit.CloudAgent.UpdateAgent:input_type -> livekit.UpdateAgentRequest
	12, // 19: livekit.CloudAgent.DeployAgent:input_type -> livekit.DeployAgentRequest
	14, // 20: livekit.CloudAgent.UpdateAgentSecrets:input_type -> livekit.UpdateAgentSecretsRequest
	16, // 21: livekit.CloudAgent.RollbackAgent:input_type -> livekit.RollbackAgentRequest
	18, // 22: livekit.CloudAgent.DeleteAgent:input_type -> livekit.DeleteAgentRequest
	24, // 23: livekit.CloudAgent.GetClientSettings:input_type -> livekit.ClientSettingsRequest
	2,  // 24: livekit.CloudAgent.CreateAgent:output_type -> livekit.CreateAgentResponse
	6,  // 25: livekit.CloudAgent.ListAgents:output_type -> livekit.ListAgentsResponse
	9,  // 26: livekit.CloudAgent.ListAgentVersions:output_type -> livekit.ListAgentVersionsResponse
	21, // 27: livekit.CloudAgent.ListAgentSecrets:output_type -> livekit.ListAgentSecretsResponse
	11, // 28: livekit.CloudAgent.UpdateAgent:output_type -> livekit.UpdateAgentResponse
	13, // 29: livekit.CloudAgent.DeployAgent:output_type -> livekit.DeployAgentResponse
	15, // 30: livekit.CloudAgent.UpdateAgentSecrets:output_type -> livekit.UpdateAgentSecretsResponse
	17, // 31: livekit.CloudAgent.RollbackAgent:output_type -> livekit.RollbackAgentResponse
	19, // 32: livekit.CloudAgent.DeleteAgent:output_type -> livekit.DeleteAgentResponse
	23, // 33: livekit.CloudAgent.GetClientSettings:output_type -> livekit.ClientSettingsResponse
	24, // [24:34] is the sub-list for method output_type
	14, // [14:24] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_livekit_cloud_agent_proto_init() }
func file_livekit_cloud_agent_proto_init() {
	if File_livekit_cloud_agent_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_livekit_cloud_agent_proto_rawDesc), len(file_livekit_cloud_agent_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   25,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_livekit_cloud_agent_proto_goTypes,
		DependencyIndexes: file_livekit_cloud_agent_proto_depIdxs,
		MessageInfos:      file_livekit_cloud_agent_proto_msgTypes,
	}.Build()
	File_livekit_cloud_agent_proto = out.File
	file_livekit_cloud_agent_proto_goTypes = nil
	file_livekit_cloud_agent_proto_depIdxs = nil
}
