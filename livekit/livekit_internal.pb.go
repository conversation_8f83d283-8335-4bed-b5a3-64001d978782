// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: livekit_internal.proto

package livekit

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NodeType int32

const (
	NodeType_SERVER       NodeType = 0
	NodeType_CONTROLLER   NodeType = 1
	NodeType_MEDIA        NodeType = 2
	NodeType_TURN         NodeType = 4
	NodeType_SWEEPER      NodeType = 5
	NodeType_DIRECTOR     NodeType = 6
	NodeType_HOSTED_AGENT NodeType = 7
)

// Enum value maps for NodeType.
var (
	NodeType_name = map[int32]string{
		0: "SERVER",
		1: "CONTROLLER",
		2: "MEDIA",
		4: "TURN",
		5: "SWEEPER",
		6: "DIRECTOR",
		7: "HOSTED_AGENT",
	}
	NodeType_value = map[string]int32{
		"SERVER":       0,
		"CONTROLLER":   1,
		"MEDIA":        2,
		"TURN":         4,
		"SWEEPER":      5,
		"DIRECTOR":     6,
		"HOSTED_AGENT": 7,
	}
)

func (x NodeType) Enum() *NodeType {
	p := new(NodeType)
	*p = x
	return p
}

func (x NodeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NodeType) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_internal_proto_enumTypes[0].Descriptor()
}

func (NodeType) Type() protoreflect.EnumType {
	return &file_livekit_internal_proto_enumTypes[0]
}

func (x NodeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NodeType.Descriptor instead.
func (NodeType) EnumDescriptor() ([]byte, []int) {
	return file_livekit_internal_proto_rawDescGZIP(), []int{0}
}

type NodeState int32

const (
	NodeState_STARTING_UP   NodeState = 0
	NodeState_SERVING       NodeState = 1
	NodeState_SHUTTING_DOWN NodeState = 2
)

// Enum value maps for NodeState.
var (
	NodeState_name = map[int32]string{
		0: "STARTING_UP",
		1: "SERVING",
		2: "SHUTTING_DOWN",
	}
	NodeState_value = map[string]int32{
		"STARTING_UP":   0,
		"SERVING":       1,
		"SHUTTING_DOWN": 2,
	}
)

func (x NodeState) Enum() *NodeState {
	p := new(NodeState)
	*p = x
	return p
}

func (x NodeState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NodeState) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_internal_proto_enumTypes[1].Descriptor()
}

func (NodeState) Type() protoreflect.EnumType {
	return &file_livekit_internal_proto_enumTypes[1]
}

func (x NodeState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NodeState.Descriptor instead.
func (NodeState) EnumDescriptor() ([]byte, []int) {
	return file_livekit_internal_proto_rawDescGZIP(), []int{1}
}

type ICECandidateType int32

const (
	ICECandidateType_ICT_NONE ICECandidateType = 0
	ICECandidateType_ICT_TCP  ICECandidateType = 1
	ICECandidateType_ICT_TLS  ICECandidateType = 2
)

// Enum value maps for ICECandidateType.
var (
	ICECandidateType_name = map[int32]string{
		0: "ICT_NONE",
		1: "ICT_TCP",
		2: "ICT_TLS",
	}
	ICECandidateType_value = map[string]int32{
		"ICT_NONE": 0,
		"ICT_TCP":  1,
		"ICT_TLS":  2,
	}
)

func (x ICECandidateType) Enum() *ICECandidateType {
	p := new(ICECandidateType)
	*p = x
	return p
}

func (x ICECandidateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ICECandidateType) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_internal_proto_enumTypes[2].Descriptor()
}

func (ICECandidateType) Type() protoreflect.EnumType {
	return &file_livekit_internal_proto_enumTypes[2]
}

func (x ICECandidateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ICECandidateType.Descriptor instead.
func (ICECandidateType) EnumDescriptor() ([]byte, []int) {
	return file_livekit_internal_proto_rawDescGZIP(), []int{2}
}

type Node struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Ip            string                 `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`
	NumCpus       uint32                 `protobuf:"varint,3,opt,name=num_cpus,json=numCpus,proto3" json:"num_cpus,omitempty"`
	Stats         *NodeStats             `protobuf:"bytes,4,opt,name=stats,proto3" json:"stats,omitempty"`
	Type          NodeType               `protobuf:"varint,5,opt,name=type,proto3,enum=livekit.NodeType" json:"type,omitempty"`
	State         NodeState              `protobuf:"varint,6,opt,name=state,proto3,enum=livekit.NodeState" json:"state,omitempty"`
	Region        string                 `protobuf:"bytes,7,opt,name=region,proto3" json:"region,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Node) Reset() {
	*x = Node{}
	mi := &file_livekit_internal_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Node) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Node) ProtoMessage() {}

func (x *Node) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_internal_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Node.ProtoReflect.Descriptor instead.
func (*Node) Descriptor() ([]byte, []int) {
	return file_livekit_internal_proto_rawDescGZIP(), []int{0}
}

func (x *Node) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Node) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *Node) GetNumCpus() uint32 {
	if x != nil {
		return x.NumCpus
	}
	return 0
}

func (x *Node) GetStats() *NodeStats {
	if x != nil {
		return x.Stats
	}
	return nil
}

func (x *Node) GetType() NodeType {
	if x != nil {
		return x.Type
	}
	return NodeType_SERVER
}

func (x *Node) GetState() NodeState {
	if x != nil {
		return x.State
	}
	return NodeState_STARTING_UP
}

func (x *Node) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type NodeStats struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// when server was started
	StartedAt int64 `protobuf:"varint,1,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	// when server last reported its status
	UpdatedAt int64 `protobuf:"varint,2,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// room
	NumRooms                int32 `protobuf:"varint,3,opt,name=num_rooms,json=numRooms,proto3" json:"num_rooms,omitempty"`
	NumClients              int32 `protobuf:"varint,4,opt,name=num_clients,json=numClients,proto3" json:"num_clients,omitempty"`
	NumTracksIn             int32 `protobuf:"varint,5,opt,name=num_tracks_in,json=numTracksIn,proto3" json:"num_tracks_in,omitempty"`
	NumTracksOut            int32 `protobuf:"varint,6,opt,name=num_tracks_out,json=numTracksOut,proto3" json:"num_tracks_out,omitempty"`
	NumTrackPublishAttempts int32 `protobuf:"varint,36,opt,name=num_track_publish_attempts,json=numTrackPublishAttempts,proto3" json:"num_track_publish_attempts,omitempty"`
	// Deprecated: Marked as deprecated in livekit_internal.proto.
	TrackPublishAttemptsPerSec float32 `protobuf:"fixed32,37,opt,name=track_publish_attempts_per_sec,json=trackPublishAttemptsPerSec,proto3" json:"track_publish_attempts_per_sec,omitempty"`
	NumTrackPublishSuccess     int32   `protobuf:"varint,38,opt,name=num_track_publish_success,json=numTrackPublishSuccess,proto3" json:"num_track_publish_success,omitempty"`
	// Deprecated: Marked as deprecated in livekit_internal.proto.
	TrackPublishSuccessPerSec float32 `protobuf:"fixed32,39,opt,name=track_publish_success_per_sec,json=trackPublishSuccessPerSec,proto3" json:"track_publish_success_per_sec,omitempty"`
	NumTrackSubscribeAttempts int32   `protobuf:"varint,40,opt,name=num_track_subscribe_attempts,json=numTrackSubscribeAttempts,proto3" json:"num_track_subscribe_attempts,omitempty"`
	// Deprecated: Marked as deprecated in livekit_internal.proto.
	TrackSubscribeAttemptsPerSec float32 `protobuf:"fixed32,41,opt,name=track_subscribe_attempts_per_sec,json=trackSubscribeAttemptsPerSec,proto3" json:"track_subscribe_attempts_per_sec,omitempty"`
	NumTrackSubscribeSuccess     int32   `protobuf:"varint,42,opt,name=num_track_subscribe_success,json=numTrackSubscribeSuccess,proto3" json:"num_track_subscribe_success,omitempty"`
	// Deprecated: Marked as deprecated in livekit_internal.proto.
	TrackSubscribeSuccessPerSec float32 `protobuf:"fixed32,43,opt,name=track_subscribe_success_per_sec,json=trackSubscribeSuccessPerSec,proto3" json:"track_subscribe_success_per_sec,omitempty"`
	// packet
	BytesIn    uint64 `protobuf:"varint,7,opt,name=bytes_in,json=bytesIn,proto3" json:"bytes_in,omitempty"`
	BytesOut   uint64 `protobuf:"varint,8,opt,name=bytes_out,json=bytesOut,proto3" json:"bytes_out,omitempty"`
	PacketsIn  uint64 `protobuf:"varint,9,opt,name=packets_in,json=packetsIn,proto3" json:"packets_in,omitempty"`
	PacketsOut uint64 `protobuf:"varint,10,opt,name=packets_out,json=packetsOut,proto3" json:"packets_out,omitempty"`
	NackTotal  uint64 `protobuf:"varint,11,opt,name=nack_total,json=nackTotal,proto3" json:"nack_total,omitempty"`
	// Deprecated: Marked as deprecated in livekit_internal.proto.
	BytesInPerSec float32 `protobuf:"fixed32,12,opt,name=bytes_in_per_sec,json=bytesInPerSec,proto3" json:"bytes_in_per_sec,omitempty"`
	// Deprecated: Marked as deprecated in livekit_internal.proto.
	BytesOutPerSec float32 `protobuf:"fixed32,13,opt,name=bytes_out_per_sec,json=bytesOutPerSec,proto3" json:"bytes_out_per_sec,omitempty"`
	// Deprecated: Marked as deprecated in livekit_internal.proto.
	PacketsInPerSec float32 `protobuf:"fixed32,14,opt,name=packets_in_per_sec,json=packetsInPerSec,proto3" json:"packets_in_per_sec,omitempty"`
	// Deprecated: Marked as deprecated in livekit_internal.proto.
	PacketsOutPerSec float32 `protobuf:"fixed32,15,opt,name=packets_out_per_sec,json=packetsOutPerSec,proto3" json:"packets_out_per_sec,omitempty"`
	// Deprecated: Marked as deprecated in livekit_internal.proto.
	NackPerSec float32 `protobuf:"fixed32,16,opt,name=nack_per_sec,json=nackPerSec,proto3" json:"nack_per_sec,omitempty"`
	// system
	NumCpus          uint32  `protobuf:"varint,17,opt,name=num_cpus,json=numCpus,proto3" json:"num_cpus,omitempty"`
	LoadAvgLast1Min  float32 `protobuf:"fixed32,18,opt,name=load_avg_last1min,json=loadAvgLast1min,proto3" json:"load_avg_last1min,omitempty"`
	LoadAvgLast5Min  float32 `protobuf:"fixed32,19,opt,name=load_avg_last5min,json=loadAvgLast5min,proto3" json:"load_avg_last5min,omitempty"`
	LoadAvgLast15Min float32 `protobuf:"fixed32,20,opt,name=load_avg_last15min,json=loadAvgLast15min,proto3" json:"load_avg_last15min,omitempty"`
	CpuLoad          float32 `protobuf:"fixed32,21,opt,name=cpu_load,json=cpuLoad,proto3" json:"cpu_load,omitempty"`
	// Deprecated: Marked as deprecated in livekit_internal.proto.
	MemoryLoad        float32 `protobuf:"fixed32,33,opt,name=memory_load,json=memoryLoad,proto3" json:"memory_load,omitempty"`
	MemoryTotal       uint64  `protobuf:"varint,34,opt,name=memory_total,json=memoryTotal,proto3" json:"memory_total,omitempty"`
	MemoryUsed        uint64  `protobuf:"varint,35,opt,name=memory_used,json=memoryUsed,proto3" json:"memory_used,omitempty"`
	SysPacketsOut     uint32  `protobuf:"varint,28,opt,name=sys_packets_out,json=sysPacketsOut,proto3" json:"sys_packets_out,omitempty"`
	SysPacketsDropped uint32  `protobuf:"varint,29,opt,name=sys_packets_dropped,json=sysPacketsDropped,proto3" json:"sys_packets_dropped,omitempty"`
	// Deprecated: Marked as deprecated in livekit_internal.proto.
	SysPacketsOutPerSec float32 `protobuf:"fixed32,30,opt,name=sys_packets_out_per_sec,json=sysPacketsOutPerSec,proto3" json:"sys_packets_out_per_sec,omitempty"`
	// Deprecated: Marked as deprecated in livekit_internal.proto.
	SysPacketsDroppedPerSec float32 `protobuf:"fixed32,31,opt,name=sys_packets_dropped_per_sec,json=sysPacketsDroppedPerSec,proto3" json:"sys_packets_dropped_per_sec,omitempty"`
	// Deprecated: Marked as deprecated in livekit_internal.proto.
	SysPacketsDroppedPctPerSec float32 `protobuf:"fixed32,32,opt,name=sys_packets_dropped_pct_per_sec,json=sysPacketsDroppedPctPerSec,proto3" json:"sys_packets_dropped_pct_per_sec,omitempty"`
	// retransmissions
	RetransmitBytesOut   uint64 `protobuf:"varint,22,opt,name=retransmit_bytes_out,json=retransmitBytesOut,proto3" json:"retransmit_bytes_out,omitempty"`
	RetransmitPacketsOut uint64 `protobuf:"varint,23,opt,name=retransmit_packets_out,json=retransmitPacketsOut,proto3" json:"retransmit_packets_out,omitempty"`
	// Deprecated: Marked as deprecated in livekit_internal.proto.
	RetransmitBytesOutPerSec float32 `protobuf:"fixed32,24,opt,name=retransmit_bytes_out_per_sec,json=retransmitBytesOutPerSec,proto3" json:"retransmit_bytes_out_per_sec,omitempty"`
	// Deprecated: Marked as deprecated in livekit_internal.proto.
	RetransmitPacketsOutPerSec float32 `protobuf:"fixed32,25,opt,name=retransmit_packets_out_per_sec,json=retransmitPacketsOutPerSec,proto3" json:"retransmit_packets_out_per_sec,omitempty"`
	// participant joins
	ParticipantSignalConnected uint64 `protobuf:"varint,26,opt,name=participant_signal_connected,json=participantSignalConnected,proto3" json:"participant_signal_connected,omitempty"`
	// Deprecated: Marked as deprecated in livekit_internal.proto.
	ParticipantSignalConnectedPerSec float32 `protobuf:"fixed32,27,opt,name=participant_signal_connected_per_sec,json=participantSignalConnectedPerSec,proto3" json:"participant_signal_connected_per_sec,omitempty"`
	ParticipantRtcConnected          uint64  `protobuf:"varint,44,opt,name=participant_rtc_connected,json=participantRtcConnected,proto3" json:"participant_rtc_connected,omitempty"`
	// Deprecated: Marked as deprecated in livekit_internal.proto.
	ParticipantRtcConnectedPerSec float32 `protobuf:"fixed32,45,opt,name=participant_rtc_connected_per_sec,json=participantRtcConnectedPerSec,proto3" json:"participant_rtc_connected_per_sec,omitempty"`
	ParticipantRtcInit            uint64  `protobuf:"varint,46,opt,name=participant_rtc_init,json=participantRtcInit,proto3" json:"participant_rtc_init,omitempty"`
	// Deprecated: Marked as deprecated in livekit_internal.proto.
	ParticipantRtcInitPerSec float32 `protobuf:"fixed32,47,opt,name=participant_rtc_init_per_sec,json=participantRtcInitPerSec,proto3" json:"participant_rtc_init_per_sec,omitempty"`
	// forward metrics
	ForwardLatency uint32           `protobuf:"varint,48,opt,name=forward_latency,json=forwardLatency,proto3" json:"forward_latency,omitempty"`
	ForwardJitter  uint32           `protobuf:"varint,49,opt,name=forward_jitter,json=forwardJitter,proto3" json:"forward_jitter,omitempty"`
	Rates          []*NodeStatsRate `protobuf:"bytes,50,rep,name=rates,proto3" json:"rates,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *NodeStats) Reset() {
	*x = NodeStats{}
	mi := &file_livekit_internal_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeStats) ProtoMessage() {}

func (x *NodeStats) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_internal_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeStats.ProtoReflect.Descriptor instead.
func (*NodeStats) Descriptor() ([]byte, []int) {
	return file_livekit_internal_proto_rawDescGZIP(), []int{1}
}

func (x *NodeStats) GetStartedAt() int64 {
	if x != nil {
		return x.StartedAt
	}
	return 0
}

func (x *NodeStats) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *NodeStats) GetNumRooms() int32 {
	if x != nil {
		return x.NumRooms
	}
	return 0
}

func (x *NodeStats) GetNumClients() int32 {
	if x != nil {
		return x.NumClients
	}
	return 0
}

func (x *NodeStats) GetNumTracksIn() int32 {
	if x != nil {
		return x.NumTracksIn
	}
	return 0
}

func (x *NodeStats) GetNumTracksOut() int32 {
	if x != nil {
		return x.NumTracksOut
	}
	return 0
}

func (x *NodeStats) GetNumTrackPublishAttempts() int32 {
	if x != nil {
		return x.NumTrackPublishAttempts
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_internal.proto.
func (x *NodeStats) GetTrackPublishAttemptsPerSec() float32 {
	if x != nil {
		return x.TrackPublishAttemptsPerSec
	}
	return 0
}

func (x *NodeStats) GetNumTrackPublishSuccess() int32 {
	if x != nil {
		return x.NumTrackPublishSuccess
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_internal.proto.
func (x *NodeStats) GetTrackPublishSuccessPerSec() float32 {
	if x != nil {
		return x.TrackPublishSuccessPerSec
	}
	return 0
}

func (x *NodeStats) GetNumTrackSubscribeAttempts() int32 {
	if x != nil {
		return x.NumTrackSubscribeAttempts
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_internal.proto.
func (x *NodeStats) GetTrackSubscribeAttemptsPerSec() float32 {
	if x != nil {
		return x.TrackSubscribeAttemptsPerSec
	}
	return 0
}

func (x *NodeStats) GetNumTrackSubscribeSuccess() int32 {
	if x != nil {
		return x.NumTrackSubscribeSuccess
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_internal.proto.
func (x *NodeStats) GetTrackSubscribeSuccessPerSec() float32 {
	if x != nil {
		return x.TrackSubscribeSuccessPerSec
	}
	return 0
}

func (x *NodeStats) GetBytesIn() uint64 {
	if x != nil {
		return x.BytesIn
	}
	return 0
}

func (x *NodeStats) GetBytesOut() uint64 {
	if x != nil {
		return x.BytesOut
	}
	return 0
}

func (x *NodeStats) GetPacketsIn() uint64 {
	if x != nil {
		return x.PacketsIn
	}
	return 0
}

func (x *NodeStats) GetPacketsOut() uint64 {
	if x != nil {
		return x.PacketsOut
	}
	return 0
}

func (x *NodeStats) GetNackTotal() uint64 {
	if x != nil {
		return x.NackTotal
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_internal.proto.
func (x *NodeStats) GetBytesInPerSec() float32 {
	if x != nil {
		return x.BytesInPerSec
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_internal.proto.
func (x *NodeStats) GetBytesOutPerSec() float32 {
	if x != nil {
		return x.BytesOutPerSec
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_internal.proto.
func (x *NodeStats) GetPacketsInPerSec() float32 {
	if x != nil {
		return x.PacketsInPerSec
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_internal.proto.
func (x *NodeStats) GetPacketsOutPerSec() float32 {
	if x != nil {
		return x.PacketsOutPerSec
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_internal.proto.
func (x *NodeStats) GetNackPerSec() float32 {
	if x != nil {
		return x.NackPerSec
	}
	return 0
}

func (x *NodeStats) GetNumCpus() uint32 {
	if x != nil {
		return x.NumCpus
	}
	return 0
}

func (x *NodeStats) GetLoadAvgLast1Min() float32 {
	if x != nil {
		return x.LoadAvgLast1Min
	}
	return 0
}

func (x *NodeStats) GetLoadAvgLast5Min() float32 {
	if x != nil {
		return x.LoadAvgLast5Min
	}
	return 0
}

func (x *NodeStats) GetLoadAvgLast15Min() float32 {
	if x != nil {
		return x.LoadAvgLast15Min
	}
	return 0
}

func (x *NodeStats) GetCpuLoad() float32 {
	if x != nil {
		return x.CpuLoad
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_internal.proto.
func (x *NodeStats) GetMemoryLoad() float32 {
	if x != nil {
		return x.MemoryLoad
	}
	return 0
}

func (x *NodeStats) GetMemoryTotal() uint64 {
	if x != nil {
		return x.MemoryTotal
	}
	return 0
}

func (x *NodeStats) GetMemoryUsed() uint64 {
	if x != nil {
		return x.MemoryUsed
	}
	return 0
}

func (x *NodeStats) GetSysPacketsOut() uint32 {
	if x != nil {
		return x.SysPacketsOut
	}
	return 0
}

func (x *NodeStats) GetSysPacketsDropped() uint32 {
	if x != nil {
		return x.SysPacketsDropped
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_internal.proto.
func (x *NodeStats) GetSysPacketsOutPerSec() float32 {
	if x != nil {
		return x.SysPacketsOutPerSec
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_internal.proto.
func (x *NodeStats) GetSysPacketsDroppedPerSec() float32 {
	if x != nil {
		return x.SysPacketsDroppedPerSec
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_internal.proto.
func (x *NodeStats) GetSysPacketsDroppedPctPerSec() float32 {
	if x != nil {
		return x.SysPacketsDroppedPctPerSec
	}
	return 0
}

func (x *NodeStats) GetRetransmitBytesOut() uint64 {
	if x != nil {
		return x.RetransmitBytesOut
	}
	return 0
}

func (x *NodeStats) GetRetransmitPacketsOut() uint64 {
	if x != nil {
		return x.RetransmitPacketsOut
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_internal.proto.
func (x *NodeStats) GetRetransmitBytesOutPerSec() float32 {
	if x != nil {
		return x.RetransmitBytesOutPerSec
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_internal.proto.
func (x *NodeStats) GetRetransmitPacketsOutPerSec() float32 {
	if x != nil {
		return x.RetransmitPacketsOutPerSec
	}
	return 0
}

func (x *NodeStats) GetParticipantSignalConnected() uint64 {
	if x != nil {
		return x.ParticipantSignalConnected
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_internal.proto.
func (x *NodeStats) GetParticipantSignalConnectedPerSec() float32 {
	if x != nil {
		return x.ParticipantSignalConnectedPerSec
	}
	return 0
}

func (x *NodeStats) GetParticipantRtcConnected() uint64 {
	if x != nil {
		return x.ParticipantRtcConnected
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_internal.proto.
func (x *NodeStats) GetParticipantRtcConnectedPerSec() float32 {
	if x != nil {
		return x.ParticipantRtcConnectedPerSec
	}
	return 0
}

func (x *NodeStats) GetParticipantRtcInit() uint64 {
	if x != nil {
		return x.ParticipantRtcInit
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_internal.proto.
func (x *NodeStats) GetParticipantRtcInitPerSec() float32 {
	if x != nil {
		return x.ParticipantRtcInitPerSec
	}
	return 0
}

func (x *NodeStats) GetForwardLatency() uint32 {
	if x != nil {
		return x.ForwardLatency
	}
	return 0
}

func (x *NodeStats) GetForwardJitter() uint32 {
	if x != nil {
		return x.ForwardJitter
	}
	return 0
}

func (x *NodeStats) GetRates() []*NodeStatsRate {
	if x != nil {
		return x.Rates
	}
	return nil
}

// rates of different node stats (per second)
type NodeStatsRate struct {
	state                      protoimpl.MessageState `protogen:"open.v1"`
	StartedAt                  int64                  `protobuf:"varint,1,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	EndedAt                    int64                  `protobuf:"varint,2,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`
	Duration                   int64                  `protobuf:"varint,3,opt,name=duration,proto3" json:"duration,omitempty"`
	TrackPublishAttempts       float32                `protobuf:"fixed32,4,opt,name=track_publish_attempts,json=trackPublishAttempts,proto3" json:"track_publish_attempts,omitempty"`
	TrackPublishSuccess        float32                `protobuf:"fixed32,5,opt,name=track_publish_success,json=trackPublishSuccess,proto3" json:"track_publish_success,omitempty"`
	TrackSubscribeAttempts     float32                `protobuf:"fixed32,6,opt,name=track_subscribe_attempts,json=trackSubscribeAttempts,proto3" json:"track_subscribe_attempts,omitempty"`
	TrackSubscribeSuccess      float32                `protobuf:"fixed32,7,opt,name=track_subscribe_success,json=trackSubscribeSuccess,proto3" json:"track_subscribe_success,omitempty"`
	BytesIn                    float32                `protobuf:"fixed32,8,opt,name=bytes_in,json=bytesIn,proto3" json:"bytes_in,omitempty"`
	BytesOut                   float32                `protobuf:"fixed32,9,opt,name=bytes_out,json=bytesOut,proto3" json:"bytes_out,omitempty"`
	PacketsIn                  float32                `protobuf:"fixed32,10,opt,name=packets_in,json=packetsIn,proto3" json:"packets_in,omitempty"`
	PacketsOut                 float32                `protobuf:"fixed32,11,opt,name=packets_out,json=packetsOut,proto3" json:"packets_out,omitempty"`
	NackTotal                  float32                `protobuf:"fixed32,12,opt,name=nack_total,json=nackTotal,proto3" json:"nack_total,omitempty"`
	SysPacketsOut              float32                `protobuf:"fixed32,13,opt,name=sys_packets_out,json=sysPacketsOut,proto3" json:"sys_packets_out,omitempty"`
	SysPacketsDropped          float32                `protobuf:"fixed32,14,opt,name=sys_packets_dropped,json=sysPacketsDropped,proto3" json:"sys_packets_dropped,omitempty"`
	RetransmitBytesOut         float32                `protobuf:"fixed32,15,opt,name=retransmit_bytes_out,json=retransmitBytesOut,proto3" json:"retransmit_bytes_out,omitempty"`
	RetransmitPacketsOut       float32                `protobuf:"fixed32,16,opt,name=retransmit_packets_out,json=retransmitPacketsOut,proto3" json:"retransmit_packets_out,omitempty"`
	ParticipantSignalConnected float32                `protobuf:"fixed32,17,opt,name=participant_signal_connected,json=participantSignalConnected,proto3" json:"participant_signal_connected,omitempty"`
	ParticipantRtcConnected    float32                `protobuf:"fixed32,18,opt,name=participant_rtc_connected,json=participantRtcConnected,proto3" json:"participant_rtc_connected,omitempty"`
	ParticipantRtcInit         float32                `protobuf:"fixed32,19,opt,name=participant_rtc_init,json=participantRtcInit,proto3" json:"participant_rtc_init,omitempty"`
	// time weighted averages across stats windows forming part of a rate measurement interval
	CpuLoad       float32 `protobuf:"fixed32,20,opt,name=cpu_load,json=cpuLoad,proto3" json:"cpu_load,omitempty"`
	MemoryLoad    float32 `protobuf:"fixed32,21,opt,name=memory_load,json=memoryLoad,proto3" json:"memory_load,omitempty"`
	MemoryUsed    float32 `protobuf:"fixed32,22,opt,name=memory_used,json=memoryUsed,proto3" json:"memory_used,omitempty"`
	MemoryTotal   float32 `protobuf:"fixed32,23,opt,name=memory_total,json=memoryTotal,proto3" json:"memory_total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NodeStatsRate) Reset() {
	*x = NodeStatsRate{}
	mi := &file_livekit_internal_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeStatsRate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeStatsRate) ProtoMessage() {}

func (x *NodeStatsRate) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_internal_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeStatsRate.ProtoReflect.Descriptor instead.
func (*NodeStatsRate) Descriptor() ([]byte, []int) {
	return file_livekit_internal_proto_rawDescGZIP(), []int{2}
}

func (x *NodeStatsRate) GetStartedAt() int64 {
	if x != nil {
		return x.StartedAt
	}
	return 0
}

func (x *NodeStatsRate) GetEndedAt() int64 {
	if x != nil {
		return x.EndedAt
	}
	return 0
}

func (x *NodeStatsRate) GetDuration() int64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *NodeStatsRate) GetTrackPublishAttempts() float32 {
	if x != nil {
		return x.TrackPublishAttempts
	}
	return 0
}

func (x *NodeStatsRate) GetTrackPublishSuccess() float32 {
	if x != nil {
		return x.TrackPublishSuccess
	}
	return 0
}

func (x *NodeStatsRate) GetTrackSubscribeAttempts() float32 {
	if x != nil {
		return x.TrackSubscribeAttempts
	}
	return 0
}

func (x *NodeStatsRate) GetTrackSubscribeSuccess() float32 {
	if x != nil {
		return x.TrackSubscribeSuccess
	}
	return 0
}

func (x *NodeStatsRate) GetBytesIn() float32 {
	if x != nil {
		return x.BytesIn
	}
	return 0
}

func (x *NodeStatsRate) GetBytesOut() float32 {
	if x != nil {
		return x.BytesOut
	}
	return 0
}

func (x *NodeStatsRate) GetPacketsIn() float32 {
	if x != nil {
		return x.PacketsIn
	}
	return 0
}

func (x *NodeStatsRate) GetPacketsOut() float32 {
	if x != nil {
		return x.PacketsOut
	}
	return 0
}

func (x *NodeStatsRate) GetNackTotal() float32 {
	if x != nil {
		return x.NackTotal
	}
	return 0
}

func (x *NodeStatsRate) GetSysPacketsOut() float32 {
	if x != nil {
		return x.SysPacketsOut
	}
	return 0
}

func (x *NodeStatsRate) GetSysPacketsDropped() float32 {
	if x != nil {
		return x.SysPacketsDropped
	}
	return 0
}

func (x *NodeStatsRate) GetRetransmitBytesOut() float32 {
	if x != nil {
		return x.RetransmitBytesOut
	}
	return 0
}

func (x *NodeStatsRate) GetRetransmitPacketsOut() float32 {
	if x != nil {
		return x.RetransmitPacketsOut
	}
	return 0
}

func (x *NodeStatsRate) GetParticipantSignalConnected() float32 {
	if x != nil {
		return x.ParticipantSignalConnected
	}
	return 0
}

func (x *NodeStatsRate) GetParticipantRtcConnected() float32 {
	if x != nil {
		return x.ParticipantRtcConnected
	}
	return 0
}

func (x *NodeStatsRate) GetParticipantRtcInit() float32 {
	if x != nil {
		return x.ParticipantRtcInit
	}
	return 0
}

func (x *NodeStatsRate) GetCpuLoad() float32 {
	if x != nil {
		return x.CpuLoad
	}
	return 0
}

func (x *NodeStatsRate) GetMemoryLoad() float32 {
	if x != nil {
		return x.MemoryLoad
	}
	return 0
}

func (x *NodeStatsRate) GetMemoryUsed() float32 {
	if x != nil {
		return x.MemoryUsed
	}
	return 0
}

func (x *NodeStatsRate) GetMemoryTotal() float32 {
	if x != nil {
		return x.MemoryTotal
	}
	return 0
}

type StartSession struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	RoomName     string                 `protobuf:"bytes,1,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`
	Identity     string                 `protobuf:"bytes,2,opt,name=identity,proto3" json:"identity,omitempty"`
	ConnectionId string                 `protobuf:"bytes,3,opt,name=connection_id,json=connectionId,proto3" json:"connection_id,omitempty"`
	// if a client is reconnecting (i.e. resume instead of restart)
	Reconnect     bool        `protobuf:"varint,4,opt,name=reconnect,proto3" json:"reconnect,omitempty"`
	AutoSubscribe bool        `protobuf:"varint,9,opt,name=auto_subscribe,json=autoSubscribe,proto3" json:"auto_subscribe,omitempty"`
	Hidden        bool        `protobuf:"varint,10,opt,name=hidden,proto3" json:"hidden,omitempty"`
	Client        *ClientInfo `protobuf:"bytes,11,opt,name=client,proto3" json:"client,omitempty"`
	Recorder      bool        `protobuf:"varint,12,opt,name=recorder,proto3" json:"recorder,omitempty"`
	Name          string      `protobuf:"bytes,13,opt,name=name,proto3" json:"name,omitempty"`
	// A user's ClaimGrants serialized in JSON
	GrantsJson     string `protobuf:"bytes,14,opt,name=grants_json,json=grantsJson,proto3" json:"grants_json,omitempty"`
	AdaptiveStream bool   `protobuf:"varint,15,opt,name=adaptive_stream,json=adaptiveStream,proto3" json:"adaptive_stream,omitempty"`
	// if reconnect, client will set current sid
	ParticipantId        string             `protobuf:"bytes,16,opt,name=participant_id,json=participantId,proto3" json:"participant_id,omitempty"`
	ReconnectReason      ReconnectReason    `protobuf:"varint,17,opt,name=reconnect_reason,json=reconnectReason,proto3,enum=livekit.ReconnectReason" json:"reconnect_reason,omitempty"`
	SubscriberAllowPause *bool              `protobuf:"varint,18,opt,name=subscriber_allow_pause,json=subscriberAllowPause,proto3,oneof" json:"subscriber_allow_pause,omitempty"`
	DisableIceLite       bool               `protobuf:"varint,19,opt,name=disable_ice_lite,json=disableIceLite,proto3" json:"disable_ice_lite,omitempty"`
	CreateRoom           *CreateRoomRequest `protobuf:"bytes,20,opt,name=create_room,json=createRoom,proto3" json:"create_room,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *StartSession) Reset() {
	*x = StartSession{}
	mi := &file_livekit_internal_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartSession) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartSession) ProtoMessage() {}

func (x *StartSession) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_internal_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartSession.ProtoReflect.Descriptor instead.
func (*StartSession) Descriptor() ([]byte, []int) {
	return file_livekit_internal_proto_rawDescGZIP(), []int{3}
}

func (x *StartSession) GetRoomName() string {
	if x != nil {
		return x.RoomName
	}
	return ""
}

func (x *StartSession) GetIdentity() string {
	if x != nil {
		return x.Identity
	}
	return ""
}

func (x *StartSession) GetConnectionId() string {
	if x != nil {
		return x.ConnectionId
	}
	return ""
}

func (x *StartSession) GetReconnect() bool {
	if x != nil {
		return x.Reconnect
	}
	return false
}

func (x *StartSession) GetAutoSubscribe() bool {
	if x != nil {
		return x.AutoSubscribe
	}
	return false
}

func (x *StartSession) GetHidden() bool {
	if x != nil {
		return x.Hidden
	}
	return false
}

func (x *StartSession) GetClient() *ClientInfo {
	if x != nil {
		return x.Client
	}
	return nil
}

func (x *StartSession) GetRecorder() bool {
	if x != nil {
		return x.Recorder
	}
	return false
}

func (x *StartSession) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *StartSession) GetGrantsJson() string {
	if x != nil {
		return x.GrantsJson
	}
	return ""
}

func (x *StartSession) GetAdaptiveStream() bool {
	if x != nil {
		return x.AdaptiveStream
	}
	return false
}

func (x *StartSession) GetParticipantId() string {
	if x != nil {
		return x.ParticipantId
	}
	return ""
}

func (x *StartSession) GetReconnectReason() ReconnectReason {
	if x != nil {
		return x.ReconnectReason
	}
	return ReconnectReason_RR_UNKNOWN
}

func (x *StartSession) GetSubscriberAllowPause() bool {
	if x != nil && x.SubscriberAllowPause != nil {
		return *x.SubscriberAllowPause
	}
	return false
}

func (x *StartSession) GetDisableIceLite() bool {
	if x != nil {
		return x.DisableIceLite
	}
	return false
}

func (x *StartSession) GetCreateRoom() *CreateRoomRequest {
	if x != nil {
		return x.CreateRoom
	}
	return nil
}

// room info that should not be returned to clients
type RoomInternal struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	TrackEgress       *AutoTrackEgress       `protobuf:"bytes,1,opt,name=track_egress,json=trackEgress,proto3" json:"track_egress,omitempty"`
	ParticipantEgress *AutoParticipantEgress `protobuf:"bytes,2,opt,name=participant_egress,json=participantEgress,proto3" json:"participant_egress,omitempty"`
	PlayoutDelay      *PlayoutDelay          `protobuf:"bytes,3,opt,name=playout_delay,json=playoutDelay,proto3" json:"playout_delay,omitempty"`
	AgentDispatches   []*RoomAgentDispatch   `protobuf:"bytes,5,rep,name=agent_dispatches,json=agentDispatches,proto3" json:"agent_dispatches,omitempty"`
	SyncStreams       bool                   `protobuf:"varint,4,opt,name=sync_streams,json=syncStreams,proto3" json:"sync_streams,omitempty"`
	ReplayEnabled     bool                   `protobuf:"varint,6,opt,name=replay_enabled,json=replayEnabled,proto3" json:"replay_enabled,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *RoomInternal) Reset() {
	*x = RoomInternal{}
	mi := &file_livekit_internal_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoomInternal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomInternal) ProtoMessage() {}

func (x *RoomInternal) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_internal_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomInternal.ProtoReflect.Descriptor instead.
func (*RoomInternal) Descriptor() ([]byte, []int) {
	return file_livekit_internal_proto_rawDescGZIP(), []int{4}
}

func (x *RoomInternal) GetTrackEgress() *AutoTrackEgress {
	if x != nil {
		return x.TrackEgress
	}
	return nil
}

func (x *RoomInternal) GetParticipantEgress() *AutoParticipantEgress {
	if x != nil {
		return x.ParticipantEgress
	}
	return nil
}

func (x *RoomInternal) GetPlayoutDelay() *PlayoutDelay {
	if x != nil {
		return x.PlayoutDelay
	}
	return nil
}

func (x *RoomInternal) GetAgentDispatches() []*RoomAgentDispatch {
	if x != nil {
		return x.AgentDispatches
	}
	return nil
}

func (x *RoomInternal) GetSyncStreams() bool {
	if x != nil {
		return x.SyncStreams
	}
	return false
}

func (x *RoomInternal) GetReplayEnabled() bool {
	if x != nil {
		return x.ReplayEnabled
	}
	return false
}

type ICEConfig struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	PreferenceSubscriber ICECandidateType       `protobuf:"varint,1,opt,name=preference_subscriber,json=preferenceSubscriber,proto3,enum=livekit.ICECandidateType" json:"preference_subscriber,omitempty"`
	PreferencePublisher  ICECandidateType       `protobuf:"varint,2,opt,name=preference_publisher,json=preferencePublisher,proto3,enum=livekit.ICECandidateType" json:"preference_publisher,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *ICEConfig) Reset() {
	*x = ICEConfig{}
	mi := &file_livekit_internal_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ICEConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ICEConfig) ProtoMessage() {}

func (x *ICEConfig) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_internal_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ICEConfig.ProtoReflect.Descriptor instead.
func (*ICEConfig) Descriptor() ([]byte, []int) {
	return file_livekit_internal_proto_rawDescGZIP(), []int{5}
}

func (x *ICEConfig) GetPreferenceSubscriber() ICECandidateType {
	if x != nil {
		return x.PreferenceSubscriber
	}
	return ICECandidateType_ICT_NONE
}

func (x *ICEConfig) GetPreferencePublisher() ICECandidateType {
	if x != nil {
		return x.PreferencePublisher
	}
	return ICECandidateType_ICT_NONE
}

var File_livekit_internal_proto protoreflect.FileDescriptor

const file_livekit_internal_proto_rawDesc = "" +
	"\n" +
	"\x16livekit_internal.proto\x12\alivekit\x1a\x14livekit_models.proto\x1a\x14livekit_egress.proto\x1a\x1clivekit_agent_dispatch.proto\x1a\x12livekit_room.proto\"\xd4\x01\n" +
	"\x04Node\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x0e\n" +
	"\x02ip\x18\x02 \x01(\tR\x02ip\x12\x19\n" +
	"\bnum_cpus\x18\x03 \x01(\rR\anumCpus\x12(\n" +
	"\x05stats\x18\x04 \x01(\v2\x12.livekit.NodeStatsR\x05stats\x12%\n" +
	"\x04type\x18\x05 \x01(\x0e2\x11.livekit.NodeTypeR\x04type\x12(\n" +
	"\x05state\x18\x06 \x01(\x0e2\x12.livekit.NodeStateR\x05state\x12\x16\n" +
	"\x06region\x18\a \x01(\tR\x06region\"\xb4\x13\n" +
	"\tNodeStats\x12\x1d\n" +
	"\n" +
	"started_at\x18\x01 \x01(\x03R\tstartedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\x02 \x01(\x03R\tupdatedAt\x12\x1b\n" +
	"\tnum_rooms\x18\x03 \x01(\x05R\bnumRooms\x12\x1f\n" +
	"\vnum_clients\x18\x04 \x01(\x05R\n" +
	"numClients\x12\"\n" +
	"\rnum_tracks_in\x18\x05 \x01(\x05R\vnumTracksIn\x12$\n" +
	"\x0enum_tracks_out\x18\x06 \x01(\x05R\fnumTracksOut\x12;\n" +
	"\x1anum_track_publish_attempts\x18$ \x01(\x05R\x17numTrackPublishAttempts\x12F\n" +
	"\x1etrack_publish_attempts_per_sec\x18% \x01(\x02B\x02\x18\x01R\x1atrackPublishAttemptsPerSec\x129\n" +
	"\x19num_track_publish_success\x18& \x01(\x05R\x16numTrackPublishSuccess\x12D\n" +
	"\x1dtrack_publish_success_per_sec\x18' \x01(\x02B\x02\x18\x01R\x19trackPublishSuccessPerSec\x12?\n" +
	"\x1cnum_track_subscribe_attempts\x18( \x01(\x05R\x19numTrackSubscribeAttempts\x12J\n" +
	" track_subscribe_attempts_per_sec\x18) \x01(\x02B\x02\x18\x01R\x1ctrackSubscribeAttemptsPerSec\x12=\n" +
	"\x1bnum_track_subscribe_success\x18* \x01(\x05R\x18numTrackSubscribeSuccess\x12H\n" +
	"\x1ftrack_subscribe_success_per_sec\x18+ \x01(\x02B\x02\x18\x01R\x1btrackSubscribeSuccessPerSec\x12\x19\n" +
	"\bbytes_in\x18\a \x01(\x04R\abytesIn\x12\x1b\n" +
	"\tbytes_out\x18\b \x01(\x04R\bbytesOut\x12\x1d\n" +
	"\n" +
	"packets_in\x18\t \x01(\x04R\tpacketsIn\x12\x1f\n" +
	"\vpackets_out\x18\n" +
	" \x01(\x04R\n" +
	"packetsOut\x12\x1d\n" +
	"\n" +
	"nack_total\x18\v \x01(\x04R\tnackTotal\x12+\n" +
	"\x10bytes_in_per_sec\x18\f \x01(\x02B\x02\x18\x01R\rbytesInPerSec\x12-\n" +
	"\x11bytes_out_per_sec\x18\r \x01(\x02B\x02\x18\x01R\x0ebytesOutPerSec\x12/\n" +
	"\x12packets_in_per_sec\x18\x0e \x01(\x02B\x02\x18\x01R\x0fpacketsInPerSec\x121\n" +
	"\x13packets_out_per_sec\x18\x0f \x01(\x02B\x02\x18\x01R\x10packetsOutPerSec\x12$\n" +
	"\fnack_per_sec\x18\x10 \x01(\x02B\x02\x18\x01R\n" +
	"nackPerSec\x12\x19\n" +
	"\bnum_cpus\x18\x11 \x01(\rR\anumCpus\x12*\n" +
	"\x11load_avg_last1min\x18\x12 \x01(\x02R\x0floadAvgLast1min\x12*\n" +
	"\x11load_avg_last5min\x18\x13 \x01(\x02R\x0floadAvgLast5min\x12,\n" +
	"\x12load_avg_last15min\x18\x14 \x01(\x02R\x10loadAvgLast15min\x12\x19\n" +
	"\bcpu_load\x18\x15 \x01(\x02R\acpuLoad\x12#\n" +
	"\vmemory_load\x18! \x01(\x02B\x02\x18\x01R\n" +
	"memoryLoad\x12!\n" +
	"\fmemory_total\x18\" \x01(\x04R\vmemoryTotal\x12\x1f\n" +
	"\vmemory_used\x18# \x01(\x04R\n" +
	"memoryUsed\x12&\n" +
	"\x0fsys_packets_out\x18\x1c \x01(\rR\rsysPacketsOut\x12.\n" +
	"\x13sys_packets_dropped\x18\x1d \x01(\rR\x11sysPacketsDropped\x128\n" +
	"\x17sys_packets_out_per_sec\x18\x1e \x01(\x02B\x02\x18\x01R\x13sysPacketsOutPerSec\x12@\n" +
	"\x1bsys_packets_dropped_per_sec\x18\x1f \x01(\x02B\x02\x18\x01R\x17sysPacketsDroppedPerSec\x12G\n" +
	"\x1fsys_packets_dropped_pct_per_sec\x18  \x01(\x02B\x02\x18\x01R\x1asysPacketsDroppedPctPerSec\x120\n" +
	"\x14retransmit_bytes_out\x18\x16 \x01(\x04R\x12retransmitBytesOut\x124\n" +
	"\x16retransmit_packets_out\x18\x17 \x01(\x04R\x14retransmitPacketsOut\x12B\n" +
	"\x1cretransmit_bytes_out_per_sec\x18\x18 \x01(\x02B\x02\x18\x01R\x18retransmitBytesOutPerSec\x12F\n" +
	"\x1eretransmit_packets_out_per_sec\x18\x19 \x01(\x02B\x02\x18\x01R\x1aretransmitPacketsOutPerSec\x12@\n" +
	"\x1cparticipant_signal_connected\x18\x1a \x01(\x04R\x1aparticipantSignalConnected\x12R\n" +
	"$participant_signal_connected_per_sec\x18\x1b \x01(\x02B\x02\x18\x01R participantSignalConnectedPerSec\x12:\n" +
	"\x19participant_rtc_connected\x18, \x01(\x04R\x17participantRtcConnected\x12L\n" +
	"!participant_rtc_connected_per_sec\x18- \x01(\x02B\x02\x18\x01R\x1dparticipantRtcConnectedPerSec\x120\n" +
	"\x14participant_rtc_init\x18. \x01(\x04R\x12participantRtcInit\x12B\n" +
	"\x1cparticipant_rtc_init_per_sec\x18/ \x01(\x02B\x02\x18\x01R\x18participantRtcInitPerSec\x12'\n" +
	"\x0fforward_latency\x180 \x01(\rR\x0eforwardLatency\x12%\n" +
	"\x0eforward_jitter\x181 \x01(\rR\rforwardJitter\x12,\n" +
	"\x05rates\x182 \x03(\v2\x16.livekit.NodeStatsRateR\x05rates\"\xc8\a\n" +
	"\rNodeStatsRate\x12\x1d\n" +
	"\n" +
	"started_at\x18\x01 \x01(\x03R\tstartedAt\x12\x19\n" +
	"\bended_at\x18\x02 \x01(\x03R\aendedAt\x12\x1a\n" +
	"\bduration\x18\x03 \x01(\x03R\bduration\x124\n" +
	"\x16track_publish_attempts\x18\x04 \x01(\x02R\x14trackPublishAttempts\x122\n" +
	"\x15track_publish_success\x18\x05 \x01(\x02R\x13trackPublishSuccess\x128\n" +
	"\x18track_subscribe_attempts\x18\x06 \x01(\x02R\x16trackSubscribeAttempts\x126\n" +
	"\x17track_subscribe_success\x18\a \x01(\x02R\x15trackSubscribeSuccess\x12\x19\n" +
	"\bbytes_in\x18\b \x01(\x02R\abytesIn\x12\x1b\n" +
	"\tbytes_out\x18\t \x01(\x02R\bbytesOut\x12\x1d\n" +
	"\n" +
	"packets_in\x18\n" +
	" \x01(\x02R\tpacketsIn\x12\x1f\n" +
	"\vpackets_out\x18\v \x01(\x02R\n" +
	"packetsOut\x12\x1d\n" +
	"\n" +
	"nack_total\x18\f \x01(\x02R\tnackTotal\x12&\n" +
	"\x0fsys_packets_out\x18\r \x01(\x02R\rsysPacketsOut\x12.\n" +
	"\x13sys_packets_dropped\x18\x0e \x01(\x02R\x11sysPacketsDropped\x120\n" +
	"\x14retransmit_bytes_out\x18\x0f \x01(\x02R\x12retransmitBytesOut\x124\n" +
	"\x16retransmit_packets_out\x18\x10 \x01(\x02R\x14retransmitPacketsOut\x12@\n" +
	"\x1cparticipant_signal_connected\x18\x11 \x01(\x02R\x1aparticipantSignalConnected\x12:\n" +
	"\x19participant_rtc_connected\x18\x12 \x01(\x02R\x17participantRtcConnected\x120\n" +
	"\x14participant_rtc_init\x18\x13 \x01(\x02R\x12participantRtcInit\x12\x19\n" +
	"\bcpu_load\x18\x14 \x01(\x02R\acpuLoad\x12\x1f\n" +
	"\vmemory_load\x18\x15 \x01(\x02R\n" +
	"memoryLoad\x12\x1f\n" +
	"\vmemory_used\x18\x16 \x01(\x02R\n" +
	"memoryUsed\x12!\n" +
	"\fmemory_total\x18\x17 \x01(\x02R\vmemoryTotal\"\x99\x05\n" +
	"\fStartSession\x12\x1b\n" +
	"\troom_name\x18\x01 \x01(\tR\broomName\x12\x1a\n" +
	"\bidentity\x18\x02 \x01(\tR\bidentity\x12#\n" +
	"\rconnection_id\x18\x03 \x01(\tR\fconnectionId\x12\x1c\n" +
	"\treconnect\x18\x04 \x01(\bR\treconnect\x12%\n" +
	"\x0eauto_subscribe\x18\t \x01(\bR\rautoSubscribe\x12\x16\n" +
	"\x06hidden\x18\n" +
	" \x01(\bR\x06hidden\x12+\n" +
	"\x06client\x18\v \x01(\v2\x13.livekit.ClientInfoR\x06client\x12\x1a\n" +
	"\brecorder\x18\f \x01(\bR\brecorder\x12\x12\n" +
	"\x04name\x18\r \x01(\tR\x04name\x12\x1f\n" +
	"\vgrants_json\x18\x0e \x01(\tR\n" +
	"grantsJson\x12'\n" +
	"\x0fadaptive_stream\x18\x0f \x01(\bR\x0eadaptiveStream\x12%\n" +
	"\x0eparticipant_id\x18\x10 \x01(\tR\rparticipantId\x12C\n" +
	"\x10reconnect_reason\x18\x11 \x01(\x0e2\x18.livekit.ReconnectReasonR\x0freconnectReason\x129\n" +
	"\x16subscriber_allow_pause\x18\x12 \x01(\bH\x00R\x14subscriberAllowPause\x88\x01\x01\x12(\n" +
	"\x10disable_ice_lite\x18\x13 \x01(\bR\x0edisableIceLite\x12;\n" +
	"\vcreate_room\x18\x14 \x01(\v2\x1a.livekit.CreateRoomRequestR\n" +
	"createRoomB\x19\n" +
	"\x17_subscriber_allow_pause\"\xe7\x02\n" +
	"\fRoomInternal\x12;\n" +
	"\ftrack_egress\x18\x01 \x01(\v2\x18.livekit.AutoTrackEgressR\vtrackEgress\x12M\n" +
	"\x12participant_egress\x18\x02 \x01(\v2\x1e.livekit.AutoParticipantEgressR\x11participantEgress\x12:\n" +
	"\rplayout_delay\x18\x03 \x01(\v2\x15.livekit.PlayoutDelayR\fplayoutDelay\x12E\n" +
	"\x10agent_dispatches\x18\x05 \x03(\v2\x1a.livekit.RoomAgentDispatchR\x0fagentDispatches\x12!\n" +
	"\fsync_streams\x18\x04 \x01(\bR\vsyncStreams\x12%\n" +
	"\x0ereplay_enabled\x18\x06 \x01(\bR\rreplayEnabled\"\xa9\x01\n" +
	"\tICEConfig\x12N\n" +
	"\x15preference_subscriber\x18\x01 \x01(\x0e2\x19.livekit.ICECandidateTypeR\x14preferenceSubscriber\x12L\n" +
	"\x14preference_publisher\x18\x02 \x01(\x0e2\x19.livekit.ICECandidateTypeR\x13preferencePublisher*h\n" +
	"\bNodeType\x12\n" +
	"\n" +
	"\x06SERVER\x10\x00\x12\x0e\n" +
	"\n" +
	"CONTROLLER\x10\x01\x12\t\n" +
	"\x05MEDIA\x10\x02\x12\b\n" +
	"\x04TURN\x10\x04\x12\v\n" +
	"\aSWEEPER\x10\x05\x12\f\n" +
	"\bDIRECTOR\x10\x06\x12\x10\n" +
	"\fHOSTED_AGENT\x10\a*<\n" +
	"\tNodeState\x12\x0f\n" +
	"\vSTARTING_UP\x10\x00\x12\v\n" +
	"\aSERVING\x10\x01\x12\x11\n" +
	"\rSHUTTING_DOWN\x10\x02*:\n" +
	"\x10ICECandidateType\x12\f\n" +
	"\bICT_NONE\x10\x00\x12\v\n" +
	"\aICT_TCP\x10\x01\x12\v\n" +
	"\aICT_TLS\x10\x02BFZ#github.com/livekit/protocol/livekit\xaa\x02\rLiveKit.Proto\xea\x02\x0eLiveKit::Protob\x06proto3"

var (
	file_livekit_internal_proto_rawDescOnce sync.Once
	file_livekit_internal_proto_rawDescData []byte
)

func file_livekit_internal_proto_rawDescGZIP() []byte {
	file_livekit_internal_proto_rawDescOnce.Do(func() {
		file_livekit_internal_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_livekit_internal_proto_rawDesc), len(file_livekit_internal_proto_rawDesc)))
	})
	return file_livekit_internal_proto_rawDescData
}

var file_livekit_internal_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_livekit_internal_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_livekit_internal_proto_goTypes = []any{
	(NodeType)(0),                 // 0: livekit.NodeType
	(NodeState)(0),                // 1: livekit.NodeState
	(ICECandidateType)(0),         // 2: livekit.ICECandidateType
	(*Node)(nil),                  // 3: livekit.Node
	(*NodeStats)(nil),             // 4: livekit.NodeStats
	(*NodeStatsRate)(nil),         // 5: livekit.NodeStatsRate
	(*StartSession)(nil),          // 6: livekit.StartSession
	(*RoomInternal)(nil),          // 7: livekit.RoomInternal
	(*ICEConfig)(nil),             // 8: livekit.ICEConfig
	(*ClientInfo)(nil),            // 9: livekit.ClientInfo
	(ReconnectReason)(0),          // 10: livekit.ReconnectReason
	(*CreateRoomRequest)(nil),     // 11: livekit.CreateRoomRequest
	(*AutoTrackEgress)(nil),       // 12: livekit.AutoTrackEgress
	(*AutoParticipantEgress)(nil), // 13: livekit.AutoParticipantEgress
	(*PlayoutDelay)(nil),          // 14: livekit.PlayoutDelay
	(*RoomAgentDispatch)(nil),     // 15: livekit.RoomAgentDispatch
}
var file_livekit_internal_proto_depIdxs = []int32{
	4,  // 0: livekit.Node.stats:type_name -> livekit.NodeStats
	0,  // 1: livekit.Node.type:type_name -> livekit.NodeType
	1,  // 2: livekit.Node.state:type_name -> livekit.NodeState
	5,  // 3: livekit.NodeStats.rates:type_name -> livekit.NodeStatsRate
	9,  // 4: livekit.StartSession.client:type_name -> livekit.ClientInfo
	10, // 5: livekit.StartSession.reconnect_reason:type_name -> livekit.ReconnectReason
	11, // 6: livekit.StartSession.create_room:type_name -> livekit.CreateRoomRequest
	12, // 7: livekit.RoomInternal.track_egress:type_name -> livekit.AutoTrackEgress
	13, // 8: livekit.RoomInternal.participant_egress:type_name -> livekit.AutoParticipantEgress
	14, // 9: livekit.RoomInternal.playout_delay:type_name -> livekit.PlayoutDelay
	15, // 10: livekit.RoomInternal.agent_dispatches:type_name -> livekit.RoomAgentDispatch
	2,  // 11: livekit.ICEConfig.preference_subscriber:type_name -> livekit.ICECandidateType
	2,  // 12: livekit.ICEConfig.preference_publisher:type_name -> livekit.ICECandidateType
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_livekit_internal_proto_init() }
func file_livekit_internal_proto_init() {
	if File_livekit_internal_proto != nil {
		return
	}
	file_livekit_models_proto_init()
	file_livekit_egress_proto_init()
	file_livekit_agent_dispatch_proto_init()
	file_livekit_room_proto_init()
	file_livekit_internal_proto_msgTypes[3].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_livekit_internal_proto_rawDesc), len(file_livekit_internal_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_livekit_internal_proto_goTypes,
		DependencyIndexes: file_livekit_internal_proto_depIdxs,
		EnumInfos:         file_livekit_internal_proto_enumTypes,
		MessageInfos:      file_livekit_internal_proto_msgTypes,
	}.Build()
	File_livekit_internal_proto = out.File
	file_livekit_internal_proto_goTypes = nil
	file_livekit_internal_proto_depIdxs = nil
}
