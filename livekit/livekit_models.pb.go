// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: livekit_models.proto

package livekit

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AudioCodec int32

const (
	AudioCodec_DEFAULT_AC AudioCodec = 0
	AudioCodec_OPUS       AudioCodec = 1
	AudioCodec_AAC        AudioCodec = 2
)

// Enum value maps for AudioCodec.
var (
	AudioCodec_name = map[int32]string{
		0: "DEFAULT_AC",
		1: "OPUS",
		2: "AAC",
	}
	AudioCodec_value = map[string]int32{
		"DEFAULT_AC": 0,
		"OPUS":       1,
		"AAC":        2,
	}
)

func (x AudioCodec) Enum() *AudioCodec {
	p := new(AudioCodec)
	*p = x
	return p
}

func (x AudioCodec) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AudioCodec) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_models_proto_enumTypes[0].Descriptor()
}

func (AudioCodec) Type() protoreflect.EnumType {
	return &file_livekit_models_proto_enumTypes[0]
}

func (x AudioCodec) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AudioCodec.Descriptor instead.
func (AudioCodec) EnumDescriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{0}
}

type VideoCodec int32

const (
	VideoCodec_DEFAULT_VC    VideoCodec = 0
	VideoCodec_H264_BASELINE VideoCodec = 1
	VideoCodec_H264_MAIN     VideoCodec = 2
	VideoCodec_H264_HIGH     VideoCodec = 3
	VideoCodec_VP8           VideoCodec = 4
)

// Enum value maps for VideoCodec.
var (
	VideoCodec_name = map[int32]string{
		0: "DEFAULT_VC",
		1: "H264_BASELINE",
		2: "H264_MAIN",
		3: "H264_HIGH",
		4: "VP8",
	}
	VideoCodec_value = map[string]int32{
		"DEFAULT_VC":    0,
		"H264_BASELINE": 1,
		"H264_MAIN":     2,
		"H264_HIGH":     3,
		"VP8":           4,
	}
)

func (x VideoCodec) Enum() *VideoCodec {
	p := new(VideoCodec)
	*p = x
	return p
}

func (x VideoCodec) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VideoCodec) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_models_proto_enumTypes[1].Descriptor()
}

func (VideoCodec) Type() protoreflect.EnumType {
	return &file_livekit_models_proto_enumTypes[1]
}

func (x VideoCodec) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VideoCodec.Descriptor instead.
func (VideoCodec) EnumDescriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{1}
}

type ImageCodec int32

const (
	ImageCodec_IC_DEFAULT ImageCodec = 0
	ImageCodec_IC_JPEG    ImageCodec = 1
)

// Enum value maps for ImageCodec.
var (
	ImageCodec_name = map[int32]string{
		0: "IC_DEFAULT",
		1: "IC_JPEG",
	}
	ImageCodec_value = map[string]int32{
		"IC_DEFAULT": 0,
		"IC_JPEG":    1,
	}
)

func (x ImageCodec) Enum() *ImageCodec {
	p := new(ImageCodec)
	*p = x
	return p
}

func (x ImageCodec) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ImageCodec) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_models_proto_enumTypes[2].Descriptor()
}

func (ImageCodec) Type() protoreflect.EnumType {
	return &file_livekit_models_proto_enumTypes[2]
}

func (x ImageCodec) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ImageCodec.Descriptor instead.
func (ImageCodec) EnumDescriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{2}
}

// Policy for publisher to handle subscribers that are unable to support the primary codec of a track
type BackupCodecPolicy int32

const (
	// default behavior, the track prefer to regress to backup codec and all subscribers will receive the backup codec,
	// the sfu will try to regress codec if possible but not assured.
	BackupCodecPolicy_PREFER_REGRESSION BackupCodecPolicy = 0
	// encoding/send the primary and backup codec simultaneously
	BackupCodecPolicy_SIMULCAST BackupCodecPolicy = 1
	// force the track to regress to backup codec, this option can be used in video conference or the publisher has limited bandwidth/encoding power
	BackupCodecPolicy_REGRESSION BackupCodecPolicy = 2
)

// Enum value maps for BackupCodecPolicy.
var (
	BackupCodecPolicy_name = map[int32]string{
		0: "PREFER_REGRESSION",
		1: "SIMULCAST",
		2: "REGRESSION",
	}
	BackupCodecPolicy_value = map[string]int32{
		"PREFER_REGRESSION": 0,
		"SIMULCAST":         1,
		"REGRESSION":        2,
	}
)

func (x BackupCodecPolicy) Enum() *BackupCodecPolicy {
	p := new(BackupCodecPolicy)
	*p = x
	return p
}

func (x BackupCodecPolicy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BackupCodecPolicy) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_models_proto_enumTypes[3].Descriptor()
}

func (BackupCodecPolicy) Type() protoreflect.EnumType {
	return &file_livekit_models_proto_enumTypes[3]
}

func (x BackupCodecPolicy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BackupCodecPolicy.Descriptor instead.
func (BackupCodecPolicy) EnumDescriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{3}
}

type TrackType int32

const (
	TrackType_AUDIO TrackType = 0
	TrackType_VIDEO TrackType = 1
	TrackType_DATA  TrackType = 2
)

// Enum value maps for TrackType.
var (
	TrackType_name = map[int32]string{
		0: "AUDIO",
		1: "VIDEO",
		2: "DATA",
	}
	TrackType_value = map[string]int32{
		"AUDIO": 0,
		"VIDEO": 1,
		"DATA":  2,
	}
)

func (x TrackType) Enum() *TrackType {
	p := new(TrackType)
	*p = x
	return p
}

func (x TrackType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrackType) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_models_proto_enumTypes[4].Descriptor()
}

func (TrackType) Type() protoreflect.EnumType {
	return &file_livekit_models_proto_enumTypes[4]
}

func (x TrackType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrackType.Descriptor instead.
func (TrackType) EnumDescriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{4}
}

type TrackSource int32

const (
	TrackSource_UNKNOWN            TrackSource = 0
	TrackSource_CAMERA             TrackSource = 1
	TrackSource_MICROPHONE         TrackSource = 2
	TrackSource_SCREEN_SHARE       TrackSource = 3
	TrackSource_SCREEN_SHARE_AUDIO TrackSource = 4
)

// Enum value maps for TrackSource.
var (
	TrackSource_name = map[int32]string{
		0: "UNKNOWN",
		1: "CAMERA",
		2: "MICROPHONE",
		3: "SCREEN_SHARE",
		4: "SCREEN_SHARE_AUDIO",
	}
	TrackSource_value = map[string]int32{
		"UNKNOWN":            0,
		"CAMERA":             1,
		"MICROPHONE":         2,
		"SCREEN_SHARE":       3,
		"SCREEN_SHARE_AUDIO": 4,
	}
)

func (x TrackSource) Enum() *TrackSource {
	p := new(TrackSource)
	*p = x
	return p
}

func (x TrackSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrackSource) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_models_proto_enumTypes[5].Descriptor()
}

func (TrackSource) Type() protoreflect.EnumType {
	return &file_livekit_models_proto_enumTypes[5]
}

func (x TrackSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrackSource.Descriptor instead.
func (TrackSource) EnumDescriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{5}
}

type VideoQuality int32

const (
	VideoQuality_LOW    VideoQuality = 0
	VideoQuality_MEDIUM VideoQuality = 1
	VideoQuality_HIGH   VideoQuality = 2
	VideoQuality_OFF    VideoQuality = 3
)

// Enum value maps for VideoQuality.
var (
	VideoQuality_name = map[int32]string{
		0: "LOW",
		1: "MEDIUM",
		2: "HIGH",
		3: "OFF",
	}
	VideoQuality_value = map[string]int32{
		"LOW":    0,
		"MEDIUM": 1,
		"HIGH":   2,
		"OFF":    3,
	}
)

func (x VideoQuality) Enum() *VideoQuality {
	p := new(VideoQuality)
	*p = x
	return p
}

func (x VideoQuality) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VideoQuality) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_models_proto_enumTypes[6].Descriptor()
}

func (VideoQuality) Type() protoreflect.EnumType {
	return &file_livekit_models_proto_enumTypes[6]
}

func (x VideoQuality) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VideoQuality.Descriptor instead.
func (VideoQuality) EnumDescriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{6}
}

type ConnectionQuality int32

const (
	ConnectionQuality_POOR      ConnectionQuality = 0
	ConnectionQuality_GOOD      ConnectionQuality = 1
	ConnectionQuality_EXCELLENT ConnectionQuality = 2
	ConnectionQuality_LOST      ConnectionQuality = 3
)

// Enum value maps for ConnectionQuality.
var (
	ConnectionQuality_name = map[int32]string{
		0: "POOR",
		1: "GOOD",
		2: "EXCELLENT",
		3: "LOST",
	}
	ConnectionQuality_value = map[string]int32{
		"POOR":      0,
		"GOOD":      1,
		"EXCELLENT": 2,
		"LOST":      3,
	}
)

func (x ConnectionQuality) Enum() *ConnectionQuality {
	p := new(ConnectionQuality)
	*p = x
	return p
}

func (x ConnectionQuality) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConnectionQuality) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_models_proto_enumTypes[7].Descriptor()
}

func (ConnectionQuality) Type() protoreflect.EnumType {
	return &file_livekit_models_proto_enumTypes[7]
}

func (x ConnectionQuality) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConnectionQuality.Descriptor instead.
func (ConnectionQuality) EnumDescriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{7}
}

type ClientConfigSetting int32

const (
	ClientConfigSetting_UNSET    ClientConfigSetting = 0
	ClientConfigSetting_DISABLED ClientConfigSetting = 1
	ClientConfigSetting_ENABLED  ClientConfigSetting = 2
)

// Enum value maps for ClientConfigSetting.
var (
	ClientConfigSetting_name = map[int32]string{
		0: "UNSET",
		1: "DISABLED",
		2: "ENABLED",
	}
	ClientConfigSetting_value = map[string]int32{
		"UNSET":    0,
		"DISABLED": 1,
		"ENABLED":  2,
	}
)

func (x ClientConfigSetting) Enum() *ClientConfigSetting {
	p := new(ClientConfigSetting)
	*p = x
	return p
}

func (x ClientConfigSetting) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClientConfigSetting) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_models_proto_enumTypes[8].Descriptor()
}

func (ClientConfigSetting) Type() protoreflect.EnumType {
	return &file_livekit_models_proto_enumTypes[8]
}

func (x ClientConfigSetting) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ClientConfigSetting.Descriptor instead.
func (ClientConfigSetting) EnumDescriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{8}
}

type DisconnectReason int32

const (
	DisconnectReason_UNKNOWN_REASON DisconnectReason = 0
	// the client initiated the disconnect
	DisconnectReason_CLIENT_INITIATED DisconnectReason = 1
	// another participant with the same identity has joined the room
	DisconnectReason_DUPLICATE_IDENTITY DisconnectReason = 2
	// the server instance is shutting down
	DisconnectReason_SERVER_SHUTDOWN DisconnectReason = 3
	// RoomService.RemoveParticipant was called
	DisconnectReason_PARTICIPANT_REMOVED DisconnectReason = 4
	// RoomService.DeleteRoom was called
	DisconnectReason_ROOM_DELETED DisconnectReason = 5
	// the client is attempting to resume a session, but server is not aware of it
	DisconnectReason_STATE_MISMATCH DisconnectReason = 6
	// client was unable to connect fully
	DisconnectReason_JOIN_FAILURE DisconnectReason = 7
	// Cloud-only, the server requested Participant to migrate the connection elsewhere
	DisconnectReason_MIGRATION DisconnectReason = 8
	// the signal websocket was closed unexpectedly
	DisconnectReason_SIGNAL_CLOSE DisconnectReason = 9
	// the room was closed, due to all Standard and Ingress participants having left
	DisconnectReason_ROOM_CLOSED DisconnectReason = 10
	// SIP callee did not respond in time
	DisconnectReason_USER_UNAVAILABLE DisconnectReason = 11
	// SIP callee rejected the call (busy)
	DisconnectReason_USER_REJECTED DisconnectReason = 12
	// SIP protocol failure or unexpected response
	DisconnectReason_SIP_TRUNK_FAILURE DisconnectReason = 13
	// server timed out a participant session
	DisconnectReason_CONNECTION_TIMEOUT DisconnectReason = 14
	// media stream failure or media timeout
	DisconnectReason_MEDIA_FAILURE DisconnectReason = 15
)

// Enum value maps for DisconnectReason.
var (
	DisconnectReason_name = map[int32]string{
		0:  "UNKNOWN_REASON",
		1:  "CLIENT_INITIATED",
		2:  "DUPLICATE_IDENTITY",
		3:  "SERVER_SHUTDOWN",
		4:  "PARTICIPANT_REMOVED",
		5:  "ROOM_DELETED",
		6:  "STATE_MISMATCH",
		7:  "JOIN_FAILURE",
		8:  "MIGRATION",
		9:  "SIGNAL_CLOSE",
		10: "ROOM_CLOSED",
		11: "USER_UNAVAILABLE",
		12: "USER_REJECTED",
		13: "SIP_TRUNK_FAILURE",
		14: "CONNECTION_TIMEOUT",
		15: "MEDIA_FAILURE",
	}
	DisconnectReason_value = map[string]int32{
		"UNKNOWN_REASON":      0,
		"CLIENT_INITIATED":    1,
		"DUPLICATE_IDENTITY":  2,
		"SERVER_SHUTDOWN":     3,
		"PARTICIPANT_REMOVED": 4,
		"ROOM_DELETED":        5,
		"STATE_MISMATCH":      6,
		"JOIN_FAILURE":        7,
		"MIGRATION":           8,
		"SIGNAL_CLOSE":        9,
		"ROOM_CLOSED":         10,
		"USER_UNAVAILABLE":    11,
		"USER_REJECTED":       12,
		"SIP_TRUNK_FAILURE":   13,
		"CONNECTION_TIMEOUT":  14,
		"MEDIA_FAILURE":       15,
	}
)

func (x DisconnectReason) Enum() *DisconnectReason {
	p := new(DisconnectReason)
	*p = x
	return p
}

func (x DisconnectReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DisconnectReason) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_models_proto_enumTypes[9].Descriptor()
}

func (DisconnectReason) Type() protoreflect.EnumType {
	return &file_livekit_models_proto_enumTypes[9]
}

func (x DisconnectReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DisconnectReason.Descriptor instead.
func (DisconnectReason) EnumDescriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{9}
}

type ReconnectReason int32

const (
	ReconnectReason_RR_UNKNOWN             ReconnectReason = 0
	ReconnectReason_RR_SIGNAL_DISCONNECTED ReconnectReason = 1
	ReconnectReason_RR_PUBLISHER_FAILED    ReconnectReason = 2
	ReconnectReason_RR_SUBSCRIBER_FAILED   ReconnectReason = 3
	ReconnectReason_RR_SWITCH_CANDIDATE    ReconnectReason = 4
)

// Enum value maps for ReconnectReason.
var (
	ReconnectReason_name = map[int32]string{
		0: "RR_UNKNOWN",
		1: "RR_SIGNAL_DISCONNECTED",
		2: "RR_PUBLISHER_FAILED",
		3: "RR_SUBSCRIBER_FAILED",
		4: "RR_SWITCH_CANDIDATE",
	}
	ReconnectReason_value = map[string]int32{
		"RR_UNKNOWN":             0,
		"RR_SIGNAL_DISCONNECTED": 1,
		"RR_PUBLISHER_FAILED":    2,
		"RR_SUBSCRIBER_FAILED":   3,
		"RR_SWITCH_CANDIDATE":    4,
	}
)

func (x ReconnectReason) Enum() *ReconnectReason {
	p := new(ReconnectReason)
	*p = x
	return p
}

func (x ReconnectReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReconnectReason) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_models_proto_enumTypes[10].Descriptor()
}

func (ReconnectReason) Type() protoreflect.EnumType {
	return &file_livekit_models_proto_enumTypes[10]
}

func (x ReconnectReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReconnectReason.Descriptor instead.
func (ReconnectReason) EnumDescriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{10}
}

type SubscriptionError int32

const (
	SubscriptionError_SE_UNKNOWN           SubscriptionError = 0
	SubscriptionError_SE_CODEC_UNSUPPORTED SubscriptionError = 1
	SubscriptionError_SE_TRACK_NOTFOUND    SubscriptionError = 2
)

// Enum value maps for SubscriptionError.
var (
	SubscriptionError_name = map[int32]string{
		0: "SE_UNKNOWN",
		1: "SE_CODEC_UNSUPPORTED",
		2: "SE_TRACK_NOTFOUND",
	}
	SubscriptionError_value = map[string]int32{
		"SE_UNKNOWN":           0,
		"SE_CODEC_UNSUPPORTED": 1,
		"SE_TRACK_NOTFOUND":    2,
	}
)

func (x SubscriptionError) Enum() *SubscriptionError {
	p := new(SubscriptionError)
	*p = x
	return p
}

func (x SubscriptionError) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SubscriptionError) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_models_proto_enumTypes[11].Descriptor()
}

func (SubscriptionError) Type() protoreflect.EnumType {
	return &file_livekit_models_proto_enumTypes[11]
}

func (x SubscriptionError) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SubscriptionError.Descriptor instead.
func (SubscriptionError) EnumDescriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{11}
}

type AudioTrackFeature int32

const (
	AudioTrackFeature_TF_STEREO                      AudioTrackFeature = 0
	AudioTrackFeature_TF_NO_DTX                      AudioTrackFeature = 1
	AudioTrackFeature_TF_AUTO_GAIN_CONTROL           AudioTrackFeature = 2
	AudioTrackFeature_TF_ECHO_CANCELLATION           AudioTrackFeature = 3
	AudioTrackFeature_TF_NOISE_SUPPRESSION           AudioTrackFeature = 4
	AudioTrackFeature_TF_ENHANCED_NOISE_CANCELLATION AudioTrackFeature = 5
	AudioTrackFeature_TF_PRECONNECT_BUFFER           AudioTrackFeature = 6 // client will buffer audio once available and send it to the server via bytes stream once connected
)

// Enum value maps for AudioTrackFeature.
var (
	AudioTrackFeature_name = map[int32]string{
		0: "TF_STEREO",
		1: "TF_NO_DTX",
		2: "TF_AUTO_GAIN_CONTROL",
		3: "TF_ECHO_CANCELLATION",
		4: "TF_NOISE_SUPPRESSION",
		5: "TF_ENHANCED_NOISE_CANCELLATION",
		6: "TF_PRECONNECT_BUFFER",
	}
	AudioTrackFeature_value = map[string]int32{
		"TF_STEREO":                      0,
		"TF_NO_DTX":                      1,
		"TF_AUTO_GAIN_CONTROL":           2,
		"TF_ECHO_CANCELLATION":           3,
		"TF_NOISE_SUPPRESSION":           4,
		"TF_ENHANCED_NOISE_CANCELLATION": 5,
		"TF_PRECONNECT_BUFFER":           6,
	}
)

func (x AudioTrackFeature) Enum() *AudioTrackFeature {
	p := new(AudioTrackFeature)
	*p = x
	return p
}

func (x AudioTrackFeature) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AudioTrackFeature) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_models_proto_enumTypes[12].Descriptor()
}

func (AudioTrackFeature) Type() protoreflect.EnumType {
	return &file_livekit_models_proto_enumTypes[12]
}

func (x AudioTrackFeature) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AudioTrackFeature.Descriptor instead.
func (AudioTrackFeature) EnumDescriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{12}
}

type ParticipantInfo_State int32

const (
	// websocket' connected, but not offered yet
	ParticipantInfo_JOINING ParticipantInfo_State = 0
	// server received client offer
	ParticipantInfo_JOINED ParticipantInfo_State = 1
	// ICE connectivity established
	ParticipantInfo_ACTIVE ParticipantInfo_State = 2
	// WS disconnected
	ParticipantInfo_DISCONNECTED ParticipantInfo_State = 3
)

// Enum value maps for ParticipantInfo_State.
var (
	ParticipantInfo_State_name = map[int32]string{
		0: "JOINING",
		1: "JOINED",
		2: "ACTIVE",
		3: "DISCONNECTED",
	}
	ParticipantInfo_State_value = map[string]int32{
		"JOINING":      0,
		"JOINED":       1,
		"ACTIVE":       2,
		"DISCONNECTED": 3,
	}
)

func (x ParticipantInfo_State) Enum() *ParticipantInfo_State {
	p := new(ParticipantInfo_State)
	*p = x
	return p
}

func (x ParticipantInfo_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ParticipantInfo_State) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_models_proto_enumTypes[13].Descriptor()
}

func (ParticipantInfo_State) Type() protoreflect.EnumType {
	return &file_livekit_models_proto_enumTypes[13]
}

func (x ParticipantInfo_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ParticipantInfo_State.Descriptor instead.
func (ParticipantInfo_State) EnumDescriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{6, 0}
}

type ParticipantInfo_Kind int32

const (
	// standard participants, e.g. web clients
	ParticipantInfo_STANDARD ParticipantInfo_Kind = 0
	// only ingests streams
	ParticipantInfo_INGRESS ParticipantInfo_Kind = 1
	// only consumes streams
	ParticipantInfo_EGRESS ParticipantInfo_Kind = 2
	// SIP participants
	ParticipantInfo_SIP ParticipantInfo_Kind = 3
	// LiveKit agents
	ParticipantInfo_AGENT ParticipantInfo_Kind = 4
)

// Enum value maps for ParticipantInfo_Kind.
var (
	ParticipantInfo_Kind_name = map[int32]string{
		0: "STANDARD",
		1: "INGRESS",
		2: "EGRESS",
		3: "SIP",
		4: "AGENT",
	}
	ParticipantInfo_Kind_value = map[string]int32{
		"STANDARD": 0,
		"INGRESS":  1,
		"EGRESS":   2,
		"SIP":      3,
		"AGENT":    4,
	}
)

func (x ParticipantInfo_Kind) Enum() *ParticipantInfo_Kind {
	p := new(ParticipantInfo_Kind)
	*p = x
	return p
}

func (x ParticipantInfo_Kind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ParticipantInfo_Kind) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_models_proto_enumTypes[14].Descriptor()
}

func (ParticipantInfo_Kind) Type() protoreflect.EnumType {
	return &file_livekit_models_proto_enumTypes[14]
}

func (x ParticipantInfo_Kind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ParticipantInfo_Kind.Descriptor instead.
func (ParticipantInfo_Kind) EnumDescriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{6, 1}
}

type ParticipantInfo_KindDetail int32

const (
	ParticipantInfo_CLOUD_AGENT ParticipantInfo_KindDetail = 0
	ParticipantInfo_FORWARDED   ParticipantInfo_KindDetail = 1
)

// Enum value maps for ParticipantInfo_KindDetail.
var (
	ParticipantInfo_KindDetail_name = map[int32]string{
		0: "CLOUD_AGENT",
		1: "FORWARDED",
	}
	ParticipantInfo_KindDetail_value = map[string]int32{
		"CLOUD_AGENT": 0,
		"FORWARDED":   1,
	}
)

func (x ParticipantInfo_KindDetail) Enum() *ParticipantInfo_KindDetail {
	p := new(ParticipantInfo_KindDetail)
	*p = x
	return p
}

func (x ParticipantInfo_KindDetail) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ParticipantInfo_KindDetail) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_models_proto_enumTypes[15].Descriptor()
}

func (ParticipantInfo_KindDetail) Type() protoreflect.EnumType {
	return &file_livekit_models_proto_enumTypes[15]
}

func (x ParticipantInfo_KindDetail) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ParticipantInfo_KindDetail.Descriptor instead.
func (ParticipantInfo_KindDetail) EnumDescriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{6, 2}
}

type Encryption_Type int32

const (
	Encryption_NONE   Encryption_Type = 0
	Encryption_GCM    Encryption_Type = 1
	Encryption_CUSTOM Encryption_Type = 2
)

// Enum value maps for Encryption_Type.
var (
	Encryption_Type_name = map[int32]string{
		0: "NONE",
		1: "GCM",
		2: "CUSTOM",
	}
	Encryption_Type_value = map[string]int32{
		"NONE":   0,
		"GCM":    1,
		"CUSTOM": 2,
	}
)

func (x Encryption_Type) Enum() *Encryption_Type {
	p := new(Encryption_Type)
	*p = x
	return p
}

func (x Encryption_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Encryption_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_models_proto_enumTypes[16].Descriptor()
}

func (Encryption_Type) Type() protoreflect.EnumType {
	return &file_livekit_models_proto_enumTypes[16]
}

func (x Encryption_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Encryption_Type.Descriptor instead.
func (Encryption_Type) EnumDescriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{8, 0}
}

type DataPacket_Kind int32

const (
	DataPacket_RELIABLE DataPacket_Kind = 0
	DataPacket_LOSSY    DataPacket_Kind = 1
)

// Enum value maps for DataPacket_Kind.
var (
	DataPacket_Kind_name = map[int32]string{
		0: "RELIABLE",
		1: "LOSSY",
	}
	DataPacket_Kind_value = map[string]int32{
		"RELIABLE": 0,
		"LOSSY":    1,
	}
)

func (x DataPacket_Kind) Enum() *DataPacket_Kind {
	p := new(DataPacket_Kind)
	*p = x
	return p
}

func (x DataPacket_Kind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DataPacket_Kind) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_models_proto_enumTypes[17].Descriptor()
}

func (DataPacket_Kind) Type() protoreflect.EnumType {
	return &file_livekit_models_proto_enumTypes[17]
}

func (x DataPacket_Kind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DataPacket_Kind.Descriptor instead.
func (DataPacket_Kind) EnumDescriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{15, 0}
}

type ServerInfo_Edition int32

const (
	ServerInfo_Standard ServerInfo_Edition = 0
	ServerInfo_Cloud    ServerInfo_Edition = 1
)

// Enum value maps for ServerInfo_Edition.
var (
	ServerInfo_Edition_name = map[int32]string{
		0: "Standard",
		1: "Cloud",
	}
	ServerInfo_Edition_value = map[string]int32{
		"Standard": 0,
		"Cloud":    1,
	}
)

func (x ServerInfo_Edition) Enum() *ServerInfo_Edition {
	p := new(ServerInfo_Edition)
	*p = x
	return p
}

func (x ServerInfo_Edition) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServerInfo_Edition) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_models_proto_enumTypes[18].Descriptor()
}

func (ServerInfo_Edition) Type() protoreflect.EnumType {
	return &file_livekit_models_proto_enumTypes[18]
}

func (x ServerInfo_Edition) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServerInfo_Edition.Descriptor instead.
func (ServerInfo_Edition) EnumDescriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{28, 0}
}

type ClientInfo_SDK int32

const (
	ClientInfo_UNKNOWN      ClientInfo_SDK = 0
	ClientInfo_JS           ClientInfo_SDK = 1
	ClientInfo_SWIFT        ClientInfo_SDK = 2
	ClientInfo_ANDROID      ClientInfo_SDK = 3
	ClientInfo_FLUTTER      ClientInfo_SDK = 4
	ClientInfo_GO           ClientInfo_SDK = 5
	ClientInfo_UNITY        ClientInfo_SDK = 6
	ClientInfo_REACT_NATIVE ClientInfo_SDK = 7
	ClientInfo_RUST         ClientInfo_SDK = 8
	ClientInfo_PYTHON       ClientInfo_SDK = 9
	ClientInfo_CPP          ClientInfo_SDK = 10
	ClientInfo_UNITY_WEB    ClientInfo_SDK = 11
	ClientInfo_NODE         ClientInfo_SDK = 12
	ClientInfo_UNREAL       ClientInfo_SDK = 13
	ClientInfo_ESP32        ClientInfo_SDK = 14
)

// Enum value maps for ClientInfo_SDK.
var (
	ClientInfo_SDK_name = map[int32]string{
		0:  "UNKNOWN",
		1:  "JS",
		2:  "SWIFT",
		3:  "ANDROID",
		4:  "FLUTTER",
		5:  "GO",
		6:  "UNITY",
		7:  "REACT_NATIVE",
		8:  "RUST",
		9:  "PYTHON",
		10: "CPP",
		11: "UNITY_WEB",
		12: "NODE",
		13: "UNREAL",
		14: "ESP32",
	}
	ClientInfo_SDK_value = map[string]int32{
		"UNKNOWN":      0,
		"JS":           1,
		"SWIFT":        2,
		"ANDROID":      3,
		"FLUTTER":      4,
		"GO":           5,
		"UNITY":        6,
		"REACT_NATIVE": 7,
		"RUST":         8,
		"PYTHON":       9,
		"CPP":          10,
		"UNITY_WEB":    11,
		"NODE":         12,
		"UNREAL":       13,
		"ESP32":        14,
	}
)

func (x ClientInfo_SDK) Enum() *ClientInfo_SDK {
	p := new(ClientInfo_SDK)
	*p = x
	return p
}

func (x ClientInfo_SDK) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClientInfo_SDK) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_models_proto_enumTypes[19].Descriptor()
}

func (ClientInfo_SDK) Type() protoreflect.EnumType {
	return &file_livekit_models_proto_enumTypes[19]
}

func (x ClientInfo_SDK) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ClientInfo_SDK.Descriptor instead.
func (ClientInfo_SDK) EnumDescriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{29, 0}
}

// enum for operation types (specific to TextHeader)
type DataStream_OperationType int32

const (
	DataStream_CREATE   DataStream_OperationType = 0
	DataStream_UPDATE   DataStream_OperationType = 1
	DataStream_DELETE   DataStream_OperationType = 2
	DataStream_REACTION DataStream_OperationType = 3
)

// Enum value maps for DataStream_OperationType.
var (
	DataStream_OperationType_name = map[int32]string{
		0: "CREATE",
		1: "UPDATE",
		2: "DELETE",
		3: "REACTION",
	}
	DataStream_OperationType_value = map[string]int32{
		"CREATE":   0,
		"UPDATE":   1,
		"DELETE":   2,
		"REACTION": 3,
	}
)

func (x DataStream_OperationType) Enum() *DataStream_OperationType {
	p := new(DataStream_OperationType)
	*p = x
	return p
}

func (x DataStream_OperationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DataStream_OperationType) Descriptor() protoreflect.EnumDescriptor {
	return file_livekit_models_proto_enumTypes[20].Descriptor()
}

func (DataStream_OperationType) Type() protoreflect.EnumType {
	return &file_livekit_models_proto_enumTypes[20]
}

func (x DataStream_OperationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DataStream_OperationType.Descriptor instead.
func (DataStream_OperationType) EnumDescriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{40, 0}
}

type Pagination struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AfterId       string                 `protobuf:"bytes,1,opt,name=after_id,json=afterId,proto3" json:"after_id,omitempty"` // list entities which IDs are greater
	Limit         int32                  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Pagination) Reset() {
	*x = Pagination{}
	mi := &file_livekit_models_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Pagination) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pagination) ProtoMessage() {}

func (x *Pagination) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pagination.ProtoReflect.Descriptor instead.
func (*Pagination) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{0}
}

func (x *Pagination) GetAfterId() string {
	if x != nil {
		return x.AfterId
	}
	return ""
}

func (x *Pagination) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

// ListUpdate is used for updated APIs where 'repeated string' field is modified.
type ListUpdate struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Set           []string               `protobuf:"bytes,1,rep,name=set,proto3" json:"set,omitempty"` // set the field to a new list
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUpdate) Reset() {
	*x = ListUpdate{}
	mi := &file_livekit_models_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUpdate) ProtoMessage() {}

func (x *ListUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUpdate.ProtoReflect.Descriptor instead.
func (*ListUpdate) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{1}
}

func (x *ListUpdate) GetSet() []string {
	if x != nil {
		return x.Set
	}
	return nil
}

type Room struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Sid              string                 `protobuf:"bytes,1,opt,name=sid,proto3" json:"sid,omitempty"`
	Name             string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	EmptyTimeout     uint32                 `protobuf:"varint,3,opt,name=empty_timeout,json=emptyTimeout,proto3" json:"empty_timeout,omitempty"`
	DepartureTimeout uint32                 `protobuf:"varint,14,opt,name=departure_timeout,json=departureTimeout,proto3" json:"departure_timeout,omitempty"`
	MaxParticipants  uint32                 `protobuf:"varint,4,opt,name=max_participants,json=maxParticipants,proto3" json:"max_participants,omitempty"`
	CreationTime     int64                  `protobuf:"varint,5,opt,name=creation_time,json=creationTime,proto3" json:"creation_time,omitempty"`
	CreationTimeMs   int64                  `protobuf:"varint,15,opt,name=creation_time_ms,json=creationTimeMs,proto3" json:"creation_time_ms,omitempty"`
	TurnPassword     string                 `protobuf:"bytes,6,opt,name=turn_password,json=turnPassword,proto3" json:"turn_password,omitempty"`
	EnabledCodecs    []*Codec               `protobuf:"bytes,7,rep,name=enabled_codecs,json=enabledCodecs,proto3" json:"enabled_codecs,omitempty"`
	Metadata         string                 `protobuf:"bytes,8,opt,name=metadata,proto3" json:"metadata,omitempty"`
	NumParticipants  uint32                 `protobuf:"varint,9,opt,name=num_participants,json=numParticipants,proto3" json:"num_participants,omitempty"`
	NumPublishers    uint32                 `protobuf:"varint,11,opt,name=num_publishers,json=numPublishers,proto3" json:"num_publishers,omitempty"`
	ActiveRecording  bool                   `protobuf:"varint,10,opt,name=active_recording,json=activeRecording,proto3" json:"active_recording,omitempty"`
	Version          *TimedVersion          `protobuf:"bytes,13,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *Room) Reset() {
	*x = Room{}
	mi := &file_livekit_models_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Room) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Room) ProtoMessage() {}

func (x *Room) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Room.ProtoReflect.Descriptor instead.
func (*Room) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{2}
}

func (x *Room) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *Room) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Room) GetEmptyTimeout() uint32 {
	if x != nil {
		return x.EmptyTimeout
	}
	return 0
}

func (x *Room) GetDepartureTimeout() uint32 {
	if x != nil {
		return x.DepartureTimeout
	}
	return 0
}

func (x *Room) GetMaxParticipants() uint32 {
	if x != nil {
		return x.MaxParticipants
	}
	return 0
}

func (x *Room) GetCreationTime() int64 {
	if x != nil {
		return x.CreationTime
	}
	return 0
}

func (x *Room) GetCreationTimeMs() int64 {
	if x != nil {
		return x.CreationTimeMs
	}
	return 0
}

func (x *Room) GetTurnPassword() string {
	if x != nil {
		return x.TurnPassword
	}
	return ""
}

func (x *Room) GetEnabledCodecs() []*Codec {
	if x != nil {
		return x.EnabledCodecs
	}
	return nil
}

func (x *Room) GetMetadata() string {
	if x != nil {
		return x.Metadata
	}
	return ""
}

func (x *Room) GetNumParticipants() uint32 {
	if x != nil {
		return x.NumParticipants
	}
	return 0
}

func (x *Room) GetNumPublishers() uint32 {
	if x != nil {
		return x.NumPublishers
	}
	return 0
}

func (x *Room) GetActiveRecording() bool {
	if x != nil {
		return x.ActiveRecording
	}
	return false
}

func (x *Room) GetVersion() *TimedVersion {
	if x != nil {
		return x.Version
	}
	return nil
}

type Codec struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Mime          string                 `protobuf:"bytes,1,opt,name=mime,proto3" json:"mime,omitempty"`
	FmtpLine      string                 `protobuf:"bytes,2,opt,name=fmtp_line,json=fmtpLine,proto3" json:"fmtp_line,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Codec) Reset() {
	*x = Codec{}
	mi := &file_livekit_models_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Codec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Codec) ProtoMessage() {}

func (x *Codec) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Codec.ProtoReflect.Descriptor instead.
func (*Codec) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{3}
}

func (x *Codec) GetMime() string {
	if x != nil {
		return x.Mime
	}
	return ""
}

func (x *Codec) GetFmtpLine() string {
	if x != nil {
		return x.FmtpLine
	}
	return ""
}

type PlayoutDelay struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Enabled       bool                   `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	Min           uint32                 `protobuf:"varint,2,opt,name=min,proto3" json:"min,omitempty"`
	Max           uint32                 `protobuf:"varint,3,opt,name=max,proto3" json:"max,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlayoutDelay) Reset() {
	*x = PlayoutDelay{}
	mi := &file_livekit_models_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlayoutDelay) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayoutDelay) ProtoMessage() {}

func (x *PlayoutDelay) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayoutDelay.ProtoReflect.Descriptor instead.
func (*PlayoutDelay) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{4}
}

func (x *PlayoutDelay) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *PlayoutDelay) GetMin() uint32 {
	if x != nil {
		return x.Min
	}
	return 0
}

func (x *PlayoutDelay) GetMax() uint32 {
	if x != nil {
		return x.Max
	}
	return 0
}

type ParticipantPermission struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// allow participant to subscribe to other tracks in the room
	CanSubscribe bool `protobuf:"varint,1,opt,name=can_subscribe,json=canSubscribe,proto3" json:"can_subscribe,omitempty"`
	// allow participant to publish new tracks to room
	CanPublish bool `protobuf:"varint,2,opt,name=can_publish,json=canPublish,proto3" json:"can_publish,omitempty"`
	// allow participant to publish data
	CanPublishData bool `protobuf:"varint,3,opt,name=can_publish_data,json=canPublishData,proto3" json:"can_publish_data,omitempty"`
	// sources that are allowed to be published
	CanPublishSources []TrackSource `protobuf:"varint,9,rep,packed,name=can_publish_sources,json=canPublishSources,proto3,enum=livekit.TrackSource" json:"can_publish_sources,omitempty"`
	// indicates that it's hidden to others
	Hidden bool `protobuf:"varint,7,opt,name=hidden,proto3" json:"hidden,omitempty"`
	// indicates it's a recorder instance
	// deprecated: use ParticipantInfo.kind instead
	//
	// Deprecated: Marked as deprecated in livekit_models.proto.
	Recorder bool `protobuf:"varint,8,opt,name=recorder,proto3" json:"recorder,omitempty"`
	// indicates that participant can update own metadata and attributes
	CanUpdateMetadata bool `protobuf:"varint,10,opt,name=can_update_metadata,json=canUpdateMetadata,proto3" json:"can_update_metadata,omitempty"`
	// indicates that participant is an agent
	// deprecated: use ParticipantInfo.kind instead
	//
	// Deprecated: Marked as deprecated in livekit_models.proto.
	Agent bool `protobuf:"varint,11,opt,name=agent,proto3" json:"agent,omitempty"`
	// if a participant can subscribe to metrics
	CanSubscribeMetrics bool `protobuf:"varint,12,opt,name=can_subscribe_metrics,json=canSubscribeMetrics,proto3" json:"can_subscribe_metrics,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *ParticipantPermission) Reset() {
	*x = ParticipantPermission{}
	mi := &file_livekit_models_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParticipantPermission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParticipantPermission) ProtoMessage() {}

func (x *ParticipantPermission) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParticipantPermission.ProtoReflect.Descriptor instead.
func (*ParticipantPermission) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{5}
}

func (x *ParticipantPermission) GetCanSubscribe() bool {
	if x != nil {
		return x.CanSubscribe
	}
	return false
}

func (x *ParticipantPermission) GetCanPublish() bool {
	if x != nil {
		return x.CanPublish
	}
	return false
}

func (x *ParticipantPermission) GetCanPublishData() bool {
	if x != nil {
		return x.CanPublishData
	}
	return false
}

func (x *ParticipantPermission) GetCanPublishSources() []TrackSource {
	if x != nil {
		return x.CanPublishSources
	}
	return nil
}

func (x *ParticipantPermission) GetHidden() bool {
	if x != nil {
		return x.Hidden
	}
	return false
}

// Deprecated: Marked as deprecated in livekit_models.proto.
func (x *ParticipantPermission) GetRecorder() bool {
	if x != nil {
		return x.Recorder
	}
	return false
}

func (x *ParticipantPermission) GetCanUpdateMetadata() bool {
	if x != nil {
		return x.CanUpdateMetadata
	}
	return false
}

// Deprecated: Marked as deprecated in livekit_models.proto.
func (x *ParticipantPermission) GetAgent() bool {
	if x != nil {
		return x.Agent
	}
	return false
}

func (x *ParticipantPermission) GetCanSubscribeMetrics() bool {
	if x != nil {
		return x.CanSubscribeMetrics
	}
	return false
}

type ParticipantInfo struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	Sid      string                 `protobuf:"bytes,1,opt,name=sid,proto3" json:"sid,omitempty"`
	Identity string                 `protobuf:"bytes,2,opt,name=identity,proto3" json:"identity,omitempty"`
	State    ParticipantInfo_State  `protobuf:"varint,3,opt,name=state,proto3,enum=livekit.ParticipantInfo_State" json:"state,omitempty"`
	Tracks   []*TrackInfo           `protobuf:"bytes,4,rep,name=tracks,proto3" json:"tracks,omitempty"`
	Metadata string                 `protobuf:"bytes,5,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// timestamp when participant joined room, in seconds
	JoinedAt int64 `protobuf:"varint,6,opt,name=joined_at,json=joinedAt,proto3" json:"joined_at,omitempty"`
	// timestamp when participant joined room, in milliseconds
	JoinedAtMs int64                  `protobuf:"varint,17,opt,name=joined_at_ms,json=joinedAtMs,proto3" json:"joined_at_ms,omitempty"`
	Name       string                 `protobuf:"bytes,9,opt,name=name,proto3" json:"name,omitempty"`
	Version    uint32                 `protobuf:"varint,10,opt,name=version,proto3" json:"version,omitempty"`
	Permission *ParticipantPermission `protobuf:"bytes,11,opt,name=permission,proto3" json:"permission,omitempty"`
	Region     string                 `protobuf:"bytes,12,opt,name=region,proto3" json:"region,omitempty"`
	// indicates the participant has an active publisher connection
	// and can publish to the server
	IsPublisher      bool                         `protobuf:"varint,13,opt,name=is_publisher,json=isPublisher,proto3" json:"is_publisher,omitempty"`
	Kind             ParticipantInfo_Kind         `protobuf:"varint,14,opt,name=kind,proto3,enum=livekit.ParticipantInfo_Kind" json:"kind,omitempty"`
	Attributes       map[string]string            `protobuf:"bytes,15,rep,name=attributes,proto3" json:"attributes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	DisconnectReason DisconnectReason             `protobuf:"varint,16,opt,name=disconnect_reason,json=disconnectReason,proto3,enum=livekit.DisconnectReason" json:"disconnect_reason,omitempty"`
	KindDetails      []ParticipantInfo_KindDetail `protobuf:"varint,18,rep,packed,name=kind_details,json=kindDetails,proto3,enum=livekit.ParticipantInfo_KindDetail" json:"kind_details,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ParticipantInfo) Reset() {
	*x = ParticipantInfo{}
	mi := &file_livekit_models_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParticipantInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParticipantInfo) ProtoMessage() {}

func (x *ParticipantInfo) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParticipantInfo.ProtoReflect.Descriptor instead.
func (*ParticipantInfo) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{6}
}

func (x *ParticipantInfo) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *ParticipantInfo) GetIdentity() string {
	if x != nil {
		return x.Identity
	}
	return ""
}

func (x *ParticipantInfo) GetState() ParticipantInfo_State {
	if x != nil {
		return x.State
	}
	return ParticipantInfo_JOINING
}

func (x *ParticipantInfo) GetTracks() []*TrackInfo {
	if x != nil {
		return x.Tracks
	}
	return nil
}

func (x *ParticipantInfo) GetMetadata() string {
	if x != nil {
		return x.Metadata
	}
	return ""
}

func (x *ParticipantInfo) GetJoinedAt() int64 {
	if x != nil {
		return x.JoinedAt
	}
	return 0
}

func (x *ParticipantInfo) GetJoinedAtMs() int64 {
	if x != nil {
		return x.JoinedAtMs
	}
	return 0
}

func (x *ParticipantInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ParticipantInfo) GetVersion() uint32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *ParticipantInfo) GetPermission() *ParticipantPermission {
	if x != nil {
		return x.Permission
	}
	return nil
}

func (x *ParticipantInfo) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ParticipantInfo) GetIsPublisher() bool {
	if x != nil {
		return x.IsPublisher
	}
	return false
}

func (x *ParticipantInfo) GetKind() ParticipantInfo_Kind {
	if x != nil {
		return x.Kind
	}
	return ParticipantInfo_STANDARD
}

func (x *ParticipantInfo) GetAttributes() map[string]string {
	if x != nil {
		return x.Attributes
	}
	return nil
}

func (x *ParticipantInfo) GetDisconnectReason() DisconnectReason {
	if x != nil {
		return x.DisconnectReason
	}
	return DisconnectReason_UNKNOWN_REASON
}

func (x *ParticipantInfo) GetKindDetails() []ParticipantInfo_KindDetail {
	if x != nil {
		return x.KindDetails
	}
	return nil
}

type RemoteParticipantInfo struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ParticipantInfo *ParticipantInfo       `protobuf:"bytes,1,opt,name=participantInfo,proto3" json:"participantInfo,omitempty"`
	NodeId          string                 `protobuf:"bytes,2,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	NodeIp          string                 `protobuf:"bytes,3,opt,name=node_ip,json=nodeIp,proto3" json:"node_ip,omitempty"`
	NodeRelayPort   int32                  `protobuf:"varint,4,opt,name=node_relay_port,json=nodeRelayPort,proto3" json:"node_relay_port,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *RemoteParticipantInfo) Reset() {
	*x = RemoteParticipantInfo{}
	mi := &file_livekit_models_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoteParticipantInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoteParticipantInfo) ProtoMessage() {}

func (x *RemoteParticipantInfo) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoteParticipantInfo.ProtoReflect.Descriptor instead.
func (*RemoteParticipantInfo) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{7}
}

func (x *RemoteParticipantInfo) GetParticipantInfo() *ParticipantInfo {
	if x != nil {
		return x.ParticipantInfo
	}
	return nil
}

func (x *RemoteParticipantInfo) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *RemoteParticipantInfo) GetNodeIp() string {
	if x != nil {
		return x.NodeIp
	}
	return ""
}

func (x *RemoteParticipantInfo) GetNodeRelayPort() int32 {
	if x != nil {
		return x.NodeRelayPort
	}
	return 0
}

type Encryption struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Encryption) Reset() {
	*x = Encryption{}
	mi := &file_livekit_models_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Encryption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Encryption) ProtoMessage() {}

func (x *Encryption) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Encryption.ProtoReflect.Descriptor instead.
func (*Encryption) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{8}
}

type SimulcastCodecInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MimeType      string                 `protobuf:"bytes,1,opt,name=mime_type,json=mimeType,proto3" json:"mime_type,omitempty"`
	Mid           string                 `protobuf:"bytes,2,opt,name=mid,proto3" json:"mid,omitempty"`
	Cid           string                 `protobuf:"bytes,3,opt,name=cid,proto3" json:"cid,omitempty"`
	Layers        []*VideoLayer          `protobuf:"bytes,4,rep,name=layers,proto3" json:"layers,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SimulcastCodecInfo) Reset() {
	*x = SimulcastCodecInfo{}
	mi := &file_livekit_models_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SimulcastCodecInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimulcastCodecInfo) ProtoMessage() {}

func (x *SimulcastCodecInfo) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimulcastCodecInfo.ProtoReflect.Descriptor instead.
func (*SimulcastCodecInfo) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{9}
}

func (x *SimulcastCodecInfo) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

func (x *SimulcastCodecInfo) GetMid() string {
	if x != nil {
		return x.Mid
	}
	return ""
}

func (x *SimulcastCodecInfo) GetCid() string {
	if x != nil {
		return x.Cid
	}
	return ""
}

func (x *SimulcastCodecInfo) GetLayers() []*VideoLayer {
	if x != nil {
		return x.Layers
	}
	return nil
}

type TrackInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Sid   string                 `protobuf:"bytes,1,opt,name=sid,proto3" json:"sid,omitempty"`
	Type  TrackType              `protobuf:"varint,2,opt,name=type,proto3,enum=livekit.TrackType" json:"type,omitempty"`
	Name  string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Muted bool                   `protobuf:"varint,4,opt,name=muted,proto3" json:"muted,omitempty"`
	// original width of video (unset for audio)
	// clients may receive a lower resolution version with simulcast
	Width uint32 `protobuf:"varint,5,opt,name=width,proto3" json:"width,omitempty"`
	// original height of video (unset for audio)
	Height uint32 `protobuf:"varint,6,opt,name=height,proto3" json:"height,omitempty"`
	// true if track is simulcasted
	Simulcast bool `protobuf:"varint,7,opt,name=simulcast,proto3" json:"simulcast,omitempty"`
	// true if DTX (Discontinuous Transmission) is disabled for audio
	DisableDtx bool `protobuf:"varint,8,opt,name=disable_dtx,json=disableDtx,proto3" json:"disable_dtx,omitempty"`
	// source of media
	Source TrackSource   `protobuf:"varint,9,opt,name=source,proto3,enum=livekit.TrackSource" json:"source,omitempty"`
	Layers []*VideoLayer `protobuf:"bytes,10,rep,name=layers,proto3" json:"layers,omitempty"`
	// mime type of codec
	MimeType string                `protobuf:"bytes,11,opt,name=mime_type,json=mimeType,proto3" json:"mime_type,omitempty"`
	Mid      string                `protobuf:"bytes,12,opt,name=mid,proto3" json:"mid,omitempty"`
	Codecs   []*SimulcastCodecInfo `protobuf:"bytes,13,rep,name=codecs,proto3" json:"codecs,omitempty"`
	Stereo   bool                  `protobuf:"varint,14,opt,name=stereo,proto3" json:"stereo,omitempty"`
	// true if RED (Redundant Encoding) is disabled for audio
	DisableRed        bool                `protobuf:"varint,15,opt,name=disable_red,json=disableRed,proto3" json:"disable_red,omitempty"`
	Encryption        Encryption_Type     `protobuf:"varint,16,opt,name=encryption,proto3,enum=livekit.Encryption_Type" json:"encryption,omitempty"`
	Stream            string              `protobuf:"bytes,17,opt,name=stream,proto3" json:"stream,omitempty"`
	Version           *TimedVersion       `protobuf:"bytes,18,opt,name=version,proto3" json:"version,omitempty"`
	AudioFeatures     []AudioTrackFeature `protobuf:"varint,19,rep,packed,name=audio_features,json=audioFeatures,proto3,enum=livekit.AudioTrackFeature" json:"audio_features,omitempty"`
	BackupCodecPolicy BackupCodecPolicy   `protobuf:"varint,20,opt,name=backup_codec_policy,json=backupCodecPolicy,proto3,enum=livekit.BackupCodecPolicy" json:"backup_codec_policy,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *TrackInfo) Reset() {
	*x = TrackInfo{}
	mi := &file_livekit_models_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrackInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackInfo) ProtoMessage() {}

func (x *TrackInfo) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackInfo.ProtoReflect.Descriptor instead.
func (*TrackInfo) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{10}
}

func (x *TrackInfo) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *TrackInfo) GetType() TrackType {
	if x != nil {
		return x.Type
	}
	return TrackType_AUDIO
}

func (x *TrackInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TrackInfo) GetMuted() bool {
	if x != nil {
		return x.Muted
	}
	return false
}

func (x *TrackInfo) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *TrackInfo) GetHeight() uint32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *TrackInfo) GetSimulcast() bool {
	if x != nil {
		return x.Simulcast
	}
	return false
}

func (x *TrackInfo) GetDisableDtx() bool {
	if x != nil {
		return x.DisableDtx
	}
	return false
}

func (x *TrackInfo) GetSource() TrackSource {
	if x != nil {
		return x.Source
	}
	return TrackSource_UNKNOWN
}

func (x *TrackInfo) GetLayers() []*VideoLayer {
	if x != nil {
		return x.Layers
	}
	return nil
}

func (x *TrackInfo) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

func (x *TrackInfo) GetMid() string {
	if x != nil {
		return x.Mid
	}
	return ""
}

func (x *TrackInfo) GetCodecs() []*SimulcastCodecInfo {
	if x != nil {
		return x.Codecs
	}
	return nil
}

func (x *TrackInfo) GetStereo() bool {
	if x != nil {
		return x.Stereo
	}
	return false
}

func (x *TrackInfo) GetDisableRed() bool {
	if x != nil {
		return x.DisableRed
	}
	return false
}

func (x *TrackInfo) GetEncryption() Encryption_Type {
	if x != nil {
		return x.Encryption
	}
	return Encryption_NONE
}

func (x *TrackInfo) GetStream() string {
	if x != nil {
		return x.Stream
	}
	return ""
}

func (x *TrackInfo) GetVersion() *TimedVersion {
	if x != nil {
		return x.Version
	}
	return nil
}

func (x *TrackInfo) GetAudioFeatures() []AudioTrackFeature {
	if x != nil {
		return x.AudioFeatures
	}
	return nil
}

func (x *TrackInfo) GetBackupCodecPolicy() BackupCodecPolicy {
	if x != nil {
		return x.BackupCodecPolicy
	}
	return BackupCodecPolicy_PREFER_REGRESSION
}

type RTPCodecParameters struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Capability    *RTPCodecCapability    `protobuf:"bytes,1,opt,name=capability,proto3" json:"capability,omitempty"`
	PayloadType   uint32                 `protobuf:"varint,2,opt,name=payload_type,json=payloadType,proto3" json:"payload_type,omitempty"`
	StatsId       string                 `protobuf:"bytes,3,opt,name=stats_id,json=statsId,proto3" json:"stats_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RTPCodecParameters) Reset() {
	*x = RTPCodecParameters{}
	mi := &file_livekit_models_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RTPCodecParameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTPCodecParameters) ProtoMessage() {}

func (x *RTPCodecParameters) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTPCodecParameters.ProtoReflect.Descriptor instead.
func (*RTPCodecParameters) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{11}
}

func (x *RTPCodecParameters) GetCapability() *RTPCodecCapability {
	if x != nil {
		return x.Capability
	}
	return nil
}

func (x *RTPCodecParameters) GetPayloadType() uint32 {
	if x != nil {
		return x.PayloadType
	}
	return 0
}

func (x *RTPCodecParameters) GetStatsId() string {
	if x != nil {
		return x.StatsId
	}
	return ""
}

type RTPCodecCapability struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MimeType      string                 `protobuf:"bytes,1,opt,name=mime_type,json=mimeType,proto3" json:"mime_type,omitempty"`
	ClockRate     uint32                 `protobuf:"varint,2,opt,name=clock_rate,json=clockRate,proto3" json:"clock_rate,omitempty"`
	Channels      uint32                 `protobuf:"varint,3,opt,name=channels,proto3" json:"channels,omitempty"`
	SdpFmtpLine   string                 `protobuf:"bytes,4,opt,name=sdp_fmtp_line,json=sdpFmtpLine,proto3" json:"sdp_fmtp_line,omitempty"`
	RtcpFeedback  []*RTCPFeedback        `protobuf:"bytes,5,rep,name=rtcp_feedback,json=rtcpFeedback,proto3" json:"rtcp_feedback,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RTPCodecCapability) Reset() {
	*x = RTPCodecCapability{}
	mi := &file_livekit_models_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RTPCodecCapability) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTPCodecCapability) ProtoMessage() {}

func (x *RTPCodecCapability) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTPCodecCapability.ProtoReflect.Descriptor instead.
func (*RTPCodecCapability) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{12}
}

func (x *RTPCodecCapability) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

func (x *RTPCodecCapability) GetClockRate() uint32 {
	if x != nil {
		return x.ClockRate
	}
	return 0
}

func (x *RTPCodecCapability) GetChannels() uint32 {
	if x != nil {
		return x.Channels
	}
	return 0
}

func (x *RTPCodecCapability) GetSdpFmtpLine() string {
	if x != nil {
		return x.SdpFmtpLine
	}
	return ""
}

func (x *RTPCodecCapability) GetRtcpFeedback() []*RTCPFeedback {
	if x != nil {
		return x.RtcpFeedback
	}
	return nil
}

type RTCPFeedback struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Parameter     string                 `protobuf:"bytes,2,opt,name=parameter,proto3" json:"parameter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RTCPFeedback) Reset() {
	*x = RTCPFeedback{}
	mi := &file_livekit_models_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RTCPFeedback) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTCPFeedback) ProtoMessage() {}

func (x *RTCPFeedback) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTCPFeedback.ProtoReflect.Descriptor instead.
func (*RTCPFeedback) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{13}
}

func (x *RTCPFeedback) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *RTCPFeedback) GetParameter() string {
	if x != nil {
		return x.Parameter
	}
	return ""
}

// provide information about available spatial layers
type VideoLayer struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// for tracks with a single layer, this should be HIGH
	Quality VideoQuality `protobuf:"varint,1,opt,name=quality,proto3,enum=livekit.VideoQuality" json:"quality,omitempty"`
	Width   uint32       `protobuf:"varint,2,opt,name=width,proto3" json:"width,omitempty"`
	Height  uint32       `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	// target bitrate in bit per second (bps), server will measure actual
	Bitrate       uint32 `protobuf:"varint,4,opt,name=bitrate,proto3" json:"bitrate,omitempty"`
	Ssrc          uint32 `protobuf:"varint,5,opt,name=ssrc,proto3" json:"ssrc,omitempty"`
	SpatialLayer  int32  `protobuf:"varint,6,opt,name=spatial_layer,json=spatialLayer,proto3" json:"spatial_layer,omitempty"`
	Rid           string `protobuf:"bytes,7,opt,name=rid,proto3" json:"rid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoLayer) Reset() {
	*x = VideoLayer{}
	mi := &file_livekit_models_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoLayer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoLayer) ProtoMessage() {}

func (x *VideoLayer) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoLayer.ProtoReflect.Descriptor instead.
func (*VideoLayer) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{14}
}

func (x *VideoLayer) GetQuality() VideoQuality {
	if x != nil {
		return x.Quality
	}
	return VideoQuality_LOW
}

func (x *VideoLayer) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *VideoLayer) GetHeight() uint32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *VideoLayer) GetBitrate() uint32 {
	if x != nil {
		return x.Bitrate
	}
	return 0
}

func (x *VideoLayer) GetSsrc() uint32 {
	if x != nil {
		return x.Ssrc
	}
	return 0
}

func (x *VideoLayer) GetSpatialLayer() int32 {
	if x != nil {
		return x.SpatialLayer
	}
	return 0
}

func (x *VideoLayer) GetRid() string {
	if x != nil {
		return x.Rid
	}
	return ""
}

// new DataPacket API
type DataPacket struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Deprecated: Marked as deprecated in livekit_models.proto.
	Kind DataPacket_Kind `protobuf:"varint,1,opt,name=kind,proto3,enum=livekit.DataPacket_Kind" json:"kind,omitempty"`
	// participant identity of user that sent the message
	ParticipantIdentity string `protobuf:"bytes,4,opt,name=participant_identity,json=participantIdentity,proto3" json:"participant_identity,omitempty"`
	// identities of participants who will receive the message (sent to all by default)
	DestinationIdentities []string `protobuf:"bytes,5,rep,name=destination_identities,json=destinationIdentities,proto3" json:"destination_identities,omitempty"`
	// Types that are valid to be assigned to Value:
	//
	//	*DataPacket_User
	//	*DataPacket_Speaker
	//	*DataPacket_SipDtmf
	//	*DataPacket_Transcription
	//	*DataPacket_Metrics
	//	*DataPacket_ChatMessage
	//	*DataPacket_RpcRequest
	//	*DataPacket_RpcAck
	//	*DataPacket_RpcResponse
	//	*DataPacket_StreamHeader
	//	*DataPacket_StreamChunk
	//	*DataPacket_StreamTrailer
	Value isDataPacket_Value `protobuf_oneof:"value"`
	// sequence number of reliable packet
	Sequence uint32 `protobuf:"varint,16,opt,name=sequence,proto3" json:"sequence,omitempty"`
	// sid of the user that sent the message
	ParticipantSid string `protobuf:"bytes,17,opt,name=participant_sid,json=participantSid,proto3" json:"participant_sid,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *DataPacket) Reset() {
	*x = DataPacket{}
	mi := &file_livekit_models_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataPacket) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataPacket) ProtoMessage() {}

func (x *DataPacket) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataPacket.ProtoReflect.Descriptor instead.
func (*DataPacket) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{15}
}

// Deprecated: Marked as deprecated in livekit_models.proto.
func (x *DataPacket) GetKind() DataPacket_Kind {
	if x != nil {
		return x.Kind
	}
	return DataPacket_RELIABLE
}

func (x *DataPacket) GetParticipantIdentity() string {
	if x != nil {
		return x.ParticipantIdentity
	}
	return ""
}

func (x *DataPacket) GetDestinationIdentities() []string {
	if x != nil {
		return x.DestinationIdentities
	}
	return nil
}

func (x *DataPacket) GetValue() isDataPacket_Value {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *DataPacket) GetUser() *UserPacket {
	if x != nil {
		if x, ok := x.Value.(*DataPacket_User); ok {
			return x.User
		}
	}
	return nil
}

// Deprecated: Marked as deprecated in livekit_models.proto.
func (x *DataPacket) GetSpeaker() *ActiveSpeakerUpdate {
	if x != nil {
		if x, ok := x.Value.(*DataPacket_Speaker); ok {
			return x.Speaker
		}
	}
	return nil
}

func (x *DataPacket) GetSipDtmf() *SipDTMF {
	if x != nil {
		if x, ok := x.Value.(*DataPacket_SipDtmf); ok {
			return x.SipDtmf
		}
	}
	return nil
}

func (x *DataPacket) GetTranscription() *Transcription {
	if x != nil {
		if x, ok := x.Value.(*DataPacket_Transcription); ok {
			return x.Transcription
		}
	}
	return nil
}

func (x *DataPacket) GetMetrics() *MetricsBatch {
	if x != nil {
		if x, ok := x.Value.(*DataPacket_Metrics); ok {
			return x.Metrics
		}
	}
	return nil
}

func (x *DataPacket) GetChatMessage() *ChatMessage {
	if x != nil {
		if x, ok := x.Value.(*DataPacket_ChatMessage); ok {
			return x.ChatMessage
		}
	}
	return nil
}

func (x *DataPacket) GetRpcRequest() *RpcRequest {
	if x != nil {
		if x, ok := x.Value.(*DataPacket_RpcRequest); ok {
			return x.RpcRequest
		}
	}
	return nil
}

func (x *DataPacket) GetRpcAck() *RpcAck {
	if x != nil {
		if x, ok := x.Value.(*DataPacket_RpcAck); ok {
			return x.RpcAck
		}
	}
	return nil
}

func (x *DataPacket) GetRpcResponse() *RpcResponse {
	if x != nil {
		if x, ok := x.Value.(*DataPacket_RpcResponse); ok {
			return x.RpcResponse
		}
	}
	return nil
}

func (x *DataPacket) GetStreamHeader() *DataStream_Header {
	if x != nil {
		if x, ok := x.Value.(*DataPacket_StreamHeader); ok {
			return x.StreamHeader
		}
	}
	return nil
}

func (x *DataPacket) GetStreamChunk() *DataStream_Chunk {
	if x != nil {
		if x, ok := x.Value.(*DataPacket_StreamChunk); ok {
			return x.StreamChunk
		}
	}
	return nil
}

func (x *DataPacket) GetStreamTrailer() *DataStream_Trailer {
	if x != nil {
		if x, ok := x.Value.(*DataPacket_StreamTrailer); ok {
			return x.StreamTrailer
		}
	}
	return nil
}

func (x *DataPacket) GetSequence() uint32 {
	if x != nil {
		return x.Sequence
	}
	return 0
}

func (x *DataPacket) GetParticipantSid() string {
	if x != nil {
		return x.ParticipantSid
	}
	return ""
}

type isDataPacket_Value interface {
	isDataPacket_Value()
}

type DataPacket_User struct {
	User *UserPacket `protobuf:"bytes,2,opt,name=user,proto3,oneof"`
}

type DataPacket_Speaker struct {
	// Deprecated: Marked as deprecated in livekit_models.proto.
	Speaker *ActiveSpeakerUpdate `protobuf:"bytes,3,opt,name=speaker,proto3,oneof"`
}

type DataPacket_SipDtmf struct {
	SipDtmf *SipDTMF `protobuf:"bytes,6,opt,name=sip_dtmf,json=sipDtmf,proto3,oneof"`
}

type DataPacket_Transcription struct {
	Transcription *Transcription `protobuf:"bytes,7,opt,name=transcription,proto3,oneof"`
}

type DataPacket_Metrics struct {
	Metrics *MetricsBatch `protobuf:"bytes,8,opt,name=metrics,proto3,oneof"`
}

type DataPacket_ChatMessage struct {
	ChatMessage *ChatMessage `protobuf:"bytes,9,opt,name=chat_message,json=chatMessage,proto3,oneof"`
}

type DataPacket_RpcRequest struct {
	RpcRequest *RpcRequest `protobuf:"bytes,10,opt,name=rpc_request,json=rpcRequest,proto3,oneof"`
}

type DataPacket_RpcAck struct {
	RpcAck *RpcAck `protobuf:"bytes,11,opt,name=rpc_ack,json=rpcAck,proto3,oneof"`
}

type DataPacket_RpcResponse struct {
	RpcResponse *RpcResponse `protobuf:"bytes,12,opt,name=rpc_response,json=rpcResponse,proto3,oneof"`
}

type DataPacket_StreamHeader struct {
	StreamHeader *DataStream_Header `protobuf:"bytes,13,opt,name=stream_header,json=streamHeader,proto3,oneof"`
}

type DataPacket_StreamChunk struct {
	StreamChunk *DataStream_Chunk `protobuf:"bytes,14,opt,name=stream_chunk,json=streamChunk,proto3,oneof"`
}

type DataPacket_StreamTrailer struct {
	StreamTrailer *DataStream_Trailer `protobuf:"bytes,15,opt,name=stream_trailer,json=streamTrailer,proto3,oneof"`
}

func (*DataPacket_User) isDataPacket_Value() {}

func (*DataPacket_Speaker) isDataPacket_Value() {}

func (*DataPacket_SipDtmf) isDataPacket_Value() {}

func (*DataPacket_Transcription) isDataPacket_Value() {}

func (*DataPacket_Metrics) isDataPacket_Value() {}

func (*DataPacket_ChatMessage) isDataPacket_Value() {}

func (*DataPacket_RpcRequest) isDataPacket_Value() {}

func (*DataPacket_RpcAck) isDataPacket_Value() {}

func (*DataPacket_RpcResponse) isDataPacket_Value() {}

func (*DataPacket_StreamHeader) isDataPacket_Value() {}

func (*DataPacket_StreamChunk) isDataPacket_Value() {}

func (*DataPacket_StreamTrailer) isDataPacket_Value() {}

type ActiveSpeakerUpdate struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Speakers      []*SpeakerInfo         `protobuf:"bytes,1,rep,name=speakers,proto3" json:"speakers,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActiveSpeakerUpdate) Reset() {
	*x = ActiveSpeakerUpdate{}
	mi := &file_livekit_models_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActiveSpeakerUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActiveSpeakerUpdate) ProtoMessage() {}

func (x *ActiveSpeakerUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActiveSpeakerUpdate.ProtoReflect.Descriptor instead.
func (*ActiveSpeakerUpdate) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{16}
}

func (x *ActiveSpeakerUpdate) GetSpeakers() []*SpeakerInfo {
	if x != nil {
		return x.Speakers
	}
	return nil
}

type SpeakerInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Sid   string                 `protobuf:"bytes,1,opt,name=sid,proto3" json:"sid,omitempty"`
	// audio level, 0-1.0, 1 is loudest
	Level float32 `protobuf:"fixed32,2,opt,name=level,proto3" json:"level,omitempty"`
	// true if speaker is currently active
	Active        bool `protobuf:"varint,3,opt,name=active,proto3" json:"active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SpeakerInfo) Reset() {
	*x = SpeakerInfo{}
	mi := &file_livekit_models_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SpeakerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpeakerInfo) ProtoMessage() {}

func (x *SpeakerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpeakerInfo.ProtoReflect.Descriptor instead.
func (*SpeakerInfo) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{17}
}

func (x *SpeakerInfo) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *SpeakerInfo) GetLevel() float32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *SpeakerInfo) GetActive() bool {
	if x != nil {
		return x.Active
	}
	return false
}

type UserPacket struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// participant ID of user that sent the message
	//
	// Deprecated: Marked as deprecated in livekit_models.proto.
	ParticipantSid string `protobuf:"bytes,1,opt,name=participant_sid,json=participantSid,proto3" json:"participant_sid,omitempty"`
	// Deprecated: Marked as deprecated in livekit_models.proto.
	ParticipantIdentity string `protobuf:"bytes,5,opt,name=participant_identity,json=participantIdentity,proto3" json:"participant_identity,omitempty"`
	// user defined payload
	Payload []byte `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
	// the ID of the participants who will receive the message (sent to all by default)
	//
	// Deprecated: Marked as deprecated in livekit_models.proto.
	DestinationSids []string `protobuf:"bytes,3,rep,name=destination_sids,json=destinationSids,proto3" json:"destination_sids,omitempty"`
	// identities of participants who will receive the message (sent to all by default)
	//
	// Deprecated: Marked as deprecated in livekit_models.proto.
	DestinationIdentities []string `protobuf:"bytes,6,rep,name=destination_identities,json=destinationIdentities,proto3" json:"destination_identities,omitempty"`
	// topic under which the message was published
	Topic *string `protobuf:"bytes,4,opt,name=topic,proto3,oneof" json:"topic,omitempty"`
	// Unique ID to indentify the message
	Id *string `protobuf:"bytes,8,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// start and end time allow relating the message to specific media time
	StartTime *uint64 `protobuf:"varint,9,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	EndTime   *uint64 `protobuf:"varint,10,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
	// added by SDK to enable de-duping of messages, for INTERNAL USE ONLY
	Nonce         []byte `protobuf:"bytes,11,opt,name=nonce,proto3" json:"nonce,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserPacket) Reset() {
	*x = UserPacket{}
	mi := &file_livekit_models_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserPacket) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserPacket) ProtoMessage() {}

func (x *UserPacket) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserPacket.ProtoReflect.Descriptor instead.
func (*UserPacket) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{18}
}

// Deprecated: Marked as deprecated in livekit_models.proto.
func (x *UserPacket) GetParticipantSid() string {
	if x != nil {
		return x.ParticipantSid
	}
	return ""
}

// Deprecated: Marked as deprecated in livekit_models.proto.
func (x *UserPacket) GetParticipantIdentity() string {
	if x != nil {
		return x.ParticipantIdentity
	}
	return ""
}

func (x *UserPacket) GetPayload() []byte {
	if x != nil {
		return x.Payload
	}
	return nil
}

// Deprecated: Marked as deprecated in livekit_models.proto.
func (x *UserPacket) GetDestinationSids() []string {
	if x != nil {
		return x.DestinationSids
	}
	return nil
}

// Deprecated: Marked as deprecated in livekit_models.proto.
func (x *UserPacket) GetDestinationIdentities() []string {
	if x != nil {
		return x.DestinationIdentities
	}
	return nil
}

func (x *UserPacket) GetTopic() string {
	if x != nil && x.Topic != nil {
		return *x.Topic
	}
	return ""
}

func (x *UserPacket) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *UserPacket) GetStartTime() uint64 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

func (x *UserPacket) GetEndTime() uint64 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

func (x *UserPacket) GetNonce() []byte {
	if x != nil {
		return x.Nonce
	}
	return nil
}

type SipDTMF struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,3,opt,name=code,proto3" json:"code,omitempty"`
	Digit         string                 `protobuf:"bytes,4,opt,name=digit,proto3" json:"digit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SipDTMF) Reset() {
	*x = SipDTMF{}
	mi := &file_livekit_models_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SipDTMF) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SipDTMF) ProtoMessage() {}

func (x *SipDTMF) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SipDTMF.ProtoReflect.Descriptor instead.
func (*SipDTMF) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{19}
}

func (x *SipDTMF) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SipDTMF) GetDigit() string {
	if x != nil {
		return x.Digit
	}
	return ""
}

type Transcription struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Participant that got its speech transcribed
	TranscribedParticipantIdentity string                  `protobuf:"bytes,2,opt,name=transcribed_participant_identity,json=transcribedParticipantIdentity,proto3" json:"transcribed_participant_identity,omitempty"`
	TrackId                        string                  `protobuf:"bytes,3,opt,name=track_id,json=trackId,proto3" json:"track_id,omitempty"`
	Segments                       []*TranscriptionSegment `protobuf:"bytes,4,rep,name=segments,proto3" json:"segments,omitempty"`
	unknownFields                  protoimpl.UnknownFields
	sizeCache                      protoimpl.SizeCache
}

func (x *Transcription) Reset() {
	*x = Transcription{}
	mi := &file_livekit_models_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Transcription) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Transcription) ProtoMessage() {}

func (x *Transcription) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Transcription.ProtoReflect.Descriptor instead.
func (*Transcription) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{20}
}

func (x *Transcription) GetTranscribedParticipantIdentity() string {
	if x != nil {
		return x.TranscribedParticipantIdentity
	}
	return ""
}

func (x *Transcription) GetTrackId() string {
	if x != nil {
		return x.TrackId
	}
	return ""
}

func (x *Transcription) GetSegments() []*TranscriptionSegment {
	if x != nil {
		return x.Segments
	}
	return nil
}

type TranscriptionSegment struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Text          string                 `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	StartTime     uint64                 `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime       uint64                 `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Final         bool                   `protobuf:"varint,5,opt,name=final,proto3" json:"final,omitempty"`
	Language      string                 `protobuf:"bytes,6,opt,name=language,proto3" json:"language,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TranscriptionSegment) Reset() {
	*x = TranscriptionSegment{}
	mi := &file_livekit_models_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TranscriptionSegment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TranscriptionSegment) ProtoMessage() {}

func (x *TranscriptionSegment) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TranscriptionSegment.ProtoReflect.Descriptor instead.
func (*TranscriptionSegment) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{21}
}

func (x *TranscriptionSegment) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TranscriptionSegment) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *TranscriptionSegment) GetStartTime() uint64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *TranscriptionSegment) GetEndTime() uint64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *TranscriptionSegment) GetFinal() bool {
	if x != nil {
		return x.Final
	}
	return false
}

func (x *TranscriptionSegment) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

type ChatMessage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` // uuid
	Timestamp     int64                  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	EditTimestamp *int64                 `protobuf:"varint,3,opt,name=edit_timestamp,json=editTimestamp,proto3,oneof" json:"edit_timestamp,omitempty"` // populated only if the intent is to edit/update an existing message
	Message       string                 `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
	Deleted       bool                   `protobuf:"varint,5,opt,name=deleted,proto3" json:"deleted,omitempty"`     // true to remove message
	Generated     bool                   `protobuf:"varint,6,opt,name=generated,proto3" json:"generated,omitempty"` // true if the chat message has been generated by an agent from a participant's audio transcription
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChatMessage) Reset() {
	*x = ChatMessage{}
	mi := &file_livekit_models_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatMessage) ProtoMessage() {}

func (x *ChatMessage) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatMessage.ProtoReflect.Descriptor instead.
func (*ChatMessage) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{22}
}

func (x *ChatMessage) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ChatMessage) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *ChatMessage) GetEditTimestamp() int64 {
	if x != nil && x.EditTimestamp != nil {
		return *x.EditTimestamp
	}
	return 0
}

func (x *ChatMessage) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ChatMessage) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *ChatMessage) GetGenerated() bool {
	if x != nil {
		return x.Generated
	}
	return false
}

type RpcRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Method            string                 `protobuf:"bytes,2,opt,name=method,proto3" json:"method,omitempty"`
	Payload           string                 `protobuf:"bytes,3,opt,name=payload,proto3" json:"payload,omitempty"`
	ResponseTimeoutMs uint32                 `protobuf:"varint,4,opt,name=response_timeout_ms,json=responseTimeoutMs,proto3" json:"response_timeout_ms,omitempty"`
	Version           uint32                 `protobuf:"varint,5,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *RpcRequest) Reset() {
	*x = RpcRequest{}
	mi := &file_livekit_models_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcRequest) ProtoMessage() {}

func (x *RpcRequest) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcRequest.ProtoReflect.Descriptor instead.
func (*RpcRequest) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{23}
}

func (x *RpcRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RpcRequest) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *RpcRequest) GetPayload() string {
	if x != nil {
		return x.Payload
	}
	return ""
}

func (x *RpcRequest) GetResponseTimeoutMs() uint32 {
	if x != nil {
		return x.ResponseTimeoutMs
	}
	return 0
}

func (x *RpcRequest) GetVersion() uint32 {
	if x != nil {
		return x.Version
	}
	return 0
}

type RpcAck struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RequestId     string                 `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcAck) Reset() {
	*x = RpcAck{}
	mi := &file_livekit_models_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcAck) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcAck) ProtoMessage() {}

func (x *RpcAck) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcAck.ProtoReflect.Descriptor instead.
func (*RpcAck) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{24}
}

func (x *RpcAck) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

type RpcResponse struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	RequestId string                 `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Types that are valid to be assigned to Value:
	//
	//	*RpcResponse_Payload
	//	*RpcResponse_Error
	Value         isRpcResponse_Value `protobuf_oneof:"value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcResponse) Reset() {
	*x = RpcResponse{}
	mi := &file_livekit_models_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcResponse) ProtoMessage() {}

func (x *RpcResponse) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcResponse.ProtoReflect.Descriptor instead.
func (*RpcResponse) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{25}
}

func (x *RpcResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *RpcResponse) GetValue() isRpcResponse_Value {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *RpcResponse) GetPayload() string {
	if x != nil {
		if x, ok := x.Value.(*RpcResponse_Payload); ok {
			return x.Payload
		}
	}
	return ""
}

func (x *RpcResponse) GetError() *RpcError {
	if x != nil {
		if x, ok := x.Value.(*RpcResponse_Error); ok {
			return x.Error
		}
	}
	return nil
}

type isRpcResponse_Value interface {
	isRpcResponse_Value()
}

type RpcResponse_Payload struct {
	Payload string `protobuf:"bytes,2,opt,name=payload,proto3,oneof"`
}

type RpcResponse_Error struct {
	Error *RpcError `protobuf:"bytes,3,opt,name=error,proto3,oneof"`
}

func (*RpcResponse_Payload) isRpcResponse_Value() {}

func (*RpcResponse_Error) isRpcResponse_Value() {}

type RpcError struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          string                 `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcError) Reset() {
	*x = RpcError{}
	mi := &file_livekit_models_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcError) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcError) ProtoMessage() {}

func (x *RpcError) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcError.ProtoReflect.Descriptor instead.
func (*RpcError) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{26}
}

func (x *RpcError) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RpcError) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *RpcError) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type ParticipantTracks struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// participant ID of participant to whom the tracks belong
	ParticipantSid string   `protobuf:"bytes,1,opt,name=participant_sid,json=participantSid,proto3" json:"participant_sid,omitempty"`
	TrackSids      []string `protobuf:"bytes,2,rep,name=track_sids,json=trackSids,proto3" json:"track_sids,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ParticipantTracks) Reset() {
	*x = ParticipantTracks{}
	mi := &file_livekit_models_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParticipantTracks) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParticipantTracks) ProtoMessage() {}

func (x *ParticipantTracks) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParticipantTracks.ProtoReflect.Descriptor instead.
func (*ParticipantTracks) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{27}
}

func (x *ParticipantTracks) GetParticipantSid() string {
	if x != nil {
		return x.ParticipantSid
	}
	return ""
}

func (x *ParticipantTracks) GetTrackSids() []string {
	if x != nil {
		return x.TrackSids
	}
	return nil
}

// details about the server
type ServerInfo struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	Edition  ServerInfo_Edition     `protobuf:"varint,1,opt,name=edition,proto3,enum=livekit.ServerInfo_Edition" json:"edition,omitempty"`
	Version  string                 `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Protocol int32                  `protobuf:"varint,3,opt,name=protocol,proto3" json:"protocol,omitempty"`
	Region   string                 `protobuf:"bytes,4,opt,name=region,proto3" json:"region,omitempty"`
	NodeId   string                 `protobuf:"bytes,5,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	// additional debugging information. sent only if server is in development mode
	DebugInfo     string `protobuf:"bytes,6,opt,name=debug_info,json=debugInfo,proto3" json:"debug_info,omitempty"`
	AgentProtocol int32  `protobuf:"varint,7,opt,name=agent_protocol,json=agentProtocol,proto3" json:"agent_protocol,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServerInfo) Reset() {
	*x = ServerInfo{}
	mi := &file_livekit_models_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerInfo) ProtoMessage() {}

func (x *ServerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerInfo.ProtoReflect.Descriptor instead.
func (*ServerInfo) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{28}
}

func (x *ServerInfo) GetEdition() ServerInfo_Edition {
	if x != nil {
		return x.Edition
	}
	return ServerInfo_Standard
}

func (x *ServerInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ServerInfo) GetProtocol() int32 {
	if x != nil {
		return x.Protocol
	}
	return 0
}

func (x *ServerInfo) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ServerInfo) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *ServerInfo) GetDebugInfo() string {
	if x != nil {
		return x.DebugInfo
	}
	return ""
}

func (x *ServerInfo) GetAgentProtocol() int32 {
	if x != nil {
		return x.AgentProtocol
	}
	return 0
}

// details about the client
type ClientInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Sdk            ClientInfo_SDK         `protobuf:"varint,1,opt,name=sdk,proto3,enum=livekit.ClientInfo_SDK" json:"sdk,omitempty"`
	Version        string                 `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Protocol       int32                  `protobuf:"varint,3,opt,name=protocol,proto3" json:"protocol,omitempty"`
	Os             string                 `protobuf:"bytes,4,opt,name=os,proto3" json:"os,omitempty"`
	OsVersion      string                 `protobuf:"bytes,5,opt,name=os_version,json=osVersion,proto3" json:"os_version,omitempty"`
	DeviceModel    string                 `protobuf:"bytes,6,opt,name=device_model,json=deviceModel,proto3" json:"device_model,omitempty"`
	Browser        string                 `protobuf:"bytes,7,opt,name=browser,proto3" json:"browser,omitempty"`
	BrowserVersion string                 `protobuf:"bytes,8,opt,name=browser_version,json=browserVersion,proto3" json:"browser_version,omitempty"`
	Address        string                 `protobuf:"bytes,9,opt,name=address,proto3" json:"address,omitempty"`
	// wifi, wired, cellular, vpn, empty if not known
	Network string `protobuf:"bytes,10,opt,name=network,proto3" json:"network,omitempty"`
	// comma separated list of additional LiveKit SDKs in use of this client, with versions
	// e.g. "components-js:1.2.3,track-processors-js:1.2.3"
	OtherSdks     string `protobuf:"bytes,11,opt,name=other_sdks,json=otherSdks,proto3" json:"other_sdks,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientInfo) Reset() {
	*x = ClientInfo{}
	mi := &file_livekit_models_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientInfo) ProtoMessage() {}

func (x *ClientInfo) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientInfo.ProtoReflect.Descriptor instead.
func (*ClientInfo) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{29}
}

func (x *ClientInfo) GetSdk() ClientInfo_SDK {
	if x != nil {
		return x.Sdk
	}
	return ClientInfo_UNKNOWN
}

func (x *ClientInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ClientInfo) GetProtocol() int32 {
	if x != nil {
		return x.Protocol
	}
	return 0
}

func (x *ClientInfo) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *ClientInfo) GetOsVersion() string {
	if x != nil {
		return x.OsVersion
	}
	return ""
}

func (x *ClientInfo) GetDeviceModel() string {
	if x != nil {
		return x.DeviceModel
	}
	return ""
}

func (x *ClientInfo) GetBrowser() string {
	if x != nil {
		return x.Browser
	}
	return ""
}

func (x *ClientInfo) GetBrowserVersion() string {
	if x != nil {
		return x.BrowserVersion
	}
	return ""
}

func (x *ClientInfo) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *ClientInfo) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *ClientInfo) GetOtherSdks() string {
	if x != nil {
		return x.OtherSdks
	}
	return ""
}

// server provided client configuration
type ClientConfiguration struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Video            *VideoConfiguration    `protobuf:"bytes,1,opt,name=video,proto3" json:"video,omitempty"`
	Screen           *VideoConfiguration    `protobuf:"bytes,2,opt,name=screen,proto3" json:"screen,omitempty"`
	ResumeConnection ClientConfigSetting    `protobuf:"varint,3,opt,name=resume_connection,json=resumeConnection,proto3,enum=livekit.ClientConfigSetting" json:"resume_connection,omitempty"`
	DisabledCodecs   *DisabledCodecs        `protobuf:"bytes,4,opt,name=disabled_codecs,json=disabledCodecs,proto3" json:"disabled_codecs,omitempty"`
	ForceRelay       ClientConfigSetting    `protobuf:"varint,5,opt,name=force_relay,json=forceRelay,proto3,enum=livekit.ClientConfigSetting" json:"force_relay,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ClientConfiguration) Reset() {
	*x = ClientConfiguration{}
	mi := &file_livekit_models_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientConfiguration) ProtoMessage() {}

func (x *ClientConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientConfiguration.ProtoReflect.Descriptor instead.
func (*ClientConfiguration) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{30}
}

func (x *ClientConfiguration) GetVideo() *VideoConfiguration {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *ClientConfiguration) GetScreen() *VideoConfiguration {
	if x != nil {
		return x.Screen
	}
	return nil
}

func (x *ClientConfiguration) GetResumeConnection() ClientConfigSetting {
	if x != nil {
		return x.ResumeConnection
	}
	return ClientConfigSetting_UNSET
}

func (x *ClientConfiguration) GetDisabledCodecs() *DisabledCodecs {
	if x != nil {
		return x.DisabledCodecs
	}
	return nil
}

func (x *ClientConfiguration) GetForceRelay() ClientConfigSetting {
	if x != nil {
		return x.ForceRelay
	}
	return ClientConfigSetting_UNSET
}

type VideoConfiguration struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	HardwareEncoder ClientConfigSetting    `protobuf:"varint,1,opt,name=hardware_encoder,json=hardwareEncoder,proto3,enum=livekit.ClientConfigSetting" json:"hardware_encoder,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *VideoConfiguration) Reset() {
	*x = VideoConfiguration{}
	mi := &file_livekit_models_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoConfiguration) ProtoMessage() {}

func (x *VideoConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoConfiguration.ProtoReflect.Descriptor instead.
func (*VideoConfiguration) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{31}
}

func (x *VideoConfiguration) GetHardwareEncoder() ClientConfigSetting {
	if x != nil {
		return x.HardwareEncoder
	}
	return ClientConfigSetting_UNSET
}

type DisabledCodecs struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// disabled for both publish and subscribe
	Codecs []*Codec `protobuf:"bytes,1,rep,name=codecs,proto3" json:"codecs,omitempty"`
	// only disable for publish
	Publish       []*Codec `protobuf:"bytes,2,rep,name=publish,proto3" json:"publish,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DisabledCodecs) Reset() {
	*x = DisabledCodecs{}
	mi := &file_livekit_models_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DisabledCodecs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisabledCodecs) ProtoMessage() {}

func (x *DisabledCodecs) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisabledCodecs.ProtoReflect.Descriptor instead.
func (*DisabledCodecs) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{32}
}

func (x *DisabledCodecs) GetCodecs() []*Codec {
	if x != nil {
		return x.Codecs
	}
	return nil
}

func (x *DisabledCodecs) GetPublish() []*Codec {
	if x != nil {
		return x.Publish
	}
	return nil
}

type RTPDrift struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	StartTime      *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime        *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Duration       float64                `protobuf:"fixed64,3,opt,name=duration,proto3" json:"duration,omitempty"`
	StartTimestamp uint64                 `protobuf:"varint,4,opt,name=start_timestamp,json=startTimestamp,proto3" json:"start_timestamp,omitempty"`
	EndTimestamp   uint64                 `protobuf:"varint,5,opt,name=end_timestamp,json=endTimestamp,proto3" json:"end_timestamp,omitempty"`
	RtpClockTicks  uint64                 `protobuf:"varint,6,opt,name=rtp_clock_ticks,json=rtpClockTicks,proto3" json:"rtp_clock_ticks,omitempty"`
	DriftSamples   int64                  `protobuf:"varint,7,opt,name=drift_samples,json=driftSamples,proto3" json:"drift_samples,omitempty"`
	DriftMs        float64                `protobuf:"fixed64,8,opt,name=drift_ms,json=driftMs,proto3" json:"drift_ms,omitempty"`
	ClockRate      float64                `protobuf:"fixed64,9,opt,name=clock_rate,json=clockRate,proto3" json:"clock_rate,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *RTPDrift) Reset() {
	*x = RTPDrift{}
	mi := &file_livekit_models_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RTPDrift) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTPDrift) ProtoMessage() {}

func (x *RTPDrift) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTPDrift.ProtoReflect.Descriptor instead.
func (*RTPDrift) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{33}
}

func (x *RTPDrift) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *RTPDrift) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *RTPDrift) GetDuration() float64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *RTPDrift) GetStartTimestamp() uint64 {
	if x != nil {
		return x.StartTimestamp
	}
	return 0
}

func (x *RTPDrift) GetEndTimestamp() uint64 {
	if x != nil {
		return x.EndTimestamp
	}
	return 0
}

func (x *RTPDrift) GetRtpClockTicks() uint64 {
	if x != nil {
		return x.RtpClockTicks
	}
	return 0
}

func (x *RTPDrift) GetDriftSamples() int64 {
	if x != nil {
		return x.DriftSamples
	}
	return 0
}

func (x *RTPDrift) GetDriftMs() float64 {
	if x != nil {
		return x.DriftMs
	}
	return 0
}

func (x *RTPDrift) GetClockRate() float64 {
	if x != nil {
		return x.ClockRate
	}
	return 0
}

type RTPStats struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	StartTime            *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Duration             float64                `protobuf:"fixed64,3,opt,name=duration,proto3" json:"duration,omitempty"`
	Packets              uint32                 `protobuf:"varint,4,opt,name=packets,proto3" json:"packets,omitempty"`
	PacketRate           float64                `protobuf:"fixed64,5,opt,name=packet_rate,json=packetRate,proto3" json:"packet_rate,omitempty"`
	Bytes                uint64                 `protobuf:"varint,6,opt,name=bytes,proto3" json:"bytes,omitempty"`
	HeaderBytes          uint64                 `protobuf:"varint,39,opt,name=header_bytes,json=headerBytes,proto3" json:"header_bytes,omitempty"`
	Bitrate              float64                `protobuf:"fixed64,7,opt,name=bitrate,proto3" json:"bitrate,omitempty"`
	PacketsLost          uint32                 `protobuf:"varint,8,opt,name=packets_lost,json=packetsLost,proto3" json:"packets_lost,omitempty"`
	PacketLossRate       float64                `protobuf:"fixed64,9,opt,name=packet_loss_rate,json=packetLossRate,proto3" json:"packet_loss_rate,omitempty"`
	PacketLossPercentage float32                `protobuf:"fixed32,10,opt,name=packet_loss_percentage,json=packetLossPercentage,proto3" json:"packet_loss_percentage,omitempty"`
	PacketsDuplicate     uint32                 `protobuf:"varint,11,opt,name=packets_duplicate,json=packetsDuplicate,proto3" json:"packets_duplicate,omitempty"`
	PacketDuplicateRate  float64                `protobuf:"fixed64,12,opt,name=packet_duplicate_rate,json=packetDuplicateRate,proto3" json:"packet_duplicate_rate,omitempty"`
	BytesDuplicate       uint64                 `protobuf:"varint,13,opt,name=bytes_duplicate,json=bytesDuplicate,proto3" json:"bytes_duplicate,omitempty"`
	HeaderBytesDuplicate uint64                 `protobuf:"varint,40,opt,name=header_bytes_duplicate,json=headerBytesDuplicate,proto3" json:"header_bytes_duplicate,omitempty"`
	BitrateDuplicate     float64                `protobuf:"fixed64,14,opt,name=bitrate_duplicate,json=bitrateDuplicate,proto3" json:"bitrate_duplicate,omitempty"`
	PacketsPadding       uint32                 `protobuf:"varint,15,opt,name=packets_padding,json=packetsPadding,proto3" json:"packets_padding,omitempty"`
	PacketPaddingRate    float64                `protobuf:"fixed64,16,opt,name=packet_padding_rate,json=packetPaddingRate,proto3" json:"packet_padding_rate,omitempty"`
	BytesPadding         uint64                 `protobuf:"varint,17,opt,name=bytes_padding,json=bytesPadding,proto3" json:"bytes_padding,omitempty"`
	HeaderBytesPadding   uint64                 `protobuf:"varint,41,opt,name=header_bytes_padding,json=headerBytesPadding,proto3" json:"header_bytes_padding,omitempty"`
	BitratePadding       float64                `protobuf:"fixed64,18,opt,name=bitrate_padding,json=bitratePadding,proto3" json:"bitrate_padding,omitempty"`
	PacketsOutOfOrder    uint32                 `protobuf:"varint,19,opt,name=packets_out_of_order,json=packetsOutOfOrder,proto3" json:"packets_out_of_order,omitempty"`
	Frames               uint32                 `protobuf:"varint,20,opt,name=frames,proto3" json:"frames,omitempty"`
	FrameRate            float64                `protobuf:"fixed64,21,opt,name=frame_rate,json=frameRate,proto3" json:"frame_rate,omitempty"`
	JitterCurrent        float64                `protobuf:"fixed64,22,opt,name=jitter_current,json=jitterCurrent,proto3" json:"jitter_current,omitempty"`
	JitterMax            float64                `protobuf:"fixed64,23,opt,name=jitter_max,json=jitterMax,proto3" json:"jitter_max,omitempty"`
	GapHistogram         map[int32]uint32       `protobuf:"bytes,24,rep,name=gap_histogram,json=gapHistogram,proto3" json:"gap_histogram,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	Nacks                uint32                 `protobuf:"varint,25,opt,name=nacks,proto3" json:"nacks,omitempty"`
	NackAcks             uint32                 `protobuf:"varint,37,opt,name=nack_acks,json=nackAcks,proto3" json:"nack_acks,omitempty"`
	NackMisses           uint32                 `protobuf:"varint,26,opt,name=nack_misses,json=nackMisses,proto3" json:"nack_misses,omitempty"`
	NackRepeated         uint32                 `protobuf:"varint,38,opt,name=nack_repeated,json=nackRepeated,proto3" json:"nack_repeated,omitempty"`
	Plis                 uint32                 `protobuf:"varint,27,opt,name=plis,proto3" json:"plis,omitempty"`
	LastPli              *timestamppb.Timestamp `protobuf:"bytes,28,opt,name=last_pli,json=lastPli,proto3" json:"last_pli,omitempty"`
	Firs                 uint32                 `protobuf:"varint,29,opt,name=firs,proto3" json:"firs,omitempty"`
	LastFir              *timestamppb.Timestamp `protobuf:"bytes,30,opt,name=last_fir,json=lastFir,proto3" json:"last_fir,omitempty"`
	RttCurrent           uint32                 `protobuf:"varint,31,opt,name=rtt_current,json=rttCurrent,proto3" json:"rtt_current,omitempty"`
	RttMax               uint32                 `protobuf:"varint,32,opt,name=rtt_max,json=rttMax,proto3" json:"rtt_max,omitempty"`
	KeyFrames            uint32                 `protobuf:"varint,33,opt,name=key_frames,json=keyFrames,proto3" json:"key_frames,omitempty"`
	LastKeyFrame         *timestamppb.Timestamp `protobuf:"bytes,34,opt,name=last_key_frame,json=lastKeyFrame,proto3" json:"last_key_frame,omitempty"`
	LayerLockPlis        uint32                 `protobuf:"varint,35,opt,name=layer_lock_plis,json=layerLockPlis,proto3" json:"layer_lock_plis,omitempty"`
	LastLayerLockPli     *timestamppb.Timestamp `protobuf:"bytes,36,opt,name=last_layer_lock_pli,json=lastLayerLockPli,proto3" json:"last_layer_lock_pli,omitempty"`
	PacketDrift          *RTPDrift              `protobuf:"bytes,44,opt,name=packet_drift,json=packetDrift,proto3" json:"packet_drift,omitempty"`
	NtpReportDrift       *RTPDrift              `protobuf:"bytes,45,opt,name=ntp_report_drift,json=ntpReportDrift,proto3" json:"ntp_report_drift,omitempty"`
	RebasedReportDrift   *RTPDrift              `protobuf:"bytes,46,opt,name=rebased_report_drift,json=rebasedReportDrift,proto3" json:"rebased_report_drift,omitempty"`
	ReceivedReportDrift  *RTPDrift              `protobuf:"bytes,47,opt,name=received_report_drift,json=receivedReportDrift,proto3" json:"received_report_drift,omitempty"` // NEXT_ID: 48
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *RTPStats) Reset() {
	*x = RTPStats{}
	mi := &file_livekit_models_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RTPStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTPStats) ProtoMessage() {}

func (x *RTPStats) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTPStats.ProtoReflect.Descriptor instead.
func (*RTPStats) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{34}
}

func (x *RTPStats) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *RTPStats) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *RTPStats) GetDuration() float64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *RTPStats) GetPackets() uint32 {
	if x != nil {
		return x.Packets
	}
	return 0
}

func (x *RTPStats) GetPacketRate() float64 {
	if x != nil {
		return x.PacketRate
	}
	return 0
}

func (x *RTPStats) GetBytes() uint64 {
	if x != nil {
		return x.Bytes
	}
	return 0
}

func (x *RTPStats) GetHeaderBytes() uint64 {
	if x != nil {
		return x.HeaderBytes
	}
	return 0
}

func (x *RTPStats) GetBitrate() float64 {
	if x != nil {
		return x.Bitrate
	}
	return 0
}

func (x *RTPStats) GetPacketsLost() uint32 {
	if x != nil {
		return x.PacketsLost
	}
	return 0
}

func (x *RTPStats) GetPacketLossRate() float64 {
	if x != nil {
		return x.PacketLossRate
	}
	return 0
}

func (x *RTPStats) GetPacketLossPercentage() float32 {
	if x != nil {
		return x.PacketLossPercentage
	}
	return 0
}

func (x *RTPStats) GetPacketsDuplicate() uint32 {
	if x != nil {
		return x.PacketsDuplicate
	}
	return 0
}

func (x *RTPStats) GetPacketDuplicateRate() float64 {
	if x != nil {
		return x.PacketDuplicateRate
	}
	return 0
}

func (x *RTPStats) GetBytesDuplicate() uint64 {
	if x != nil {
		return x.BytesDuplicate
	}
	return 0
}

func (x *RTPStats) GetHeaderBytesDuplicate() uint64 {
	if x != nil {
		return x.HeaderBytesDuplicate
	}
	return 0
}

func (x *RTPStats) GetBitrateDuplicate() float64 {
	if x != nil {
		return x.BitrateDuplicate
	}
	return 0
}

func (x *RTPStats) GetPacketsPadding() uint32 {
	if x != nil {
		return x.PacketsPadding
	}
	return 0
}

func (x *RTPStats) GetPacketPaddingRate() float64 {
	if x != nil {
		return x.PacketPaddingRate
	}
	return 0
}

func (x *RTPStats) GetBytesPadding() uint64 {
	if x != nil {
		return x.BytesPadding
	}
	return 0
}

func (x *RTPStats) GetHeaderBytesPadding() uint64 {
	if x != nil {
		return x.HeaderBytesPadding
	}
	return 0
}

func (x *RTPStats) GetBitratePadding() float64 {
	if x != nil {
		return x.BitratePadding
	}
	return 0
}

func (x *RTPStats) GetPacketsOutOfOrder() uint32 {
	if x != nil {
		return x.PacketsOutOfOrder
	}
	return 0
}

func (x *RTPStats) GetFrames() uint32 {
	if x != nil {
		return x.Frames
	}
	return 0
}

func (x *RTPStats) GetFrameRate() float64 {
	if x != nil {
		return x.FrameRate
	}
	return 0
}

func (x *RTPStats) GetJitterCurrent() float64 {
	if x != nil {
		return x.JitterCurrent
	}
	return 0
}

func (x *RTPStats) GetJitterMax() float64 {
	if x != nil {
		return x.JitterMax
	}
	return 0
}

func (x *RTPStats) GetGapHistogram() map[int32]uint32 {
	if x != nil {
		return x.GapHistogram
	}
	return nil
}

func (x *RTPStats) GetNacks() uint32 {
	if x != nil {
		return x.Nacks
	}
	return 0
}

func (x *RTPStats) GetNackAcks() uint32 {
	if x != nil {
		return x.NackAcks
	}
	return 0
}

func (x *RTPStats) GetNackMisses() uint32 {
	if x != nil {
		return x.NackMisses
	}
	return 0
}

func (x *RTPStats) GetNackRepeated() uint32 {
	if x != nil {
		return x.NackRepeated
	}
	return 0
}

func (x *RTPStats) GetPlis() uint32 {
	if x != nil {
		return x.Plis
	}
	return 0
}

func (x *RTPStats) GetLastPli() *timestamppb.Timestamp {
	if x != nil {
		return x.LastPli
	}
	return nil
}

func (x *RTPStats) GetFirs() uint32 {
	if x != nil {
		return x.Firs
	}
	return 0
}

func (x *RTPStats) GetLastFir() *timestamppb.Timestamp {
	if x != nil {
		return x.LastFir
	}
	return nil
}

func (x *RTPStats) GetRttCurrent() uint32 {
	if x != nil {
		return x.RttCurrent
	}
	return 0
}

func (x *RTPStats) GetRttMax() uint32 {
	if x != nil {
		return x.RttMax
	}
	return 0
}

func (x *RTPStats) GetKeyFrames() uint32 {
	if x != nil {
		return x.KeyFrames
	}
	return 0
}

func (x *RTPStats) GetLastKeyFrame() *timestamppb.Timestamp {
	if x != nil {
		return x.LastKeyFrame
	}
	return nil
}

func (x *RTPStats) GetLayerLockPlis() uint32 {
	if x != nil {
		return x.LayerLockPlis
	}
	return 0
}

func (x *RTPStats) GetLastLayerLockPli() *timestamppb.Timestamp {
	if x != nil {
		return x.LastLayerLockPli
	}
	return nil
}

func (x *RTPStats) GetPacketDrift() *RTPDrift {
	if x != nil {
		return x.PacketDrift
	}
	return nil
}

func (x *RTPStats) GetNtpReportDrift() *RTPDrift {
	if x != nil {
		return x.NtpReportDrift
	}
	return nil
}

func (x *RTPStats) GetRebasedReportDrift() *RTPDrift {
	if x != nil {
		return x.RebasedReportDrift
	}
	return nil
}

func (x *RTPStats) GetReceivedReportDrift() *RTPDrift {
	if x != nil {
		return x.ReceivedReportDrift
	}
	return nil
}

type RTCPSenderReportState struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	RtpTimestamp    uint32                 `protobuf:"varint,1,opt,name=rtp_timestamp,json=rtpTimestamp,proto3" json:"rtp_timestamp,omitempty"`
	RtpTimestampExt uint64                 `protobuf:"varint,2,opt,name=rtp_timestamp_ext,json=rtpTimestampExt,proto3" json:"rtp_timestamp_ext,omitempty"`
	NtpTimestamp    uint64                 `protobuf:"varint,3,opt,name=ntp_timestamp,json=ntpTimestamp,proto3" json:"ntp_timestamp,omitempty"`
	At              int64                  `protobuf:"varint,4,opt,name=at,proto3" json:"at,omitempty"` // time at which this happened
	AtAdjusted      int64                  `protobuf:"varint,5,opt,name=at_adjusted,json=atAdjusted,proto3" json:"at_adjusted,omitempty"`
	Packets         uint32                 `protobuf:"varint,6,opt,name=packets,proto3" json:"packets,omitempty"`
	Octets          uint64                 `protobuf:"varint,7,opt,name=octets,proto3" json:"octets,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *RTCPSenderReportState) Reset() {
	*x = RTCPSenderReportState{}
	mi := &file_livekit_models_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RTCPSenderReportState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTCPSenderReportState) ProtoMessage() {}

func (x *RTCPSenderReportState) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTCPSenderReportState.ProtoReflect.Descriptor instead.
func (*RTCPSenderReportState) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{35}
}

func (x *RTCPSenderReportState) GetRtpTimestamp() uint32 {
	if x != nil {
		return x.RtpTimestamp
	}
	return 0
}

func (x *RTCPSenderReportState) GetRtpTimestampExt() uint64 {
	if x != nil {
		return x.RtpTimestampExt
	}
	return 0
}

func (x *RTCPSenderReportState) GetNtpTimestamp() uint64 {
	if x != nil {
		return x.NtpTimestamp
	}
	return 0
}

func (x *RTCPSenderReportState) GetAt() int64 {
	if x != nil {
		return x.At
	}
	return 0
}

func (x *RTCPSenderReportState) GetAtAdjusted() int64 {
	if x != nil {
		return x.AtAdjusted
	}
	return 0
}

func (x *RTCPSenderReportState) GetPackets() uint32 {
	if x != nil {
		return x.Packets
	}
	return 0
}

func (x *RTCPSenderReportState) GetOctets() uint64 {
	if x != nil {
		return x.Octets
	}
	return 0
}

type RTPForwarderState struct {
	state                     protoimpl.MessageState `protogen:"open.v1"`
	Started                   bool                   `protobuf:"varint,1,opt,name=started,proto3" json:"started,omitempty"`
	ReferenceLayerSpatial     int32                  `protobuf:"varint,2,opt,name=reference_layer_spatial,json=referenceLayerSpatial,proto3" json:"reference_layer_spatial,omitempty"`
	PreStartTime              int64                  `protobuf:"varint,3,opt,name=pre_start_time,json=preStartTime,proto3" json:"pre_start_time,omitempty"`
	ExtFirstTimestamp         uint64                 `protobuf:"varint,4,opt,name=ext_first_timestamp,json=extFirstTimestamp,proto3" json:"ext_first_timestamp,omitempty"`
	DummyStartTimestampOffset uint64                 `protobuf:"varint,5,opt,name=dummy_start_timestamp_offset,json=dummyStartTimestampOffset,proto3" json:"dummy_start_timestamp_offset,omitempty"`
	RtpMunger                 *RTPMungerState        `protobuf:"bytes,6,opt,name=rtp_munger,json=rtpMunger,proto3" json:"rtp_munger,omitempty"`
	// Types that are valid to be assigned to CodecMunger:
	//
	//	*RTPForwarderState_Vp8Munger
	CodecMunger       isRTPForwarderState_CodecMunger `protobuf_oneof:"codec_munger"`
	SenderReportState []*RTCPSenderReportState        `protobuf:"bytes,8,rep,name=sender_report_state,json=senderReportState,proto3" json:"sender_report_state,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *RTPForwarderState) Reset() {
	*x = RTPForwarderState{}
	mi := &file_livekit_models_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RTPForwarderState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTPForwarderState) ProtoMessage() {}

func (x *RTPForwarderState) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTPForwarderState.ProtoReflect.Descriptor instead.
func (*RTPForwarderState) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{36}
}

func (x *RTPForwarderState) GetStarted() bool {
	if x != nil {
		return x.Started
	}
	return false
}

func (x *RTPForwarderState) GetReferenceLayerSpatial() int32 {
	if x != nil {
		return x.ReferenceLayerSpatial
	}
	return 0
}

func (x *RTPForwarderState) GetPreStartTime() int64 {
	if x != nil {
		return x.PreStartTime
	}
	return 0
}

func (x *RTPForwarderState) GetExtFirstTimestamp() uint64 {
	if x != nil {
		return x.ExtFirstTimestamp
	}
	return 0
}

func (x *RTPForwarderState) GetDummyStartTimestampOffset() uint64 {
	if x != nil {
		return x.DummyStartTimestampOffset
	}
	return 0
}

func (x *RTPForwarderState) GetRtpMunger() *RTPMungerState {
	if x != nil {
		return x.RtpMunger
	}
	return nil
}

func (x *RTPForwarderState) GetCodecMunger() isRTPForwarderState_CodecMunger {
	if x != nil {
		return x.CodecMunger
	}
	return nil
}

func (x *RTPForwarderState) GetVp8Munger() *VP8MungerState {
	if x != nil {
		if x, ok := x.CodecMunger.(*RTPForwarderState_Vp8Munger); ok {
			return x.Vp8Munger
		}
	}
	return nil
}

func (x *RTPForwarderState) GetSenderReportState() []*RTCPSenderReportState {
	if x != nil {
		return x.SenderReportState
	}
	return nil
}

type isRTPForwarderState_CodecMunger interface {
	isRTPForwarderState_CodecMunger()
}

type RTPForwarderState_Vp8Munger struct {
	Vp8Munger *VP8MungerState `protobuf:"bytes,7,opt,name=vp8_munger,json=vp8Munger,proto3,oneof"`
}

func (*RTPForwarderState_Vp8Munger) isRTPForwarderState_CodecMunger() {}

type RTPMungerState struct {
	state                       protoimpl.MessageState `protogen:"open.v1"`
	ExtLastSequenceNumber       uint64                 `protobuf:"varint,1,opt,name=ext_last_sequence_number,json=extLastSequenceNumber,proto3" json:"ext_last_sequence_number,omitempty"`
	ExtSecondLastSequenceNumber uint64                 `protobuf:"varint,2,opt,name=ext_second_last_sequence_number,json=extSecondLastSequenceNumber,proto3" json:"ext_second_last_sequence_number,omitempty"`
	ExtLastTimestamp            uint64                 `protobuf:"varint,3,opt,name=ext_last_timestamp,json=extLastTimestamp,proto3" json:"ext_last_timestamp,omitempty"`
	ExtSecondLastTimestamp      uint64                 `protobuf:"varint,4,opt,name=ext_second_last_timestamp,json=extSecondLastTimestamp,proto3" json:"ext_second_last_timestamp,omitempty"`
	LastMarker                  bool                   `protobuf:"varint,5,opt,name=last_marker,json=lastMarker,proto3" json:"last_marker,omitempty"`
	SecondLastMarker            bool                   `protobuf:"varint,6,opt,name=second_last_marker,json=secondLastMarker,proto3" json:"second_last_marker,omitempty"`
	unknownFields               protoimpl.UnknownFields
	sizeCache                   protoimpl.SizeCache
}

func (x *RTPMungerState) Reset() {
	*x = RTPMungerState{}
	mi := &file_livekit_models_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RTPMungerState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTPMungerState) ProtoMessage() {}

func (x *RTPMungerState) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTPMungerState.ProtoReflect.Descriptor instead.
func (*RTPMungerState) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{37}
}

func (x *RTPMungerState) GetExtLastSequenceNumber() uint64 {
	if x != nil {
		return x.ExtLastSequenceNumber
	}
	return 0
}

func (x *RTPMungerState) GetExtSecondLastSequenceNumber() uint64 {
	if x != nil {
		return x.ExtSecondLastSequenceNumber
	}
	return 0
}

func (x *RTPMungerState) GetExtLastTimestamp() uint64 {
	if x != nil {
		return x.ExtLastTimestamp
	}
	return 0
}

func (x *RTPMungerState) GetExtSecondLastTimestamp() uint64 {
	if x != nil {
		return x.ExtSecondLastTimestamp
	}
	return 0
}

func (x *RTPMungerState) GetLastMarker() bool {
	if x != nil {
		return x.LastMarker
	}
	return false
}

func (x *RTPMungerState) GetSecondLastMarker() bool {
	if x != nil {
		return x.SecondLastMarker
	}
	return false
}

type VP8MungerState struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ExtLastPictureId int32                  `protobuf:"varint,1,opt,name=ext_last_picture_id,json=extLastPictureId,proto3" json:"ext_last_picture_id,omitempty"`
	PictureIdUsed    bool                   `protobuf:"varint,2,opt,name=picture_id_used,json=pictureIdUsed,proto3" json:"picture_id_used,omitempty"`
	LastTl0PicIdx    uint32                 `protobuf:"varint,3,opt,name=last_tl0_pic_idx,json=lastTl0PicIdx,proto3" json:"last_tl0_pic_idx,omitempty"`
	Tl0PicIdxUsed    bool                   `protobuf:"varint,4,opt,name=tl0_pic_idx_used,json=tl0PicIdxUsed,proto3" json:"tl0_pic_idx_used,omitempty"`
	TidUsed          bool                   `protobuf:"varint,5,opt,name=tid_used,json=tidUsed,proto3" json:"tid_used,omitempty"`
	LastKeyIdx       uint32                 `protobuf:"varint,6,opt,name=last_key_idx,json=lastKeyIdx,proto3" json:"last_key_idx,omitempty"`
	KeyIdxUsed       bool                   `protobuf:"varint,7,opt,name=key_idx_used,json=keyIdxUsed,proto3" json:"key_idx_used,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *VP8MungerState) Reset() {
	*x = VP8MungerState{}
	mi := &file_livekit_models_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VP8MungerState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VP8MungerState) ProtoMessage() {}

func (x *VP8MungerState) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VP8MungerState.ProtoReflect.Descriptor instead.
func (*VP8MungerState) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{38}
}

func (x *VP8MungerState) GetExtLastPictureId() int32 {
	if x != nil {
		return x.ExtLastPictureId
	}
	return 0
}

func (x *VP8MungerState) GetPictureIdUsed() bool {
	if x != nil {
		return x.PictureIdUsed
	}
	return false
}

func (x *VP8MungerState) GetLastTl0PicIdx() uint32 {
	if x != nil {
		return x.LastTl0PicIdx
	}
	return 0
}

func (x *VP8MungerState) GetTl0PicIdxUsed() bool {
	if x != nil {
		return x.Tl0PicIdxUsed
	}
	return false
}

func (x *VP8MungerState) GetTidUsed() bool {
	if x != nil {
		return x.TidUsed
	}
	return false
}

func (x *VP8MungerState) GetLastKeyIdx() uint32 {
	if x != nil {
		return x.LastKeyIdx
	}
	return 0
}

func (x *VP8MungerState) GetKeyIdxUsed() bool {
	if x != nil {
		return x.KeyIdxUsed
	}
	return false
}

type TimedVersion struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UnixMicro     int64                  `protobuf:"varint,1,opt,name=unix_micro,json=unixMicro,proto3" json:"unix_micro,omitempty"`
	Ticks         int32                  `protobuf:"varint,2,opt,name=ticks,proto3" json:"ticks,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TimedVersion) Reset() {
	*x = TimedVersion{}
	mi := &file_livekit_models_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TimedVersion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimedVersion) ProtoMessage() {}

func (x *TimedVersion) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimedVersion.ProtoReflect.Descriptor instead.
func (*TimedVersion) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{39}
}

func (x *TimedVersion) GetUnixMicro() int64 {
	if x != nil {
		return x.UnixMicro
	}
	return 0
}

func (x *TimedVersion) GetTicks() int32 {
	if x != nil {
		return x.Ticks
	}
	return 0
}

type DataStream struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DataStream) Reset() {
	*x = DataStream{}
	mi := &file_livekit_models_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataStream) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataStream) ProtoMessage() {}

func (x *DataStream) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataStream.ProtoReflect.Descriptor instead.
func (*DataStream) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{40}
}

type WebhookConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Url           string                 `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	SigningKey    string                 `protobuf:"bytes,2,opt,name=signing_key,json=signingKey,proto3" json:"signing_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WebhookConfig) Reset() {
	*x = WebhookConfig{}
	mi := &file_livekit_models_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WebhookConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebhookConfig) ProtoMessage() {}

func (x *WebhookConfig) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebhookConfig.ProtoReflect.Descriptor instead.
func (*WebhookConfig) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{41}
}

func (x *WebhookConfig) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *WebhookConfig) GetSigningKey() string {
	if x != nil {
		return x.SigningKey
	}
	return ""
}

// header properties specific to text streams
type DataStream_TextHeader struct {
	state             protoimpl.MessageState   `protogen:"open.v1"`
	OperationType     DataStream_OperationType `protobuf:"varint,1,opt,name=operation_type,json=operationType,proto3,enum=livekit.DataStream_OperationType" json:"operation_type,omitempty"`
	Version           int32                    `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`                                               // Optional: Version for updates/edits
	ReplyToStreamId   string                   `protobuf:"bytes,3,opt,name=reply_to_stream_id,json=replyToStreamId,proto3" json:"reply_to_stream_id,omitempty"`     // Optional: Reply to specific message
	AttachedStreamIds []string                 `protobuf:"bytes,4,rep,name=attached_stream_ids,json=attachedStreamIds,proto3" json:"attached_stream_ids,omitempty"` // file attachments for text streams
	Generated         bool                     `protobuf:"varint,5,opt,name=generated,proto3" json:"generated,omitempty"`                                           // true if the text has been generated by an agent from a participant's audio transcription
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *DataStream_TextHeader) Reset() {
	*x = DataStream_TextHeader{}
	mi := &file_livekit_models_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataStream_TextHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataStream_TextHeader) ProtoMessage() {}

func (x *DataStream_TextHeader) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataStream_TextHeader.ProtoReflect.Descriptor instead.
func (*DataStream_TextHeader) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{40, 0}
}

func (x *DataStream_TextHeader) GetOperationType() DataStream_OperationType {
	if x != nil {
		return x.OperationType
	}
	return DataStream_CREATE
}

func (x *DataStream_TextHeader) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *DataStream_TextHeader) GetReplyToStreamId() string {
	if x != nil {
		return x.ReplyToStreamId
	}
	return ""
}

func (x *DataStream_TextHeader) GetAttachedStreamIds() []string {
	if x != nil {
		return x.AttachedStreamIds
	}
	return nil
}

func (x *DataStream_TextHeader) GetGenerated() bool {
	if x != nil {
		return x.Generated
	}
	return false
}

// header properties specific to byte or file streams
type DataStream_ByteHeader struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DataStream_ByteHeader) Reset() {
	*x = DataStream_ByteHeader{}
	mi := &file_livekit_models_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataStream_ByteHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataStream_ByteHeader) ProtoMessage() {}

func (x *DataStream_ByteHeader) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataStream_ByteHeader.ProtoReflect.Descriptor instead.
func (*DataStream_ByteHeader) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{40, 1}
}

func (x *DataStream_ByteHeader) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// main DataStream.Header that contains a oneof for specific headers
type DataStream_Header struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	StreamId       string                 `protobuf:"bytes,1,opt,name=stream_id,json=streamId,proto3" json:"stream_id,omitempty"` // unique identifier for this data stream
	Timestamp      int64                  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`              // using int64 for Unix timestamp
	Topic          string                 `protobuf:"bytes,3,opt,name=topic,proto3" json:"topic,omitempty"`
	MimeType       string                 `protobuf:"bytes,4,opt,name=mime_type,json=mimeType,proto3" json:"mime_type,omitempty"`
	TotalLength    *uint64                `protobuf:"varint,5,opt,name=total_length,json=totalLength,proto3,oneof" json:"total_length,omitempty"`                                               // only populated for finite streams, if it's a stream of unknown size this stays empty
	EncryptionType Encryption_Type        `protobuf:"varint,7,opt,name=encryption_type,json=encryptionType,proto3,enum=livekit.Encryption_Type" json:"encryption_type,omitempty"`               // defaults to NONE
	Attributes     map[string]string      `protobuf:"bytes,8,rep,name=attributes,proto3" json:"attributes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // user defined attributes map that can carry additional info
	// oneof to choose between specific header types
	//
	// Types that are valid to be assigned to ContentHeader:
	//
	//	*DataStream_Header_TextHeader
	//	*DataStream_Header_ByteHeader
	ContentHeader isDataStream_Header_ContentHeader `protobuf_oneof:"content_header"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DataStream_Header) Reset() {
	*x = DataStream_Header{}
	mi := &file_livekit_models_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataStream_Header) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataStream_Header) ProtoMessage() {}

func (x *DataStream_Header) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataStream_Header.ProtoReflect.Descriptor instead.
func (*DataStream_Header) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{40, 2}
}

func (x *DataStream_Header) GetStreamId() string {
	if x != nil {
		return x.StreamId
	}
	return ""
}

func (x *DataStream_Header) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *DataStream_Header) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

func (x *DataStream_Header) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

func (x *DataStream_Header) GetTotalLength() uint64 {
	if x != nil && x.TotalLength != nil {
		return *x.TotalLength
	}
	return 0
}

func (x *DataStream_Header) GetEncryptionType() Encryption_Type {
	if x != nil {
		return x.EncryptionType
	}
	return Encryption_NONE
}

func (x *DataStream_Header) GetAttributes() map[string]string {
	if x != nil {
		return x.Attributes
	}
	return nil
}

func (x *DataStream_Header) GetContentHeader() isDataStream_Header_ContentHeader {
	if x != nil {
		return x.ContentHeader
	}
	return nil
}

func (x *DataStream_Header) GetTextHeader() *DataStream_TextHeader {
	if x != nil {
		if x, ok := x.ContentHeader.(*DataStream_Header_TextHeader); ok {
			return x.TextHeader
		}
	}
	return nil
}

func (x *DataStream_Header) GetByteHeader() *DataStream_ByteHeader {
	if x != nil {
		if x, ok := x.ContentHeader.(*DataStream_Header_ByteHeader); ok {
			return x.ByteHeader
		}
	}
	return nil
}

type isDataStream_Header_ContentHeader interface {
	isDataStream_Header_ContentHeader()
}

type DataStream_Header_TextHeader struct {
	TextHeader *DataStream_TextHeader `protobuf:"bytes,9,opt,name=text_header,json=textHeader,proto3,oneof"`
}

type DataStream_Header_ByteHeader struct {
	ByteHeader *DataStream_ByteHeader `protobuf:"bytes,10,opt,name=byte_header,json=byteHeader,proto3,oneof"`
}

func (*DataStream_Header_TextHeader) isDataStream_Header_ContentHeader() {}

func (*DataStream_Header_ByteHeader) isDataStream_Header_ContentHeader() {}

type DataStream_Chunk struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamId      string                 `protobuf:"bytes,1,opt,name=stream_id,json=streamId,proto3" json:"stream_id,omitempty"` // unique identifier for this data stream to map it to the correct header
	ChunkIndex    uint64                 `protobuf:"varint,2,opt,name=chunk_index,json=chunkIndex,proto3" json:"chunk_index,omitempty"`
	Content       []byte                 `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`  // content as binary (bytes)
	Version       int32                  `protobuf:"varint,4,opt,name=version,proto3" json:"version,omitempty"` // a version indicating that this chunk_index has been retroactively modified and the original one needs to be replaced
	Iv            []byte                 `protobuf:"bytes,5,opt,name=iv,proto3,oneof" json:"iv,omitempty"`      // optional, initialization vector for AES-GCM encryption
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DataStream_Chunk) Reset() {
	*x = DataStream_Chunk{}
	mi := &file_livekit_models_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataStream_Chunk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataStream_Chunk) ProtoMessage() {}

func (x *DataStream_Chunk) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataStream_Chunk.ProtoReflect.Descriptor instead.
func (*DataStream_Chunk) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{40, 3}
}

func (x *DataStream_Chunk) GetStreamId() string {
	if x != nil {
		return x.StreamId
	}
	return ""
}

func (x *DataStream_Chunk) GetChunkIndex() uint64 {
	if x != nil {
		return x.ChunkIndex
	}
	return 0
}

func (x *DataStream_Chunk) GetContent() []byte {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *DataStream_Chunk) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *DataStream_Chunk) GetIv() []byte {
	if x != nil {
		return x.Iv
	}
	return nil
}

type DataStream_Trailer struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamId      string                 `protobuf:"bytes,1,opt,name=stream_id,json=streamId,proto3" json:"stream_id,omitempty"`                                                               // unique identifier for this data stream
	Reason        string                 `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`                                                                                   // reason why the stream was closed (could contain "error" / "interrupted" / empty for expected end)
	Attributes    map[string]string      `protobuf:"bytes,3,rep,name=attributes,proto3" json:"attributes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // finalizing updates for the stream, can also include additional insights for errors or endTime for transcription
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DataStream_Trailer) Reset() {
	*x = DataStream_Trailer{}
	mi := &file_livekit_models_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataStream_Trailer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataStream_Trailer) ProtoMessage() {}

func (x *DataStream_Trailer) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_models_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataStream_Trailer.ProtoReflect.Descriptor instead.
func (*DataStream_Trailer) Descriptor() ([]byte, []int) {
	return file_livekit_models_proto_rawDescGZIP(), []int{40, 4}
}

func (x *DataStream_Trailer) GetStreamId() string {
	if x != nil {
		return x.StreamId
	}
	return ""
}

func (x *DataStream_Trailer) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *DataStream_Trailer) GetAttributes() map[string]string {
	if x != nil {
		return x.Attributes
	}
	return nil
}

var File_livekit_models_proto protoreflect.FileDescriptor

const file_livekit_models_proto_rawDesc = "" +
	"\n" +
	"\x14livekit_models.proto\x12\alivekit\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x15livekit_metrics.proto\"=\n" +
	"\n" +
	"Pagination\x12\x19\n" +
	"\bafter_id\x18\x01 \x01(\tR\aafterId\x12\x14\n" +
	"\x05limit\x18\x02 \x01(\x05R\x05limit\"\x1e\n" +
	"\n" +
	"ListUpdate\x12\x10\n" +
	"\x03set\x18\x01 \x03(\tR\x03set\"\x9e\x04\n" +
	"\x04Room\x12\x10\n" +
	"\x03sid\x18\x01 \x01(\tR\x03sid\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12#\n" +
	"\rempty_timeout\x18\x03 \x01(\rR\femptyTimeout\x12+\n" +
	"\x11departure_timeout\x18\x0e \x01(\rR\x10departureTimeout\x12)\n" +
	"\x10max_participants\x18\x04 \x01(\rR\x0fmaxParticipants\x12#\n" +
	"\rcreation_time\x18\x05 \x01(\x03R\fcreationTime\x12(\n" +
	"\x10creation_time_ms\x18\x0f \x01(\x03R\x0ecreationTimeMs\x12#\n" +
	"\rturn_password\x18\x06 \x01(\tR\fturnPassword\x125\n" +
	"\x0eenabled_codecs\x18\a \x03(\v2\x0e.livekit.CodecR\renabledCodecs\x12\x1a\n" +
	"\bmetadata\x18\b \x01(\tR\bmetadata\x12)\n" +
	"\x10num_participants\x18\t \x01(\rR\x0fnumParticipants\x12%\n" +
	"\x0enum_publishers\x18\v \x01(\rR\rnumPublishers\x12)\n" +
	"\x10active_recording\x18\n" +
	" \x01(\bR\x0factiveRecording\x12/\n" +
	"\aversion\x18\r \x01(\v2\x15.livekit.TimedVersionR\aversion\"8\n" +
	"\x05Codec\x12\x12\n" +
	"\x04mime\x18\x01 \x01(\tR\x04mime\x12\x1b\n" +
	"\tfmtp_line\x18\x02 \x01(\tR\bfmtpLine\"L\n" +
	"\fPlayoutDelay\x12\x18\n" +
	"\aenabled\x18\x01 \x01(\bR\aenabled\x12\x10\n" +
	"\x03min\x18\x02 \x01(\rR\x03min\x12\x10\n" +
	"\x03max\x18\x03 \x01(\rR\x03max\"\x83\x03\n" +
	"\x15ParticipantPermission\x12#\n" +
	"\rcan_subscribe\x18\x01 \x01(\bR\fcanSubscribe\x12\x1f\n" +
	"\vcan_publish\x18\x02 \x01(\bR\n" +
	"canPublish\x12(\n" +
	"\x10can_publish_data\x18\x03 \x01(\bR\x0ecanPublishData\x12D\n" +
	"\x13can_publish_sources\x18\t \x03(\x0e2\x14.livekit.TrackSourceR\x11canPublishSources\x12\x16\n" +
	"\x06hidden\x18\a \x01(\bR\x06hidden\x12\x1e\n" +
	"\brecorder\x18\b \x01(\bB\x02\x18\x01R\brecorder\x12.\n" +
	"\x13can_update_metadata\x18\n" +
	" \x01(\bR\x11canUpdateMetadata\x12\x18\n" +
	"\x05agent\x18\v \x01(\bB\x02\x18\x01R\x05agent\x122\n" +
	"\x15can_subscribe_metrics\x18\f \x01(\bR\x13canSubscribeMetrics\"\xa2\a\n" +
	"\x0fParticipantInfo\x12\x10\n" +
	"\x03sid\x18\x01 \x01(\tR\x03sid\x12\x1a\n" +
	"\bidentity\x18\x02 \x01(\tR\bidentity\x124\n" +
	"\x05state\x18\x03 \x01(\x0e2\x1e.livekit.ParticipantInfo.StateR\x05state\x12*\n" +
	"\x06tracks\x18\x04 \x03(\v2\x12.livekit.TrackInfoR\x06tracks\x12\x1a\n" +
	"\bmetadata\x18\x05 \x01(\tR\bmetadata\x12\x1b\n" +
	"\tjoined_at\x18\x06 \x01(\x03R\bjoinedAt\x12 \n" +
	"\fjoined_at_ms\x18\x11 \x01(\x03R\n" +
	"joinedAtMs\x12\x12\n" +
	"\x04name\x18\t \x01(\tR\x04name\x12\x18\n" +
	"\aversion\x18\n" +
	" \x01(\rR\aversion\x12>\n" +
	"\n" +
	"permission\x18\v \x01(\v2\x1e.livekit.ParticipantPermissionR\n" +
	"permission\x12\x16\n" +
	"\x06region\x18\f \x01(\tR\x06region\x12!\n" +
	"\fis_publisher\x18\r \x01(\bR\visPublisher\x121\n" +
	"\x04kind\x18\x0e \x01(\x0e2\x1d.livekit.ParticipantInfo.KindR\x04kind\x12H\n" +
	"\n" +
	"attributes\x18\x0f \x03(\v2(.livekit.ParticipantInfo.AttributesEntryR\n" +
	"attributes\x12F\n" +
	"\x11disconnect_reason\x18\x10 \x01(\x0e2\x19.livekit.DisconnectReasonR\x10disconnectReason\x12F\n" +
	"\fkind_details\x18\x12 \x03(\x0e2#.livekit.ParticipantInfo.KindDetailR\vkindDetails\x1a=\n" +
	"\x0fAttributesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\">\n" +
	"\x05State\x12\v\n" +
	"\aJOINING\x10\x00\x12\n" +
	"\n" +
	"\x06JOINED\x10\x01\x12\n" +
	"\n" +
	"\x06ACTIVE\x10\x02\x12\x10\n" +
	"\fDISCONNECTED\x10\x03\"A\n" +
	"\x04Kind\x12\f\n" +
	"\bSTANDARD\x10\x00\x12\v\n" +
	"\aINGRESS\x10\x01\x12\n" +
	"\n" +
	"\x06EGRESS\x10\x02\x12\a\n" +
	"\x03SIP\x10\x03\x12\t\n" +
	"\x05AGENT\x10\x04\",\n" +
	"\n" +
	"KindDetail\x12\x0f\n" +
	"\vCLOUD_AGENT\x10\x00\x12\r\n" +
	"\tFORWARDED\x10\x01\"\xb5\x01\n" +
	"\x15RemoteParticipantInfo\x12B\n" +
	"\x0fparticipantInfo\x18\x01 \x01(\v2\x18.livekit.ParticipantInfoR\x0fparticipantInfo\x12\x17\n" +
	"\anode_id\x18\x02 \x01(\tR\x06nodeId\x12\x17\n" +
	"\anode_ip\x18\x03 \x01(\tR\x06nodeIp\x12&\n" +
	"\x0fnode_relay_port\x18\x04 \x01(\x05R\rnodeRelayPort\"3\n" +
	"\n" +
	"Encryption\"%\n" +
	"\x04Type\x12\b\n" +
	"\x04NONE\x10\x00\x12\a\n" +
	"\x03GCM\x10\x01\x12\n" +
	"\n" +
	"\x06CUSTOM\x10\x02\"\x82\x01\n" +
	"\x12SimulcastCodecInfo\x12\x1b\n" +
	"\tmime_type\x18\x01 \x01(\tR\bmimeType\x12\x10\n" +
	"\x03mid\x18\x02 \x01(\tR\x03mid\x12\x10\n" +
	"\x03cid\x18\x03 \x01(\tR\x03cid\x12+\n" +
	"\x06layers\x18\x04 \x03(\v2\x13.livekit.VideoLayerR\x06layers\"\xe6\x05\n" +
	"\tTrackInfo\x12\x10\n" +
	"\x03sid\x18\x01 \x01(\tR\x03sid\x12&\n" +
	"\x04type\x18\x02 \x01(\x0e2\x12.livekit.TrackTypeR\x04type\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x14\n" +
	"\x05muted\x18\x04 \x01(\bR\x05muted\x12\x14\n" +
	"\x05width\x18\x05 \x01(\rR\x05width\x12\x16\n" +
	"\x06height\x18\x06 \x01(\rR\x06height\x12\x1c\n" +
	"\tsimulcast\x18\a \x01(\bR\tsimulcast\x12\x1f\n" +
	"\vdisable_dtx\x18\b \x01(\bR\n" +
	"disableDtx\x12,\n" +
	"\x06source\x18\t \x01(\x0e2\x14.livekit.TrackSourceR\x06source\x12+\n" +
	"\x06layers\x18\n" +
	" \x03(\v2\x13.livekit.VideoLayerR\x06layers\x12\x1b\n" +
	"\tmime_type\x18\v \x01(\tR\bmimeType\x12\x10\n" +
	"\x03mid\x18\f \x01(\tR\x03mid\x123\n" +
	"\x06codecs\x18\r \x03(\v2\x1b.livekit.SimulcastCodecInfoR\x06codecs\x12\x16\n" +
	"\x06stereo\x18\x0e \x01(\bR\x06stereo\x12\x1f\n" +
	"\vdisable_red\x18\x0f \x01(\bR\n" +
	"disableRed\x128\n" +
	"\n" +
	"encryption\x18\x10 \x01(\x0e2\x18.livekit.Encryption.TypeR\n" +
	"encryption\x12\x16\n" +
	"\x06stream\x18\x11 \x01(\tR\x06stream\x12/\n" +
	"\aversion\x18\x12 \x01(\v2\x15.livekit.TimedVersionR\aversion\x12A\n" +
	"\x0eaudio_features\x18\x13 \x03(\x0e2\x1a.livekit.AudioTrackFeatureR\raudioFeatures\x12J\n" +
	"\x13backup_codec_policy\x18\x14 \x01(\x0e2\x1a.livekit.BackupCodecPolicyR\x11backupCodecPolicy\"\x8f\x01\n" +
	"\x12RTPCodecParameters\x12;\n" +
	"\n" +
	"capability\x18\x01 \x01(\v2\x1b.livekit.RTPCodecCapabilityR\n" +
	"capability\x12!\n" +
	"\fpayload_type\x18\x02 \x01(\rR\vpayloadType\x12\x19\n" +
	"\bstats_id\x18\x03 \x01(\tR\astatsId\"\xcc\x01\n" +
	"\x12RTPCodecCapability\x12\x1b\n" +
	"\tmime_type\x18\x01 \x01(\tR\bmimeType\x12\x1d\n" +
	"\n" +
	"clock_rate\x18\x02 \x01(\rR\tclockRate\x12\x1a\n" +
	"\bchannels\x18\x03 \x01(\rR\bchannels\x12\"\n" +
	"\rsdp_fmtp_line\x18\x04 \x01(\tR\vsdpFmtpLine\x12:\n" +
	"\rrtcp_feedback\x18\x05 \x03(\v2\x15.livekit.RTCPFeedbackR\frtcpFeedback\"@\n" +
	"\fRTCPFeedback\x12\x12\n" +
	"\x04type\x18\x01 \x01(\tR\x04type\x12\x1c\n" +
	"\tparameter\x18\x02 \x01(\tR\tparameter\"\xd0\x01\n" +
	"\n" +
	"VideoLayer\x12/\n" +
	"\aquality\x18\x01 \x01(\x0e2\x15.livekit.VideoQualityR\aquality\x12\x14\n" +
	"\x05width\x18\x02 \x01(\rR\x05width\x12\x16\n" +
	"\x06height\x18\x03 \x01(\rR\x06height\x12\x18\n" +
	"\abitrate\x18\x04 \x01(\rR\abitrate\x12\x12\n" +
	"\x04ssrc\x18\x05 \x01(\rR\x04ssrc\x12#\n" +
	"\rspatial_layer\x18\x06 \x01(\x05R\fspatialLayer\x12\x10\n" +
	"\x03rid\x18\a \x01(\tR\x03rid\"\xc5\a\n" +
	"\n" +
	"DataPacket\x120\n" +
	"\x04kind\x18\x01 \x01(\x0e2\x18.livekit.DataPacket.KindB\x02\x18\x01R\x04kind\x121\n" +
	"\x14participant_identity\x18\x04 \x01(\tR\x13participantIdentity\x125\n" +
	"\x16destination_identities\x18\x05 \x03(\tR\x15destinationIdentities\x12)\n" +
	"\x04user\x18\x02 \x01(\v2\x13.livekit.UserPacketH\x00R\x04user\x12<\n" +
	"\aspeaker\x18\x03 \x01(\v2\x1c.livekit.ActiveSpeakerUpdateB\x02\x18\x01H\x00R\aspeaker\x12-\n" +
	"\bsip_dtmf\x18\x06 \x01(\v2\x10.livekit.SipDTMFH\x00R\asipDtmf\x12>\n" +
	"\rtranscription\x18\a \x01(\v2\x16.livekit.TranscriptionH\x00R\rtranscription\x121\n" +
	"\ametrics\x18\b \x01(\v2\x15.livekit.MetricsBatchH\x00R\ametrics\x129\n" +
	"\fchat_message\x18\t \x01(\v2\x14.livekit.ChatMessageH\x00R\vchatMessage\x126\n" +
	"\vrpc_request\x18\n" +
	" \x01(\v2\x13.livekit.RpcRequestH\x00R\n" +
	"rpcRequest\x12*\n" +
	"\arpc_ack\x18\v \x01(\v2\x0f.livekit.RpcAckH\x00R\x06rpcAck\x129\n" +
	"\frpc_response\x18\f \x01(\v2\x14.livekit.RpcResponseH\x00R\vrpcResponse\x12A\n" +
	"\rstream_header\x18\r \x01(\v2\x1a.livekit.DataStream.HeaderH\x00R\fstreamHeader\x12>\n" +
	"\fstream_chunk\x18\x0e \x01(\v2\x19.livekit.DataStream.ChunkH\x00R\vstreamChunk\x12D\n" +
	"\x0estream_trailer\x18\x0f \x01(\v2\x1b.livekit.DataStream.TrailerH\x00R\rstreamTrailer\x12\x1a\n" +
	"\bsequence\x18\x10 \x01(\rR\bsequence\x12'\n" +
	"\x0fparticipant_sid\x18\x11 \x01(\tR\x0eparticipantSid\"\x1f\n" +
	"\x04Kind\x12\f\n" +
	"\bRELIABLE\x10\x00\x12\t\n" +
	"\x05LOSSY\x10\x01B\a\n" +
	"\x05value\"G\n" +
	"\x13ActiveSpeakerUpdate\x120\n" +
	"\bspeakers\x18\x01 \x03(\v2\x14.livekit.SpeakerInfoR\bspeakers\"M\n" +
	"\vSpeakerInfo\x12\x10\n" +
	"\x03sid\x18\x01 \x01(\tR\x03sid\x12\x14\n" +
	"\x05level\x18\x02 \x01(\x02R\x05level\x12\x16\n" +
	"\x06active\x18\x03 \x01(\bR\x06active\"\xab\x03\n" +
	"\n" +
	"UserPacket\x12+\n" +
	"\x0fparticipant_sid\x18\x01 \x01(\tB\x02\x18\x01R\x0eparticipantSid\x125\n" +
	"\x14participant_identity\x18\x05 \x01(\tB\x02\x18\x01R\x13participantIdentity\x12\x18\n" +
	"\apayload\x18\x02 \x01(\fR\apayload\x12-\n" +
	"\x10destination_sids\x18\x03 \x03(\tB\x02\x18\x01R\x0fdestinationSids\x129\n" +
	"\x16destination_identities\x18\x06 \x03(\tB\x02\x18\x01R\x15destinationIdentities\x12\x19\n" +
	"\x05topic\x18\x04 \x01(\tH\x00R\x05topic\x88\x01\x01\x12\x13\n" +
	"\x02id\x18\b \x01(\tH\x01R\x02id\x88\x01\x01\x12\"\n" +
	"\n" +
	"start_time\x18\t \x01(\x04H\x02R\tstartTime\x88\x01\x01\x12\x1e\n" +
	"\bend_time\x18\n" +
	" \x01(\x04H\x03R\aendTime\x88\x01\x01\x12\x14\n" +
	"\x05nonce\x18\v \x01(\fR\x05nonceB\b\n" +
	"\x06_topicB\x05\n" +
	"\x03_idB\r\n" +
	"\v_start_timeB\v\n" +
	"\t_end_time\"3\n" +
	"\aSipDTMF\x12\x12\n" +
	"\x04code\x18\x03 \x01(\rR\x04code\x12\x14\n" +
	"\x05digit\x18\x04 \x01(\tR\x05digit\"\xaf\x01\n" +
	"\rTranscription\x12H\n" +
	" transcribed_participant_identity\x18\x02 \x01(\tR\x1etranscribedParticipantIdentity\x12\x19\n" +
	"\btrack_id\x18\x03 \x01(\tR\atrackId\x129\n" +
	"\bsegments\x18\x04 \x03(\v2\x1d.livekit.TranscriptionSegmentR\bsegments\"\xa6\x01\n" +
	"\x14TranscriptionSegment\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04text\x18\x02 \x01(\tR\x04text\x12\x1d\n" +
	"\n" +
	"start_time\x18\x03 \x01(\x04R\tstartTime\x12\x19\n" +
	"\bend_time\x18\x04 \x01(\x04R\aendTime\x12\x14\n" +
	"\x05final\x18\x05 \x01(\bR\x05final\x12\x1a\n" +
	"\blanguage\x18\x06 \x01(\tR\blanguage\"\xcc\x01\n" +
	"\vChatMessage\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x03R\ttimestamp\x12*\n" +
	"\x0eedit_timestamp\x18\x03 \x01(\x03H\x00R\reditTimestamp\x88\x01\x01\x12\x18\n" +
	"\amessage\x18\x04 \x01(\tR\amessage\x12\x18\n" +
	"\adeleted\x18\x05 \x01(\bR\adeleted\x12\x1c\n" +
	"\tgenerated\x18\x06 \x01(\bR\tgeneratedB\x11\n" +
	"\x0f_edit_timestamp\"\x98\x01\n" +
	"\n" +
	"RpcRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x16\n" +
	"\x06method\x18\x02 \x01(\tR\x06method\x12\x18\n" +
	"\apayload\x18\x03 \x01(\tR\apayload\x12.\n" +
	"\x13response_timeout_ms\x18\x04 \x01(\rR\x11responseTimeoutMs\x12\x18\n" +
	"\aversion\x18\x05 \x01(\rR\aversion\"'\n" +
	"\x06RpcAck\x12\x1d\n" +
	"\n" +
	"request_id\x18\x01 \x01(\tR\trequestId\"|\n" +
	"\vRpcResponse\x12\x1d\n" +
	"\n" +
	"request_id\x18\x01 \x01(\tR\trequestId\x12\x1a\n" +
	"\apayload\x18\x02 \x01(\tH\x00R\apayload\x12)\n" +
	"\x05error\x18\x03 \x01(\v2\x11.livekit.RpcErrorH\x00R\x05errorB\a\n" +
	"\x05value\"L\n" +
	"\bRpcError\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x12\n" +
	"\x04data\x18\x03 \x01(\tR\x04data\"[\n" +
	"\x11ParticipantTracks\x12'\n" +
	"\x0fparticipant_sid\x18\x01 \x01(\tR\x0eparticipantSid\x12\x1d\n" +
	"\n" +
	"track_sids\x18\x02 \x03(\tR\ttrackSids\"\x94\x02\n" +
	"\n" +
	"ServerInfo\x125\n" +
	"\aedition\x18\x01 \x01(\x0e2\x1b.livekit.ServerInfo.EditionR\aedition\x12\x18\n" +
	"\aversion\x18\x02 \x01(\tR\aversion\x12\x1a\n" +
	"\bprotocol\x18\x03 \x01(\x05R\bprotocol\x12\x16\n" +
	"\x06region\x18\x04 \x01(\tR\x06region\x12\x17\n" +
	"\anode_id\x18\x05 \x01(\tR\x06nodeId\x12\x1d\n" +
	"\n" +
	"debug_info\x18\x06 \x01(\tR\tdebugInfo\x12%\n" +
	"\x0eagent_protocol\x18\a \x01(\x05R\ragentProtocol\"\"\n" +
	"\aEdition\x12\f\n" +
	"\bStandard\x10\x00\x12\t\n" +
	"\x05Cloud\x10\x01\"\x8b\x04\n" +
	"\n" +
	"ClientInfo\x12)\n" +
	"\x03sdk\x18\x01 \x01(\x0e2\x17.livekit.ClientInfo.SDKR\x03sdk\x12\x18\n" +
	"\aversion\x18\x02 \x01(\tR\aversion\x12\x1a\n" +
	"\bprotocol\x18\x03 \x01(\x05R\bprotocol\x12\x0e\n" +
	"\x02os\x18\x04 \x01(\tR\x02os\x12\x1d\n" +
	"\n" +
	"os_version\x18\x05 \x01(\tR\tosVersion\x12!\n" +
	"\fdevice_model\x18\x06 \x01(\tR\vdeviceModel\x12\x18\n" +
	"\abrowser\x18\a \x01(\tR\abrowser\x12'\n" +
	"\x0fbrowser_version\x18\b \x01(\tR\x0ebrowserVersion\x12\x18\n" +
	"\aaddress\x18\t \x01(\tR\aaddress\x12\x18\n" +
	"\anetwork\x18\n" +
	" \x01(\tR\anetwork\x12\x1d\n" +
	"\n" +
	"other_sdks\x18\v \x01(\tR\totherSdks\"\xb3\x01\n" +
	"\x03SDK\x12\v\n" +
	"\aUNKNOWN\x10\x00\x12\x06\n" +
	"\x02JS\x10\x01\x12\t\n" +
	"\x05SWIFT\x10\x02\x12\v\n" +
	"\aANDROID\x10\x03\x12\v\n" +
	"\aFLUTTER\x10\x04\x12\x06\n" +
	"\x02GO\x10\x05\x12\t\n" +
	"\x05UNITY\x10\x06\x12\x10\n" +
	"\fREACT_NATIVE\x10\a\x12\b\n" +
	"\x04RUST\x10\b\x12\n" +
	"\n" +
	"\x06PYTHON\x10\t\x12\a\n" +
	"\x03CPP\x10\n" +
	"\x12\r\n" +
	"\tUNITY_WEB\x10\v\x12\b\n" +
	"\x04NODE\x10\f\x12\n" +
	"\n" +
	"\x06UNREAL\x10\r\x12\t\n" +
	"\x05ESP32\x10\x0e\"\xc9\x02\n" +
	"\x13ClientConfiguration\x121\n" +
	"\x05video\x18\x01 \x01(\v2\x1b.livekit.VideoConfigurationR\x05video\x123\n" +
	"\x06screen\x18\x02 \x01(\v2\x1b.livekit.VideoConfigurationR\x06screen\x12I\n" +
	"\x11resume_connection\x18\x03 \x01(\x0e2\x1c.livekit.ClientConfigSettingR\x10resumeConnection\x12@\n" +
	"\x0fdisabled_codecs\x18\x04 \x01(\v2\x17.livekit.DisabledCodecsR\x0edisabledCodecs\x12=\n" +
	"\vforce_relay\x18\x05 \x01(\x0e2\x1c.livekit.ClientConfigSettingR\n" +
	"forceRelay\"]\n" +
	"\x12VideoConfiguration\x12G\n" +
	"\x10hardware_encoder\x18\x01 \x01(\x0e2\x1c.livekit.ClientConfigSettingR\x0fhardwareEncoder\"b\n" +
	"\x0eDisabledCodecs\x12&\n" +
	"\x06codecs\x18\x01 \x03(\v2\x0e.livekit.CodecR\x06codecs\x12(\n" +
	"\apublish\x18\x02 \x03(\v2\x0e.livekit.CodecR\apublish\"\xed\x02\n" +
	"\bRTPDrift\x129\n" +
	"\n" +
	"start_time\x18\x01 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12\x1a\n" +
	"\bduration\x18\x03 \x01(\x01R\bduration\x12'\n" +
	"\x0fstart_timestamp\x18\x04 \x01(\x04R\x0estartTimestamp\x12#\n" +
	"\rend_timestamp\x18\x05 \x01(\x04R\fendTimestamp\x12&\n" +
	"\x0frtp_clock_ticks\x18\x06 \x01(\x04R\rrtpClockTicks\x12#\n" +
	"\rdrift_samples\x18\a \x01(\x03R\fdriftSamples\x12\x19\n" +
	"\bdrift_ms\x18\b \x01(\x01R\adriftMs\x12\x1d\n" +
	"\n" +
	"clock_rate\x18\t \x01(\x01R\tclockRate\"\xc4\x0f\n" +
	"\bRTPStats\x129\n" +
	"\n" +
	"start_time\x18\x01 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12\x1a\n" +
	"\bduration\x18\x03 \x01(\x01R\bduration\x12\x18\n" +
	"\apackets\x18\x04 \x01(\rR\apackets\x12\x1f\n" +
	"\vpacket_rate\x18\x05 \x01(\x01R\n" +
	"packetRate\x12\x14\n" +
	"\x05bytes\x18\x06 \x01(\x04R\x05bytes\x12!\n" +
	"\fheader_bytes\x18' \x01(\x04R\vheaderBytes\x12\x18\n" +
	"\abitrate\x18\a \x01(\x01R\abitrate\x12!\n" +
	"\fpackets_lost\x18\b \x01(\rR\vpacketsLost\x12(\n" +
	"\x10packet_loss_rate\x18\t \x01(\x01R\x0epacketLossRate\x124\n" +
	"\x16packet_loss_percentage\x18\n" +
	" \x01(\x02R\x14packetLossPercentage\x12+\n" +
	"\x11packets_duplicate\x18\v \x01(\rR\x10packetsDuplicate\x122\n" +
	"\x15packet_duplicate_rate\x18\f \x01(\x01R\x13packetDuplicateRate\x12'\n" +
	"\x0fbytes_duplicate\x18\r \x01(\x04R\x0ebytesDuplicate\x124\n" +
	"\x16header_bytes_duplicate\x18( \x01(\x04R\x14headerBytesDuplicate\x12+\n" +
	"\x11bitrate_duplicate\x18\x0e \x01(\x01R\x10bitrateDuplicate\x12'\n" +
	"\x0fpackets_padding\x18\x0f \x01(\rR\x0epacketsPadding\x12.\n" +
	"\x13packet_padding_rate\x18\x10 \x01(\x01R\x11packetPaddingRate\x12#\n" +
	"\rbytes_padding\x18\x11 \x01(\x04R\fbytesPadding\x120\n" +
	"\x14header_bytes_padding\x18) \x01(\x04R\x12headerBytesPadding\x12'\n" +
	"\x0fbitrate_padding\x18\x12 \x01(\x01R\x0ebitratePadding\x12/\n" +
	"\x14packets_out_of_order\x18\x13 \x01(\rR\x11packetsOutOfOrder\x12\x16\n" +
	"\x06frames\x18\x14 \x01(\rR\x06frames\x12\x1d\n" +
	"\n" +
	"frame_rate\x18\x15 \x01(\x01R\tframeRate\x12%\n" +
	"\x0ejitter_current\x18\x16 \x01(\x01R\rjitterCurrent\x12\x1d\n" +
	"\n" +
	"jitter_max\x18\x17 \x01(\x01R\tjitterMax\x12H\n" +
	"\rgap_histogram\x18\x18 \x03(\v2#.livekit.RTPStats.GapHistogramEntryR\fgapHistogram\x12\x14\n" +
	"\x05nacks\x18\x19 \x01(\rR\x05nacks\x12\x1b\n" +
	"\tnack_acks\x18% \x01(\rR\bnackAcks\x12\x1f\n" +
	"\vnack_misses\x18\x1a \x01(\rR\n" +
	"nackMisses\x12#\n" +
	"\rnack_repeated\x18& \x01(\rR\fnackRepeated\x12\x12\n" +
	"\x04plis\x18\x1b \x01(\rR\x04plis\x125\n" +
	"\blast_pli\x18\x1c \x01(\v2\x1a.google.protobuf.TimestampR\alastPli\x12\x12\n" +
	"\x04firs\x18\x1d \x01(\rR\x04firs\x125\n" +
	"\blast_fir\x18\x1e \x01(\v2\x1a.google.protobuf.TimestampR\alastFir\x12\x1f\n" +
	"\vrtt_current\x18\x1f \x01(\rR\n" +
	"rttCurrent\x12\x17\n" +
	"\artt_max\x18  \x01(\rR\x06rttMax\x12\x1d\n" +
	"\n" +
	"key_frames\x18! \x01(\rR\tkeyFrames\x12@\n" +
	"\x0elast_key_frame\x18\" \x01(\v2\x1a.google.protobuf.TimestampR\flastKeyFrame\x12&\n" +
	"\x0flayer_lock_plis\x18# \x01(\rR\rlayerLockPlis\x12I\n" +
	"\x13last_layer_lock_pli\x18$ \x01(\v2\x1a.google.protobuf.TimestampR\x10lastLayerLockPli\x124\n" +
	"\fpacket_drift\x18, \x01(\v2\x11.livekit.RTPDriftR\vpacketDrift\x12;\n" +
	"\x10ntp_report_drift\x18- \x01(\v2\x11.livekit.RTPDriftR\x0entpReportDrift\x12C\n" +
	"\x14rebased_report_drift\x18. \x01(\v2\x11.livekit.RTPDriftR\x12rebasedReportDrift\x12E\n" +
	"\x15received_report_drift\x18/ \x01(\v2\x11.livekit.RTPDriftR\x13receivedReportDrift\x1a?\n" +
	"\x11GapHistogramEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x05R\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\rR\x05value:\x028\x01\"\xf0\x01\n" +
	"\x15RTCPSenderReportState\x12#\n" +
	"\rrtp_timestamp\x18\x01 \x01(\rR\frtpTimestamp\x12*\n" +
	"\x11rtp_timestamp_ext\x18\x02 \x01(\x04R\x0frtpTimestampExt\x12#\n" +
	"\rntp_timestamp\x18\x03 \x01(\x04R\fntpTimestamp\x12\x0e\n" +
	"\x02at\x18\x04 \x01(\x03R\x02at\x12\x1f\n" +
	"\vat_adjusted\x18\x05 \x01(\x03R\n" +
	"atAdjusted\x12\x18\n" +
	"\apackets\x18\x06 \x01(\rR\apackets\x12\x16\n" +
	"\x06octets\x18\a \x01(\x04R\x06octets\"\xce\x03\n" +
	"\x11RTPForwarderState\x12\x18\n" +
	"\astarted\x18\x01 \x01(\bR\astarted\x126\n" +
	"\x17reference_layer_spatial\x18\x02 \x01(\x05R\x15referenceLayerSpatial\x12$\n" +
	"\x0epre_start_time\x18\x03 \x01(\x03R\fpreStartTime\x12.\n" +
	"\x13ext_first_timestamp\x18\x04 \x01(\x04R\x11extFirstTimestamp\x12?\n" +
	"\x1cdummy_start_timestamp_offset\x18\x05 \x01(\x04R\x19dummyStartTimestampOffset\x126\n" +
	"\n" +
	"rtp_munger\x18\x06 \x01(\v2\x17.livekit.RTPMungerStateR\trtpMunger\x128\n" +
	"\n" +
	"vp8_munger\x18\a \x01(\v2\x17.livekit.VP8MungerStateH\x00R\tvp8Munger\x12N\n" +
	"\x13sender_report_state\x18\b \x03(\v2\x1e.livekit.RTCPSenderReportStateR\x11senderReportStateB\x0e\n" +
	"\fcodec_munger\"\xc7\x02\n" +
	"\x0eRTPMungerState\x127\n" +
	"\x18ext_last_sequence_number\x18\x01 \x01(\x04R\x15extLastSequenceNumber\x12D\n" +
	"\x1fext_second_last_sequence_number\x18\x02 \x01(\x04R\x1bextSecondLastSequenceNumber\x12,\n" +
	"\x12ext_last_timestamp\x18\x03 \x01(\x04R\x10extLastTimestamp\x129\n" +
	"\x19ext_second_last_timestamp\x18\x04 \x01(\x04R\x16extSecondLastTimestamp\x12\x1f\n" +
	"\vlast_marker\x18\x05 \x01(\bR\n" +
	"lastMarker\x12,\n" +
	"\x12second_last_marker\x18\x06 \x01(\bR\x10secondLastMarker\"\x98\x02\n" +
	"\x0eVP8MungerState\x12-\n" +
	"\x13ext_last_picture_id\x18\x01 \x01(\x05R\x10extLastPictureId\x12&\n" +
	"\x0fpicture_id_used\x18\x02 \x01(\bR\rpictureIdUsed\x12'\n" +
	"\x10last_tl0_pic_idx\x18\x03 \x01(\rR\rlastTl0PicIdx\x12'\n" +
	"\x10tl0_pic_idx_used\x18\x04 \x01(\bR\rtl0PicIdxUsed\x12\x19\n" +
	"\btid_used\x18\x05 \x01(\bR\atidUsed\x12 \n" +
	"\flast_key_idx\x18\x06 \x01(\rR\n" +
	"lastKeyIdx\x12 \n" +
	"\fkey_idx_used\x18\a \x01(\bR\n" +
	"keyIdxUsed\"C\n" +
	"\fTimedVersion\x12\x1d\n" +
	"\n" +
	"unix_micro\x18\x01 \x01(\x03R\tunixMicro\x12\x14\n" +
	"\x05ticks\x18\x02 \x01(\x05R\x05ticks\"\xdc\t\n" +
	"\n" +
	"DataStream\x1a\xeb\x01\n" +
	"\n" +
	"TextHeader\x12H\n" +
	"\x0eoperation_type\x18\x01 \x01(\x0e2!.livekit.DataStream.OperationTypeR\roperationType\x12\x18\n" +
	"\aversion\x18\x02 \x01(\x05R\aversion\x12+\n" +
	"\x12reply_to_stream_id\x18\x03 \x01(\tR\x0freplyToStreamId\x12.\n" +
	"\x13attached_stream_ids\x18\x04 \x03(\tR\x11attachedStreamIds\x12\x1c\n" +
	"\tgenerated\x18\x05 \x01(\bR\tgenerated\x1a \n" +
	"\n" +
	"ByteHeader\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x1a\x95\x04\n" +
	"\x06Header\x12\x1b\n" +
	"\tstream_id\x18\x01 \x01(\tR\bstreamId\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x03R\ttimestamp\x12\x14\n" +
	"\x05topic\x18\x03 \x01(\tR\x05topic\x12\x1b\n" +
	"\tmime_type\x18\x04 \x01(\tR\bmimeType\x12&\n" +
	"\ftotal_length\x18\x05 \x01(\x04H\x01R\vtotalLength\x88\x01\x01\x12A\n" +
	"\x0fencryption_type\x18\a \x01(\x0e2\x18.livekit.Encryption.TypeR\x0eencryptionType\x12J\n" +
	"\n" +
	"attributes\x18\b \x03(\v2*.livekit.DataStream.Header.AttributesEntryR\n" +
	"attributes\x12A\n" +
	"\vtext_header\x18\t \x01(\v2\x1e.livekit.DataStream.TextHeaderH\x00R\n" +
	"textHeader\x12A\n" +
	"\vbyte_header\x18\n" +
	" \x01(\v2\x1e.livekit.DataStream.ByteHeaderH\x00R\n" +
	"byteHeader\x1a=\n" +
	"\x0fAttributesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01B\x10\n" +
	"\x0econtent_headerB\x0f\n" +
	"\r_total_length\x1a\x95\x01\n" +
	"\x05Chunk\x12\x1b\n" +
	"\tstream_id\x18\x01 \x01(\tR\bstreamId\x12\x1f\n" +
	"\vchunk_index\x18\x02 \x01(\x04R\n" +
	"chunkIndex\x12\x18\n" +
	"\acontent\x18\x03 \x01(\fR\acontent\x12\x18\n" +
	"\aversion\x18\x04 \x01(\x05R\aversion\x12\x13\n" +
	"\x02iv\x18\x05 \x01(\fH\x00R\x02iv\x88\x01\x01B\x05\n" +
	"\x03_iv\x1a\xca\x01\n" +
	"\aTrailer\x12\x1b\n" +
	"\tstream_id\x18\x01 \x01(\tR\bstreamId\x12\x16\n" +
	"\x06reason\x18\x02 \x01(\tR\x06reason\x12K\n" +
	"\n" +
	"attributes\x18\x03 \x03(\v2+.livekit.DataStream.Trailer.AttributesEntryR\n" +
	"attributes\x1a=\n" +
	"\x0fAttributesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"A\n" +
	"\rOperationType\x12\n" +
	"\n" +
	"\x06CREATE\x10\x00\x12\n" +
	"\n" +
	"\x06UPDATE\x10\x01\x12\n" +
	"\n" +
	"\x06DELETE\x10\x02\x12\f\n" +
	"\bREACTION\x10\x03\"B\n" +
	"\rWebhookConfig\x12\x10\n" +
	"\x03url\x18\x01 \x01(\tR\x03url\x12\x1f\n" +
	"\vsigning_key\x18\x02 \x01(\tR\n" +
	"signingKey*/\n" +
	"\n" +
	"AudioCodec\x12\x0e\n" +
	"\n" +
	"DEFAULT_AC\x10\x00\x12\b\n" +
	"\x04OPUS\x10\x01\x12\a\n" +
	"\x03AAC\x10\x02*V\n" +
	"\n" +
	"VideoCodec\x12\x0e\n" +
	"\n" +
	"DEFAULT_VC\x10\x00\x12\x11\n" +
	"\rH264_BASELINE\x10\x01\x12\r\n" +
	"\tH264_MAIN\x10\x02\x12\r\n" +
	"\tH264_HIGH\x10\x03\x12\a\n" +
	"\x03VP8\x10\x04*)\n" +
	"\n" +
	"ImageCodec\x12\x0e\n" +
	"\n" +
	"IC_DEFAULT\x10\x00\x12\v\n" +
	"\aIC_JPEG\x10\x01*I\n" +
	"\x11BackupCodecPolicy\x12\x15\n" +
	"\x11PREFER_REGRESSION\x10\x00\x12\r\n" +
	"\tSIMULCAST\x10\x01\x12\x0e\n" +
	"\n" +
	"REGRESSION\x10\x02*+\n" +
	"\tTrackType\x12\t\n" +
	"\x05AUDIO\x10\x00\x12\t\n" +
	"\x05VIDEO\x10\x01\x12\b\n" +
	"\x04DATA\x10\x02*`\n" +
	"\vTrackSource\x12\v\n" +
	"\aUNKNOWN\x10\x00\x12\n" +
	"\n" +
	"\x06CAMERA\x10\x01\x12\x0e\n" +
	"\n" +
	"MICROPHONE\x10\x02\x12\x10\n" +
	"\fSCREEN_SHARE\x10\x03\x12\x16\n" +
	"\x12SCREEN_SHARE_AUDIO\x10\x04*6\n" +
	"\fVideoQuality\x12\a\n" +
	"\x03LOW\x10\x00\x12\n" +
	"\n" +
	"\x06MEDIUM\x10\x01\x12\b\n" +
	"\x04HIGH\x10\x02\x12\a\n" +
	"\x03OFF\x10\x03*@\n" +
	"\x11ConnectionQuality\x12\b\n" +
	"\x04POOR\x10\x00\x12\b\n" +
	"\x04GOOD\x10\x01\x12\r\n" +
	"\tEXCELLENT\x10\x02\x12\b\n" +
	"\x04LOST\x10\x03*;\n" +
	"\x13ClientConfigSetting\x12\t\n" +
	"\x05UNSET\x10\x00\x12\f\n" +
	"\bDISABLED\x10\x01\x12\v\n" +
	"\aENABLED\x10\x02*\xd7\x02\n" +
	"\x10DisconnectReason\x12\x12\n" +
	"\x0eUNKNOWN_REASON\x10\x00\x12\x14\n" +
	"\x10CLIENT_INITIATED\x10\x01\x12\x16\n" +
	"\x12DUPLICATE_IDENTITY\x10\x02\x12\x13\n" +
	"\x0fSERVER_SHUTDOWN\x10\x03\x12\x17\n" +
	"\x13PARTICIPANT_REMOVED\x10\x04\x12\x10\n" +
	"\fROOM_DELETED\x10\x05\x12\x12\n" +
	"\x0eSTATE_MISMATCH\x10\x06\x12\x10\n" +
	"\fJOIN_FAILURE\x10\a\x12\r\n" +
	"\tMIGRATION\x10\b\x12\x10\n" +
	"\fSIGNAL_CLOSE\x10\t\x12\x0f\n" +
	"\vROOM_CLOSED\x10\n" +
	"\x12\x14\n" +
	"\x10USER_UNAVAILABLE\x10\v\x12\x11\n" +
	"\rUSER_REJECTED\x10\f\x12\x15\n" +
	"\x11SIP_TRUNK_FAILURE\x10\r\x12\x16\n" +
	"\x12CONNECTION_TIMEOUT\x10\x0e\x12\x11\n" +
	"\rMEDIA_FAILURE\x10\x0f*\x89\x01\n" +
	"\x0fReconnectReason\x12\x0e\n" +
	"\n" +
	"RR_UNKNOWN\x10\x00\x12\x1a\n" +
	"\x16RR_SIGNAL_DISCONNECTED\x10\x01\x12\x17\n" +
	"\x13RR_PUBLISHER_FAILED\x10\x02\x12\x18\n" +
	"\x14RR_SUBSCRIBER_FAILED\x10\x03\x12\x17\n" +
	"\x13RR_SWITCH_CANDIDATE\x10\x04*T\n" +
	"\x11SubscriptionError\x12\x0e\n" +
	"\n" +
	"SE_UNKNOWN\x10\x00\x12\x18\n" +
	"\x14SE_CODEC_UNSUPPORTED\x10\x01\x12\x15\n" +
	"\x11SE_TRACK_NOTFOUND\x10\x02*\xbd\x01\n" +
	"\x11AudioTrackFeature\x12\r\n" +
	"\tTF_STEREO\x10\x00\x12\r\n" +
	"\tTF_NO_DTX\x10\x01\x12\x18\n" +
	"\x14TF_AUTO_GAIN_CONTROL\x10\x02\x12\x18\n" +
	"\x14TF_ECHO_CANCELLATION\x10\x03\x12\x18\n" +
	"\x14TF_NOISE_SUPPRESSION\x10\x04\x12\"\n" +
	"\x1eTF_ENHANCED_NOISE_CANCELLATION\x10\x05\x12\x18\n" +
	"\x14TF_PRECONNECT_BUFFER\x10\x06BFZ#github.com/livekit/protocol/livekit\xaa\x02\rLiveKit.Proto\xea\x02\x0eLiveKit::Protob\x06proto3"

var (
	file_livekit_models_proto_rawDescOnce sync.Once
	file_livekit_models_proto_rawDescData []byte
)

func file_livekit_models_proto_rawDescGZIP() []byte {
	file_livekit_models_proto_rawDescOnce.Do(func() {
		file_livekit_models_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_livekit_models_proto_rawDesc), len(file_livekit_models_proto_rawDesc)))
	})
	return file_livekit_models_proto_rawDescData
}

var file_livekit_models_proto_enumTypes = make([]protoimpl.EnumInfo, 21)
var file_livekit_models_proto_msgTypes = make([]protoimpl.MessageInfo, 51)
var file_livekit_models_proto_goTypes = []any{
	(AudioCodec)(0),                 // 0: livekit.AudioCodec
	(VideoCodec)(0),                 // 1: livekit.VideoCodec
	(ImageCodec)(0),                 // 2: livekit.ImageCodec
	(BackupCodecPolicy)(0),          // 3: livekit.BackupCodecPolicy
	(TrackType)(0),                  // 4: livekit.TrackType
	(TrackSource)(0),                // 5: livekit.TrackSource
	(VideoQuality)(0),               // 6: livekit.VideoQuality
	(ConnectionQuality)(0),          // 7: livekit.ConnectionQuality
	(ClientConfigSetting)(0),        // 8: livekit.ClientConfigSetting
	(DisconnectReason)(0),           // 9: livekit.DisconnectReason
	(ReconnectReason)(0),            // 10: livekit.ReconnectReason
	(SubscriptionError)(0),          // 11: livekit.SubscriptionError
	(AudioTrackFeature)(0),          // 12: livekit.AudioTrackFeature
	(ParticipantInfo_State)(0),      // 13: livekit.ParticipantInfo.State
	(ParticipantInfo_Kind)(0),       // 14: livekit.ParticipantInfo.Kind
	(ParticipantInfo_KindDetail)(0), // 15: livekit.ParticipantInfo.KindDetail
	(Encryption_Type)(0),            // 16: livekit.Encryption.Type
	(DataPacket_Kind)(0),            // 17: livekit.DataPacket.Kind
	(ServerInfo_Edition)(0),         // 18: livekit.ServerInfo.Edition
	(ClientInfo_SDK)(0),             // 19: livekit.ClientInfo.SDK
	(DataStream_OperationType)(0),   // 20: livekit.DataStream.OperationType
	(*Pagination)(nil),              // 21: livekit.Pagination
	(*ListUpdate)(nil),              // 22: livekit.ListUpdate
	(*Room)(nil),                    // 23: livekit.Room
	(*Codec)(nil),                   // 24: livekit.Codec
	(*PlayoutDelay)(nil),            // 25: livekit.PlayoutDelay
	(*ParticipantPermission)(nil),   // 26: livekit.ParticipantPermission
	(*ParticipantInfo)(nil),         // 27: livekit.ParticipantInfo
	(*RemoteParticipantInfo)(nil),   // 28: livekit.RemoteParticipantInfo
	(*Encryption)(nil),              // 29: livekit.Encryption
	(*SimulcastCodecInfo)(nil),      // 30: livekit.SimulcastCodecInfo
	(*TrackInfo)(nil),               // 31: livekit.TrackInfo
	(*RTPCodecParameters)(nil),      // 32: livekit.RTPCodecParameters
	(*RTPCodecCapability)(nil),      // 33: livekit.RTPCodecCapability
	(*RTCPFeedback)(nil),            // 34: livekit.RTCPFeedback
	(*VideoLayer)(nil),              // 35: livekit.VideoLayer
	(*DataPacket)(nil),              // 36: livekit.DataPacket
	(*ActiveSpeakerUpdate)(nil),     // 37: livekit.ActiveSpeakerUpdate
	(*SpeakerInfo)(nil),             // 38: livekit.SpeakerInfo
	(*UserPacket)(nil),              // 39: livekit.UserPacket
	(*SipDTMF)(nil),                 // 40: livekit.SipDTMF
	(*Transcription)(nil),           // 41: livekit.Transcription
	(*TranscriptionSegment)(nil),    // 42: livekit.TranscriptionSegment
	(*ChatMessage)(nil),             // 43: livekit.ChatMessage
	(*RpcRequest)(nil),              // 44: livekit.RpcRequest
	(*RpcAck)(nil),                  // 45: livekit.RpcAck
	(*RpcResponse)(nil),             // 46: livekit.RpcResponse
	(*RpcError)(nil),                // 47: livekit.RpcError
	(*ParticipantTracks)(nil),       // 48: livekit.ParticipantTracks
	(*ServerInfo)(nil),              // 49: livekit.ServerInfo
	(*ClientInfo)(nil),              // 50: livekit.ClientInfo
	(*ClientConfiguration)(nil),     // 51: livekit.ClientConfiguration
	(*VideoConfiguration)(nil),      // 52: livekit.VideoConfiguration
	(*DisabledCodecs)(nil),          // 53: livekit.DisabledCodecs
	(*RTPDrift)(nil),                // 54: livekit.RTPDrift
	(*RTPStats)(nil),                // 55: livekit.RTPStats
	(*RTCPSenderReportState)(nil),   // 56: livekit.RTCPSenderReportState
	(*RTPForwarderState)(nil),       // 57: livekit.RTPForwarderState
	(*RTPMungerState)(nil),          // 58: livekit.RTPMungerState
	(*VP8MungerState)(nil),          // 59: livekit.VP8MungerState
	(*TimedVersion)(nil),            // 60: livekit.TimedVersion
	(*DataStream)(nil),              // 61: livekit.DataStream
	(*WebhookConfig)(nil),           // 62: livekit.WebhookConfig
	nil,                             // 63: livekit.ParticipantInfo.AttributesEntry
	nil,                             // 64: livekit.RTPStats.GapHistogramEntry
	(*DataStream_TextHeader)(nil),   // 65: livekit.DataStream.TextHeader
	(*DataStream_ByteHeader)(nil),   // 66: livekit.DataStream.ByteHeader
	(*DataStream_Header)(nil),       // 67: livekit.DataStream.Header
	(*DataStream_Chunk)(nil),        // 68: livekit.DataStream.Chunk
	(*DataStream_Trailer)(nil),      // 69: livekit.DataStream.Trailer
	nil,                             // 70: livekit.DataStream.Header.AttributesEntry
	nil,                             // 71: livekit.DataStream.Trailer.AttributesEntry
	(*MetricsBatch)(nil),            // 72: livekit.MetricsBatch
	(*timestamppb.Timestamp)(nil),   // 73: google.protobuf.Timestamp
}
var file_livekit_models_proto_depIdxs = []int32{
	24, // 0: livekit.Room.enabled_codecs:type_name -> livekit.Codec
	60, // 1: livekit.Room.version:type_name -> livekit.TimedVersion
	5,  // 2: livekit.ParticipantPermission.can_publish_sources:type_name -> livekit.TrackSource
	13, // 3: livekit.ParticipantInfo.state:type_name -> livekit.ParticipantInfo.State
	31, // 4: livekit.ParticipantInfo.tracks:type_name -> livekit.TrackInfo
	26, // 5: livekit.ParticipantInfo.permission:type_name -> livekit.ParticipantPermission
	14, // 6: livekit.ParticipantInfo.kind:type_name -> livekit.ParticipantInfo.Kind
	63, // 7: livekit.ParticipantInfo.attributes:type_name -> livekit.ParticipantInfo.AttributesEntry
	9,  // 8: livekit.ParticipantInfo.disconnect_reason:type_name -> livekit.DisconnectReason
	15, // 9: livekit.ParticipantInfo.kind_details:type_name -> livekit.ParticipantInfo.KindDetail
	27, // 10: livekit.RemoteParticipantInfo.participantInfo:type_name -> livekit.ParticipantInfo
	35, // 11: livekit.SimulcastCodecInfo.layers:type_name -> livekit.VideoLayer
	4,  // 12: livekit.TrackInfo.type:type_name -> livekit.TrackType
	5,  // 13: livekit.TrackInfo.source:type_name -> livekit.TrackSource
	35, // 14: livekit.TrackInfo.layers:type_name -> livekit.VideoLayer
	30, // 15: livekit.TrackInfo.codecs:type_name -> livekit.SimulcastCodecInfo
	16, // 16: livekit.TrackInfo.encryption:type_name -> livekit.Encryption.Type
	60, // 17: livekit.TrackInfo.version:type_name -> livekit.TimedVersion
	12, // 18: livekit.TrackInfo.audio_features:type_name -> livekit.AudioTrackFeature
	3,  // 19: livekit.TrackInfo.backup_codec_policy:type_name -> livekit.BackupCodecPolicy
	33, // 20: livekit.RTPCodecParameters.capability:type_name -> livekit.RTPCodecCapability
	34, // 21: livekit.RTPCodecCapability.rtcp_feedback:type_name -> livekit.RTCPFeedback
	6,  // 22: livekit.VideoLayer.quality:type_name -> livekit.VideoQuality
	17, // 23: livekit.DataPacket.kind:type_name -> livekit.DataPacket.Kind
	39, // 24: livekit.DataPacket.user:type_name -> livekit.UserPacket
	37, // 25: livekit.DataPacket.speaker:type_name -> livekit.ActiveSpeakerUpdate
	40, // 26: livekit.DataPacket.sip_dtmf:type_name -> livekit.SipDTMF
	41, // 27: livekit.DataPacket.transcription:type_name -> livekit.Transcription
	72, // 28: livekit.DataPacket.metrics:type_name -> livekit.MetricsBatch
	43, // 29: livekit.DataPacket.chat_message:type_name -> livekit.ChatMessage
	44, // 30: livekit.DataPacket.rpc_request:type_name -> livekit.RpcRequest
	45, // 31: livekit.DataPacket.rpc_ack:type_name -> livekit.RpcAck
	46, // 32: livekit.DataPacket.rpc_response:type_name -> livekit.RpcResponse
	67, // 33: livekit.DataPacket.stream_header:type_name -> livekit.DataStream.Header
	68, // 34: livekit.DataPacket.stream_chunk:type_name -> livekit.DataStream.Chunk
	69, // 35: livekit.DataPacket.stream_trailer:type_name -> livekit.DataStream.Trailer
	38, // 36: livekit.ActiveSpeakerUpdate.speakers:type_name -> livekit.SpeakerInfo
	42, // 37: livekit.Transcription.segments:type_name -> livekit.TranscriptionSegment
	47, // 38: livekit.RpcResponse.error:type_name -> livekit.RpcError
	18, // 39: livekit.ServerInfo.edition:type_name -> livekit.ServerInfo.Edition
	19, // 40: livekit.ClientInfo.sdk:type_name -> livekit.ClientInfo.SDK
	52, // 41: livekit.ClientConfiguration.video:type_name -> livekit.VideoConfiguration
	52, // 42: livekit.ClientConfiguration.screen:type_name -> livekit.VideoConfiguration
	8,  // 43: livekit.ClientConfiguration.resume_connection:type_name -> livekit.ClientConfigSetting
	53, // 44: livekit.ClientConfiguration.disabled_codecs:type_name -> livekit.DisabledCodecs
	8,  // 45: livekit.ClientConfiguration.force_relay:type_name -> livekit.ClientConfigSetting
	8,  // 46: livekit.VideoConfiguration.hardware_encoder:type_name -> livekit.ClientConfigSetting
	24, // 47: livekit.DisabledCodecs.codecs:type_name -> livekit.Codec
	24, // 48: livekit.DisabledCodecs.publish:type_name -> livekit.Codec
	73, // 49: livekit.RTPDrift.start_time:type_name -> google.protobuf.Timestamp
	73, // 50: livekit.RTPDrift.end_time:type_name -> google.protobuf.Timestamp
	73, // 51: livekit.RTPStats.start_time:type_name -> google.protobuf.Timestamp
	73, // 52: livekit.RTPStats.end_time:type_name -> google.protobuf.Timestamp
	64, // 53: livekit.RTPStats.gap_histogram:type_name -> livekit.RTPStats.GapHistogramEntry
	73, // 54: livekit.RTPStats.last_pli:type_name -> google.protobuf.Timestamp
	73, // 55: livekit.RTPStats.last_fir:type_name -> google.protobuf.Timestamp
	73, // 56: livekit.RTPStats.last_key_frame:type_name -> google.protobuf.Timestamp
	73, // 57: livekit.RTPStats.last_layer_lock_pli:type_name -> google.protobuf.Timestamp
	54, // 58: livekit.RTPStats.packet_drift:type_name -> livekit.RTPDrift
	54, // 59: livekit.RTPStats.ntp_report_drift:type_name -> livekit.RTPDrift
	54, // 60: livekit.RTPStats.rebased_report_drift:type_name -> livekit.RTPDrift
	54, // 61: livekit.RTPStats.received_report_drift:type_name -> livekit.RTPDrift
	58, // 62: livekit.RTPForwarderState.rtp_munger:type_name -> livekit.RTPMungerState
	59, // 63: livekit.RTPForwarderState.vp8_munger:type_name -> livekit.VP8MungerState
	56, // 64: livekit.RTPForwarderState.sender_report_state:type_name -> livekit.RTCPSenderReportState
	20, // 65: livekit.DataStream.TextHeader.operation_type:type_name -> livekit.DataStream.OperationType
	16, // 66: livekit.DataStream.Header.encryption_type:type_name -> livekit.Encryption.Type
	70, // 67: livekit.DataStream.Header.attributes:type_name -> livekit.DataStream.Header.AttributesEntry
	65, // 68: livekit.DataStream.Header.text_header:type_name -> livekit.DataStream.TextHeader
	66, // 69: livekit.DataStream.Header.byte_header:type_name -> livekit.DataStream.ByteHeader
	71, // 70: livekit.DataStream.Trailer.attributes:type_name -> livekit.DataStream.Trailer.AttributesEntry
	71, // [71:71] is the sub-list for method output_type
	71, // [71:71] is the sub-list for method input_type
	71, // [71:71] is the sub-list for extension type_name
	71, // [71:71] is the sub-list for extension extendee
	0,  // [0:71] is the sub-list for field type_name
}

func init() { file_livekit_models_proto_init() }
func file_livekit_models_proto_init() {
	if File_livekit_models_proto != nil {
		return
	}
	file_livekit_metrics_proto_init()
	file_livekit_models_proto_msgTypes[15].OneofWrappers = []any{
		(*DataPacket_User)(nil),
		(*DataPacket_Speaker)(nil),
		(*DataPacket_SipDtmf)(nil),
		(*DataPacket_Transcription)(nil),
		(*DataPacket_Metrics)(nil),
		(*DataPacket_ChatMessage)(nil),
		(*DataPacket_RpcRequest)(nil),
		(*DataPacket_RpcAck)(nil),
		(*DataPacket_RpcResponse)(nil),
		(*DataPacket_StreamHeader)(nil),
		(*DataPacket_StreamChunk)(nil),
		(*DataPacket_StreamTrailer)(nil),
	}
	file_livekit_models_proto_msgTypes[18].OneofWrappers = []any{}
	file_livekit_models_proto_msgTypes[22].OneofWrappers = []any{}
	file_livekit_models_proto_msgTypes[25].OneofWrappers = []any{
		(*RpcResponse_Payload)(nil),
		(*RpcResponse_Error)(nil),
	}
	file_livekit_models_proto_msgTypes[36].OneofWrappers = []any{
		(*RTPForwarderState_Vp8Munger)(nil),
	}
	file_livekit_models_proto_msgTypes[46].OneofWrappers = []any{
		(*DataStream_Header_TextHeader)(nil),
		(*DataStream_Header_ByteHeader)(nil),
	}
	file_livekit_models_proto_msgTypes[47].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_livekit_models_proto_rawDesc), len(file_livekit_models_proto_rawDesc)),
			NumEnums:      21,
			NumMessages:   51,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_livekit_models_proto_goTypes,
		DependencyIndexes: file_livekit_models_proto_depIdxs,
		EnumInfos:         file_livekit_models_proto_enumTypes,
		MessageInfos:      file_livekit_models_proto_msgTypes,
	}.Build()
	File_livekit_models_proto = out.File
	file_livekit_models_proto_goTypes = nil
	file_livekit_models_proto_depIdxs = nil
}
