// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: livekit_webhook.proto

package livekit

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type WebhookEvent struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// one of room_started, room_finished, participant_joined, participant_left,
	// track_published, track_unpublished, egress_started, egress_updated, egress_ended,
	// ingress_started, ingress_ended
	Event string `protobuf:"bytes,1,opt,name=event,proto3" json:"event,omitempty"`
	Room  *Room  `protobuf:"bytes,2,opt,name=room,proto3" json:"room,omitempty"`
	// set when event is participant_* or track_*
	Participant *ParticipantInfo `protobuf:"bytes,3,opt,name=participant,proto3" json:"participant,omitempty"`
	// set when event is egress_*
	EgressInfo *EgressInfo `protobuf:"bytes,9,opt,name=egress_info,json=egressInfo,proto3" json:"egress_info,omitempty"`
	// set when event is ingress_*
	IngressInfo *IngressInfo `protobuf:"bytes,10,opt,name=ingress_info,json=ingressInfo,proto3" json:"ingress_info,omitempty"`
	// set when event is track_*
	Track *TrackInfo `protobuf:"bytes,8,opt,name=track,proto3" json:"track,omitempty"`
	// unique event uuid
	Id string `protobuf:"bytes,6,opt,name=id,proto3" json:"id,omitempty"`
	// timestamp in seconds
	CreatedAt int64 `protobuf:"varint,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// Deprecated: Marked as deprecated in livekit_webhook.proto.
	NumDropped    int32 `protobuf:"varint,11,opt,name=num_dropped,json=numDropped,proto3" json:"num_dropped,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WebhookEvent) Reset() {
	*x = WebhookEvent{}
	mi := &file_livekit_webhook_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WebhookEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebhookEvent) ProtoMessage() {}

func (x *WebhookEvent) ProtoReflect() protoreflect.Message {
	mi := &file_livekit_webhook_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebhookEvent.ProtoReflect.Descriptor instead.
func (*WebhookEvent) Descriptor() ([]byte, []int) {
	return file_livekit_webhook_proto_rawDescGZIP(), []int{0}
}

func (x *WebhookEvent) GetEvent() string {
	if x != nil {
		return x.Event
	}
	return ""
}

func (x *WebhookEvent) GetRoom() *Room {
	if x != nil {
		return x.Room
	}
	return nil
}

func (x *WebhookEvent) GetParticipant() *ParticipantInfo {
	if x != nil {
		return x.Participant
	}
	return nil
}

func (x *WebhookEvent) GetEgressInfo() *EgressInfo {
	if x != nil {
		return x.EgressInfo
	}
	return nil
}

func (x *WebhookEvent) GetIngressInfo() *IngressInfo {
	if x != nil {
		return x.IngressInfo
	}
	return nil
}

func (x *WebhookEvent) GetTrack() *TrackInfo {
	if x != nil {
		return x.Track
	}
	return nil
}

func (x *WebhookEvent) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WebhookEvent) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

// Deprecated: Marked as deprecated in livekit_webhook.proto.
func (x *WebhookEvent) GetNumDropped() int32 {
	if x != nil {
		return x.NumDropped
	}
	return 0
}

var File_livekit_webhook_proto protoreflect.FileDescriptor

const file_livekit_webhook_proto_rawDesc = "" +
	"\n" +
	"\x15livekit_webhook.proto\x12\alivekit\x1a\x14livekit_models.proto\x1a\x14livekit_egress.proto\x1a\x15livekit_ingress.proto\"\xf0\x02\n" +
	"\fWebhookEvent\x12\x14\n" +
	"\x05event\x18\x01 \x01(\tR\x05event\x12!\n" +
	"\x04room\x18\x02 \x01(\v2\r.livekit.RoomR\x04room\x12:\n" +
	"\vparticipant\x18\x03 \x01(\v2\x18.livekit.ParticipantInfoR\vparticipant\x124\n" +
	"\vegress_info\x18\t \x01(\v2\x13.livekit.EgressInfoR\n" +
	"egressInfo\x127\n" +
	"\fingress_info\x18\n" +
	" \x01(\v2\x14.livekit.IngressInfoR\vingressInfo\x12(\n" +
	"\x05track\x18\b \x01(\v2\x12.livekit.TrackInfoR\x05track\x12\x0e\n" +
	"\x02id\x18\x06 \x01(\tR\x02id\x12\x1d\n" +
	"\n" +
	"created_at\x18\a \x01(\x03R\tcreatedAt\x12#\n" +
	"\vnum_dropped\x18\v \x01(\x05B\x02\x18\x01R\n" +
	"numDroppedBFZ#github.com/livekit/protocol/livekit\xaa\x02\rLiveKit.Proto\xea\x02\x0eLiveKit::Protob\x06proto3"

var (
	file_livekit_webhook_proto_rawDescOnce sync.Once
	file_livekit_webhook_proto_rawDescData []byte
)

func file_livekit_webhook_proto_rawDescGZIP() []byte {
	file_livekit_webhook_proto_rawDescOnce.Do(func() {
		file_livekit_webhook_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_livekit_webhook_proto_rawDesc), len(file_livekit_webhook_proto_rawDesc)))
	})
	return file_livekit_webhook_proto_rawDescData
}

var file_livekit_webhook_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_livekit_webhook_proto_goTypes = []any{
	(*WebhookEvent)(nil),    // 0: livekit.WebhookEvent
	(*Room)(nil),            // 1: livekit.Room
	(*ParticipantInfo)(nil), // 2: livekit.ParticipantInfo
	(*EgressInfo)(nil),      // 3: livekit.EgressInfo
	(*IngressInfo)(nil),     // 4: livekit.IngressInfo
	(*TrackInfo)(nil),       // 5: livekit.TrackInfo
}
var file_livekit_webhook_proto_depIdxs = []int32{
	1, // 0: livekit.WebhookEvent.room:type_name -> livekit.Room
	2, // 1: livekit.WebhookEvent.participant:type_name -> livekit.ParticipantInfo
	3, // 2: livekit.WebhookEvent.egress_info:type_name -> livekit.EgressInfo
	4, // 3: livekit.WebhookEvent.ingress_info:type_name -> livekit.IngressInfo
	5, // 4: livekit.WebhookEvent.track:type_name -> livekit.TrackInfo
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_livekit_webhook_proto_init() }
func file_livekit_webhook_proto_init() {
	if File_livekit_webhook_proto != nil {
		return
	}
	file_livekit_models_proto_init()
	file_livekit_egress_proto_init()
	file_livekit_ingress_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_livekit_webhook_proto_rawDesc), len(file_livekit_webhook_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_livekit_webhook_proto_goTypes,
		DependencyIndexes: file_livekit_webhook_proto_depIdxs,
		MessageInfos:      file_livekit_webhook_proto_msgTypes,
	}.Build()
	File_livekit_webhook_proto = out.File
	file_livekit_webhook_proto_goTypes = nil
	file_livekit_webhook_proto_depIdxs = nil
}
