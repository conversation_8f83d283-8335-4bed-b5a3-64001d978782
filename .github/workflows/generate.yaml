# Copyright 2023 LiveKit, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: Generate

on:
  workflow_dispatch:
  push:
    branches-ignore: [main]

jobs:
  generate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@3df4ab11eba7bda6032a0b82a6bb43b11571feac # v4

      - name: Install Protoc
        uses: arduino/setup-protoc@v2
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: ">=1.20"

      - name: Go mod tidy
        run: go mod tidy

      - name: Install generators
        uses: magefile/mage-action@v3
        with:
          version: latest
          install-only: true

      - name: Bootstrap
        run: mage bootstrap

      - name: Generate Protobuf
        run: mage proto

      - uses: pnpm/action-setup@v4

      - name: Use Node.js 20
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: "pnpm"

      - name: Install JS dependencies
        run: pnpm install

      - name: Test generate JS files
        run: |
          pnpm --filter @livekit/protocol run generate:proto

      - name: Add changes
        uses: EndBug/add-and-commit@v9
        with:
          add: '["livekit", "replay", "rpc", "infra", "packages/javascript/src/gen"]'
          default_author: github_actions
          message: generated protobuf
