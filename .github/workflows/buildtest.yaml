# Copyright 2023 LiveKit, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: Test

on:
  workflow_dispatch:
  push:
    branches: [master]
  pull_request:
    branches: [master]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: shogo82148/actions-setup-redis@v1
        with:
          redis-version: "6.x"
          auto-start: true
      - run: redis-cli ping

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version-file: "go.mod"

      - name: Set up gotestfmt
        run: go install github.com/gotesttools/gotestfmt/v2/cmd/gotestfmt@v2.4.1

      - name: Replace mutexes
        run: |
          go get github.com/sasha-s/go-deadlock
          grep -rl sync.Mutex ./pkg | xargs sed -i 's/sync\.Mutex/deadlock\.Mutex/g'
          grep -rl sync.RWMutex ./pkg | xargs sed -i 's/sync\.RWMutex/deadlock\.RWMutex/g'
          go install golang.org/x/tools/cmd/goimports
          grep -rl deadlock.Mutex ./pkg | xargs goimports -w
          grep -rl deadlock.RWMutex ./pkg | xargs goimports -w
          go mod tidy

      - name: Mage Build
        uses: magefile/mage-action@v3
        with:
          version: latest
          args: build

      - name: Static Check
        uses: amarpal/staticcheck-action@master
        with:
          checks: '["all", "-ST1000", "-ST1003", "-ST1020", "-ST1021", "-ST1022", "-SA1019"]'
          install-go: false

      - name: Test
        run: |
          set -euo pipefail
          MallocNanoZone=0 go test -race -json -v ./... 2>&1 | tee /tmp/gotest.log | gotestfmt

      # Upload the original go test log as an artifact for later review.
      - name: Upload test log
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-log
          path: /tmp/gotest.log
          if-no-files-found: error
