---
name: Bug report
about: File a bug report and help us improve LiveKit
title: ''
labels: ''
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**Server**
- Version: [0.x.x]
- Environment: [e.g. local dev, EKS]
- any other information about your deployment setup

**Client**
- SDK: [e.g. js, ios]
- Version: [0.x.x]

**To Reproduce**
Steps to reproduce the behavior:
1. two clients are connected to room
2. 3rd client joins
3. 3rd client does '...'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Additional context**
Add any other context about the problem here.
