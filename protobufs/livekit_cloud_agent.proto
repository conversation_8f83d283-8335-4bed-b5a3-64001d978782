// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "google/protobuf/timestamp.proto";

package livekit;
option go_package = "github.com/livekit/protocol/livekit";
option csharp_namespace = "LiveKit.Proto";
option ruby_package = "LiveKit::Proto";

message AgentSecret {
    string name = 1;
    bytes value = 2;
    google.protobuf.Timestamp created_at = 3;
    google.protobuf.Timestamp updated_at = 4;
}

message CreateAgentRequest {
    string agent_name            = 1 [deprecated=true];
    repeated AgentSecret secrets = 2;
    int32 replicas               = 3 [deprecated=true];
    int32 max_replicas           = 4 [deprecated=true];
    string cpu_req               = 5 [deprecated=true];
    repeated string regions      = 6;
}

message CreateAgentResponse {
    string agent_id = 1;
    string agent_name = 2;
    string status = 3;
    string version = 4;
    string presigned_url = 5;
}

message AgentDeployment {
    string region = 1;
    string agent_id = 2;
    string status = 3;
    int32 replicas = 4;
    int32 min_replicas = 5;
    int32 max_replicas = 6;
    string cpu_req = 7;
    string cur_cpu = 8;
    string cur_mem = 9;
    string mem_req = 10;
    string mem_limit = 11;
    string cpu_limit = 12;
}

message AgentInfo {
    string agent_id = 1;
    string agent_name = 2;
    string version = 3;
    repeated AgentDeployment agent_deployments = 4;
    repeated AgentSecret secrets = 5;
    google.protobuf.Timestamp deployed_at = 6;
}

message ListAgentsRequest {
    string agent_name = 1;
    string agent_id = 2;
}

message ListAgentsResponse {
    repeated AgentInfo agents = 1;
}

message AgentVersion {
    string version = 1;
    bool current = 2;
    google.protobuf.Timestamp created_at = 3;
}

message ListAgentVersionsRequest {
    string agent_id = 1;
    string agent_name = 2;
}

message ListAgentVersionsResponse {
    repeated AgentVersion versions = 1;
}

message UpdateAgentRequest {
    string agent_id              = 1;
    string agent_name            = 2 [deprecated=true];
    int32 replicas               = 3 [deprecated=true];
    int32 max_replicas           = 4 [deprecated=true];
    string cpu_req               = 5 [deprecated=true];
    repeated string regions      = 6;
    repeated AgentSecret secrets = 7;
}

message UpdateAgentResponse {
    bool success = 1;
    string message = 2;
}

message DeployAgentRequest {
    string agent_id              = 1;
    string agent_name            = 2 [deprecated=true];
    repeated AgentSecret secrets = 3;
    int32 replicas               = 4 [deprecated=true];
    int32 max_replicas           = 5 [deprecated=true];
    string cpu_req               = 6 [deprecated=true];
}

message DeployAgentResponse {
    bool success = 1;
    string message = 2;
    string agent_id = 3;
    string presigned_url = 4;
}

message UpdateAgentSecretsRequest {
    string agent_id = 1;
    string agent_name = 2;
    bool overwrite = 3;
    repeated AgentSecret secrets = 4;
}

message UpdateAgentSecretsResponse {
    bool success = 1;
    string message = 2;
}

message RollbackAgentRequest {
    string agent_id = 1;
    string agent_name = 2;
    string version = 3;
}

message RollbackAgentResponse {
    bool success = 1;
    string message = 2;
}

message DeleteAgentRequest {
    string agent_id = 1;
    string agent_name = 2;
}

message DeleteAgentResponse {
    bool success = 1;
    string message = 2;
}

message ListAgentSecretsRequest {
    string agent_id = 1;
    string agent_name = 2;
}

message ListAgentSecretsResponse {
    repeated AgentSecret secrets = 1;
}

message SettingsParam {
    string name = 1;
    string value = 2;
}

message ClientSettingsResponse {
    repeated SettingsParam params = 1;
}

message ClientSettingsRequest {
}

service CloudAgent {
    rpc CreateAgent(CreateAgentRequest) returns (CreateAgentResponse) {}
    rpc ListAgents(ListAgentsRequest) returns (ListAgentsResponse) {}
    rpc ListAgentVersions(ListAgentVersionsRequest) returns (ListAgentVersionsResponse) {}
    rpc ListAgentSecrets(ListAgentSecretsRequest) returns (ListAgentSecretsResponse) {}
    rpc UpdateAgent(UpdateAgentRequest) returns (UpdateAgentResponse) {}
    rpc DeployAgent(DeployAgentRequest) returns (DeployAgentResponse) {}
    rpc UpdateAgentSecrets(UpdateAgentSecretsRequest) returns (UpdateAgentSecretsResponse) {}
    rpc RollbackAgent(RollbackAgentRequest) returns (RollbackAgentResponse) {}
    rpc DeleteAgent(DeleteAgentRequest) returns (DeleteAgentResponse) {}
    rpc GetClientSettings(ClientSettingsRequest) returns (ClientSettingsResponse) {}
}
