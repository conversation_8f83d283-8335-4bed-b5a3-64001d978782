syntax = "proto3";

package livekit;
option go_package = "github.com/livekit/protocol/livekit";

message ExtPacket {
  VideoLayerSVC video_layer = 1;
  int64 arrival = 2;
  uint64 ext_sequence_number = 3;
  uint64 ext_timestamp = 4;
  bool key_frame = 5;
  int32 mime_type = 6;
  bytes raw_packet = 7;
  optional ExtDependencyDescriptor dependency_descriptor = 8;
  optional bytes abs_capture_time_ext = 9;
  bool is_out_of_order = 10;
}

message VideoLayerSVC {
  int32 spatial = 1;
  int32 temporal = 2;
}

message ExtDependencyDescriptor {
  optional DependencyDescriptor descriptor = 1;
  repeated DependencyDescriptorDecodeTarget decode_targets = 2;
  bool structure_updated = 3;
  bool active_decode_targets_updated = 4;
  bool integrity = 5;
  uint64 ext_frame_num = 6;
  uint64 ext_key_frame_num = 7;
}

message DependencyDescriptorDecodeTarget {
  int32 target = 1;
  VideoLayerSVC layer = 2;
}

message DependencyDescriptor {
  bool first_packet_in_frame = 1;
  bool last_packet_in_frame = 2;
  uint32 frame_number = 3;
  optional FrameDependencyTemplate frame_dependencies = 4;
  optional RenderResolution resolution = 5;
  optional uint32 active_decode_targets_bitmask = 6;
  optional FrameDependencyStructure attached_structure = 7;
}

message FrameDependencyTemplate {
  int32 spatial_id = 1;
  int32 temporal_id = 2;
  repeated int32 decode_target_indications = 3;
  repeated int32 frame_diffs = 4;
  repeated int32 chain_diffs = 5;
}

message RenderResolution {
  int32 width = 1;
  int32 height = 2;
}

message FrameDependencyStructure {
  int32 structure_id = 1;
  int32 num_decode_targets = 2;
  int32 num_chains = 3;
  repeated int32 decode_target_protected_by_chain = 4;
  repeated RenderResolution resolutions = 5;
  repeated FrameDependencyTemplate templates = 6;
}
