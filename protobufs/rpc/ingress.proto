// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package rpc;

option go_package = "github.com/livekit/protocol/rpc";

import "options.proto";
import "livekit_ingress.proto";
import "google/protobuf/empty.proto";

service IngressInternal {
  rpc StartIngress(StartIngressRequest) returns (livekit.IngressInfo) {
    option (psrpc.options).affinity_func = true;
  };

  rpc ListActiveIngress(ListActiveIngressRequest) returns (ListActiveIngressResponse) {
    option (psrpc.options).multi = true;
    option (psrpc.options).topics = true;
  };

  rpc KillIngressSession(KillIngressSessionRequest) returns (google.protobuf.Empty) {
    option (psrpc.options) = {
      topics: true
      topic_params: {
        names: ["ingress_id", "resource_id"]
        typed: false
      }
    };
  };
}

service IngressHandler {
  rpc UpdateIngress(livekit.UpdateIngressRequest) returns (livekit.IngressState) {
    option (psrpc.options).topics = true;
  };
  rpc DeleteIngress(livekit.DeleteIngressRequest) returns (livekit.IngressState) {
    option (psrpc.options).topics = true;
  };
  rpc DeleteWHIPResource(DeleteWHIPResourceRequest) returns (google.protobuf.Empty) {
    option (psrpc.options).topics = true;
  };
  rpc ICERestartWHIPResource(ICERestartWHIPResourceRequest) returns (ICERestartWHIPResourceResponse) {
    option (psrpc.options).topics = true;
  };

}

message ListActiveIngressRequest {}

message ListActiveIngressResponse {
  repeated string ingress_ids = 1 [deprecated = true];
  repeated IngressSession ingress_sessions = 2;
}

message DeleteWHIPResourceRequest {
  string resource_id = 1;
  string stream_key = 2;
}

message ICERestartWHIPResourceRequest {
  string resource_id = 1;
  string stream_key = 2;
  string user_fragment = 3;
  string password = 4;
  repeated string candidates = 5;
}

message ICERestartWHIPResourceResponse {
  string trickle_ice_sdpfrag = 1;
}

message StartIngressRequest {
  livekit.IngressInfo info = 1;
  string token = 2;
  string ws_url = 3;
  map<string,string> logging_fields = 4;
}

message IngressSession {
  string ingress_id = 1;
  string resource_id = 2;
}

message KillIngressSessionRequest {
  IngressSession session = 1;
}

