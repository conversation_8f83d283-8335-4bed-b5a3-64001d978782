// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package livekit;
option go_package = "github.com/livekit/protocol/rpc";

import "google/protobuf/empty.proto";
import "livekit_analytics.proto";

service AnalyticsRecorderService {
  rpc IngestStats(stream livekit.AnalyticsStats) returns (google.protobuf.Empty){};
  rpc IngestEvents(stream livekit.AnalyticsEvents) returns (google.protobuf.Empty){};
  rpc IngestNodeRoomStates(stream livekit.AnalyticsNodeRooms) returns (google.protobuf.Empty){};
}
