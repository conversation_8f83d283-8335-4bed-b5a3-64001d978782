// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package rpc;

option go_package = "github.com/livekit/protocol/rpc";

import "options.proto";
import "livekit_rtc.proto";
import "livekit_models.proto";

service BroadcastSignal {
  rpc Update(BroadcastUpdate) returns (BroadcastUpdate) {
    option (psrpc.options) = {
      subscription: true
      multi: true
      topics: true
      topic_params: {
        group: "room"
        names: ["room"]
        typed: true
      };
    };
  };

  rpc GetAllParticipant(GetAllParticipantRequest) returns (GetAllParticipantResponse) {
    option (psrpc.options) = {
      multi: true
      topics: true
      topic_params: {
        group: "room"
        names: ["room"]
        typed: true
      };
    };
  }
}

message BroadcastUpdate {
  repeated string receive_identities = 1;
  BroadcastUpdateMessage update = 2;
}
message BroadcastUpdateMessage {
  oneof message {
    livekit.RemoteParticipantUpdate remote_participant_update = 1;
  }
}

message GetAllParticipantRequest {
  string room = 1;
}

message GetAllParticipantResponse {
  repeated livekit.RemoteParticipantInfo participants = 1;
}