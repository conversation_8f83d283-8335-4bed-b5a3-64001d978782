// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package rpc;

option go_package = "github.com/livekit/protocol/rpc";

import "options.proto";
import "livekit_extpacket.proto";

service MediaRelay {
  // 用于发布者向订阅者「发送媒体数据」「同步上行轨道的状态」
  //  rpc RelayMedia(rpc.RelayMediaRequest) returns (rpc.RelayMediaResponse) {
  //    option (psrpc.options) = {
  //      stream: true
  //      topics: true
  //      topic_params: {
  //        group: "node"
  //        names: ["node_id"]
  //        typed: true
  //        single_server: true
  //      };
  //    };
  //  };
  // 用于订阅者向发布者「请求数据」「请求关键帧」「同步订阅轨道的状态」
  rpc RelayTrackReceiver(rpc.RelayTrackReceiverRequest) returns (rpc.RelayTrackReceiverResponse) {
    option (psrpc.options) = {
      topics: true
      topic_params: {
        group: "node"
        names: ["node_id"]
        typed: true
        single_server: true
      };
    };
  };
}

message RelayMediaRequest {
  StartRequest start_request = 1;
  CloseRequest close_request = 2;
}
message StartRequest {
  string room_name = 1;
  string subscriber_node_id = 2;
  string subscriber_node_ip = 3;
  string subscriber_identity = 4;
  string publisher_node_id = 5;
  string publisher_node_ip = 6;
  int32 publisher_node_relay_port = 7;
  string publisher_identity = 8;
  string track_id = 9;
}
message CloseRequest {
  string room_name = 1;
  string subscriber_node_id = 2;
  string track_id = 3;
}

message RelayMediaResponse {
  string connection_id = 1; // room_name + track_id
  MediaResponse message = 2;
}
message MediaResponse {
  oneof message {
    StartResponse start_response = 1;
    PacketResponse packet_response = 2;
    HandleRTCPSenderReportData handle_rtcp_sender_report_data = 3;
    Resync resync = 4;
    Closed closed = 5;
  }
}
message StartResponse {
  string msg = 1;
}
message PacketResponse {
  livekit.ExtPacket extPacket = 1;
  int32 layer = 2;
}
message HandleRTCPSenderReportData {
  int32 payloadType = 1;
  bool isSvc = 2;
  int32 layer = 3;
  bytes publisherSRData = 4;
}
message Resync {}
message Closed {}

// ----------------------------
message RelayTrackReceiverRequest {
  string node_id = 1;
  string room_name = 2;
  string participant_identity = 3;
  string track_id = 4;
  TrackReceiverRequest request = 5;
}
message TrackReceiverRequest {
  oneof message {
    Codec codec_request = 1;
    HeaderExtensions header_extensions_request = 2;
    SendPli send_pli_request = 3;
    OnSetupReceiver on_setup_receiver_request = 4;
    OnSubscriberMaxQualityChange on_subscriber_max_quality_change_request = 5;
    OnCodecRegression on_codec_regression_request = 6;
    OnTrackSubscribed on_track_subscribed_request = 7;
    GetAudioLevel get_audio_level_request = 8;
    GetTemporalLayerFpsForSpatial get_temporal_layer_fps_for_spatial_request = 9;
    SetUpTrackPaused set_up_track_paused_request = 10;
    SetMaxExpectedSpatialLayer set_max_expected_spatial_layer_request = 11;
  }
}
message Codec {}
message HeaderExtensions {}
message SendPli {
  int32 layer = 1;
  bool force = 2;
}
message OnSetupReceiver {
  int32 mime_type = 1;
}
message OnSubscriberMaxQualityChange {
  string subscriber_id = 1;
  int32 mime_type = 2;
  int32 layer = 3;
}
message OnCodecRegression {
  bytes old = 1;
  bytes new = 2;
}
message OnTrackSubscribed {}
message GetAudioLevel {}
message GetTemporalLayerFpsForSpatial {
  int32 spatial = 1;
}
message SetUpTrackPaused {
  bool paused = 1;
}
message SetMaxExpectedSpatialLayer {
  int32 layer = 1;
}

message RelayTrackReceiverResponse {
  oneof message {
    CodecResponse codec_response = 1;
    HeaderExtensionsResponse header_extensions_response = 2;
    GetAudioLevelResponse get_audio_level_response = 3;
    GetTemporalLayerFpsForSpatialResponse get_temporal_layer_fps_for_spatial_response = 4;
  }
}
message CodecResponse {
  bytes bytes = 1;
}
message HeaderExtensionsResponse {
  bytes bytes = 1;
}
message GetAudioLevelResponse {
  double level = 1;
  bool active = 2;
}
message GetTemporalLayerFpsForSpatialResponse {
  repeated float fps = 1;
}
