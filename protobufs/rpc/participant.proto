// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package rpc;

option go_package = "github.com/livekit/protocol/rpc";

import "options.proto";
import "livekit_models.proto";
import "livekit_room.proto";

service Participant {
  rpc RemoveParticipant(livekit.RoomParticipantIdentity) returns (livekit.RemoveParticipantResponse) {
    option (psrpc.options) = {
      topics: true
      topic_params: {
        group: "participant"
        names: ["participant"]
        typed: true
      };
    };
  };
  rpc MutePublishedTrack(livekit.MuteRoomTrackRequest) returns (livekit.MuteRoomTrackResponse) {
    option (psrpc.options) = {
      topics: true
      topic_params: {
        group: "participant"
        names: ["participant"]
        typed: true
      };
    };
  };
  rpc UpdateParticipant(livekit.UpdateParticipantRequest) returns (livekit.ParticipantInfo) {
    option (psrpc.options) = {
      topics: true
      topic_params: {
        group: "participant"
        names: ["participant"]
        typed: true
      };
    };
  };
  rpc UpdateSubscriptions(livekit.UpdateSubscriptionsRequest) returns (livekit.UpdateSubscriptionsResponse) {
    option (psrpc.options) = {
      topics: true
      topic_params: {
        group: "participant"
        names: ["participant"]
        typed: true
      };
    };
  };
  rpc ForwardParticipant(livekit.ForwardParticipantRequest) returns (livekit.ForwardParticipantResponse) {
    option (psrpc.options) = {
      topics: true
      topic_params: {
        group: "participant"
        names: ["participant"]
        typed: true
      };
    };
  };
  rpc MoveParticipant(livekit.MoveParticipantRequest) returns (livekit.MoveParticipantResponse) {
    option (psrpc.options) = {
      topics: true
      topic_params: {
        group: "participant"
        names: ["participant"]
        typed: true
      };
    };
  };
}
