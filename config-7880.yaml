port: 7880
relay_port: 5104

# when redis is set, LiveKit will automatically operate in a fully distributed fashion
# clients could connect to any node and be routed to the same room
redis:
  address: *************:6379
  db: 0
  password: 123456

# WebRTC configuration
rtc:
  tcp_port: 7881
  use_external_ip: false
  udp_port: 7885
  use_ice_lite: false

keys:
  API4rFGefmxjydD: 89oZXDAF9fMNuNWzOQDgzTQf48zdREUc9f9tIWE7YODA
logging:
  level: debug

# turn server
turn:
  enabled: true
  udp_port: 3478
  relay_range_start: 30000
  relay_range_end: 31000