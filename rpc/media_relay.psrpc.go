// Code generated by protoc-gen-psrpc v0.6.0, DO NOT EDIT.
// source: rpc/media_relay.proto

package rpc

import (
	"context"

	"github.com/livekit/psrpc"
	"github.com/livekit/psrpc/pkg/client"
	"github.com/livekit/psrpc/pkg/info"
	"github.com/livekit/psrpc/pkg/rand"
	"github.com/livekit/psrpc/pkg/server"
	"github.com/livekit/psrpc/version"
)

var _ = version.PsrpcVersion_0_6

// ===========================
// MediaRelay Client Interface
// ===========================

type MediaRelayClient[NodeIdTopicType ~string] interface {
	// 用于发布者向订阅者「发送媒体数据」「同步上行轨道的状态」
	//  rpc RelayMedia(rpc.RelayMediaRequest) returns (rpc.RelayMediaResponse) {
	//    option (psrpc.options) = {
	//      stream: true
	//      topics: true
	//      topic_params: {
	//        group: "node"
	//        names: ["node_id"]
	//        typed: true
	//        single_server: true
	//      };
	//    };
	//  };
	// 用于订阅者向发布者「请求数据」「请求关键帧」「同步订阅轨道的状态」
	RelayTrackReceiver(ctx context.Context, nodeId NodeIdTopicType, req *RelayTrackReceiverRequest, opts ...psrpc.RequestOption) (*RelayTrackReceiverResponse, error)

	// Close immediately, without waiting for pending RPCs
	Close()
}

// ===============================
// MediaRelay ServerImpl Interface
// ===============================

type MediaRelayServerImpl interface {
	// 用于发布者向订阅者「发送媒体数据」「同步上行轨道的状态」
	//  rpc RelayMedia(rpc.RelayMediaRequest) returns (rpc.RelayMediaResponse) {
	//    option (psrpc.options) = {
	//      stream: true
	//      topics: true
	//      topic_params: {
	//        group: "node"
	//        names: ["node_id"]
	//        typed: true
	//        single_server: true
	//      };
	//    };
	//  };
	// 用于订阅者向发布者「请求数据」「请求关键帧」「同步订阅轨道的状态」
	RelayTrackReceiver(context.Context, *RelayTrackReceiverRequest) (*RelayTrackReceiverResponse, error)
}

// ===========================
// MediaRelay Server Interface
// ===========================

type MediaRelayServer[NodeIdTopicType ~string] interface {
	// 用于发布者向订阅者「发送媒体数据」「同步上行轨道的状态」
	//  rpc RelayMedia(rpc.RelayMediaRequest) returns (rpc.RelayMediaResponse) {
	//    option (psrpc.options) = {
	//      stream: true
	//      topics: true
	//      topic_params: {
	//        group: "node"
	//        names: ["node_id"]
	//        typed: true
	//        single_server: true
	//      };
	//    };
	//  };
	// 用于订阅者向发布者「请求数据」「请求关键帧」「同步订阅轨道的状态」
	RegisterRelayTrackReceiverTopic(nodeId NodeIdTopicType) error
	DeregisterRelayTrackReceiverTopic(nodeId NodeIdTopicType)
	RegisterAllNodeTopics(nodeId NodeIdTopicType) error
	DeregisterAllNodeTopics(nodeId NodeIdTopicType)

	// Close and wait for pending RPCs to complete
	Shutdown()

	// Close immediately, without waiting for pending RPCs
	Kill()
}

// =================
// MediaRelay Client
// =================

type mediaRelayClient[NodeIdTopicType ~string] struct {
	client *client.RPCClient
}

// NewMediaRelayClient creates a psrpc client that implements the MediaRelayClient interface.
func NewMediaRelayClient[NodeIdTopicType ~string](bus psrpc.MessageBus, opts ...psrpc.ClientOption) (MediaRelayClient[NodeIdTopicType], error) {
	sd := &info.ServiceDefinition{
		Name: "MediaRelay",
		ID:   rand.NewClientID(),
	}

	sd.RegisterMethod("RelayTrackReceiver", false, false, false, true)

	rpcClient, err := client.NewRPCClient(sd, bus, opts...)
	if err != nil {
		return nil, err
	}

	return &mediaRelayClient[NodeIdTopicType]{
		client: rpcClient,
	}, nil
}

func (c *mediaRelayClient[NodeIdTopicType]) RelayTrackReceiver(ctx context.Context, nodeId NodeIdTopicType, req *RelayTrackReceiverRequest, opts ...psrpc.RequestOption) (*RelayTrackReceiverResponse, error) {
	return client.RequestSingle[*RelayTrackReceiverResponse](ctx, c.client, "RelayTrackReceiver", []string{string(nodeId)}, req, opts...)
}

func (s *mediaRelayClient[NodeIdTopicType]) Close() {
	s.client.Close()
}

// =================
// MediaRelay Server
// =================

type mediaRelayServer[NodeIdTopicType ~string] struct {
	svc MediaRelayServerImpl
	rpc *server.RPCServer
}

// NewMediaRelayServer builds a RPCServer that will route requests
// to the corresponding method in the provided svc implementation.
func NewMediaRelayServer[NodeIdTopicType ~string](svc MediaRelayServerImpl, bus psrpc.MessageBus, opts ...psrpc.ServerOption) (MediaRelayServer[NodeIdTopicType], error) {
	sd := &info.ServiceDefinition{
		Name: "MediaRelay",
		ID:   rand.NewServerID(),
	}

	s := server.NewRPCServer(sd, bus, opts...)

	sd.RegisterMethod("RelayTrackReceiver", false, false, false, true)
	return &mediaRelayServer[NodeIdTopicType]{
		svc: svc,
		rpc: s,
	}, nil
}

func (s *mediaRelayServer[NodeIdTopicType]) RegisterRelayTrackReceiverTopic(nodeId NodeIdTopicType) error {
	return server.RegisterHandler(s.rpc, "RelayTrackReceiver", []string{string(nodeId)}, s.svc.RelayTrackReceiver, nil)
}

func (s *mediaRelayServer[NodeIdTopicType]) DeregisterRelayTrackReceiverTopic(nodeId NodeIdTopicType) {
	s.rpc.DeregisterHandler("RelayTrackReceiver", []string{string(nodeId)})
}

func (s *mediaRelayServer[NodeIdTopicType]) allNodeTopicRegisterers() server.RegistererSlice {
	return server.RegistererSlice{
		server.NewRegisterer(s.RegisterRelayTrackReceiverTopic, s.DeregisterRelayTrackReceiverTopic),
	}
}

func (s *mediaRelayServer[NodeIdTopicType]) RegisterAllNodeTopics(nodeId NodeIdTopicType) error {
	return s.allNodeTopicRegisterers().Register(nodeId)
}

func (s *mediaRelayServer[NodeIdTopicType]) DeregisterAllNodeTopics(nodeId NodeIdTopicType) {
	s.allNodeTopicRegisterers().Deregister(nodeId)
}

func (s *mediaRelayServer[NodeIdTopicType]) Shutdown() {
	s.rpc.Close(false)
}

func (s *mediaRelayServer[NodeIdTopicType]) Kill() {
	s.rpc.Close(true)
}

var psrpcFileDescriptor13 = []byte{
	// 1484 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x57, 0x5b, 0x6f, 0x1b, 0x45,
	0x14, 0xb6, 0xe3, 0x26, 0x76, 0x4e, 0xec, 0x26, 0x99, 0xdc, 0x1c, 0x87, 0x5e, 0xb2, 0x25, 0x28,
	0x0a, 0x90, 0xb4, 0xa9, 0xb8, 0x54, 0x48, 0x48, 0x34, 0xa4, 0xb8, 0xa2, 0x2d, 0xe9, 0x38, 0x15,
	0x08, 0x09, 0xad, 0x26, 0xbb, 0x13, 0x67, 0xd5, 0xf5, 0xee, 0x74, 0x67, 0x1c, 0x6c, 0x1e, 0x10,
	0x12, 0x2f, 0xf0, 0x0e, 0x3f, 0x81, 0x3f, 0xc0, 0xef, 0xe0, 0x91, 0xdf, 0x83, 0xd0, 0x5c, 0x76,
	0x77, 0xd6, 0x5d, 0x87, 0x3e, 0xf0, 0xb6, 0x73, 0x2e, 0xdf, 0x99, 0x39, 0xe7, 0x3b, 0x67, 0x66,
	0x61, 0x2d, 0x61, 0xde, 0xc1, 0x80, 0xfa, 0x01, 0x71, 0x13, 0x1a, 0x92, 0xf1, 0x3e, 0x4b, 0x62,
	0x11, 0xa3, 0x5a, 0xc2, 0xbc, 0x4e, 0x2b, 0x66, 0x22, 0x88, 0x23, 0xae, 0x65, 0x9d, 0x8d, 0x30,
	0xb8, 0xa4, 0x2f, 0x03, 0xe1, 0xd2, 0x91, 0x60, 0xc4, 0x7b, 0x49, 0x85, 0x56, 0x38, 0x3f, 0x57,
	0x61, 0x19, 0x4b, 0xe7, 0xa7, 0x12, 0x07, 0xd3, 0x57, 0x43, 0xca, 0x05, 0xfa, 0x10, 0x5a, 0x5c,
	0x90, 0x44, 0xb8, 0x89, 0x16, 0xb4, 0xab, 0xb7, 0xab, 0xbb, 0x0b, 0x87, 0xcb, 0xfb, 0x09, 0xf3,
	0xf6, 0x7b, 0x52, 0x63, 0x2c, 0x71, 0x93, 0x5b, 0x2b, 0xe9, 0xe7, 0x85, 0x31, 0xa7, 0x99, 0xdf,
	0x8c, 0xe5, 0x77, 0x24, 0x35, 0x99, 0x9f, 0x67, 0xad, 0x9c, 0xdf, 0x6b, 0xd0, 0xb4, 0x61, 0xd1,
	0x16, 0xcc, 0x27, 0x71, 0x3c, 0x70, 0x23, 0x32, 0xa0, 0x2a, 0xf8, 0x3c, 0x6e, 0x48, 0xc1, 0x33,
	0x32, 0xa0, 0xe8, 0x3d, 0x40, 0x7c, 0x78, 0xc6, 0xbd, 0x24, 0x38, 0xa3, 0x89, 0x1b, 0xc5, 0x3e,
	0x75, 0x03, 0x5f, 0x85, 0x9a, 0xc7, 0x4b, 0xb9, 0xe6, 0x59, 0xec, 0xd3, 0xc7, 0x7e, 0xa9, 0x35,
	0x6b, 0xd7, 0x4a, 0xad, 0x19, 0x3a, 0x80, 0x15, 0xcb, 0x3a, 0xf0, 0x69, 0x24, 0x02, 0x31, 0x6e,
	0x5f, 0x53, 0xe6, 0x16, 0xd0, 0x63, 0xa3, 0x41, 0x7b, 0xb0, 0xcc, 0x86, 0x67, 0x61, 0xc0, 0x2f,
	0xac, 0xbd, 0xcc, 0x2a, 0xf3, 0xc5, 0x4c, 0x61, 0xb6, 0x52, 0x62, 0xcb, 0xda, 0x73, 0x65, 0xb6,
	0x0c, 0x3d, 0x80, 0xcd, 0x09, 0x5b, 0x55, 0x63, 0x97, 0xc5, 0x89, 0x68, 0xd7, 0x6f, 0x57, 0x77,
	0x67, 0xf1, 0x7a, 0xc1, 0x47, 0x55, 0xf1, 0x24, 0x4e, 0x04, 0x7a, 0x1f, 0x50, 0xee, 0x9a, 0x1d,
	0xa1, 0xa1, 0xe2, 0xe4, 0x1b, 0xc8, 0x4e, 0xb0, 0x09, 0x0d, 0x91, 0x10, 0xef, 0xa5, 0xdc, 0xf8,
	0xbc, 0x32, 0xaa, 0xab, 0xf5, 0x63, 0xdf, 0x11, 0xd0, 0xb4, 0xab, 0xf6, 0x7f, 0x96, 0xc5, 0x8e,
	0x5a, 0x2b, 0x46, 0xed, 0x03, 0xb2, 0x29, 0xc9, 0x59, 0x1c, 0x71, 0x8a, 0xee, 0x40, 0xcb, 0x8b,
	0xa3, 0x88, 0x7a, 0x92, 0xd7, 0xd2, 0x4b, 0xc7, 0x6f, 0xe6, 0x42, 0x55, 0xec, 0xfa, 0x80, 0x72,
	0x4e, 0xfa, 0xd4, 0x50, 0x0f, 0x29, 0xea, 0x15, 0x90, 0x70, 0x6a, 0xe2, 0xfc, 0x35, 0x03, 0xad,
	0x62, 0x90, 0x4f, 0xe0, 0x7a, 0x4a, 0x7c, 0x2d, 0x31, 0xcc, 0x47, 0x36, 0xf3, 0xb5, 0xa6, 0x5b,
	0xc1, 0x2d, 0x6e, 0x0b, 0xd0, 0xa7, 0xb0, 0xa8, 0x7b, 0x2b, 0xf7, 0xd6, 0x9b, 0x58, 0x51, 0xde,
	0x27, 0x4a, 0x67, 0xb9, 0x5f, 0x67, 0x05, 0x09, 0xa2, 0x70, 0xf3, 0x82, 0x44, 0x7e, 0x48, 0xdd,
	0x44, 0x78, 0xcc, 0xe5, 0x34, 0xf2, 0x69, 0xe2, 0x26, 0x54, 0x96, 0xdc, 0xf5, 0x89, 0x20, 0x2a,
	0x51, 0x0b, 0x87, 0xb7, 0x14, 0x5c, 0x57, 0x99, 0xe2, 0xd3, 0xa3, 0x93, 0x9e, 0x32, 0xc4, 0xca,
	0xee, 0x73, 0x22, 0x48, 0xb7, 0x82, 0x3b, 0x1a, 0x08, 0x0b, 0x8f, 0x4d, 0x6a, 0xd1, 0x0e, 0xcc,
	0x25, 0x94, 0x8f, 0x23, 0x4f, 0xb1, 0x7a, 0xe1, 0x70, 0x41, 0xc1, 0x61, 0x25, 0xea, 0x56, 0xb0,
	0x51, 0x4a, 0x33, 0xd5, 0xa3, 0x9a, 0xcd, 0xa9, 0x99, 0xa2, 0x83, 0x2f, 0xcd, 0xb4, 0xf2, 0xe1,
	0x7c, 0x96, 0x71, 0x67, 0x1b, 0x5a, 0x85, 0x0c, 0xa1, 0x25, 0xa8, 0x0d, 0x78, 0xdf, 0x14, 0x4a,
	0x7e, 0x3a, 0xdf, 0xc0, 0xf5, 0x62, 0x1a, 0xd0, 0x5d, 0x98, 0xa7, 0x23, 0xa1, 0x85, 0x59, 0xb2,
	0xcd, 0xb4, 0xda, 0x3f, 0x4e, 0x35, 0x38, 0x37, 0x42, 0xab, 0x30, 0x1b, 0x92, 0x31, 0x4d, 0x54,
	0x72, 0x67, 0xb1, 0x5e, 0x38, 0xbf, 0x55, 0xa1, 0x33, 0x3d, 0x25, 0xe8, 0x36, 0x2c, 0x30, 0x32,
	0x0e, 0x63, 0xe2, 0x9f, 0x8e, 0x99, 0xae, 0xea, 0x2c, 0xb6, 0x45, 0x12, 0x36, 0xe0, 0xbd, 0x4b,
	0x4f, 0xc1, 0x36, 0xb0, 0x5e, 0xe4, 0xc1, 0x6a, 0x56, 0x30, 0xb4, 0x0b, 0x79, 0xbf, 0xf6, 0xb0,
	0x0c, 0xa0, 0x72, 0xd9, 0xc4, 0x93, 0x62, 0xa7, 0x01, 0x73, 0x3a, 0xb3, 0xf2, 0x4b, 0x27, 0xcf,
	0xf9, 0xbb, 0x0a, 0x9b, 0x8a, 0xe0, 0xa7, 0x92, 0xf0, 0x98, 0x7a, 0x34, 0xb8, 0x94, 0x9b, 0xd5,
	0x3d, 0xb6, 0x01, 0xf5, 0xb4, 0x77, 0x74, 0xe2, 0xe6, 0x22, 0xdd, 0x31, 0x85, 0xe6, 0x9b, 0x99,
	0x68, 0xbe, 0x7b, 0xb0, 0xca, 0x48, 0x22, 0x02, 0x2f, 0x60, 0x24, 0x12, 0x79, 0xd7, 0xeb, 0xd6,
	0x5a, 0xb1, 0x74, 0xa5, 0x7d, 0x7f, 0xad, 0xd0, 0x81, 0xe8, 0x3e, 0xd4, 0xd3, 0x09, 0xae, 0x8b,
	0xbf, 0xa9, 0x8a, 0x5f, 0xb6, 0x5f, 0x9c, 0x5a, 0x3a, 0xff, 0xd4, 0x61, 0xb5, 0xf4, 0x44, 0xf7,
	0x64, 0xe7, 0xfa, 0xd4, 0x9b, 0xb8, 0x4d, 0x40, 0x13, 0x4a, 0x6a, 0xba, 0x15, 0xd9, 0xc7, 0x3e,
	0xf5, 0x52, 0x97, 0x1e, 0x6c, 0x5e, 0x50, 0x22, 0xe9, 0x4f, 0x47, 0x82, 0x46, 0x5c, 0x5e, 0x65,
	0x13, 0x97, 0xca, 0x9a, 0xee, 0x02, 0x65, 0x75, 0x9c, 0x19, 0x75, 0x2b, 0x78, 0xe3, 0x62, 0x42,
	0x96, 0x82, 0x7e, 0x0c, 0x4b, 0xb2, 0xa7, 0x5c, 0x16, 0x06, 0x19, 0x96, 0xee, 0xa8, 0xa6, 0x6e,
	0x6f, 0x1a, 0xf9, 0x27, 0x61, 0x20, 0x3b, 0x93, 0xeb, 0xcf, 0xd4, 0xf3, 0x39, 0x6c, 0xc6, 0x91,
	0xcb, 0xa9, 0x18, 0x32, 0x37, 0x31, 0xa7, 0xcb, 0x20, 0x74, 0x17, 0xad, 0x2a, 0x88, 0xaf, 0xa2,
	0x9e, 0x34, 0x4a, 0x33, 0xd0, 0xad, 0xe0, 0xf5, 0xb8, 0x28, 0x4a, 0x21, 0x7f, 0x80, 0x5d, 0x09,
	0x99, 0x0f, 0xcc, 0x01, 0x19, 0xb9, 0xaf, 0x86, 0x24, 0x0c, 0xc4, 0xd8, 0xf5, 0x2e, 0x48, 0xd4,
	0xcf, 0x6f, 0x51, 0x5d, 0x83, 0xed, 0x34, 0x42, 0xe6, 0xf3, 0x94, 0x8c, 0x9e, 0x6b, 0x8f, 0x23,
	0xe5, 0xd0, 0xad, 0xe0, 0x3b, 0xf1, 0x15, 0xfa, 0x34, 0xf6, 0xd7, 0xb0, 0x15, 0x47, 0x6e, 0x5a,
	0x93, 0x7e, 0x42, 0xb9, 0xcc, 0x53, 0x16, 0x6e, 0x4e, 0x85, 0x5b, 0x37, 0xe1, 0x8e, 0x74, 0x5d,
	0x52, 0xab, 0x6e, 0x05, 0xb7, 0xe3, 0x49, 0x61, 0x11, 0x58, 0xb3, 0x2a, 0x3b, 0x9a, 0x9f, 0x01,
	0xd7, 0x0b, 0xc0, 0x8a, 0x2b, 0xd9, 0x66, 0x7d, 0x0d, 0x3c, 0x21, 0x4c, 0x81, 0xbf, 0x84, 0x8d,
	0x3e, 0x15, 0x2e, 0x19, 0xfa, 0x41, 0xec, 0x86, 0xf4, 0x92, 0x86, 0x19, 0x68, 0xc3, 0x1a, 0xd0,
	0x5f, 0x50, 0xf1, 0x99, 0x34, 0x79, 0x22, 0x2d, 0xba, 0x15, 0xbc, 0xda, 0xb7, 0x05, 0x29, 0xd8,
	0x8f, 0xb0, 0x27, 0xc1, 0x04, 0x1d, 0xb0, 0x38, 0x21, 0xa1, 0xab, 0x7a, 0xda, 0x3d, 0x67, 0xdc,
	0x3d, 0x8f, 0x13, 0x97, 0x33, 0x22, 0x02, 0x92, 0xe3, 0xcf, 0x2b, 0x7c, 0x27, 0xc5, 0x3f, 0x35,
	0x5e, 0x4f, 0xa4, 0xd3, 0x23, 0xc6, 0x1f, 0xc5, 0x49, 0x4f, 0x7b, 0x74, 0x2b, 0xf8, 0xed, 0xfe,
	0x55, 0x06, 0x69, 0xfc, 0x17, 0xb0, 0xc5, 0xa9, 0x70, 0x87, 0xcc, 0x64, 0x8a, 0x91, 0x21, 0xb7,
	0xb2, 0x04, 0x16, 0xbd, 0x7b, 0x54, 0xbc, 0x60, 0x2a, 0x27, 0x27, 0xca, 0x48, 0xd2, 0x9b, 0x4f,
	0xc8, 0x52, 0x58, 0x01, 0xef, 0x48, 0x58, 0xc9, 0x23, 0x3a, 0x62, 0xd4, 0x13, 0xd4, 0xcf, 0x0e,
	0xa3, 0x8f, 0x98, 0x46, 0x58, 0xb0, 0xae, 0x91, 0x1e, 0x15, 0x4f, 0xc9, 0xe8, 0xd8, 0x38, 0x98,
	0x9d, 0xaa, 0xbd, 0x77, 0x2b, 0x78, 0x9b, 0x4f, 0xd5, 0x9a, 0xa8, 0xf6, 0xfc, 0xaf, 0xc3, 0xac,
	0xe2, 0x85, 0x83, 0x60, 0x69, 0xb2, 0x2f, 0x9d, 0x0f, 0xa0, 0x6e, 0xfa, 0x2b, 0x9f, 0xa9, 0x55,
	0x7b, 0xa6, 0xae, 0xc2, 0xec, 0x79, 0x9c, 0x78, 0x34, 0x9d, 0xbf, 0x6a, 0xe1, 0xec, 0xc3, 0xe2,
	0x44, 0x4f, 0xc9, 0x39, 0x38, 0x08, 0x06, 0xd4, 0x15, 0xf9, 0x20, 0x6f, 0x48, 0x81, 0x9c, 0xe2,
	0xce, 0x25, 0xbc, 0x75, 0x55, 0x87, 0xc8, 0x57, 0x44, 0xe1, 0x7d, 0x97, 0xbe, 0x22, 0xec, 0x97,
	0x5d, 0x31, 0xc2, 0x4c, 0x31, 0x42, 0xf9, 0x8d, 0xe0, 0x7c, 0x04, 0xcb, 0xaf, 0xb5, 0x8a, 0xbc,
	0xff, 0xe2, 0x50, 0x87, 0x68, 0x62, 0xf9, 0x29, 0x25, 0x11, 0xfd, 0x5e, 0x61, 0x36, 0xb1, 0xfc,
	0x74, 0x56, 0xa4, 0xe3, 0x04, 0xeb, 0x9d, 0x45, 0x68, 0x15, 0xa8, 0xec, 0x3c, 0x80, 0x1b, 0x57,
	0x72, 0x0f, 0xb5, 0xa1, 0x6e, 0x6a, 0x6d, 0x52, 0x92, 0x2e, 0x9d, 0x3d, 0x58, 0x9a, 0x64, 0x11,
	0x5a, 0x87, 0x39, 0x4d, 0x3a, 0x65, 0xdc, 0xc0, 0x66, 0xe5, 0x1c, 0x42, 0x67, 0x3a, 0x1f, 0xca,
	0xeb, 0xe6, 0xfc, 0x51, 0x83, 0x4e, 0xd9, 0x6d, 0x96, 0xbf, 0xa8, 0xd2, 0x41, 0x53, 0xf2, 0xa2,
	0x32, 0x19, 0xcb, 0x5f, 0x54, 0x9e, 0x2d, 0x40, 0xdf, 0x41, 0xa7, 0xec, 0x1a, 0x28, 0x3c, 0xae,
	0x6e, 0x94, 0xde, 0x03, 0x16, 0x66, 0xfb, 0x62, 0x8a, 0x0e, 0xbd, 0x80, 0xf6, 0xeb, 0x53, 0xc5,
	0x80, 0xeb, 0x8b, 0xa1, 0xf3, 0xfa, 0x58, 0xb1, 0x90, 0xd7, 0xfa, 0x65, 0x0a, 0xf4, 0x4b, 0x15,
	0xde, 0x7d, 0xa3, 0x01, 0x63, 0x42, 0xe9, 0x0b, 0x64, 0xef, 0xbf, 0x27, 0x8c, 0x15, 0x7a, 0xa7,
	0xff, 0x26, 0x86, 0x76, 0x77, 0xee, 0x40, 0xab, 0x90, 0x6d, 0x59, 0xce, 0xb3, 0xb1, 0xa0, 0xdc,
	0xf0, 0x53, 0x2f, 0x9c, 0xbb, 0xd0, 0x9e, 0x96, 0xcb, 0x29, 0x1e, 0xc7, 0xb0, 0x56, 0x9a, 0x20,
	0xc5, 0x17, 0x29, 0x50, 0xe6, 0x55, 0xac, 0x17, 0x92, 0x7b, 0xc4, 0x13, 0xc1, 0x65, 0xda, 0xe8,
	0x66, 0xe5, 0x3c, 0x80, 0x9d, 0x37, 0x3a, 0xbc, 0xec, 0xa1, 0x73, 0x26, 0xf7, 0x50, 0xdb, 0x9d,
	0xc1, 0xf2, 0xf3, 0xf0, 0xa7, 0x2a, 0x80, 0x79, 0xc7, 0x87, 0x64, 0x8c, 0x12, 0xf3, 0xff, 0x50,
	0x20, 0x24, 0xba, 0x69, 0x9e, 0xb9, 0x53, 0xde, 0x5d, 0x9d, 0x5b, 0x53, 0xf5, 0x3a, 0xae, 0xb3,
	0xf5, 0xe7, 0xaf, 0xd5, 0x8d, 0xa5, 0x6a, 0x67, 0x05, 0xae, 0xc9, 0x17, 0x19, 0xb2, 0x9f, 0x69,
	0x0f, 0xb7, 0xbf, 0xbd, 0xd5, 0x0f, 0xc4, 0xc5, 0xf0, 0x6c, 0xdf, 0x8b, 0x07, 0x07, 0xe6, 0xfd,
	0x7a, 0xa0, 0xfe, 0xb1, 0xbd, 0x38, 0x3c, 0x48, 0x98, 0x77, 0x36, 0xa7, 0x56, 0xf7, 0xff, 0x0d,
	0x00, 0x00, 0xff, 0xff, 0x6b, 0xa6, 0xdd, 0xc7, 0xb7, 0x0f, 0x00, 0x00,
}
