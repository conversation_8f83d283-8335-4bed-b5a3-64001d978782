// Code generated by protoc-gen-psrpc v0.6.0, DO NOT EDIT.
// source: rpc/io.proto

package rpc

import (
	"context"

	"github.com/livekit/psrpc"
	"github.com/livekit/psrpc/pkg/client"
	"github.com/livekit/psrpc/pkg/info"
	"github.com/livekit/psrpc/pkg/rand"
	"github.com/livekit/psrpc/pkg/server"
	"github.com/livekit/psrpc/version"
)
import google_protobuf "google.golang.org/protobuf/types/known/emptypb"
import livekit4 "github.com/livekit/protocol/livekit"
import livekit5 "github.com/livekit/protocol/livekit"

var _ = version.PsrpcVersion_0_6

// =======================
// IOInfo Client Interface
// =======================

type IOInfoClient interface {
	// egress
	CreateEgress(ctx context.Context, req *livekit4.EgressInfo, opts ...psrpc.RequestOption) (*google_protobuf.Empty, error)

	UpdateEgress(ctx context.Context, req *livekit4.EgressInfo, opts ...psrpc.RequestOption) (*google_protobuf.Empty, error)

	GetEgress(ctx context.Context, req *GetEgressRequest, opts ...psrpc.RequestOption) (*livekit4.EgressInfo, error)

	ListEgress(ctx context.Context, req *livekit4.ListEgressRequest, opts ...psrpc.RequestOption) (*livekit4.ListEgressResponse, error)

	UpdateMetrics(ctx context.Context, req *UpdateMetricsRequest, opts ...psrpc.RequestOption) (*google_protobuf.Empty, error)

	// ingress
	CreateIngress(ctx context.Context, req *livekit5.IngressInfo, opts ...psrpc.RequestOption) (*google_protobuf.Empty, error)

	GetIngressInfo(ctx context.Context, req *GetIngressInfoRequest, opts ...psrpc.RequestOption) (*GetIngressInfoResponse, error)

	UpdateIngressState(ctx context.Context, req *UpdateIngressStateRequest, opts ...psrpc.RequestOption) (*google_protobuf.Empty, error)

	// sip
	GetSIPTrunkAuthentication(ctx context.Context, req *GetSIPTrunkAuthenticationRequest, opts ...psrpc.RequestOption) (*GetSIPTrunkAuthenticationResponse, error)

	EvaluateSIPDispatchRules(ctx context.Context, req *EvaluateSIPDispatchRulesRequest, opts ...psrpc.RequestOption) (*EvaluateSIPDispatchRulesResponse, error)

	UpdateSIPCallState(ctx context.Context, req *UpdateSIPCallStateRequest, opts ...psrpc.RequestOption) (*google_protobuf.Empty, error)

	// Close immediately, without waiting for pending RPCs
	Close()
}

// ===========================
// IOInfo ServerImpl Interface
// ===========================

type IOInfoServerImpl interface {
	// egress
	CreateEgress(context.Context, *livekit4.EgressInfo) (*google_protobuf.Empty, error)

	UpdateEgress(context.Context, *livekit4.EgressInfo) (*google_protobuf.Empty, error)

	GetEgress(context.Context, *GetEgressRequest) (*livekit4.EgressInfo, error)

	ListEgress(context.Context, *livekit4.ListEgressRequest) (*livekit4.ListEgressResponse, error)

	UpdateMetrics(context.Context, *UpdateMetricsRequest) (*google_protobuf.Empty, error)

	// ingress
	CreateIngress(context.Context, *livekit5.IngressInfo) (*google_protobuf.Empty, error)

	GetIngressInfo(context.Context, *GetIngressInfoRequest) (*GetIngressInfoResponse, error)

	UpdateIngressState(context.Context, *UpdateIngressStateRequest) (*google_protobuf.Empty, error)

	// sip
	GetSIPTrunkAuthentication(context.Context, *GetSIPTrunkAuthenticationRequest) (*GetSIPTrunkAuthenticationResponse, error)

	EvaluateSIPDispatchRules(context.Context, *EvaluateSIPDispatchRulesRequest) (*EvaluateSIPDispatchRulesResponse, error)

	UpdateSIPCallState(context.Context, *UpdateSIPCallStateRequest) (*google_protobuf.Empty, error)
}

// =======================
// IOInfo Server Interface
// =======================

type IOInfoServer interface {

	// Close and wait for pending RPCs to complete
	Shutdown()

	// Close immediately, without waiting for pending RPCs
	Kill()
}

// =============
// IOInfo Client
// =============

type iOInfoClient struct {
	client *client.RPCClient
}

// NewIOInfoClient creates a psrpc client that implements the IOInfoClient interface.
func NewIOInfoClient(bus psrpc.MessageBus, opts ...psrpc.ClientOption) (IOInfoClient, error) {
	sd := &info.ServiceDefinition{
		Name: "IOInfo",
		ID:   rand.NewClientID(),
	}

	sd.RegisterMethod("CreateEgress", false, false, true, true)
	sd.RegisterMethod("UpdateEgress", false, false, true, true)
	sd.RegisterMethod("GetEgress", false, false, true, true)
	sd.RegisterMethod("ListEgress", false, false, true, true)
	sd.RegisterMethod("UpdateMetrics", false, false, true, true)
	sd.RegisterMethod("CreateIngress", false, false, true, true)
	sd.RegisterMethod("GetIngressInfo", false, false, true, true)
	sd.RegisterMethod("UpdateIngressState", false, false, true, true)
	sd.RegisterMethod("GetSIPTrunkAuthentication", false, false, true, true)
	sd.RegisterMethod("EvaluateSIPDispatchRules", false, false, true, true)
	sd.RegisterMethod("UpdateSIPCallState", false, false, true, true)

	rpcClient, err := client.NewRPCClient(sd, bus, opts...)
	if err != nil {
		return nil, err
	}

	return &iOInfoClient{
		client: rpcClient,
	}, nil
}

func (c *iOInfoClient) CreateEgress(ctx context.Context, req *livekit4.EgressInfo, opts ...psrpc.RequestOption) (*google_protobuf.Empty, error) {
	return client.RequestSingle[*google_protobuf.Empty](ctx, c.client, "CreateEgress", nil, req, opts...)
}

func (c *iOInfoClient) UpdateEgress(ctx context.Context, req *livekit4.EgressInfo, opts ...psrpc.RequestOption) (*google_protobuf.Empty, error) {
	return client.RequestSingle[*google_protobuf.Empty](ctx, c.client, "UpdateEgress", nil, req, opts...)
}

func (c *iOInfoClient) GetEgress(ctx context.Context, req *GetEgressRequest, opts ...psrpc.RequestOption) (*livekit4.EgressInfo, error) {
	return client.RequestSingle[*livekit4.EgressInfo](ctx, c.client, "GetEgress", nil, req, opts...)
}

func (c *iOInfoClient) ListEgress(ctx context.Context, req *livekit4.ListEgressRequest, opts ...psrpc.RequestOption) (*livekit4.ListEgressResponse, error) {
	return client.RequestSingle[*livekit4.ListEgressResponse](ctx, c.client, "ListEgress", nil, req, opts...)
}

func (c *iOInfoClient) UpdateMetrics(ctx context.Context, req *UpdateMetricsRequest, opts ...psrpc.RequestOption) (*google_protobuf.Empty, error) {
	return client.RequestSingle[*google_protobuf.Empty](ctx, c.client, "UpdateMetrics", nil, req, opts...)
}

func (c *iOInfoClient) CreateIngress(ctx context.Context, req *livekit5.IngressInfo, opts ...psrpc.RequestOption) (*google_protobuf.Empty, error) {
	return client.RequestSingle[*google_protobuf.Empty](ctx, c.client, "CreateIngress", nil, req, opts...)
}

func (c *iOInfoClient) GetIngressInfo(ctx context.Context, req *GetIngressInfoRequest, opts ...psrpc.RequestOption) (*GetIngressInfoResponse, error) {
	return client.RequestSingle[*GetIngressInfoResponse](ctx, c.client, "GetIngressInfo", nil, req, opts...)
}

func (c *iOInfoClient) UpdateIngressState(ctx context.Context, req *UpdateIngressStateRequest, opts ...psrpc.RequestOption) (*google_protobuf.Empty, error) {
	return client.RequestSingle[*google_protobuf.Empty](ctx, c.client, "UpdateIngressState", nil, req, opts...)
}

func (c *iOInfoClient) GetSIPTrunkAuthentication(ctx context.Context, req *GetSIPTrunkAuthenticationRequest, opts ...psrpc.RequestOption) (*GetSIPTrunkAuthenticationResponse, error) {
	return client.RequestSingle[*GetSIPTrunkAuthenticationResponse](ctx, c.client, "GetSIPTrunkAuthentication", nil, req, opts...)
}

func (c *iOInfoClient) EvaluateSIPDispatchRules(ctx context.Context, req *EvaluateSIPDispatchRulesRequest, opts ...psrpc.RequestOption) (*EvaluateSIPDispatchRulesResponse, error) {
	return client.RequestSingle[*EvaluateSIPDispatchRulesResponse](ctx, c.client, "EvaluateSIPDispatchRules", nil, req, opts...)
}

func (c *iOInfoClient) UpdateSIPCallState(ctx context.Context, req *UpdateSIPCallStateRequest, opts ...psrpc.RequestOption) (*google_protobuf.Empty, error) {
	return client.RequestSingle[*google_protobuf.Empty](ctx, c.client, "UpdateSIPCallState", nil, req, opts...)
}

func (s *iOInfoClient) Close() {
	s.client.Close()
}

// =============
// IOInfo Server
// =============

type iOInfoServer struct {
	svc IOInfoServerImpl
	rpc *server.RPCServer
}

// NewIOInfoServer builds a RPCServer that will route requests
// to the corresponding method in the provided svc implementation.
func NewIOInfoServer(svc IOInfoServerImpl, bus psrpc.MessageBus, opts ...psrpc.ServerOption) (IOInfoServer, error) {
	sd := &info.ServiceDefinition{
		Name: "IOInfo",
		ID:   rand.NewServerID(),
	}

	s := server.NewRPCServer(sd, bus, opts...)

	sd.RegisterMethod("CreateEgress", false, false, true, true)
	var err error
	err = server.RegisterHandler(s, "CreateEgress", nil, svc.CreateEgress, nil)
	if err != nil {
		s.Close(false)
		return nil, err
	}

	sd.RegisterMethod("UpdateEgress", false, false, true, true)
	err = server.RegisterHandler(s, "UpdateEgress", nil, svc.UpdateEgress, nil)
	if err != nil {
		s.Close(false)
		return nil, err
	}

	sd.RegisterMethod("GetEgress", false, false, true, true)
	err = server.RegisterHandler(s, "GetEgress", nil, svc.GetEgress, nil)
	if err != nil {
		s.Close(false)
		return nil, err
	}

	sd.RegisterMethod("ListEgress", false, false, true, true)
	err = server.RegisterHandler(s, "ListEgress", nil, svc.ListEgress, nil)
	if err != nil {
		s.Close(false)
		return nil, err
	}

	sd.RegisterMethod("UpdateMetrics", false, false, true, true)
	err = server.RegisterHandler(s, "UpdateMetrics", nil, svc.UpdateMetrics, nil)
	if err != nil {
		s.Close(false)
		return nil, err
	}

	sd.RegisterMethod("CreateIngress", false, false, true, true)
	err = server.RegisterHandler(s, "CreateIngress", nil, svc.CreateIngress, nil)
	if err != nil {
		s.Close(false)
		return nil, err
	}

	sd.RegisterMethod("GetIngressInfo", false, false, true, true)
	err = server.RegisterHandler(s, "GetIngressInfo", nil, svc.GetIngressInfo, nil)
	if err != nil {
		s.Close(false)
		return nil, err
	}

	sd.RegisterMethod("UpdateIngressState", false, false, true, true)
	err = server.RegisterHandler(s, "UpdateIngressState", nil, svc.UpdateIngressState, nil)
	if err != nil {
		s.Close(false)
		return nil, err
	}

	sd.RegisterMethod("GetSIPTrunkAuthentication", false, false, true, true)
	err = server.RegisterHandler(s, "GetSIPTrunkAuthentication", nil, svc.GetSIPTrunkAuthentication, nil)
	if err != nil {
		s.Close(false)
		return nil, err
	}

	sd.RegisterMethod("EvaluateSIPDispatchRules", false, false, true, true)
	err = server.RegisterHandler(s, "EvaluateSIPDispatchRules", nil, svc.EvaluateSIPDispatchRules, nil)
	if err != nil {
		s.Close(false)
		return nil, err
	}

	sd.RegisterMethod("UpdateSIPCallState", false, false, true, true)
	err = server.RegisterHandler(s, "UpdateSIPCallState", nil, svc.UpdateSIPCallState, nil)
	if err != nil {
		s.Close(false)
		return nil, err
	}

	return &iOInfoServer{
		svc: svc,
		rpc: s,
	}, nil
}

func (s *iOInfoServer) Shutdown() {
	s.rpc.Close(false)
}

func (s *iOInfoServer) Kill() {
	s.rpc.Close(true)
}

var psrpcFileDescriptor4 = []byte{
	// 1713 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x18, 0xdb, 0x52, 0x1b, 0xc9,
	0x35, 0x42, 0x42, 0xa0, 0x23, 0x81, 0x44, 0x23, 0xa8, 0x41, 0x24, 0x36, 0xd6, 0xc6, 0x59, 0x9c,
	0x54, 0x89, 0x2c, 0x79, 0x48, 0xb2, 0x5b, 0xbb, 0xb5, 0x80, 0x65, 0x7b, 0x12, 0x6c, 0x2b, 0x03,
	0x3c, 0x24, 0x2f, 0x93, 0x66, 0xa6, 0x11, 0xbd, 0x8c, 0xa6, 0x27, 0xdd, 0x3d, 0xd8, 0x7c, 0x41,
	0xf2, 0x92, 0x7f, 0xc9, 0x17, 0xa4, 0x2a, 0xdf, 0x90, 0x8f, 0x49, 0x1e, 0x53, 0x7d, 0x99, 0xd1,
	0x08, 0x8d, 0x6c, 0xc8, 0x93, 0xa6, 0xcf, 0xfd, 0xd6, 0xa7, 0xcf, 0x11, 0xb4, 0x78, 0x12, 0x1c,
	0x50, 0x36, 0x48, 0x38, 0x93, 0x0c, 0x55, 0x79, 0x12, 0xf4, 0xba, 0x11, 0xbd, 0x25, 0x37, 0x54,
	0xfa, 0x64, 0xcc, 0x89, 0x10, 0x06, 0xd5, 0xdb, 0xca, 0xa0, 0x34, 0x2e, 0x82, 0x37, 0x32, 0xb0,
	0xa0, 0x89, 0x05, 0xa1, 0x0c, 0xc4, 0x19, 0x9b, 0x58, 0xd8, 0xee, 0x98, 0xb1, 0x71, 0x44, 0x0e,
	0xf4, 0xe9, 0x32, 0xbd, 0x3a, 0x20, 0x93, 0x44, 0xde, 0x59, 0xe4, 0x93, 0xfb, 0xc8, 0x30, 0xe5,
	0x58, 0x52, 0x16, 0x1b, 0x7c, 0xff, 0x00, 0x3a, 0xaf, 0x89, 0x1c, 0x6a, 0xb5, 0x1e, 0xf9, 0x4b,
	0x4a, 0x84, 0x44, 0xbb, 0xd0, 0x30, 0xe6, 0xf9, 0x34, 0x74, 0x2a, 0x7b, 0x95, 0xfd, 0x86, 0xb7,
	0x6a, 0x00, 0x6e, 0xd8, 0xff, 0x6b, 0x05, 0xba, 0x17, 0x49, 0x88, 0x25, 0x79, 0x4b, 0x24, 0xa7,
	0x41, 0xce, 0xf5, 0x25, 0xd4, 0x68, 0x7c, 0xc5, 0x34, 0x43, 0xf3, 0x70, 0x73, 0x60, 0x2d, 0x1d,
	0x18, 0xd9, 0x6e, 0x7c, 0xc5, 0x3c, 0x4d, 0x80, 0xfa, 0xb0, 0x86, 0x6f, 0xc7, 0x7e, 0x90, 0xa4,
	0x7e, 0x2a, 0xf0, 0x98, 0x38, 0xd5, 0xbd, 0xca, 0xfe, 0x92, 0xd7, 0xc4, 0xb7, 0xe3, 0x93, 0x24,
	0xbd, 0x50, 0x20, 0x45, 0x33, 0xc1, 0x1f, 0x0b, 0x34, 0x35, 0x43, 0x33, 0xc1, 0x1f, 0x33, 0x9a,
	0xfe, 0x05, 0x6c, 0xbd, 0x26, 0xd2, 0x8d, 0xa7, 0xf2, 0xad, 0x25, 0x3f, 0x01, 0xb0, 0x81, 0x9c,
	0x3a, 0xd0, 0xb0, 0x10, 0x37, 0x54, 0x68, 0x21, 0x39, 0xc1, 0x13, 0xff, 0x86, 0xdc, 0x39, 0x4b,
	0x06, 0x6d, 0x20, 0xbf, 0x27, 0x77, 0xfd, 0xbf, 0x2d, 0xc1, 0xf6, 0x7d, 0xb9, 0x22, 0x61, 0xb1,
	0x20, 0x68, 0x7f, 0xc6, 0xc5, 0x6e, 0xee, 0x62, 0x91, 0xd6, 0xf8, 0xd8, 0x85, 0x65, 0xc9, 0x6e,
	0x48, 0x6c, 0xc5, 0x9b, 0x03, 0xda, 0x82, 0xfa, 0x07, 0xe1, 0xa7, 0x3c, 0xd2, 0x2e, 0x37, 0xbc,
	0xe5, 0x0f, 0xe2, 0x82, 0x47, 0xe8, 0x02, 0xd6, 0x23, 0x36, 0x1e, 0xd3, 0x78, 0xec, 0x5f, 0x51,
	0x12, 0x85, 0xc2, 0xa9, 0xed, 0x55, 0xf7, 0x9b, 0x87, 0x83, 0x01, 0x4f, 0x82, 0x41, 0xb9, 0x2d,
	0x83, 0x53, 0xc3, 0xf1, 0x4a, 0x33, 0x0c, 0x63, 0xc9, 0xef, 0xbc, 0xb5, 0xa8, 0x08, 0xeb, 0x7d,
	0x0f, 0x68, 0x9e, 0x08, 0x75, 0xa0, 0xaa, 0xdc, 0x36, 0x51, 0x51, 0x9f, 0xca, 0xd6, 0x5b, 0x1c,
	0xa5, 0x24, 0xb3, 0x55, 0x1f, 0xbe, 0x5e, 0xfa, 0x4d, 0xa5, 0x3f, 0x86, 0x1d, 0x93, 0x6a, 0x6b,
	0xc0, 0x99, 0xc4, 0x92, 0x3c, 0x30, 0xca, 0xbf, 0x80, 0x65, 0xa1, 0xc8, 0xb5, 0xd4, 0xe6, 0xe1,
	0xd6, 0xfd, 0x60, 0x19, 0x59, 0x86, 0xa6, 0xff, 0x9f, 0x0a, 0xec, 0xbd, 0x26, 0xf2, 0xcc, 0x1d,
	0x9d, 0xf3, 0x34, 0xbe, 0x39, 0x4a, 0xe5, 0x35, 0x89, 0x25, 0x0d, 0x74, 0xa5, 0x66, 0x0a, 0xfb,
	0xd0, 0x14, 0x34, 0xf1, 0x03, 0x1c, 0x45, 0x4a, 0x63, 0x5d, 0x69, 0x3c, 0x5e, 0x72, 0x2a, 0x5e,
	0x43, 0xd0, 0xe4, 0x04, 0x47, 0x91, 0x1b, 0xa2, 0x6d, 0xa8, 0x5d, 0x71, 0x36, 0x31, 0xae, 0x68,
	0xa4, 0x3e, 0xa3, 0xa7, 0xd0, 0x50, 0xbf, 0xfe, 0x35, 0x13, 0xd2, 0x59, 0xc9, 0x91, 0xab, 0x0a,
	0xf8, 0x86, 0x09, 0x89, 0x10, 0x2c, 0x49, 0x66, 0xd2, 0xa2, 0x31, 0x4b, 0x92, 0xa1, 0x5d, 0x58,
	0x91, 0xcc, 0xb0, 0x2c, 0xe7, 0x88, 0xba, 0x64, 0x9a, 0xe1, 0x0b, 0x68, 0x0a, 0x1e, 0xf8, 0x38,
	0x0c, 0x95, 0x37, 0xba, 0x3e, 0x0d, 0x01, 0x08, 0x1e, 0x1c, 0x19, 0x28, 0xda, 0x83, 0x9a, 0x32,
	0xd7, 0x59, 0xd5, 0x31, 0x68, 0xe9, 0x7c, 0x9e, 0xb9, 0x23, 0x65, 0xac, 0xa7, 0x31, 0xfd, 0x7f,
	0x54, 0xe0, 0xd9, 0x27, 0x3c, 0xb7, 0x85, 0xd7, 0x83, 0xd5, 0x54, 0x10, 0x1e, 0xe3, 0x09, 0xc9,
	0x2e, 0x64, 0x76, 0x56, 0xb8, 0x04, 0x0b, 0xf1, 0x81, 0xf1, 0xd0, 0x66, 0x30, 0x3f, 0x23, 0x04,
	0xb5, 0x90, 0xb3, 0x44, 0xfb, 0xb5, 0xea, 0xe9, 0x6f, 0xb4, 0x07, 0x2d, 0x15, 0x46, 0xa9, 0xd4,
	0xa9, 0x38, 0x6a, 0xcb, 0x3d, 0x10, 0x34, 0xd1, 0x16, 0x98, 0x0b, 0x92, 0x70, 0xf6, 0x03, 0x09,
	0xa4, 0xc2, 0x2f, 0x9b, 0xcc, 0x5a, 0x88, 0x1b, 0xf6, 0xff, 0x5d, 0x83, 0xa7, 0x43, 0x55, 0x24,
	0x58, 0x92, 0x33, 0x77, 0xf4, 0x92, 0x8a, 0x04, 0xcb, 0xe0, 0xda, 0x4b, 0x23, 0x22, 0x16, 0xe4,
	0x6a, 0xb5, 0x2c, 0x57, 0xbf, 0x04, 0xa4, 0x68, 0x12, 0xcc, 0x25, 0x0d, 0x68, 0x82, 0x63, 0x99,
	0x17, 0x92, 0x26, 0xed, 0x08, 0x9a, 0x8c, 0xa6, 0x48, 0x37, 0x9c, 0x33, 0x1d, 0xe6, 0x4c, 0x7f,
	0x01, 0xeb, 0x4a, 0xa7, 0xba, 0x4a, 0x71, 0x3a, 0xb9, 0x24, 0xbc, 0x50, 0x09, 0x6b, 0x16, 0xf3,
	0x4e, 0x23, 0xd0, 0x73, 0x68, 0x65, 0xa4, 0x3a, 0xc5, 0xcd, 0x9c, 0xb0, 0x69, 0xe1, 0x3a, 0xcf,
	0x5f, 0x82, 0xe6, 0x23, 0x61, 0x26, 0x70, 0x5a, 0x23, 0x2d, 0x83, 0xb0, 0xf2, 0x1e, 0x54, 0x10,
	0x1d, 0xa8, 0x26, 0x34, 0xb6, 0x31, 0x55, 0x9f, 0xaa, 0x27, 0xc4, 0xcc, 0x57, 0xc0, 0xba, 0x4e,
	0xd2, 0x72, 0xcc, 0x46, 0x34, 0x56, 0xd2, 0xac, 0xda, 0x7b, 0x25, 0x0b, 0x06, 0xac, 0x6d, 0x0b,
	0xa1, 0x43, 0x3e, 0x4a, 0x8e, 0x7d, 0x2c, 0x25, 0xa7, 0x97, 0xa9, 0x24, 0xc2, 0x69, 0xe8, 0xd6,
	0xf1, 0x5b, 0x5d, 0x6a, 0x9f, 0xc9, 0xd2, 0x60, 0xa8, 0x98, 0x8f, 0x72, 0x5e, 0xd3, 0x45, 0xda,
	0x64, 0x16, 0x9a, 0x17, 0x71, 0x6b, 0x51, 0x11, 0xf7, 0x8e, 0xa1, 0x5b, 0x26, 0xea, 0x51, 0xbd,
	0xe6, 0x9f, 0x2d, 0xd8, 0x5b, 0x6c, 0xaf, 0xbd, 0x07, 0xbb, 0xd0, 0x50, 0x0f, 0x9f, 0x5f, 0xbc,
	0x08, 0x0a, 0xf0, 0x4e, 0x5d, 0x84, 0xaf, 0xa0, 0x3b, 0x5b, 0x4b, 0xea, 0x22, 0xc9, 0xac, 0xc3,
	0x6f, 0x26, 0xc5, 0x52, 0x32, 0x28, 0xf4, 0x02, 0x3a, 0x45, 0x16, 0x2d, 0x56, 0x87, 0xda, 0x6b,
	0x17, 0xe0, 0x65, 0xd2, 0x27, 0x44, 0xe2, 0x10, 0x4b, 0x6c, 0x4a, 0x7b, 0x46, 0xfa, 0x5b, 0x8b,
	0x42, 0x1f, 0x60, 0xbb, 0xc8, 0x52, 0x48, 0x52, 0x53, 0x27, 0xe9, 0xfb, 0xcf, 0x24, 0xc9, 0x76,
	0xfa, 0xc2, 0x1d, 0xb8, 0x9f, 0xab, 0xad, 0xa4, 0x0c, 0xa7, 0x8a, 0x87, 0x9b, 0x14, 0xeb, 0xc2,
	0xd2, 0xb7, 0xdf, 0x14, 0x8f, 0x05, 0xab, 0x0a, 0xcb, 0x9f, 0xa8, 0x5a, 0xf9, 0x13, 0xb5, 0x5c,
	0x7c, 0xa2, 0x06, 0x50, 0xe7, 0x44, 0xa4, 0x91, 0xd4, 0x55, 0xba, 0x7e, 0xb8, 0x9d, 0x55, 0x41,
	0x6e, 0xb2, 0xc6, 0x7a, 0x96, 0x6a, 0xee, 0xa6, 0x36, 0xe6, 0x6e, 0xea, 0x01, 0x74, 0x15, 0x45,
	0x68, 0xf9, 0x7d, 0x9e, 0x46, 0x64, 0x7a, 0xa7, 0x37, 0x04, 0x4d, 0x8a, 0xd1, 0x98, 0xeb, 0x4a,
	0xad, 0x7b, 0x5d, 0x09, 0x9d, 0xc2, 0xca, 0x35, 0xc1, 0x21, 0xe1, 0xc2, 0x59, 0xd3, 0xd1, 0x3d,
	0x7c, 0x58, 0x74, 0xdf, 0x18, 0x26, 0x13, 0xcf, 0x4c, 0x04, 0xe2, 0xb0, 0x65, 0x3f, 0x7d, 0xc9,
	0x8a, 0x99, 0x5b, 0xd7, 0xb2, 0xbf, 0x7b, 0x94, 0xec, 0x73, 0x76, 0x3f, 0x6f, 0x9b, 0xd7, 0xf3,
	0x18, 0xa5, 0x73, 0xaa, 0x48, 0xa9, 0xcd, 0xfc, 0x41, 0x8f, 0xd1, 0x39, 0x15, 0x78, 0xce, 0x66,
	0x7c, 0xdb, 0xc4, 0xf3, 0x18, 0x74, 0x0c, 0x6d, 0x1a, 0x07, 0x51, 0x1a, 0x92, 0x5c, 0xdb, 0xa6,
	0x4e, 0xf0, 0x4e, 0xfe, 0x5e, 0x9f, 0xb9, 0x23, 0x43, 0xfd, 0x3e, 0x51, 0x6f, 0x92, 0xf0, 0xd6,
	0x2d, 0x47, 0x26, 0xe3, 0x3b, 0xe8, 0x90, 0x18, 0x5f, 0xaa, 0x5e, 0x75, 0x45, 0xb0, 0x4c, 0x39,
	0x11, 0x4e, 0x7b, 0xaf, 0xba, 0xbf, 0x5e, 0x18, 0x02, 0xcf, 0xdc, 0xd1, 0x2b, 0x83, 0xf3, 0xda,
	0x96, 0xd8, 0x9e, 0xb5, 0x0d, 0x9c, 0xc6, 0x7a, 0xfc, 0x91, 0x74, 0x42, 0x58, 0x2a, 0x9d, 0x8e,
	0x6e, 0x35, 0x3b, 0x03, 0x33, 0xbc, 0x0e, 0xb2, 0xe1, 0x75, 0xf0, 0xd2, 0x0e, 0xaf, 0xde, 0xba,
	0xe5, 0x38, 0x37, 0x0c, 0x68, 0x08, 0x1b, 0x7a, 0x5e, 0x54, 0xef, 0x4d, 0x36, 0xe1, 0x3a, 0x1b,
	0x9f, 0x93, 0xd2, 0x56, 0xe3, 0x24, 0x8e, 0xa2, 0x0c, 0x80, 0x9e, 0x42, 0x53, 0xf7, 0x97, 0x84,
	0x13, 0x41, 0xa4, 0xd3, 0x35, 0x55, 0xab, 0x40, 0x23, 0x0d, 0x41, 0xdf, 0x58, 0x82, 0x80, 0xc5,
	0x57, 0x74, 0xec, 0x6c, 0x69, 0x0d, 0xbd, 0xdc, 0x4d, 0x8f, 0xb1, 0xc9, 0x89, 0x46, 0x65, 0x2a,
	0x34, 0xb3, 0x01, 0xa1, 0x57, 0xd0, 0x99, 0x90, 0x90, 0x62, 0x9f, 0xc4, 0x01, 0xbf, 0xd3, 0xd1,
	0x74, 0xb6, 0x75, 0xb4, 0x77, 0x8b, 0x81, 0x7a, 0xab, 0x68, 0x86, 0x39, 0x89, 0xd7, 0x9e, 0xcc,
	0x02, 0x7a, 0x6f, 0xa0, 0xb7, 0xb8, 0x27, 0x3c, 0xa6, 0xe9, 0xf6, 0xbe, 0x86, 0x56, 0xb1, 0x46,
	0x1e, 0xc5, 0xfb, 0x0a, 0x9c, 0x45, 0xf5, 0xfd, 0x58, 0x39, 0x8b, 0x6a, 0xf6, 0x51, 0x0f, 0xc8,
	0xdf, 0x2b, 0xd9, 0xb4, 0x6a, 0x1f, 0xa7, 0x99, 0x69, 0xf5, 0x2b, 0x68, 0x98, 0x61, 0xa4, 0x6c,
	0x7e, 0xb7, 0x0c, 0x7a, 0xbe, 0x5e, 0x0d, 0xec, 0x17, 0xfa, 0x16, 0xd6, 0x24, 0xc7, 0xb1, 0xb8,
	0x22, 0xdc, 0xb0, 0x99, 0x49, 0xd6, 0x29, 0xb2, 0x9d, 0x5b, 0x02, 0xcd, 0xda, 0x92, 0x85, 0x53,
	0xff, 0xbf, 0x15, 0x58, 0xb1, 0x82, 0xd1, 0x8f, 0x01, 0xa2, 0x9b, 0x7c, 0x1a, 0xb2, 0x0f, 0x57,
	0x74, 0x63, 0x07, 0xa1, 0x5d, 0x68, 0x08, 0x96, 0xf2, 0x80, 0xf8, 0x34, 0xc9, 0x46, 0x38, 0x03,
	0x70, 0x13, 0xf4, 0x02, 0x56, 0xb2, 0x91, 0xa2, 0xaa, 0xf5, 0xb7, 0x8b, 0xfa, 0x2f, 0x38, 0xf5,
	0x32, 0x3c, 0xfa, 0xc2, 0x0e, 0xbf, 0xb5, 0x72, 0xba, 0x6c, 0x12, 0x56, 0x83, 0xee, 0x72, 0x39,
	0x89, 0x9a, 0x7a, 0x9f, 0x41, 0xf5, 0x96, 0x62, 0xa7, 0xae, 0x9b, 0xce, 0x1c, 0x85, 0xc2, 0xa1,
	0x27, 0xb3, 0xd3, 0xdd, 0x8a, 0x5d, 0xa1, 0xb2, 0xc9, 0xee, 0xe7, 0x7f, 0x86, 0x8d, 0xb9, 0xa7,
	0x01, 0x39, 0xd0, 0x3d, 0x1d, 0xbe, 0x3e, 0x3a, 0xf9, 0xa3, 0x7f, 0x74, 0x72, 0x32, 0x1c, 0x9d,
	0xfb, 0xef, 0x3d, 0x7f, 0xe4, 0xbe, 0xeb, 0xfc, 0x08, 0x01, 0xd4, 0x0d, 0xa8, 0x53, 0x41, 0x6d,
	0x68, 0x7a, 0xc3, 0x3f, 0x5c, 0x0c, 0xcf, 0xce, 0x35, 0x72, 0x49, 0x21, 0xbd, 0xe1, 0xef, 0x86,
	0x27, 0xe7, 0x9d, 0x2a, 0x5a, 0x85, 0xda, 0x4b, 0xef, 0xfd, 0xa8, 0x53, 0x3b, 0xfc, 0x57, 0x1d,
	0xea, 0xee, 0x7b, 0x9d, 0xa6, 0x6f, 0xa0, 0x75, 0xc2, 0x09, 0x96, 0xc4, 0x2c, 0x9a, 0xa8, 0x6c,
	0xf3, 0xec, 0x6d, 0xcf, 0x35, 0x81, 0xa1, 0x5a, 0x92, 0x15, 0xb3, 0xa9, 0x99, 0xff, 0x87, 0xf9,
	0xd7, 0xd0, 0xc8, 0x77, 0x67, 0xb4, 0x95, 0x2d, 0x6b, 0x33, 0xbb, 0x74, 0xaf, 0x4c, 0x20, 0x1a,
	0x02, 0x9c, 0x52, 0x91, 0x71, 0x4e, 0xdb, 0xc7, 0x14, 0x98, 0xb1, 0xef, 0x96, 0xe2, 0xec, 0x34,
	0x74, 0x0c, 0x6b, 0x33, 0x9b, 0x38, 0xda, 0xd1, 0x36, 0x94, 0x6d, 0xe7, 0x0b, 0x7d, 0xf8, 0x16,
	0xd6, 0x4c, 0xf4, 0xec, 0x5a, 0x86, 0x4a, 0xb7, 0xda, 0x85, 0xec, 0x2e, 0xac, 0xcf, 0xee, 0xa7,
	0xa8, 0x57, 0xba, 0xb4, 0x66, 0xde, 0x2c, 0x5e, 0x68, 0xd1, 0x29, 0xa0, 0xf9, 0x65, 0x13, 0x3d,
	0x29, 0xb8, 0x54, 0xb2, 0x85, 0x2e, 0x34, 0xec, 0x07, 0xd8, 0x59, 0xb8, 0x56, 0xa1, 0xe7, 0x99,
	0x1d, 0x9f, 0x5c, 0x38, 0x7b, 0x3f, 0xfb, 0x1c, 0x99, 0xb5, 0x7c, 0x0c, 0xce, 0xa2, 0x67, 0x19,
	0xfd, 0xf4, 0x21, 0x83, 0x78, 0xef, 0xf9, 0x83, 0xde, 0xf6, 0x69, 0x88, 0x8a, 0x1d, 0x6e, 0x26,
	0x44, 0x25, 0xad, 0x6f, 0x51, 0x88, 0x8e, 0x9f, 0xfd, 0xe9, 0xe9, 0x98, 0xca, 0xeb, 0xf4, 0x72,
	0x10, 0xb0, 0xc9, 0x81, 0xcd, 0xba, 0xf9, 0xa3, 0x28, 0x60, 0xd1, 0x01, 0x4f, 0x82, 0xcb, 0xba,
	0x3e, 0xfd, 0xea, 0x7f, 0x01, 0x00, 0x00, 0xff, 0xff, 0x8a, 0xf7, 0xbf, 0x78, 0xca, 0x12, 0x00,
	0x00,
}
