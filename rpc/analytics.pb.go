// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc/analytics.proto

package rpc

import (
	livekit "github.com/livekit/protocol/livekit"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_rpc_analytics_proto protoreflect.FileDescriptor

const file_rpc_analytics_proto_rawDesc = "" +
	"\n" +
	"\x13rpc/analytics.proto\x12\alivekit\x1a\x1bgoogle/protobuf/empty.proto\x1a\x17livekit_analytics.proto2\xf5\x01\n" +
	"\x18AnalyticsRecorderService\x12B\n" +
	"\vIngestStats\x12\x17.livekit.AnalyticsStats\x1a\x16.google.protobuf.Empty\"\x00(\x01\x12D\n" +
	"\fIngestEvents\x12\x18.livekit.AnalyticsEvents\x1a\x16.google.protobuf.Empty\"\x00(\x01\x12O\n" +
	"\x14IngestNodeRoomStates\x12\x1b.livekit.AnalyticsNodeRooms\x1a\x16.google.protobuf.Empty\"\x00(\x01B!Z\x1fgithub.com/livekit/protocol/rpcb\x06proto3"

var file_rpc_analytics_proto_goTypes = []any{
	(*livekit.AnalyticsStats)(nil),     // 0: livekit.AnalyticsStats
	(*livekit.AnalyticsEvents)(nil),    // 1: livekit.AnalyticsEvents
	(*livekit.AnalyticsNodeRooms)(nil), // 2: livekit.AnalyticsNodeRooms
	(*emptypb.Empty)(nil),              // 3: google.protobuf.Empty
}
var file_rpc_analytics_proto_depIdxs = []int32{
	0, // 0: livekit.AnalyticsRecorderService.IngestStats:input_type -> livekit.AnalyticsStats
	1, // 1: livekit.AnalyticsRecorderService.IngestEvents:input_type -> livekit.AnalyticsEvents
	2, // 2: livekit.AnalyticsRecorderService.IngestNodeRoomStates:input_type -> livekit.AnalyticsNodeRooms
	3, // 3: livekit.AnalyticsRecorderService.IngestStats:output_type -> google.protobuf.Empty
	3, // 4: livekit.AnalyticsRecorderService.IngestEvents:output_type -> google.protobuf.Empty
	3, // 5: livekit.AnalyticsRecorderService.IngestNodeRoomStates:output_type -> google.protobuf.Empty
	3, // [3:6] is the sub-list for method output_type
	0, // [0:3] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_rpc_analytics_proto_init() }
func file_rpc_analytics_proto_init() {
	if File_rpc_analytics_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_analytics_proto_rawDesc), len(file_rpc_analytics_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rpc_analytics_proto_goTypes,
		DependencyIndexes: file_rpc_analytics_proto_depIdxs,
	}.Build()
	File_rpc_analytics_proto = out.File
	file_rpc_analytics_proto_goTypes = nil
	file_rpc_analytics_proto_depIdxs = nil
}
