// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc/egress.proto

package rpc

import (
	livekit "github.com/livekit/protocol/livekit"
	_ "github.com/livekit/psrpc/protoc-gen-psrpc/options"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type StartEgressRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// request metadata
	EgressId string `protobuf:"bytes,1,opt,name=egress_id,json=egressId,proto3" json:"egress_id,omitempty"`
	// request
	//
	// Types that are valid to be assigned to Request:
	//
	//	*StartEgressRequest_RoomComposite
	//	*StartEgressRequest_Web
	//	*StartEgressRequest_Participant
	//	*StartEgressRequest_TrackComposite
	//	*StartEgressRequest_Track
	Request isStartEgressRequest_Request `protobuf_oneof:"request"`
	// connection info
	RoomId string `protobuf:"bytes,3,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	Token  string `protobuf:"bytes,8,opt,name=token,proto3" json:"token,omitempty"`
	WsUrl  string `protobuf:"bytes,9,opt,name=ws_url,json=wsUrl,proto3" json:"ws_url,omitempty"`
	// cloud only
	CloudBackupEnabled bool    `protobuf:"varint,10,opt,name=cloud_backup_enabled,json=cloudBackupEnabled,proto3" json:"cloud_backup_enabled,omitempty"`
	EstimatedCpu       float64 `protobuf:"fixed64,14,opt,name=estimated_cpu,json=estimatedCpu,proto3" json:"estimated_cpu,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *StartEgressRequest) Reset() {
	*x = StartEgressRequest{}
	mi := &file_rpc_egress_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartEgressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartEgressRequest) ProtoMessage() {}

func (x *StartEgressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_egress_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartEgressRequest.ProtoReflect.Descriptor instead.
func (*StartEgressRequest) Descriptor() ([]byte, []int) {
	return file_rpc_egress_proto_rawDescGZIP(), []int{0}
}

func (x *StartEgressRequest) GetEgressId() string {
	if x != nil {
		return x.EgressId
	}
	return ""
}

func (x *StartEgressRequest) GetRequest() isStartEgressRequest_Request {
	if x != nil {
		return x.Request
	}
	return nil
}

func (x *StartEgressRequest) GetRoomComposite() *livekit.RoomCompositeEgressRequest {
	if x != nil {
		if x, ok := x.Request.(*StartEgressRequest_RoomComposite); ok {
			return x.RoomComposite
		}
	}
	return nil
}

func (x *StartEgressRequest) GetWeb() *livekit.WebEgressRequest {
	if x != nil {
		if x, ok := x.Request.(*StartEgressRequest_Web); ok {
			return x.Web
		}
	}
	return nil
}

func (x *StartEgressRequest) GetParticipant() *livekit.ParticipantEgressRequest {
	if x != nil {
		if x, ok := x.Request.(*StartEgressRequest_Participant); ok {
			return x.Participant
		}
	}
	return nil
}

func (x *StartEgressRequest) GetTrackComposite() *livekit.TrackCompositeEgressRequest {
	if x != nil {
		if x, ok := x.Request.(*StartEgressRequest_TrackComposite); ok {
			return x.TrackComposite
		}
	}
	return nil
}

func (x *StartEgressRequest) GetTrack() *livekit.TrackEgressRequest {
	if x != nil {
		if x, ok := x.Request.(*StartEgressRequest_Track); ok {
			return x.Track
		}
	}
	return nil
}

func (x *StartEgressRequest) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *StartEgressRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *StartEgressRequest) GetWsUrl() string {
	if x != nil {
		return x.WsUrl
	}
	return ""
}

func (x *StartEgressRequest) GetCloudBackupEnabled() bool {
	if x != nil {
		return x.CloudBackupEnabled
	}
	return false
}

func (x *StartEgressRequest) GetEstimatedCpu() float64 {
	if x != nil {
		return x.EstimatedCpu
	}
	return 0
}

type isStartEgressRequest_Request interface {
	isStartEgressRequest_Request()
}

type StartEgressRequest_RoomComposite struct {
	RoomComposite *livekit.RoomCompositeEgressRequest `protobuf:"bytes,5,opt,name=room_composite,json=roomComposite,proto3,oneof"`
}

type StartEgressRequest_Web struct {
	Web *livekit.WebEgressRequest `protobuf:"bytes,11,opt,name=web,proto3,oneof"`
}

type StartEgressRequest_Participant struct {
	Participant *livekit.ParticipantEgressRequest `protobuf:"bytes,13,opt,name=participant,proto3,oneof"`
}

type StartEgressRequest_TrackComposite struct {
	TrackComposite *livekit.TrackCompositeEgressRequest `protobuf:"bytes,6,opt,name=track_composite,json=trackComposite,proto3,oneof"`
}

type StartEgressRequest_Track struct {
	Track *livekit.TrackEgressRequest `protobuf:"bytes,7,opt,name=track,proto3,oneof"`
}

func (*StartEgressRequest_RoomComposite) isStartEgressRequest_Request() {}

func (*StartEgressRequest_Web) isStartEgressRequest_Request() {}

func (*StartEgressRequest_Participant) isStartEgressRequest_Request() {}

func (*StartEgressRequest_TrackComposite) isStartEgressRequest_Request() {}

func (*StartEgressRequest_Track) isStartEgressRequest_Request() {}

type ListActiveEgressRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListActiveEgressRequest) Reset() {
	*x = ListActiveEgressRequest{}
	mi := &file_rpc_egress_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListActiveEgressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListActiveEgressRequest) ProtoMessage() {}

func (x *ListActiveEgressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_egress_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListActiveEgressRequest.ProtoReflect.Descriptor instead.
func (*ListActiveEgressRequest) Descriptor() ([]byte, []int) {
	return file_rpc_egress_proto_rawDescGZIP(), []int{1}
}

type ListActiveEgressResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EgressIds     []string               `protobuf:"bytes,1,rep,name=egress_ids,json=egressIds,proto3" json:"egress_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListActiveEgressResponse) Reset() {
	*x = ListActiveEgressResponse{}
	mi := &file_rpc_egress_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListActiveEgressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListActiveEgressResponse) ProtoMessage() {}

func (x *ListActiveEgressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_egress_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListActiveEgressResponse.ProtoReflect.Descriptor instead.
func (*ListActiveEgressResponse) Descriptor() ([]byte, []int) {
	return file_rpc_egress_proto_rawDescGZIP(), []int{2}
}

func (x *ListActiveEgressResponse) GetEgressIds() []string {
	if x != nil {
		return x.EgressIds
	}
	return nil
}

var File_rpc_egress_proto protoreflect.FileDescriptor

const file_rpc_egress_proto_rawDesc = "" +
	"\n" +
	"\x10rpc/egress.proto\x12\x03rpc\x1a\roptions.proto\x1a\x14livekit_egress.proto\"\xa3\x04\n" +
	"\x12StartEgressRequest\x12\x1b\n" +
	"\tegress_id\x18\x01 \x01(\tR\begressId\x12L\n" +
	"\x0eroom_composite\x18\x05 \x01(\v2#.livekit.RoomCompositeEgressRequestH\x00R\rroomComposite\x12-\n" +
	"\x03web\x18\v \x01(\v2\x19.livekit.WebEgressRequestH\x00R\x03web\x12E\n" +
	"\vparticipant\x18\r \x01(\v2!.livekit.ParticipantEgressRequestH\x00R\vparticipant\x12O\n" +
	"\x0ftrack_composite\x18\x06 \x01(\v2$.livekit.TrackCompositeEgressRequestH\x00R\x0etrackComposite\x123\n" +
	"\x05track\x18\a \x01(\v2\x1b.livekit.TrackEgressRequestH\x00R\x05track\x12\x17\n" +
	"\aroom_id\x18\x03 \x01(\tR\x06roomId\x12\x14\n" +
	"\x05token\x18\b \x01(\tR\x05token\x12\x15\n" +
	"\x06ws_url\x18\t \x01(\tR\x05wsUrl\x120\n" +
	"\x14cloud_backup_enabled\x18\n" +
	" \x01(\bR\x12cloudBackupEnabled\x12#\n" +
	"\restimated_cpu\x18\x0e \x01(\x01R\festimatedCpuB\t\n" +
	"\arequest\"\x19\n" +
	"\x17ListActiveEgressRequest\"9\n" +
	"\x18ListActiveEgressResponse\x12\x1d\n" +
	"\n" +
	"egress_ids\x18\x01 \x03(\tR\tegressIds2\xb2\x01\n" +
	"\x0eEgressInternal\x12E\n" +
	"\vStartEgress\x12\x17.rpc.StartEgressRequest\x1a\x13.livekit.EgressInfo\"\b\xb2\x89\x01\x04\x10\x010\x01\x12Y\n" +
	"\x10ListActiveEgress\x12\x1c.rpc.ListActiveEgressRequest\x1a\x1d.rpc.ListActiveEgressResponse\"\b\xb2\x89\x01\x04\x10\x01(\x012\xa1\x01\n" +
	"\rEgressHandler\x12I\n" +
	"\fUpdateStream\x12\x1c.livekit.UpdateStreamRequest\x1a\x13.livekit.EgressInfo\"\x06\xb2\x89\x01\x02\x10\x01\x12E\n" +
	"\n" +
	"StopEgress\x12\x1a.livekit.StopEgressRequest\x1a\x13.livekit.EgressInfo\"\x06\xb2\x89\x01\x02\x10\x01B!Z\x1fgithub.com/livekit/protocol/rpcb\x06proto3"

var (
	file_rpc_egress_proto_rawDescOnce sync.Once
	file_rpc_egress_proto_rawDescData []byte
)

func file_rpc_egress_proto_rawDescGZIP() []byte {
	file_rpc_egress_proto_rawDescOnce.Do(func() {
		file_rpc_egress_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_egress_proto_rawDesc), len(file_rpc_egress_proto_rawDesc)))
	})
	return file_rpc_egress_proto_rawDescData
}

var file_rpc_egress_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_rpc_egress_proto_goTypes = []any{
	(*StartEgressRequest)(nil),                  // 0: rpc.StartEgressRequest
	(*ListActiveEgressRequest)(nil),             // 1: rpc.ListActiveEgressRequest
	(*ListActiveEgressResponse)(nil),            // 2: rpc.ListActiveEgressResponse
	(*livekit.RoomCompositeEgressRequest)(nil),  // 3: livekit.RoomCompositeEgressRequest
	(*livekit.WebEgressRequest)(nil),            // 4: livekit.WebEgressRequest
	(*livekit.ParticipantEgressRequest)(nil),    // 5: livekit.ParticipantEgressRequest
	(*livekit.TrackCompositeEgressRequest)(nil), // 6: livekit.TrackCompositeEgressRequest
	(*livekit.TrackEgressRequest)(nil),          // 7: livekit.TrackEgressRequest
	(*livekit.UpdateStreamRequest)(nil),         // 8: livekit.UpdateStreamRequest
	(*livekit.StopEgressRequest)(nil),           // 9: livekit.StopEgressRequest
	(*livekit.EgressInfo)(nil),                  // 10: livekit.EgressInfo
}
var file_rpc_egress_proto_depIdxs = []int32{
	3,  // 0: rpc.StartEgressRequest.room_composite:type_name -> livekit.RoomCompositeEgressRequest
	4,  // 1: rpc.StartEgressRequest.web:type_name -> livekit.WebEgressRequest
	5,  // 2: rpc.StartEgressRequest.participant:type_name -> livekit.ParticipantEgressRequest
	6,  // 3: rpc.StartEgressRequest.track_composite:type_name -> livekit.TrackCompositeEgressRequest
	7,  // 4: rpc.StartEgressRequest.track:type_name -> livekit.TrackEgressRequest
	0,  // 5: rpc.EgressInternal.StartEgress:input_type -> rpc.StartEgressRequest
	1,  // 6: rpc.EgressInternal.ListActiveEgress:input_type -> rpc.ListActiveEgressRequest
	8,  // 7: rpc.EgressHandler.UpdateStream:input_type -> livekit.UpdateStreamRequest
	9,  // 8: rpc.EgressHandler.StopEgress:input_type -> livekit.StopEgressRequest
	10, // 9: rpc.EgressInternal.StartEgress:output_type -> livekit.EgressInfo
	2,  // 10: rpc.EgressInternal.ListActiveEgress:output_type -> rpc.ListActiveEgressResponse
	10, // 11: rpc.EgressHandler.UpdateStream:output_type -> livekit.EgressInfo
	10, // 12: rpc.EgressHandler.StopEgress:output_type -> livekit.EgressInfo
	9,  // [9:13] is the sub-list for method output_type
	5,  // [5:9] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_rpc_egress_proto_init() }
func file_rpc_egress_proto_init() {
	if File_rpc_egress_proto != nil {
		return
	}
	file_rpc_egress_proto_msgTypes[0].OneofWrappers = []any{
		(*StartEgressRequest_RoomComposite)(nil),
		(*StartEgressRequest_Web)(nil),
		(*StartEgressRequest_Participant)(nil),
		(*StartEgressRequest_TrackComposite)(nil),
		(*StartEgressRequest_Track)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_egress_proto_rawDesc), len(file_rpc_egress_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_rpc_egress_proto_goTypes,
		DependencyIndexes: file_rpc_egress_proto_depIdxs,
		MessageInfos:      file_rpc_egress_proto_msgTypes,
	}.Build()
	File_rpc_egress_proto = out.File
	file_rpc_egress_proto_goTypes = nil
	file_rpc_egress_proto_depIdxs = nil
}
