// Code generated by protoc-gen-psrpc v0.6.0, DO NOT EDIT.
// source: rpc/rest_signal.proto

package rpc

import (
	"context"

	"github.com/livekit/psrpc"
	"github.com/livekit/psrpc/pkg/client"
	"github.com/livekit/psrpc/pkg/info"
	"github.com/livekit/psrpc/pkg/rand"
	"github.com/livekit/psrpc/pkg/server"
	"github.com/livekit/psrpc/version"
)
import google_protobuf "google.golang.org/protobuf/types/known/emptypb"

var _ = version.PsrpcVersion_0_6

// ========================
// RTCRest Client Interface
// ========================

type RTCRestClient[TopicTopicType ~string] interface {
	Create(ctx context.Context, topic TopicTopicType, req *RTCRestCreateRequest, opts ...psrpc.RequestOption) (*RTCRestCreateResponse, error)

	// Close immediately, without waiting for pending RPCs
	Close()
}

// ============================
// RTCRest ServerImpl Interface
// ============================

type RTCRestServerImpl interface {
	Create(context.Context, *RTCRestCreateRequest) (*RTCRestCreateResponse, error)
}

// ========================
// RTCRest Server Interface
// ========================

type RTCRestServer[TopicTopicType ~string] interface {
	RegisterCreateTopic(topic TopicTopicType) error
	DeregisterCreateTopic(topic TopicTopicType)
	RegisterAllCommonTopics(topic TopicTopicType) error
	DeregisterAllCommonTopics(topic TopicTopicType)

	// Close and wait for pending RPCs to complete
	Shutdown()

	// Close immediately, without waiting for pending RPCs
	Kill()
}

// ==============
// RTCRest Client
// ==============

type rTCRestClient[TopicTopicType ~string] struct {
	client *client.RPCClient
}

// NewRTCRestClient creates a psrpc client that implements the RTCRestClient interface.
func NewRTCRestClient[TopicTopicType ~string](bus psrpc.MessageBus, opts ...psrpc.ClientOption) (RTCRestClient[TopicTopicType], error) {
	sd := &info.ServiceDefinition{
		Name: "RTCRest",
		ID:   rand.NewClientID(),
	}

	sd.RegisterMethod("Create", false, false, true, true)

	rpcClient, err := client.NewRPCClient(sd, bus, opts...)
	if err != nil {
		return nil, err
	}

	return &rTCRestClient[TopicTopicType]{
		client: rpcClient,
	}, nil
}

func (c *rTCRestClient[TopicTopicType]) Create(ctx context.Context, topic TopicTopicType, req *RTCRestCreateRequest, opts ...psrpc.RequestOption) (*RTCRestCreateResponse, error) {
	return client.RequestSingle[*RTCRestCreateResponse](ctx, c.client, "Create", []string{string(topic)}, req, opts...)
}

func (s *rTCRestClient[TopicTopicType]) Close() {
	s.client.Close()
}

// ==============
// RTCRest Server
// ==============

type rTCRestServer[TopicTopicType ~string] struct {
	svc RTCRestServerImpl
	rpc *server.RPCServer
}

// NewRTCRestServer builds a RPCServer that will route requests
// to the corresponding method in the provided svc implementation.
func NewRTCRestServer[TopicTopicType ~string](svc RTCRestServerImpl, bus psrpc.MessageBus, opts ...psrpc.ServerOption) (RTCRestServer[TopicTopicType], error) {
	sd := &info.ServiceDefinition{
		Name: "RTCRest",
		ID:   rand.NewServerID(),
	}

	s := server.NewRPCServer(sd, bus, opts...)

	sd.RegisterMethod("Create", false, false, true, true)
	return &rTCRestServer[TopicTopicType]{
		svc: svc,
		rpc: s,
	}, nil
}

func (s *rTCRestServer[TopicTopicType]) RegisterCreateTopic(topic TopicTopicType) error {
	return server.RegisterHandler(s.rpc, "Create", []string{string(topic)}, s.svc.Create, nil)
}

func (s *rTCRestServer[TopicTopicType]) DeregisterCreateTopic(topic TopicTopicType) {
	s.rpc.DeregisterHandler("Create", []string{string(topic)})
}

func (s *rTCRestServer[TopicTopicType]) allCommonTopicRegisterers() server.RegistererSlice {
	return server.RegistererSlice{
		server.NewRegisterer(s.RegisterCreateTopic, s.DeregisterCreateTopic),
	}
}

func (s *rTCRestServer[TopicTopicType]) RegisterAllCommonTopics(topic TopicTopicType) error {
	return s.allCommonTopicRegisterers().Register(topic)
}

func (s *rTCRestServer[TopicTopicType]) DeregisterAllCommonTopics(topic TopicTopicType) {
	s.allCommonTopicRegisterers().Deregister(topic)
}

func (s *rTCRestServer[TopicTopicType]) Shutdown() {
	s.rpc.Close(false)
}

func (s *rTCRestServer[TopicTopicType]) Kill() {
	s.rpc.Close(true)
}

// ===================================
// RTCRestParticipant Client Interface
// ===================================

type RTCRestParticipantClient[TopicTopicType ~string] interface {
	ICETrickle(ctx context.Context, topic TopicTopicType, req *RTCRestParticipantICETrickleRequest, opts ...psrpc.RequestOption) (*google_protobuf.Empty, error)

	ICERestart(ctx context.Context, topic TopicTopicType, req *RTCRestParticipantICERestartRequest, opts ...psrpc.RequestOption) (*RTCRestParticipantICERestartResponse, error)

	DeleteSession(ctx context.Context, topic TopicTopicType, req *RTCRestParticipantDeleteSessionRequest, opts ...psrpc.RequestOption) (*google_protobuf.Empty, error)

	// Close immediately, without waiting for pending RPCs
	Close()
}

// =======================================
// RTCRestParticipant ServerImpl Interface
// =======================================

type RTCRestParticipantServerImpl interface {
	ICETrickle(context.Context, *RTCRestParticipantICETrickleRequest) (*google_protobuf.Empty, error)

	ICERestart(context.Context, *RTCRestParticipantICERestartRequest) (*RTCRestParticipantICERestartResponse, error)

	DeleteSession(context.Context, *RTCRestParticipantDeleteSessionRequest) (*google_protobuf.Empty, error)
}

// ===================================
// RTCRestParticipant Server Interface
// ===================================

type RTCRestParticipantServer[TopicTopicType ~string] interface {
	RegisterICETrickleTopic(topic TopicTopicType) error
	DeregisterICETrickleTopic(topic TopicTopicType)
	RegisterICERestartTopic(topic TopicTopicType) error
	DeregisterICERestartTopic(topic TopicTopicType)
	RegisterDeleteSessionTopic(topic TopicTopicType) error
	DeregisterDeleteSessionTopic(topic TopicTopicType)
	RegisterAllCommonTopics(topic TopicTopicType) error
	DeregisterAllCommonTopics(topic TopicTopicType)

	// Close and wait for pending RPCs to complete
	Shutdown()

	// Close immediately, without waiting for pending RPCs
	Kill()
}

// =========================
// RTCRestParticipant Client
// =========================

type rTCRestParticipantClient[TopicTopicType ~string] struct {
	client *client.RPCClient
}

// NewRTCRestParticipantClient creates a psrpc client that implements the RTCRestParticipantClient interface.
func NewRTCRestParticipantClient[TopicTopicType ~string](bus psrpc.MessageBus, opts ...psrpc.ClientOption) (RTCRestParticipantClient[TopicTopicType], error) {
	sd := &info.ServiceDefinition{
		Name: "RTCRestParticipant",
		ID:   rand.NewClientID(),
	}

	sd.RegisterMethod("ICETrickle", false, false, true, true)
	sd.RegisterMethod("ICERestart", false, false, true, true)
	sd.RegisterMethod("DeleteSession", false, false, true, true)

	rpcClient, err := client.NewRPCClient(sd, bus, opts...)
	if err != nil {
		return nil, err
	}

	return &rTCRestParticipantClient[TopicTopicType]{
		client: rpcClient,
	}, nil
}

func (c *rTCRestParticipantClient[TopicTopicType]) ICETrickle(ctx context.Context, topic TopicTopicType, req *RTCRestParticipantICETrickleRequest, opts ...psrpc.RequestOption) (*google_protobuf.Empty, error) {
	return client.RequestSingle[*google_protobuf.Empty](ctx, c.client, "ICETrickle", []string{string(topic)}, req, opts...)
}

func (c *rTCRestParticipantClient[TopicTopicType]) ICERestart(ctx context.Context, topic TopicTopicType, req *RTCRestParticipantICERestartRequest, opts ...psrpc.RequestOption) (*RTCRestParticipantICERestartResponse, error) {
	return client.RequestSingle[*RTCRestParticipantICERestartResponse](ctx, c.client, "ICERestart", []string{string(topic)}, req, opts...)
}

func (c *rTCRestParticipantClient[TopicTopicType]) DeleteSession(ctx context.Context, topic TopicTopicType, req *RTCRestParticipantDeleteSessionRequest, opts ...psrpc.RequestOption) (*google_protobuf.Empty, error) {
	return client.RequestSingle[*google_protobuf.Empty](ctx, c.client, "DeleteSession", []string{string(topic)}, req, opts...)
}

func (s *rTCRestParticipantClient[TopicTopicType]) Close() {
	s.client.Close()
}

// =========================
// RTCRestParticipant Server
// =========================

type rTCRestParticipantServer[TopicTopicType ~string] struct {
	svc RTCRestParticipantServerImpl
	rpc *server.RPCServer
}

// NewRTCRestParticipantServer builds a RPCServer that will route requests
// to the corresponding method in the provided svc implementation.
func NewRTCRestParticipantServer[TopicTopicType ~string](svc RTCRestParticipantServerImpl, bus psrpc.MessageBus, opts ...psrpc.ServerOption) (RTCRestParticipantServer[TopicTopicType], error) {
	sd := &info.ServiceDefinition{
		Name: "RTCRestParticipant",
		ID:   rand.NewServerID(),
	}

	s := server.NewRPCServer(sd, bus, opts...)

	sd.RegisterMethod("ICETrickle", false, false, true, true)
	sd.RegisterMethod("ICERestart", false, false, true, true)
	sd.RegisterMethod("DeleteSession", false, false, true, true)
	return &rTCRestParticipantServer[TopicTopicType]{
		svc: svc,
		rpc: s,
	}, nil
}

func (s *rTCRestParticipantServer[TopicTopicType]) RegisterICETrickleTopic(topic TopicTopicType) error {
	return server.RegisterHandler(s.rpc, "ICETrickle", []string{string(topic)}, s.svc.ICETrickle, nil)
}

func (s *rTCRestParticipantServer[TopicTopicType]) DeregisterICETrickleTopic(topic TopicTopicType) {
	s.rpc.DeregisterHandler("ICETrickle", []string{string(topic)})
}

func (s *rTCRestParticipantServer[TopicTopicType]) RegisterICERestartTopic(topic TopicTopicType) error {
	return server.RegisterHandler(s.rpc, "ICERestart", []string{string(topic)}, s.svc.ICERestart, nil)
}

func (s *rTCRestParticipantServer[TopicTopicType]) DeregisterICERestartTopic(topic TopicTopicType) {
	s.rpc.DeregisterHandler("ICERestart", []string{string(topic)})
}

func (s *rTCRestParticipantServer[TopicTopicType]) RegisterDeleteSessionTopic(topic TopicTopicType) error {
	return server.RegisterHandler(s.rpc, "DeleteSession", []string{string(topic)}, s.svc.DeleteSession, nil)
}

func (s *rTCRestParticipantServer[TopicTopicType]) DeregisterDeleteSessionTopic(topic TopicTopicType) {
	s.rpc.DeregisterHandler("DeleteSession", []string{string(topic)})
}

func (s *rTCRestParticipantServer[TopicTopicType]) allCommonTopicRegisterers() server.RegistererSlice {
	return server.RegistererSlice{
		server.NewRegisterer(s.RegisterICETrickleTopic, s.DeregisterICETrickleTopic),
		server.NewRegisterer(s.RegisterICERestartTopic, s.DeregisterICERestartTopic),
		server.NewRegisterer(s.RegisterDeleteSessionTopic, s.DeregisterDeleteSessionTopic),
	}
}

func (s *rTCRestParticipantServer[TopicTopicType]) RegisterAllCommonTopics(topic TopicTopicType) error {
	return s.allCommonTopicRegisterers().Register(topic)
}

func (s *rTCRestParticipantServer[TopicTopicType]) DeregisterAllCommonTopics(topic TopicTopicType) {
	s.allCommonTopicRegisterers().Deregister(topic)
}

func (s *rTCRestParticipantServer[TopicTopicType]) Shutdown() {
	s.rpc.Close(false)
}

func (s *rTCRestParticipantServer[TopicTopicType]) Kill() {
	s.rpc.Close(true)
}

var psrpcFileDescriptor10 = []byte{
	// 690 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x55, 0x4d, 0x6f, 0xd3, 0x3e,
	0x1c, 0x96, 0xdb, 0x6d, 0xff, 0x7f, 0x7f, 0x5d, 0xa7, 0xcd, 0xac, 0xa3, 0x4b, 0x35, 0xad, 0xeb,
	0x06, 0x2a, 0x02, 0xa5, 0xa2, 0xbb, 0x8c, 0x1d, 0x29, 0x45, 0xaa, 0x84, 0x10, 0x4a, 0x77, 0x42,
	0x48, 0x51, 0xea, 0xb8, 0xc5, 0x6a, 0x12, 0x1b, 0xdb, 0x1d, 0xaa, 0x84, 0xb8, 0xef, 0xca, 0x81,
	0x0f, 0xb2, 0x03, 0xe2, 0xc3, 0xf0, 0x19, 0xf8, 0x0c, 0x28, 0x4e, 0xda, 0xb5, 0xeb, 0x0b, 0x13,
	0x97, 0xdd, 0x9c, 0xe7, 0xf7, 0xf6, 0x3c, 0x8f, 0xed, 0x18, 0x8a, 0x52, 0x90, 0xba, 0xa4, 0x4a,
	0xbb, 0x8a, 0xf5, 0x23, 0x2f, 0xb0, 0x85, 0xe4, 0x9a, 0xe3, 0xac, 0x14, 0xc4, 0x2a, 0xf7, 0x39,
	0xef, 0x07, 0xb4, 0x6e, 0xa0, 0xee, 0xb0, 0x57, 0xa7, 0xa1, 0xd0, 0xa3, 0x24, 0xc3, 0x2a, 0x70,
	0xa1, 0x19, 0x8f, 0x54, 0xfa, 0xb9, 0x17, 0xb0, 0x4b, 0x3a, 0x60, 0xda, 0x65, 0x91, 0xa6, 0x72,
	0xd2, 0xc8, 0xda, 0x19, 0xe3, 0x52, 0x93, 0x04, 0xaa, 0x7e, 0xcb, 0xc2, 0xae, 0x73, 0xd1, 0x74,
	0xa8, 0xd2, 0x4d, 0x49, 0x3d, 0x4d, 0x1d, 0xfa, 0x69, 0x48, 0x95, 0xc6, 0xe7, 0x50, 0x50, 0xda,
	0x93, 0xda, 0x55, 0x54, 0x29, 0xc6, 0xa3, 0x12, 0xaa, 0xa0, 0x5a, 0xbe, 0x51, 0xb4, 0xd3, 0x1e,
	0x76, 0x27, 0x8e, 0x76, 0x92, 0xa0, 0xb3, 0xa9, 0xa6, 0xbe, 0x70, 0x19, 0x72, 0xbc, 0xd7, 0xa3,
	0xd2, 0x55, 0xbe, 0x28, 0x65, 0x2a, 0xa8, 0x96, 0x73, 0xfe, 0x37, 0x40, 0xc7, 0x17, 0xf8, 0x2b,
	0x1c, 0xa8, 0x61, 0x57, 0x11, 0xc9, 0xba, 0xd4, 0x77, 0x85, 0x27, 0x35, 0x23, 0x4c, 0x78, 0x91,
	0x76, 0xb5, 0xf4, 0xc8, 0x40, 0x95, 0xb2, 0x95, 0x6c, 0x2d, 0xdf, 0x38, 0xb7, 0xa5, 0x20, 0xf6,
	0x22, 0x6a, 0x76, 0x67, 0x52, 0xfe, 0xee, 0xa6, 0xfa, 0xc2, 0x14, 0xb7, 0x22, 0x2d, 0x47, 0x4e,
	0x59, 0x2d, 0xcf, 0xb0, 0x9e, 0x41, 0xce, 0xac, 0xde, 0x30, 0xa5, 0xf1, 0x21, 0xe4, 0xcd, 0x54,
	0x37, 0xf2, 0x42, 0xaa, 0x4a, 0xa8, 0x92, 0xad, 0xe5, 0x1c, 0x30, 0xd0, 0xdb, 0x18, 0xb1, 0x14,
	0x54, 0xfe, 0x36, 0x0e, 0x6f, 0x43, 0x76, 0x40, 0x47, 0xc6, 0xa0, 0x9c, 0x13, 0x2f, 0xf1, 0x0b,
	0x58, 0xbf, 0xf4, 0x82, 0x21, 0x35, 0xe2, 0xf3, 0x8d, 0xe3, 0xe5, 0x5a, 0x26, 0x54, 0x9c, 0xa4,
	0xe2, 0x3c, 0x73, 0x86, 0xaa, 0x3f, 0x11, 0x14, 0x6f, 0x65, 0x2b, 0xc1, 0x23, 0x45, 0xf1, 0x01,
	0x80, 0x17, 0xa9, 0xcf, 0xa9, 0xb5, 0xc9, 0xc4, 0x5c, 0x82, 0xc4, 0xde, 0x3e, 0x82, 0xad, 0x69,
	0x43, 0x99, 0x9f, 0xba, 0x5f, 0x98, 0x42, 0xdb, 0x3e, 0x3e, 0x85, 0x3c, 0x23, 0xd4, 0x55, 0x54,
	0x5e, 0x52, 0x39, 0x36, 0x1c, 0x4f, 0x76, 0xb6, 0xdd, 0x6c, 0x75, 0x4c, 0xc8, 0x01, 0x46, 0x68,
	0xb2, 0x54, 0xf8, 0x04, 0xb6, 0x92, 0x22, 0xb3, 0xc7, 0x71, 0xef, 0x35, 0xd3, 0x7b, 0xd3, 0xe4,
	0x18, 0xb0, 0xed, 0x57, 0x7f, 0x21, 0x38, 0x4e, 0xa9, 0x4f, 0xb9, 0xd5, 0x6e, 0xb6, 0x2e, 0x24,
	0x23, 0x83, 0x60, 0x72, 0xbc, 0x30, 0xac, 0x49, 0xce, 0xc3, 0x54, 0x82, 0x59, 0xe3, 0xe7, 0xb0,
	0x3b, 0xcb, 0x9e, 0x46, 0x9a, 0xe9, 0x51, 0xaa, 0xe1, 0xc1, 0x8c, 0x86, 0x24, 0xb4, 0x40, 0x70,
	0x76, 0x91, 0xe0, 0x3b, 0x71, 0xc7, 0x47, 0xb0, 0xa9, 0x7c, 0xe1, 0xf6, 0xa4, 0xd7, 0x0f, 0x69,
	0xa4, 0x4b, 0xeb, 0x26, 0x27, 0xaf, 0x7c, 0xf1, 0x3a, 0x85, 0xaa, 0x3f, 0x96, 0xc9, 0x8b, 0x11,
	0x4f, 0xea, 0xfb, 0x91, 0x77, 0x9b, 0xf8, 0xda, 0x3c, 0x71, 0x0e, 0x27, 0xab, 0x79, 0xa7, 0x07,
	0x6c, 0xde, 0x29, 0x74, 0x07, 0xa7, 0x32, 0xf3, 0x03, 0xbf, 0x23, 0x78, 0x3c, 0x3f, 0xf1, 0x15,
	0x0d, 0xa8, 0x1e, 0x37, 0xba, 0x17, 0xb3, 0x1a, 0x7d, 0xf8, 0x2f, 0xe5, 0x85, 0x3f, 0xc0, 0x46,
	0x72, 0xbf, 0xf0, 0xfe, 0xd2, 0x1b, 0x6a, 0x59, 0x8b, 0x42, 0x89, 0x5b, 0xd5, 0xf2, 0xf5, 0x15,
	0x7a, 0xb8, 0x8d, 0xac, 0x1d, 0xd8, 0x20, 0x3c, 0x0c, 0x79, 0x84, 0xd7, 0x35, 0x17, 0x8c, 0x94,
	0xd0, 0x19, 0x6a, 0xfc, 0xce, 0x00, 0x9e, 0x77, 0x00, 0x33, 0x80, 0x9b, 0xeb, 0x80, 0x6b, 0xd3,
	0xdd, 0x57, 0xdd, 0x18, 0x6b, 0xcf, 0x4e, 0x5e, 0x00, 0x7b, 0xfc, 0x02, 0xd8, 0xad, 0xf8, 0x05,
	0xa8, 0xee, 0x5f, 0x5f, 0xa1, 0xe2, 0x42, 0x0e, 0xf8, 0x8b, 0x19, 0x95, 0x6e, 0xf1, 0xaa, 0x51,
	0xb3, 0xa7, 0xd7, 0x7a, 0x72, 0x87, 0xcc, 0xd4, 0x81, 0x15, 0xd3, 0x39, 0x14, 0x66, 0xb6, 0x1b,
	0x3f, 0x5d, 0xd2, 0x76, 0xd1, 0xa1, 0xf8, 0x07, 0xb9, 0x2f, 0x8f, 0xde, 0x1f, 0xf6, 0x99, 0xfe,
	0x38, 0xec, 0xda, 0x84, 0x87, 0xf5, 0xf4, 0x6f, 0x96, 0x3c, 0x98, 0x84, 0x07, 0x75, 0x29, 0x48,
	0x77, 0xc3, 0x7c, 0x9d, 0xfe, 0x09, 0x00, 0x00, 0xff, 0xff, 0xb4, 0x85, 0x92, 0x8e, 0x6a, 0x07,
	0x00, 0x00,
}
