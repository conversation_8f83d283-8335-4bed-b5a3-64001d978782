// Code generated by protoc-gen-psrpc v0.6.0, DO NOT EDIT.
// source: rpc/signal.proto

package rpc

import (
	"context"

	"github.com/livekit/psrpc"
	"github.com/livekit/psrpc/pkg/client"
	"github.com/livekit/psrpc/pkg/info"
	"github.com/livekit/psrpc/pkg/rand"
	"github.com/livekit/psrpc/pkg/server"
	"github.com/livekit/psrpc/version"
)

var _ = version.PsrpcVersion_0_6

// =======================
// Signal Client Interface
// =======================

type SignalClient[NodeIdTopicType ~string] interface {
	RelaySignal(ctx context.Context, nodeId NodeIdTopicType, opts ...psrpc.RequestOption) (psrpc.ClientStream[*RelaySignalRequest, *RelaySignalResponse], error)

	// Close immediately, without waiting for pending RPCs
	Close()
}

// ===========================
// Signal ServerImpl Interface
// ===========================

type SignalServerImpl interface {
	RelaySignal(psrpc.ServerStream[*RelaySignalResponse, *RelaySignalRequest]) error
}

// =======================
// Signal Server Interface
// =======================

type SignalServer[NodeIdTopicType ~string] interface {
	RegisterRelaySignalTopic(nodeId NodeIdTopicType) error
	DeregisterRelaySignalTopic(nodeId NodeIdTopicType)
	RegisterAllNodeTopics(nodeId NodeIdTopicType) error
	DeregisterAllNodeTopics(nodeId NodeIdTopicType)

	// Close and wait for pending RPCs to complete
	Shutdown()

	// Close immediately, without waiting for pending RPCs
	Kill()
}

// =============
// Signal Client
// =============

type signalClient[NodeIdTopicType ~string] struct {
	client *client.RPCClient
}

// NewSignalClient creates a psrpc client that implements the SignalClient interface.
func NewSignalClient[NodeIdTopicType ~string](bus psrpc.MessageBus, opts ...psrpc.ClientOption) (SignalClient[NodeIdTopicType], error) {
	sd := &info.ServiceDefinition{
		Name: "Signal",
		ID:   rand.NewClientID(),
	}

	sd.RegisterMethod("RelaySignal", false, false, false, true)

	rpcClient, err := client.NewRPCClientWithStreams(sd, bus, opts...)
	if err != nil {
		return nil, err
	}

	return &signalClient[NodeIdTopicType]{
		client: rpcClient,
	}, nil
}

func (c *signalClient[NodeIdTopicType]) RelaySignal(ctx context.Context, nodeId NodeIdTopicType, opts ...psrpc.RequestOption) (psrpc.ClientStream[*RelaySignalRequest, *RelaySignalResponse], error) {
	return client.OpenStream[*RelaySignalRequest, *RelaySignalResponse](ctx, c.client, "RelaySignal", []string{string(nodeId)}, opts...)
}

func (s *signalClient[NodeIdTopicType]) Close() {
	s.client.Close()
}

// =============
// Signal Server
// =============

type signalServer[NodeIdTopicType ~string] struct {
	svc SignalServerImpl
	rpc *server.RPCServer
}

// NewSignalServer builds a RPCServer that will route requests
// to the corresponding method in the provided svc implementation.
func NewSignalServer[NodeIdTopicType ~string](svc SignalServerImpl, bus psrpc.MessageBus, opts ...psrpc.ServerOption) (SignalServer[NodeIdTopicType], error) {
	sd := &info.ServiceDefinition{
		Name: "Signal",
		ID:   rand.NewServerID(),
	}

	s := server.NewRPCServer(sd, bus, opts...)

	sd.RegisterMethod("RelaySignal", false, false, false, true)
	return &signalServer[NodeIdTopicType]{
		svc: svc,
		rpc: s,
	}, nil
}

func (s *signalServer[NodeIdTopicType]) RegisterRelaySignalTopic(nodeId NodeIdTopicType) error {
	return server.RegisterStreamHandler(s.rpc, "RelaySignal", []string{string(nodeId)}, s.svc.RelaySignal, nil)
}

func (s *signalServer[NodeIdTopicType]) DeregisterRelaySignalTopic(nodeId NodeIdTopicType) {
	s.rpc.DeregisterHandler("RelaySignal", []string{string(nodeId)})
}

func (s *signalServer[NodeIdTopicType]) allNodeTopicRegisterers() server.RegistererSlice {
	return server.RegistererSlice{
		server.NewRegisterer(s.RegisterRelaySignalTopic, s.DeregisterRelaySignalTopic),
	}
}

func (s *signalServer[NodeIdTopicType]) RegisterAllNodeTopics(nodeId NodeIdTopicType) error {
	return s.allNodeTopicRegisterers().Register(nodeId)
}

func (s *signalServer[NodeIdTopicType]) DeregisterAllNodeTopics(nodeId NodeIdTopicType) {
	s.allNodeTopicRegisterers().Deregister(nodeId)
}

func (s *signalServer[NodeIdTopicType]) Shutdown() {
	s.rpc.Close(false)
}

func (s *signalServer[NodeIdTopicType]) Kill() {
	s.rpc.Close(true)
}

var psrpcFileDescriptor9 = []byte{
	// 313 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x6c, 0x91, 0x4f, 0x4e, 0xf3, 0x30,
	0x10, 0xc5, 0xe5, 0x2f, 0x69, 0xbf, 0xe2, 0x52, 0xa9, 0xb8, 0xd0, 0x9a, 0x48, 0x88, 0xd0, 0x55,
	0x56, 0x8e, 0x14, 0xc4, 0x86, 0x25, 0x47, 0x70, 0x77, 0x6c, 0xa2, 0xd6, 0xb5, 0x8a, 0x45, 0xb0,
	0x5d, 0x8f, 0x8b, 0xc4, 0x11, 0xb8, 0x0b, 0x2b, 0x4e, 0x88, 0x12, 0xf7, 0x4f, 0x50, 0xd8, 0x44,
	0x33, 0xf3, 0x5e, 0xde, 0xcf, 0x1e, 0xe3, 0xb1, 0xb3, 0x22, 0x07, 0xb5, 0xd1, 0xcb, 0x8a, 0x59,
	0x67, 0xbc, 0x21, 0x91, 0xb3, 0x22, 0x19, 0x19, 0xeb, 0x95, 0xd1, 0x10, 0x66, 0xc9, 0xb4, 0x52,
	0xef, 0xf2, 0x55, 0xf9, 0x52, 0x69, 0x2f, 0xdd, 0xd1, 0x9b, 0x5c, 0x1c, 0xe6, 0xce, 0x8b, 0x30,
	0x9a, 0x7f, 0x21, 0x4c, 0xb8, 0xac, 0x96, 0x1f, 0x8b, 0x26, 0x94, 0xcb, 0xed, 0x4e, 0x82, 0x27,
	0x8f, 0x78, 0x04, 0x7e, 0xe9, 0x7c, 0x09, 0x12, 0x40, 0x19, 0x4d, 0x51, 0x8a, 0xb2, 0x61, 0x71,
	0xc5, 0xf6, 0x09, 0x6c, 0x51, 0xab, 0x8b, 0x20, 0xf2, 0x73, 0x68, 0x75, 0xa4, 0xc0, 0x03, 0x17,
	0x62, 0x80, 0x46, 0x69, 0x94, 0x0d, 0x8b, 0xe9, 0xe9, 0xb7, 0x36, 0x85, 0x1f, 0x7d, 0x64, 0x8c,
	0x23, 0x90, 0x5b, 0x1a, 0xa7, 0x28, 0x8b, 0x79, 0x5d, 0x92, 0x4b, 0xdc, 0x13, 0x95, 0x01, 0x49,
	0x7b, 0x29, 0xca, 0x06, 0x3c, 0x34, 0x73, 0x8f, 0x27, 0xbf, 0x4e, 0x0b, 0xd6, 0x68, 0x90, 0xe4,
	0x01, 0x9f, 0xb9, 0x7d, 0x0d, 0xf4, 0x5f, 0xc3, 0x9c, 0x75, 0x98, 0x41, 0xe7, 0x27, 0xe7, 0x81,
	0x1a, 0xfd, 0x41, 0x8d, 0x5b, 0xd4, 0x42, 0xe1, 0x7e, 0x08, 0x21, 0x25, 0x1e, 0xb6, 0xf8, 0x64,
	0xc6, 0x9c, 0x15, 0xac, 0xbb, 0xbf, 0x84, 0x76, 0x85, 0x00, 0x9d, 0xdf, 0x7c, 0x7f, 0xa2, 0xeb,
	0x31, 0x4a, 0x26, 0x38, 0xd6, 0x66, 0x2d, 0xc9, 0xff, 0xfa, 0x5b, 0xaa, 0x75, 0xbd, 0xe1, 0x14,
	0x3d, 0xdd, 0x3d, 0xdf, 0x6e, 0x94, 0x7f, 0xd9, 0xad, 0x98, 0x30, 0x6f, 0xf9, 0xfe, 0x0a, 0x79,
	0xf3, 0x56, 0xc2, 0x54, 0xb9, 0xb3, 0x62, 0xd5, 0x6f, 0xba, 0xfb, 0x9f, 0x00, 0x00, 0x00, 0xff,
	0xff, 0xb2, 0x20, 0x3e, 0x1c, 0x0c, 0x02, 0x00, 0x00,
}
