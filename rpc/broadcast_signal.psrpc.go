// Code generated by protoc-gen-psrpc v0.6.0, DO NOT EDIT.
// source: rpc/broadcast_signal.proto

package rpc

import (
	"context"

	"github.com/livekit/psrpc"
	"github.com/livekit/psrpc/pkg/client"
	"github.com/livekit/psrpc/pkg/info"
	"github.com/livekit/psrpc/pkg/rand"
	"github.com/livekit/psrpc/pkg/server"
	"github.com/livekit/psrpc/version"
)

var _ = version.PsrpcVersion_0_6

// ================================
// BroadcastSignal Client Interface
// ================================

type BroadcastSignalClient[RoomTopicType ~string] interface {
	SubscribeUpdate(ctx context.Context, room RoomTopicType) (psrpc.Subscription[*BroadcastUpdate], error)

	GetAllParticipant(ctx context.Context, room RoomTopicType, req *GetAllParticipantRequest, opts ...psrpc.RequestOption) (<-chan *psrpc.Response[*GetAllParticipantResponse], error)

	// Close immediately, without waiting for pending RPCs
	Close()
}

// ====================================
// BroadcastSignal ServerImpl Interface
// ====================================

type BroadcastSignalServerImpl interface {
	GetAllParticipant(context.Context, *GetAllParticipantRequest) (*GetAllParticipantResponse, error)
}

// ================================
// BroadcastSignal Server Interface
// ================================

type BroadcastSignalServer[RoomTopicType ~string] interface {
	PublishUpdate(ctx context.Context, room RoomTopicType, msg *BroadcastUpdate) error

	RegisterGetAllParticipantTopic(room RoomTopicType) error
	DeregisterGetAllParticipantTopic(room RoomTopicType)
	RegisterAllRoomTopics(room RoomTopicType) error
	DeregisterAllRoomTopics(room RoomTopicType)

	// Close and wait for pending RPCs to complete
	Shutdown()

	// Close immediately, without waiting for pending RPCs
	Kill()
}

// ======================
// BroadcastSignal Client
// ======================

type broadcastSignalClient[RoomTopicType ~string] struct {
	client *client.RPCClient
}

// NewBroadcastSignalClient creates a psrpc client that implements the BroadcastSignalClient interface.
func NewBroadcastSignalClient[RoomTopicType ~string](bus psrpc.MessageBus, opts ...psrpc.ClientOption) (BroadcastSignalClient[RoomTopicType], error) {
	sd := &info.ServiceDefinition{
		Name: "BroadcastSignal",
		ID:   rand.NewClientID(),
	}

	sd.RegisterMethod("Update", false, true, false, false)
	sd.RegisterMethod("GetAllParticipant", false, true, false, false)

	rpcClient, err := client.NewRPCClient(sd, bus, opts...)
	if err != nil {
		return nil, err
	}

	return &broadcastSignalClient[RoomTopicType]{
		client: rpcClient,
	}, nil
}

func (c *broadcastSignalClient[RoomTopicType]) SubscribeUpdate(ctx context.Context, room RoomTopicType) (psrpc.Subscription[*BroadcastUpdate], error) {
	return client.Join[*BroadcastUpdate](ctx, c.client, "Update", []string{string(room)})
}

func (c *broadcastSignalClient[RoomTopicType]) GetAllParticipant(ctx context.Context, room RoomTopicType, req *GetAllParticipantRequest, opts ...psrpc.RequestOption) (<-chan *psrpc.Response[*GetAllParticipantResponse], error) {
	return client.RequestMulti[*GetAllParticipantResponse](ctx, c.client, "GetAllParticipant", []string{string(room)}, req, opts...)
}

func (s *broadcastSignalClient[RoomTopicType]) Close() {
	s.client.Close()
}

// ======================
// BroadcastSignal Server
// ======================

type broadcastSignalServer[RoomTopicType ~string] struct {
	svc BroadcastSignalServerImpl
	rpc *server.RPCServer
}

// NewBroadcastSignalServer builds a RPCServer that will route requests
// to the corresponding method in the provided svc implementation.
func NewBroadcastSignalServer[RoomTopicType ~string](svc BroadcastSignalServerImpl, bus psrpc.MessageBus, opts ...psrpc.ServerOption) (BroadcastSignalServer[RoomTopicType], error) {
	sd := &info.ServiceDefinition{
		Name: "BroadcastSignal",
		ID:   rand.NewServerID(),
	}

	s := server.NewRPCServer(sd, bus, opts...)

	sd.RegisterMethod("Update", false, true, false, false)
	sd.RegisterMethod("GetAllParticipant", false, true, false, false)
	return &broadcastSignalServer[RoomTopicType]{
		svc: svc,
		rpc: s,
	}, nil
}

func (s *broadcastSignalServer[RoomTopicType]) PublishUpdate(ctx context.Context, room RoomTopicType, msg *BroadcastUpdate) error {
	return s.rpc.Publish(ctx, "Update", []string{string(room)}, msg)
}

func (s *broadcastSignalServer[RoomTopicType]) RegisterGetAllParticipantTopic(room RoomTopicType) error {
	return server.RegisterHandler(s.rpc, "GetAllParticipant", []string{string(room)}, s.svc.GetAllParticipant, nil)
}

func (s *broadcastSignalServer[RoomTopicType]) DeregisterGetAllParticipantTopic(room RoomTopicType) {
	s.rpc.DeregisterHandler("GetAllParticipant", []string{string(room)})
}

func (s *broadcastSignalServer[RoomTopicType]) allRoomTopicRegisterers() server.RegistererSlice {
	return server.RegistererSlice{
		server.NewRegisterer(s.RegisterGetAllParticipantTopic, s.DeregisterGetAllParticipantTopic),
	}
}

func (s *broadcastSignalServer[RoomTopicType]) RegisterAllRoomTopics(room RoomTopicType) error {
	return s.allRoomTopicRegisterers().Register(room)
}

func (s *broadcastSignalServer[RoomTopicType]) DeregisterAllRoomTopics(room RoomTopicType) {
	s.allRoomTopicRegisterers().Deregister(room)
}

func (s *broadcastSignalServer[RoomTopicType]) Shutdown() {
	s.rpc.Close(false)
}

func (s *broadcastSignalServer[RoomTopicType]) Kill() {
	s.rpc.Close(true)
}

var psrpcFileDescriptor11 = []byte{
	// 383 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x74, 0x52, 0xcd, 0x6e, 0xe2, 0x30,
	0x18, 0x5c, 0xc3, 0x8a, 0x5d, 0xcc, 0xfe, 0x61, 0x21, 0x36, 0x64, 0x77, 0x59, 0x36, 0x27, 0x2e,
	0x9b, 0x48, 0xf0, 0x02, 0x6d, 0x2e, 0x2d, 0x87, 0x4a, 0x95, 0xab, 0x5e, 0x7a, 0x68, 0x14, 0x9c,
	0xaf, 0xd4, 0x6a, 0x12, 0xbb, 0xb6, 0xc3, 0x0b, 0xf4, 0xc4, 0xeb, 0xf0, 0x2e, 0x7d, 0x9f, 0x4a,
	0x8e, 0x69, 0x69, 0x1b, 0x6e, 0xc9, 0x37, 0xe3, 0x19, 0xcf, 0x7c, 0xc6, 0xbe, 0x92, 0x2c, 0x5a,
	0x2a, 0x91, 0x66, 0x2c, 0xd5, 0x26, 0xd1, 0x7c, 0x55, 0xa6, 0x79, 0x28, 0x95, 0x30, 0x82, 0xb4,
	0x95, 0x64, 0xfe, 0x57, 0x21, 0x0d, 0x17, 0xa5, 0xae, 0x67, 0x7e, 0x3f, 0xe7, 0x6b, 0xb8, 0xe3,
	0x26, 0x51, 0x86, 0xb9, 0xd1, 0x60, 0x37, 0x2a, 0x44, 0x06, 0xb9, 0x23, 0x06, 0x15, 0xfe, 0x1e,
	0xef, 0x64, 0x2f, 0x65, 0x96, 0x1a, 0x20, 0xff, 0x31, 0x51, 0xc0, 0x80, 0xaf, 0x21, 0xe1, 0x19,
	0x94, 0x86, 0x1b, 0x0e, 0xda, 0x43, 0x93, 0xf6, 0xb4, 0x4b, 0xfb, 0x0e, 0x59, 0x3c, 0x03, 0x64,
	0x8e, 0x3b, 0x95, 0x3d, 0xe8, 0xb5, 0x26, 0x68, 0xda, 0x9b, 0xfd, 0x0a, 0x95, 0x64, 0xe1, 0x1b,
	0xd1, 0x33, 0xd0, 0x3a, 0x5d, 0x01, 0x75, 0xd4, 0xe0, 0x01, 0xe1, 0x61, 0x33, 0x85, 0x5c, 0xe3,
	0x91, 0x82, 0x42, 0x18, 0x48, 0x64, 0xaa, 0x0c, 0x67, 0x5c, 0xa6, 0xa5, 0x49, 0x9c, 0x05, 0xb2,
	0x16, 0x93, 0xd0, 0x65, 0x09, 0xa9, 0x65, 0x9e, 0xbf, 0x10, 0x6b, 0xad, 0xd3, 0x0f, 0xf4, 0xa7,
	0x6a, 0x86, 0xe2, 0x2e, 0xfe, 0x54, 0xd4, 0x56, 0x41, 0x88, 0xbd, 0x13, 0x30, 0xc7, 0x79, 0xbe,
	0xc7, 0xa2, 0x70, 0x5f, 0x81, 0x36, 0x84, 0xe0, 0x8f, 0x4a, 0x88, 0xc2, 0x3a, 0x76, 0xa9, 0xfd,
	0x0e, 0x12, 0x3c, 0x6a, 0xe0, 0x6b, 0x29, 0x4a, 0x0d, 0x24, 0xc6, 0x5f, 0xf6, 0x2e, 0x5c, 0x17,
	0xd6, 0x9b, 0x8d, 0x0f, 0x5f, 0x75, 0x51, 0xde, 0x08, 0xfa, 0xea, 0xcc, 0xec, 0x11, 0xed, 0xad,
	0xe3, 0xc2, 0x2e, 0x99, 0x50, 0xdc, 0x71, 0x8b, 0x19, 0x34, 0x35, 0xeb, 0x37, 0x4e, 0x83, 0xdf,
	0xdb, 0x0d, 0xf2, 0x3e, 0xa3, 0x1f, 0xc8, 0xff, 0x56, 0x07, 0x21, 0x2e, 0xce, 0x51, 0x6b, 0x8a,
	0x48, 0x89, 0xfb, 0xef, 0x82, 0x90, 0x3f, 0x56, 0xe8, 0x50, 0x21, 0xfe, 0xf8, 0x10, 0x5c, 0xe7,
	0x0f, 0xfc, 0xed, 0x06, 0x0d, 0x9b, 0xfd, 0xe2, 0x7f, 0x57, 0x7f, 0x57, 0xdc, 0xdc, 0x56, 0xcb,
	0x90, 0x89, 0x22, 0x72, 0x8d, 0x44, 0xf6, 0x05, 0x32, 0x91, 0x47, 0x4a, 0xb2, 0x65, 0xc7, 0xfe,
	0xcd, 0x9f, 0x02, 0x00, 0x00, 0xff, 0xff, 0xee, 0xe7, 0x83, 0xfe, 0xea, 0x02, 0x00, 0x00,
}
