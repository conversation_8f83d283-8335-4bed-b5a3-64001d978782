// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc/media_relay.proto

package rpc

import (
	livekit "github.com/livekit/protocol/livekit"
	_ "github.com/livekit/psrpc/protoc-gen-psrpc/options"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RelayMediaRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StartRequest  *StartRequest          `protobuf:"bytes,1,opt,name=start_request,json=startRequest,proto3" json:"start_request,omitempty"`
	CloseRequest  *CloseRequest          `protobuf:"bytes,2,opt,name=close_request,json=closeRequest,proto3" json:"close_request,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RelayMediaRequest) Reset() {
	*x = RelayMediaRequest{}
	mi := &file_rpc_media_relay_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RelayMediaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelayMediaRequest) ProtoMessage() {}

func (x *RelayMediaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelayMediaRequest.ProtoReflect.Descriptor instead.
func (*RelayMediaRequest) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{0}
}

func (x *RelayMediaRequest) GetStartRequest() *StartRequest {
	if x != nil {
		return x.StartRequest
	}
	return nil
}

func (x *RelayMediaRequest) GetCloseRequest() *CloseRequest {
	if x != nil {
		return x.CloseRequest
	}
	return nil
}

type StartRequest struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	RoomName               string                 `protobuf:"bytes,1,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`
	SubscriberNodeId       string                 `protobuf:"bytes,2,opt,name=subscriber_node_id,json=subscriberNodeId,proto3" json:"subscriber_node_id,omitempty"`
	SubscriberNodeIp       string                 `protobuf:"bytes,3,opt,name=subscriber_node_ip,json=subscriberNodeIp,proto3" json:"subscriber_node_ip,omitempty"`
	SubscriberIdentity     string                 `protobuf:"bytes,4,opt,name=subscriber_identity,json=subscriberIdentity,proto3" json:"subscriber_identity,omitempty"`
	PublisherNodeId        string                 `protobuf:"bytes,5,opt,name=publisher_node_id,json=publisherNodeId,proto3" json:"publisher_node_id,omitempty"`
	PublisherNodeIp        string                 `protobuf:"bytes,6,opt,name=publisher_node_ip,json=publisherNodeIp,proto3" json:"publisher_node_ip,omitempty"`
	PublisherNodeRelayPort int32                  `protobuf:"varint,7,opt,name=publisher_node_relay_port,json=publisherNodeRelayPort,proto3" json:"publisher_node_relay_port,omitempty"`
	PublisherIdentity      string                 `protobuf:"bytes,8,opt,name=publisher_identity,json=publisherIdentity,proto3" json:"publisher_identity,omitempty"`
	TrackId                string                 `protobuf:"bytes,9,opt,name=track_id,json=trackId,proto3" json:"track_id,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *StartRequest) Reset() {
	*x = StartRequest{}
	mi := &file_rpc_media_relay_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartRequest) ProtoMessage() {}

func (x *StartRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartRequest.ProtoReflect.Descriptor instead.
func (*StartRequest) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{1}
}

func (x *StartRequest) GetRoomName() string {
	if x != nil {
		return x.RoomName
	}
	return ""
}

func (x *StartRequest) GetSubscriberNodeId() string {
	if x != nil {
		return x.SubscriberNodeId
	}
	return ""
}

func (x *StartRequest) GetSubscriberNodeIp() string {
	if x != nil {
		return x.SubscriberNodeIp
	}
	return ""
}

func (x *StartRequest) GetSubscriberIdentity() string {
	if x != nil {
		return x.SubscriberIdentity
	}
	return ""
}

func (x *StartRequest) GetPublisherNodeId() string {
	if x != nil {
		return x.PublisherNodeId
	}
	return ""
}

func (x *StartRequest) GetPublisherNodeIp() string {
	if x != nil {
		return x.PublisherNodeIp
	}
	return ""
}

func (x *StartRequest) GetPublisherNodeRelayPort() int32 {
	if x != nil {
		return x.PublisherNodeRelayPort
	}
	return 0
}

func (x *StartRequest) GetPublisherIdentity() string {
	if x != nil {
		return x.PublisherIdentity
	}
	return ""
}

func (x *StartRequest) GetTrackId() string {
	if x != nil {
		return x.TrackId
	}
	return ""
}

type CloseRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	RoomName         string                 `protobuf:"bytes,1,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`
	SubscriberNodeId string                 `protobuf:"bytes,2,opt,name=subscriber_node_id,json=subscriberNodeId,proto3" json:"subscriber_node_id,omitempty"`
	TrackId          string                 `protobuf:"bytes,3,opt,name=track_id,json=trackId,proto3" json:"track_id,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CloseRequest) Reset() {
	*x = CloseRequest{}
	mi := &file_rpc_media_relay_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CloseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloseRequest) ProtoMessage() {}

func (x *CloseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloseRequest.ProtoReflect.Descriptor instead.
func (*CloseRequest) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{2}
}

func (x *CloseRequest) GetRoomName() string {
	if x != nil {
		return x.RoomName
	}
	return ""
}

func (x *CloseRequest) GetSubscriberNodeId() string {
	if x != nil {
		return x.SubscriberNodeId
	}
	return ""
}

func (x *CloseRequest) GetTrackId() string {
	if x != nil {
		return x.TrackId
	}
	return ""
}

type RelayMediaResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ConnectionId  string                 `protobuf:"bytes,1,opt,name=connection_id,json=connectionId,proto3" json:"connection_id,omitempty"` // room_name + track_id
	Message       *MediaResponse         `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RelayMediaResponse) Reset() {
	*x = RelayMediaResponse{}
	mi := &file_rpc_media_relay_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RelayMediaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelayMediaResponse) ProtoMessage() {}

func (x *RelayMediaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelayMediaResponse.ProtoReflect.Descriptor instead.
func (*RelayMediaResponse) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{3}
}

func (x *RelayMediaResponse) GetConnectionId() string {
	if x != nil {
		return x.ConnectionId
	}
	return ""
}

func (x *RelayMediaResponse) GetMessage() *MediaResponse {
	if x != nil {
		return x.Message
	}
	return nil
}

type MediaResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Message:
	//
	//	*MediaResponse_StartResponse
	//	*MediaResponse_PacketResponse
	//	*MediaResponse_HandleRtcpSenderReportData
	//	*MediaResponse_Resync
	//	*MediaResponse_Closed
	Message       isMediaResponse_Message `protobuf_oneof:"message"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MediaResponse) Reset() {
	*x = MediaResponse{}
	mi := &file_rpc_media_relay_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MediaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaResponse) ProtoMessage() {}

func (x *MediaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaResponse.ProtoReflect.Descriptor instead.
func (*MediaResponse) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{4}
}

func (x *MediaResponse) GetMessage() isMediaResponse_Message {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *MediaResponse) GetStartResponse() *StartResponse {
	if x != nil {
		if x, ok := x.Message.(*MediaResponse_StartResponse); ok {
			return x.StartResponse
		}
	}
	return nil
}

func (x *MediaResponse) GetPacketResponse() *PacketResponse {
	if x != nil {
		if x, ok := x.Message.(*MediaResponse_PacketResponse); ok {
			return x.PacketResponse
		}
	}
	return nil
}

func (x *MediaResponse) GetHandleRtcpSenderReportData() *HandleRTCPSenderReportData {
	if x != nil {
		if x, ok := x.Message.(*MediaResponse_HandleRtcpSenderReportData); ok {
			return x.HandleRtcpSenderReportData
		}
	}
	return nil
}

func (x *MediaResponse) GetResync() *Resync {
	if x != nil {
		if x, ok := x.Message.(*MediaResponse_Resync); ok {
			return x.Resync
		}
	}
	return nil
}

func (x *MediaResponse) GetClosed() *Closed {
	if x != nil {
		if x, ok := x.Message.(*MediaResponse_Closed); ok {
			return x.Closed
		}
	}
	return nil
}

type isMediaResponse_Message interface {
	isMediaResponse_Message()
}

type MediaResponse_StartResponse struct {
	StartResponse *StartResponse `protobuf:"bytes,1,opt,name=start_response,json=startResponse,proto3,oneof"`
}

type MediaResponse_PacketResponse struct {
	PacketResponse *PacketResponse `protobuf:"bytes,2,opt,name=packet_response,json=packetResponse,proto3,oneof"`
}

type MediaResponse_HandleRtcpSenderReportData struct {
	HandleRtcpSenderReportData *HandleRTCPSenderReportData `protobuf:"bytes,3,opt,name=handle_rtcp_sender_report_data,json=handleRtcpSenderReportData,proto3,oneof"`
}

type MediaResponse_Resync struct {
	Resync *Resync `protobuf:"bytes,4,opt,name=resync,proto3,oneof"`
}

type MediaResponse_Closed struct {
	Closed *Closed `protobuf:"bytes,5,opt,name=closed,proto3,oneof"`
}

func (*MediaResponse_StartResponse) isMediaResponse_Message() {}

func (*MediaResponse_PacketResponse) isMediaResponse_Message() {}

func (*MediaResponse_HandleRtcpSenderReportData) isMediaResponse_Message() {}

func (*MediaResponse_Resync) isMediaResponse_Message() {}

func (*MediaResponse_Closed) isMediaResponse_Message() {}

type StartResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Msg           string                 `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StartResponse) Reset() {
	*x = StartResponse{}
	mi := &file_rpc_media_relay_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartResponse) ProtoMessage() {}

func (x *StartResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartResponse.ProtoReflect.Descriptor instead.
func (*StartResponse) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{5}
}

func (x *StartResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type PacketResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ExtPacket     *livekit.ExtPacket     `protobuf:"bytes,1,opt,name=extPacket,proto3" json:"extPacket,omitempty"`
	Layer         int32                  `protobuf:"varint,2,opt,name=layer,proto3" json:"layer,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PacketResponse) Reset() {
	*x = PacketResponse{}
	mi := &file_rpc_media_relay_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PacketResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PacketResponse) ProtoMessage() {}

func (x *PacketResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PacketResponse.ProtoReflect.Descriptor instead.
func (*PacketResponse) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{6}
}

func (x *PacketResponse) GetExtPacket() *livekit.ExtPacket {
	if x != nil {
		return x.ExtPacket
	}
	return nil
}

func (x *PacketResponse) GetLayer() int32 {
	if x != nil {
		return x.Layer
	}
	return 0
}

type HandleRTCPSenderReportData struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	PayloadType     int32                  `protobuf:"varint,1,opt,name=payloadType,proto3" json:"payloadType,omitempty"`
	IsSvc           bool                   `protobuf:"varint,2,opt,name=isSvc,proto3" json:"isSvc,omitempty"`
	Layer           int32                  `protobuf:"varint,3,opt,name=layer,proto3" json:"layer,omitempty"`
	PublisherSRData []byte                 `protobuf:"bytes,4,opt,name=publisherSRData,proto3" json:"publisherSRData,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *HandleRTCPSenderReportData) Reset() {
	*x = HandleRTCPSenderReportData{}
	mi := &file_rpc_media_relay_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandleRTCPSenderReportData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleRTCPSenderReportData) ProtoMessage() {}

func (x *HandleRTCPSenderReportData) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleRTCPSenderReportData.ProtoReflect.Descriptor instead.
func (*HandleRTCPSenderReportData) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{7}
}

func (x *HandleRTCPSenderReportData) GetPayloadType() int32 {
	if x != nil {
		return x.PayloadType
	}
	return 0
}

func (x *HandleRTCPSenderReportData) GetIsSvc() bool {
	if x != nil {
		return x.IsSvc
	}
	return false
}

func (x *HandleRTCPSenderReportData) GetLayer() int32 {
	if x != nil {
		return x.Layer
	}
	return 0
}

func (x *HandleRTCPSenderReportData) GetPublisherSRData() []byte {
	if x != nil {
		return x.PublisherSRData
	}
	return nil
}

type Resync struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Resync) Reset() {
	*x = Resync{}
	mi := &file_rpc_media_relay_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Resync) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Resync) ProtoMessage() {}

func (x *Resync) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Resync.ProtoReflect.Descriptor instead.
func (*Resync) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{8}
}

type Closed struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Closed) Reset() {
	*x = Closed{}
	mi := &file_rpc_media_relay_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Closed) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Closed) ProtoMessage() {}

func (x *Closed) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Closed.ProtoReflect.Descriptor instead.
func (*Closed) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{9}
}

// ----------------------------
type RelayTrackReceiverRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	NodeId              string                 `protobuf:"bytes,1,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	RoomName            string                 `protobuf:"bytes,2,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`
	ParticipantIdentity string                 `protobuf:"bytes,3,opt,name=participant_identity,json=participantIdentity,proto3" json:"participant_identity,omitempty"`
	TrackId             string                 `protobuf:"bytes,4,opt,name=track_id,json=trackId,proto3" json:"track_id,omitempty"`
	Request             *TrackReceiverRequest  `protobuf:"bytes,5,opt,name=request,proto3" json:"request,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *RelayTrackReceiverRequest) Reset() {
	*x = RelayTrackReceiverRequest{}
	mi := &file_rpc_media_relay_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RelayTrackReceiverRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelayTrackReceiverRequest) ProtoMessage() {}

func (x *RelayTrackReceiverRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelayTrackReceiverRequest.ProtoReflect.Descriptor instead.
func (*RelayTrackReceiverRequest) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{10}
}

func (x *RelayTrackReceiverRequest) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *RelayTrackReceiverRequest) GetRoomName() string {
	if x != nil {
		return x.RoomName
	}
	return ""
}

func (x *RelayTrackReceiverRequest) GetParticipantIdentity() string {
	if x != nil {
		return x.ParticipantIdentity
	}
	return ""
}

func (x *RelayTrackReceiverRequest) GetTrackId() string {
	if x != nil {
		return x.TrackId
	}
	return ""
}

func (x *RelayTrackReceiverRequest) GetRequest() *TrackReceiverRequest {
	if x != nil {
		return x.Request
	}
	return nil
}

type TrackReceiverRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Message:
	//
	//	*TrackReceiverRequest_CodecRequest
	//	*TrackReceiverRequest_HeaderExtensionsRequest
	//	*TrackReceiverRequest_SendPliRequest
	//	*TrackReceiverRequest_OnSetupReceiverRequest
	//	*TrackReceiverRequest_OnSubscriberMaxQualityChangeRequest
	//	*TrackReceiverRequest_OnCodecRegressionRequest
	//	*TrackReceiverRequest_OnTrackSubscribedRequest
	//	*TrackReceiverRequest_GetAudioLevelRequest
	//	*TrackReceiverRequest_GetTemporalLayerFpsForSpatialRequest
	//	*TrackReceiverRequest_SetUpTrackPausedRequest
	//	*TrackReceiverRequest_SetMaxExpectedSpatialLayerRequest
	Message       isTrackReceiverRequest_Message `protobuf_oneof:"message"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrackReceiverRequest) Reset() {
	*x = TrackReceiverRequest{}
	mi := &file_rpc_media_relay_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrackReceiverRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackReceiverRequest) ProtoMessage() {}

func (x *TrackReceiverRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackReceiverRequest.ProtoReflect.Descriptor instead.
func (*TrackReceiverRequest) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{11}
}

func (x *TrackReceiverRequest) GetMessage() isTrackReceiverRequest_Message {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *TrackReceiverRequest) GetCodecRequest() *Codec {
	if x != nil {
		if x, ok := x.Message.(*TrackReceiverRequest_CodecRequest); ok {
			return x.CodecRequest
		}
	}
	return nil
}

func (x *TrackReceiverRequest) GetHeaderExtensionsRequest() *HeaderExtensions {
	if x != nil {
		if x, ok := x.Message.(*TrackReceiverRequest_HeaderExtensionsRequest); ok {
			return x.HeaderExtensionsRequest
		}
	}
	return nil
}

func (x *TrackReceiverRequest) GetSendPliRequest() *SendPli {
	if x != nil {
		if x, ok := x.Message.(*TrackReceiverRequest_SendPliRequest); ok {
			return x.SendPliRequest
		}
	}
	return nil
}

func (x *TrackReceiverRequest) GetOnSetupReceiverRequest() *OnSetupReceiver {
	if x != nil {
		if x, ok := x.Message.(*TrackReceiverRequest_OnSetupReceiverRequest); ok {
			return x.OnSetupReceiverRequest
		}
	}
	return nil
}

func (x *TrackReceiverRequest) GetOnSubscriberMaxQualityChangeRequest() *OnSubscriberMaxQualityChange {
	if x != nil {
		if x, ok := x.Message.(*TrackReceiverRequest_OnSubscriberMaxQualityChangeRequest); ok {
			return x.OnSubscriberMaxQualityChangeRequest
		}
	}
	return nil
}

func (x *TrackReceiverRequest) GetOnCodecRegressionRequest() *OnCodecRegression {
	if x != nil {
		if x, ok := x.Message.(*TrackReceiverRequest_OnCodecRegressionRequest); ok {
			return x.OnCodecRegressionRequest
		}
	}
	return nil
}

func (x *TrackReceiverRequest) GetOnTrackSubscribedRequest() *OnTrackSubscribed {
	if x != nil {
		if x, ok := x.Message.(*TrackReceiverRequest_OnTrackSubscribedRequest); ok {
			return x.OnTrackSubscribedRequest
		}
	}
	return nil
}

func (x *TrackReceiverRequest) GetGetAudioLevelRequest() *GetAudioLevel {
	if x != nil {
		if x, ok := x.Message.(*TrackReceiverRequest_GetAudioLevelRequest); ok {
			return x.GetAudioLevelRequest
		}
	}
	return nil
}

func (x *TrackReceiverRequest) GetGetTemporalLayerFpsForSpatialRequest() *GetTemporalLayerFpsForSpatial {
	if x != nil {
		if x, ok := x.Message.(*TrackReceiverRequest_GetTemporalLayerFpsForSpatialRequest); ok {
			return x.GetTemporalLayerFpsForSpatialRequest
		}
	}
	return nil
}

func (x *TrackReceiverRequest) GetSetUpTrackPausedRequest() *SetUpTrackPaused {
	if x != nil {
		if x, ok := x.Message.(*TrackReceiverRequest_SetUpTrackPausedRequest); ok {
			return x.SetUpTrackPausedRequest
		}
	}
	return nil
}

func (x *TrackReceiverRequest) GetSetMaxExpectedSpatialLayerRequest() *SetMaxExpectedSpatialLayer {
	if x != nil {
		if x, ok := x.Message.(*TrackReceiverRequest_SetMaxExpectedSpatialLayerRequest); ok {
			return x.SetMaxExpectedSpatialLayerRequest
		}
	}
	return nil
}

type isTrackReceiverRequest_Message interface {
	isTrackReceiverRequest_Message()
}

type TrackReceiverRequest_CodecRequest struct {
	CodecRequest *Codec `protobuf:"bytes,1,opt,name=codec_request,json=codecRequest,proto3,oneof"`
}

type TrackReceiverRequest_HeaderExtensionsRequest struct {
	HeaderExtensionsRequest *HeaderExtensions `protobuf:"bytes,2,opt,name=header_extensions_request,json=headerExtensionsRequest,proto3,oneof"`
}

type TrackReceiverRequest_SendPliRequest struct {
	SendPliRequest *SendPli `protobuf:"bytes,3,opt,name=send_pli_request,json=sendPliRequest,proto3,oneof"`
}

type TrackReceiverRequest_OnSetupReceiverRequest struct {
	OnSetupReceiverRequest *OnSetupReceiver `protobuf:"bytes,4,opt,name=on_setup_receiver_request,json=onSetupReceiverRequest,proto3,oneof"`
}

type TrackReceiverRequest_OnSubscriberMaxQualityChangeRequest struct {
	OnSubscriberMaxQualityChangeRequest *OnSubscriberMaxQualityChange `protobuf:"bytes,5,opt,name=on_subscriber_max_quality_change_request,json=onSubscriberMaxQualityChangeRequest,proto3,oneof"`
}

type TrackReceiverRequest_OnCodecRegressionRequest struct {
	OnCodecRegressionRequest *OnCodecRegression `protobuf:"bytes,6,opt,name=on_codec_regression_request,json=onCodecRegressionRequest,proto3,oneof"`
}

type TrackReceiverRequest_OnTrackSubscribedRequest struct {
	OnTrackSubscribedRequest *OnTrackSubscribed `protobuf:"bytes,7,opt,name=on_track_subscribed_request,json=onTrackSubscribedRequest,proto3,oneof"`
}

type TrackReceiverRequest_GetAudioLevelRequest struct {
	GetAudioLevelRequest *GetAudioLevel `protobuf:"bytes,8,opt,name=get_audio_level_request,json=getAudioLevelRequest,proto3,oneof"`
}

type TrackReceiverRequest_GetTemporalLayerFpsForSpatialRequest struct {
	GetTemporalLayerFpsForSpatialRequest *GetTemporalLayerFpsForSpatial `protobuf:"bytes,9,opt,name=get_temporal_layer_fps_for_spatial_request,json=getTemporalLayerFpsForSpatialRequest,proto3,oneof"`
}

type TrackReceiverRequest_SetUpTrackPausedRequest struct {
	SetUpTrackPausedRequest *SetUpTrackPaused `protobuf:"bytes,10,opt,name=set_up_track_paused_request,json=setUpTrackPausedRequest,proto3,oneof"`
}

type TrackReceiverRequest_SetMaxExpectedSpatialLayerRequest struct {
	SetMaxExpectedSpatialLayerRequest *SetMaxExpectedSpatialLayer `protobuf:"bytes,11,opt,name=set_max_expected_spatial_layer_request,json=setMaxExpectedSpatialLayerRequest,proto3,oneof"`
}

func (*TrackReceiverRequest_CodecRequest) isTrackReceiverRequest_Message() {}

func (*TrackReceiverRequest_HeaderExtensionsRequest) isTrackReceiverRequest_Message() {}

func (*TrackReceiverRequest_SendPliRequest) isTrackReceiverRequest_Message() {}

func (*TrackReceiverRequest_OnSetupReceiverRequest) isTrackReceiverRequest_Message() {}

func (*TrackReceiverRequest_OnSubscriberMaxQualityChangeRequest) isTrackReceiverRequest_Message() {}

func (*TrackReceiverRequest_OnCodecRegressionRequest) isTrackReceiverRequest_Message() {}

func (*TrackReceiverRequest_OnTrackSubscribedRequest) isTrackReceiverRequest_Message() {}

func (*TrackReceiverRequest_GetAudioLevelRequest) isTrackReceiverRequest_Message() {}

func (*TrackReceiverRequest_GetTemporalLayerFpsForSpatialRequest) isTrackReceiverRequest_Message() {}

func (*TrackReceiverRequest_SetUpTrackPausedRequest) isTrackReceiverRequest_Message() {}

func (*TrackReceiverRequest_SetMaxExpectedSpatialLayerRequest) isTrackReceiverRequest_Message() {}

type Codec struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Codec) Reset() {
	*x = Codec{}
	mi := &file_rpc_media_relay_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Codec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Codec) ProtoMessage() {}

func (x *Codec) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Codec.ProtoReflect.Descriptor instead.
func (*Codec) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{12}
}

type HeaderExtensions struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HeaderExtensions) Reset() {
	*x = HeaderExtensions{}
	mi := &file_rpc_media_relay_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HeaderExtensions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeaderExtensions) ProtoMessage() {}

func (x *HeaderExtensions) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeaderExtensions.ProtoReflect.Descriptor instead.
func (*HeaderExtensions) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{13}
}

type SendPli struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Layer         int32                  `protobuf:"varint,1,opt,name=layer,proto3" json:"layer,omitempty"`
	Force         bool                   `protobuf:"varint,2,opt,name=force,proto3" json:"force,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendPli) Reset() {
	*x = SendPli{}
	mi := &file_rpc_media_relay_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendPli) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendPli) ProtoMessage() {}

func (x *SendPli) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendPli.ProtoReflect.Descriptor instead.
func (*SendPli) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{14}
}

func (x *SendPli) GetLayer() int32 {
	if x != nil {
		return x.Layer
	}
	return 0
}

func (x *SendPli) GetForce() bool {
	if x != nil {
		return x.Force
	}
	return false
}

type OnSetupReceiver struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MimeType      int32                  `protobuf:"varint,1,opt,name=mime_type,json=mimeType,proto3" json:"mime_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OnSetupReceiver) Reset() {
	*x = OnSetupReceiver{}
	mi := &file_rpc_media_relay_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OnSetupReceiver) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnSetupReceiver) ProtoMessage() {}

func (x *OnSetupReceiver) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnSetupReceiver.ProtoReflect.Descriptor instead.
func (*OnSetupReceiver) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{15}
}

func (x *OnSetupReceiver) GetMimeType() int32 {
	if x != nil {
		return x.MimeType
	}
	return 0
}

type OnSubscriberMaxQualityChange struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SubscriberId  string                 `protobuf:"bytes,1,opt,name=subscriber_id,json=subscriberId,proto3" json:"subscriber_id,omitempty"`
	MimeType      int32                  `protobuf:"varint,2,opt,name=mime_type,json=mimeType,proto3" json:"mime_type,omitempty"`
	Layer         int32                  `protobuf:"varint,3,opt,name=layer,proto3" json:"layer,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OnSubscriberMaxQualityChange) Reset() {
	*x = OnSubscriberMaxQualityChange{}
	mi := &file_rpc_media_relay_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OnSubscriberMaxQualityChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnSubscriberMaxQualityChange) ProtoMessage() {}

func (x *OnSubscriberMaxQualityChange) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnSubscriberMaxQualityChange.ProtoReflect.Descriptor instead.
func (*OnSubscriberMaxQualityChange) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{16}
}

func (x *OnSubscriberMaxQualityChange) GetSubscriberId() string {
	if x != nil {
		return x.SubscriberId
	}
	return ""
}

func (x *OnSubscriberMaxQualityChange) GetMimeType() int32 {
	if x != nil {
		return x.MimeType
	}
	return 0
}

func (x *OnSubscriberMaxQualityChange) GetLayer() int32 {
	if x != nil {
		return x.Layer
	}
	return 0
}

type OnCodecRegression struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Old           []byte                 `protobuf:"bytes,1,opt,name=old,proto3" json:"old,omitempty"`
	New           []byte                 `protobuf:"bytes,2,opt,name=new,proto3" json:"new,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OnCodecRegression) Reset() {
	*x = OnCodecRegression{}
	mi := &file_rpc_media_relay_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OnCodecRegression) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnCodecRegression) ProtoMessage() {}

func (x *OnCodecRegression) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnCodecRegression.ProtoReflect.Descriptor instead.
func (*OnCodecRegression) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{17}
}

func (x *OnCodecRegression) GetOld() []byte {
	if x != nil {
		return x.Old
	}
	return nil
}

func (x *OnCodecRegression) GetNew() []byte {
	if x != nil {
		return x.New
	}
	return nil
}

type OnTrackSubscribed struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OnTrackSubscribed) Reset() {
	*x = OnTrackSubscribed{}
	mi := &file_rpc_media_relay_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OnTrackSubscribed) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnTrackSubscribed) ProtoMessage() {}

func (x *OnTrackSubscribed) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnTrackSubscribed.ProtoReflect.Descriptor instead.
func (*OnTrackSubscribed) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{18}
}

type GetAudioLevel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAudioLevel) Reset() {
	*x = GetAudioLevel{}
	mi := &file_rpc_media_relay_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAudioLevel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAudioLevel) ProtoMessage() {}

func (x *GetAudioLevel) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAudioLevel.ProtoReflect.Descriptor instead.
func (*GetAudioLevel) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{19}
}

type GetTemporalLayerFpsForSpatial struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Spatial       int32                  `protobuf:"varint,1,opt,name=spatial,proto3" json:"spatial,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTemporalLayerFpsForSpatial) Reset() {
	*x = GetTemporalLayerFpsForSpatial{}
	mi := &file_rpc_media_relay_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTemporalLayerFpsForSpatial) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTemporalLayerFpsForSpatial) ProtoMessage() {}

func (x *GetTemporalLayerFpsForSpatial) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTemporalLayerFpsForSpatial.ProtoReflect.Descriptor instead.
func (*GetTemporalLayerFpsForSpatial) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{20}
}

func (x *GetTemporalLayerFpsForSpatial) GetSpatial() int32 {
	if x != nil {
		return x.Spatial
	}
	return 0
}

type SetUpTrackPaused struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Paused        bool                   `protobuf:"varint,1,opt,name=paused,proto3" json:"paused,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetUpTrackPaused) Reset() {
	*x = SetUpTrackPaused{}
	mi := &file_rpc_media_relay_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetUpTrackPaused) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetUpTrackPaused) ProtoMessage() {}

func (x *SetUpTrackPaused) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetUpTrackPaused.ProtoReflect.Descriptor instead.
func (*SetUpTrackPaused) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{21}
}

func (x *SetUpTrackPaused) GetPaused() bool {
	if x != nil {
		return x.Paused
	}
	return false
}

type SetMaxExpectedSpatialLayer struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Layer         int32                  `protobuf:"varint,1,opt,name=layer,proto3" json:"layer,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetMaxExpectedSpatialLayer) Reset() {
	*x = SetMaxExpectedSpatialLayer{}
	mi := &file_rpc_media_relay_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetMaxExpectedSpatialLayer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetMaxExpectedSpatialLayer) ProtoMessage() {}

func (x *SetMaxExpectedSpatialLayer) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetMaxExpectedSpatialLayer.ProtoReflect.Descriptor instead.
func (*SetMaxExpectedSpatialLayer) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{22}
}

func (x *SetMaxExpectedSpatialLayer) GetLayer() int32 {
	if x != nil {
		return x.Layer
	}
	return 0
}

type RelayTrackReceiverResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Message:
	//
	//	*RelayTrackReceiverResponse_CodecResponse
	//	*RelayTrackReceiverResponse_HeaderExtensionsResponse
	//	*RelayTrackReceiverResponse_GetAudioLevelResponse
	//	*RelayTrackReceiverResponse_GetTemporalLayerFpsForSpatialResponse
	Message       isRelayTrackReceiverResponse_Message `protobuf_oneof:"message"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RelayTrackReceiverResponse) Reset() {
	*x = RelayTrackReceiverResponse{}
	mi := &file_rpc_media_relay_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RelayTrackReceiverResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelayTrackReceiverResponse) ProtoMessage() {}

func (x *RelayTrackReceiverResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelayTrackReceiverResponse.ProtoReflect.Descriptor instead.
func (*RelayTrackReceiverResponse) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{23}
}

func (x *RelayTrackReceiverResponse) GetMessage() isRelayTrackReceiverResponse_Message {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *RelayTrackReceiverResponse) GetCodecResponse() *CodecResponse {
	if x != nil {
		if x, ok := x.Message.(*RelayTrackReceiverResponse_CodecResponse); ok {
			return x.CodecResponse
		}
	}
	return nil
}

func (x *RelayTrackReceiverResponse) GetHeaderExtensionsResponse() *HeaderExtensionsResponse {
	if x != nil {
		if x, ok := x.Message.(*RelayTrackReceiverResponse_HeaderExtensionsResponse); ok {
			return x.HeaderExtensionsResponse
		}
	}
	return nil
}

func (x *RelayTrackReceiverResponse) GetGetAudioLevelResponse() *GetAudioLevelResponse {
	if x != nil {
		if x, ok := x.Message.(*RelayTrackReceiverResponse_GetAudioLevelResponse); ok {
			return x.GetAudioLevelResponse
		}
	}
	return nil
}

func (x *RelayTrackReceiverResponse) GetGetTemporalLayerFpsForSpatialResponse() *GetTemporalLayerFpsForSpatialResponse {
	if x != nil {
		if x, ok := x.Message.(*RelayTrackReceiverResponse_GetTemporalLayerFpsForSpatialResponse); ok {
			return x.GetTemporalLayerFpsForSpatialResponse
		}
	}
	return nil
}

type isRelayTrackReceiverResponse_Message interface {
	isRelayTrackReceiverResponse_Message()
}

type RelayTrackReceiverResponse_CodecResponse struct {
	CodecResponse *CodecResponse `protobuf:"bytes,1,opt,name=codec_response,json=codecResponse,proto3,oneof"`
}

type RelayTrackReceiverResponse_HeaderExtensionsResponse struct {
	HeaderExtensionsResponse *HeaderExtensionsResponse `protobuf:"bytes,2,opt,name=header_extensions_response,json=headerExtensionsResponse,proto3,oneof"`
}

type RelayTrackReceiverResponse_GetAudioLevelResponse struct {
	GetAudioLevelResponse *GetAudioLevelResponse `protobuf:"bytes,3,opt,name=get_audio_level_response,json=getAudioLevelResponse,proto3,oneof"`
}

type RelayTrackReceiverResponse_GetTemporalLayerFpsForSpatialResponse struct {
	GetTemporalLayerFpsForSpatialResponse *GetTemporalLayerFpsForSpatialResponse `protobuf:"bytes,4,opt,name=get_temporal_layer_fps_for_spatial_response,json=getTemporalLayerFpsForSpatialResponse,proto3,oneof"`
}

func (*RelayTrackReceiverResponse_CodecResponse) isRelayTrackReceiverResponse_Message() {}

func (*RelayTrackReceiverResponse_HeaderExtensionsResponse) isRelayTrackReceiverResponse_Message() {}

func (*RelayTrackReceiverResponse_GetAudioLevelResponse) isRelayTrackReceiverResponse_Message() {}

func (*RelayTrackReceiverResponse_GetTemporalLayerFpsForSpatialResponse) isRelayTrackReceiverResponse_Message() {
}

type CodecResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Bytes         []byte                 `protobuf:"bytes,1,opt,name=bytes,proto3" json:"bytes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CodecResponse) Reset() {
	*x = CodecResponse{}
	mi := &file_rpc_media_relay_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CodecResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CodecResponse) ProtoMessage() {}

func (x *CodecResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CodecResponse.ProtoReflect.Descriptor instead.
func (*CodecResponse) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{24}
}

func (x *CodecResponse) GetBytes() []byte {
	if x != nil {
		return x.Bytes
	}
	return nil
}

type HeaderExtensionsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Bytes         []byte                 `protobuf:"bytes,1,opt,name=bytes,proto3" json:"bytes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HeaderExtensionsResponse) Reset() {
	*x = HeaderExtensionsResponse{}
	mi := &file_rpc_media_relay_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HeaderExtensionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeaderExtensionsResponse) ProtoMessage() {}

func (x *HeaderExtensionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeaderExtensionsResponse.ProtoReflect.Descriptor instead.
func (*HeaderExtensionsResponse) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{25}
}

func (x *HeaderExtensionsResponse) GetBytes() []byte {
	if x != nil {
		return x.Bytes
	}
	return nil
}

type GetAudioLevelResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Level         float64                `protobuf:"fixed64,1,opt,name=level,proto3" json:"level,omitempty"`
	Active        bool                   `protobuf:"varint,2,opt,name=active,proto3" json:"active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAudioLevelResponse) Reset() {
	*x = GetAudioLevelResponse{}
	mi := &file_rpc_media_relay_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAudioLevelResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAudioLevelResponse) ProtoMessage() {}

func (x *GetAudioLevelResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAudioLevelResponse.ProtoReflect.Descriptor instead.
func (*GetAudioLevelResponse) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{26}
}

func (x *GetAudioLevelResponse) GetLevel() float64 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *GetAudioLevelResponse) GetActive() bool {
	if x != nil {
		return x.Active
	}
	return false
}

type GetTemporalLayerFpsForSpatialResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Fps           []float32              `protobuf:"fixed32,1,rep,packed,name=fps,proto3" json:"fps,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTemporalLayerFpsForSpatialResponse) Reset() {
	*x = GetTemporalLayerFpsForSpatialResponse{}
	mi := &file_rpc_media_relay_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTemporalLayerFpsForSpatialResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTemporalLayerFpsForSpatialResponse) ProtoMessage() {}

func (x *GetTemporalLayerFpsForSpatialResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_media_relay_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTemporalLayerFpsForSpatialResponse.ProtoReflect.Descriptor instead.
func (*GetTemporalLayerFpsForSpatialResponse) Descriptor() ([]byte, []int) {
	return file_rpc_media_relay_proto_rawDescGZIP(), []int{27}
}

func (x *GetTemporalLayerFpsForSpatialResponse) GetFps() []float32 {
	if x != nil {
		return x.Fps
	}
	return nil
}

var File_rpc_media_relay_proto protoreflect.FileDescriptor

const file_rpc_media_relay_proto_rawDesc = "" +
	"\n" +
	"\x15rpc/media_relay.proto\x12\x03rpc\x1a\roptions.proto\x1a\x17livekit_extpacket.proto\"\x83\x01\n" +
	"\x11RelayMediaRequest\x126\n" +
	"\rstart_request\x18\x01 \x01(\v2\x11.rpc.StartRequestR\fstartRequest\x126\n" +
	"\rclose_request\x18\x02 \x01(\v2\x11.rpc.CloseRequestR\fcloseRequest\"\x95\x03\n" +
	"\fStartRequest\x12\x1b\n" +
	"\troom_name\x18\x01 \x01(\tR\broomName\x12,\n" +
	"\x12subscriber_node_id\x18\x02 \x01(\tR\x10subscriberNodeId\x12,\n" +
	"\x12subscriber_node_ip\x18\x03 \x01(\tR\x10subscriberNodeIp\x12/\n" +
	"\x13subscriber_identity\x18\x04 \x01(\tR\x12subscriberIdentity\x12*\n" +
	"\x11publisher_node_id\x18\x05 \x01(\tR\x0fpublisherNodeId\x12*\n" +
	"\x11publisher_node_ip\x18\x06 \x01(\tR\x0fpublisherNodeIp\x129\n" +
	"\x19publisher_node_relay_port\x18\a \x01(\x05R\x16publisherNodeRelayPort\x12-\n" +
	"\x12publisher_identity\x18\b \x01(\tR\x11publisherIdentity\x12\x19\n" +
	"\btrack_id\x18\t \x01(\tR\atrackId\"t\n" +
	"\fCloseRequest\x12\x1b\n" +
	"\troom_name\x18\x01 \x01(\tR\broomName\x12,\n" +
	"\x12subscriber_node_id\x18\x02 \x01(\tR\x10subscriberNodeId\x12\x19\n" +
	"\btrack_id\x18\x03 \x01(\tR\atrackId\"g\n" +
	"\x12RelayMediaResponse\x12#\n" +
	"\rconnection_id\x18\x01 \x01(\tR\fconnectionId\x12,\n" +
	"\amessage\x18\x02 \x01(\v2\x12.rpc.MediaResponseR\amessage\"\xcc\x02\n" +
	"\rMediaResponse\x12;\n" +
	"\x0estart_response\x18\x01 \x01(\v2\x12.rpc.StartResponseH\x00R\rstartResponse\x12>\n" +
	"\x0fpacket_response\x18\x02 \x01(\v2\x13.rpc.PacketResponseH\x00R\x0epacketResponse\x12e\n" +
	"\x1ehandle_rtcp_sender_report_data\x18\x03 \x01(\v2\x1f.rpc.HandleRTCPSenderReportDataH\x00R\x1ahandleRtcpSenderReportData\x12%\n" +
	"\x06resync\x18\x04 \x01(\v2\v.rpc.ResyncH\x00R\x06resync\x12%\n" +
	"\x06closed\x18\x05 \x01(\v2\v.rpc.ClosedH\x00R\x06closedB\t\n" +
	"\amessage\"!\n" +
	"\rStartResponse\x12\x10\n" +
	"\x03msg\x18\x01 \x01(\tR\x03msg\"X\n" +
	"\x0ePacketResponse\x120\n" +
	"\textPacket\x18\x01 \x01(\v2\x12.livekit.ExtPacketR\textPacket\x12\x14\n" +
	"\x05layer\x18\x02 \x01(\x05R\x05layer\"\x94\x01\n" +
	"\x1aHandleRTCPSenderReportData\x12 \n" +
	"\vpayloadType\x18\x01 \x01(\x05R\vpayloadType\x12\x14\n" +
	"\x05isSvc\x18\x02 \x01(\bR\x05isSvc\x12\x14\n" +
	"\x05layer\x18\x03 \x01(\x05R\x05layer\x12(\n" +
	"\x0fpublisherSRData\x18\x04 \x01(\fR\x0fpublisherSRData\"\b\n" +
	"\x06Resync\"\b\n" +
	"\x06Closed\"\xd4\x01\n" +
	"\x19RelayTrackReceiverRequest\x12\x17\n" +
	"\anode_id\x18\x01 \x01(\tR\x06nodeId\x12\x1b\n" +
	"\troom_name\x18\x02 \x01(\tR\broomName\x121\n" +
	"\x14participant_identity\x18\x03 \x01(\tR\x13participantIdentity\x12\x19\n" +
	"\btrack_id\x18\x04 \x01(\tR\atrackId\x123\n" +
	"\arequest\x18\x05 \x01(\v2\x19.rpc.TrackReceiverRequestR\arequest\"\xfe\a\n" +
	"\x14TrackReceiverRequest\x121\n" +
	"\rcodec_request\x18\x01 \x01(\v2\n" +
	".rpc.CodecH\x00R\fcodecRequest\x12S\n" +
	"\x19header_extensions_request\x18\x02 \x01(\v2\x15.rpc.HeaderExtensionsH\x00R\x17headerExtensionsRequest\x128\n" +
	"\x10send_pli_request\x18\x03 \x01(\v2\f.rpc.SendPliH\x00R\x0esendPliRequest\x12Q\n" +
	"\x19on_setup_receiver_request\x18\x04 \x01(\v2\x14.rpc.OnSetupReceiverH\x00R\x16onSetupReceiverRequest\x12z\n" +
	"(on_subscriber_max_quality_change_request\x18\x05 \x01(\v2!.rpc.OnSubscriberMaxQualityChangeH\x00R#onSubscriberMaxQualityChangeRequest\x12W\n" +
	"\x1bon_codec_regression_request\x18\x06 \x01(\v2\x16.rpc.OnCodecRegressionH\x00R\x18onCodecRegressionRequest\x12W\n" +
	"\x1bon_track_subscribed_request\x18\a \x01(\v2\x16.rpc.OnTrackSubscribedH\x00R\x18onTrackSubscribedRequest\x12K\n" +
	"\x17get_audio_level_request\x18\b \x01(\v2\x12.rpc.GetAudioLevelH\x00R\x14getAudioLevelRequest\x12~\n" +
	"*get_temporal_layer_fps_for_spatial_request\x18\t \x01(\v2\".rpc.GetTemporalLayerFpsForSpatialH\x00R$getTemporalLayerFpsForSpatialRequest\x12U\n" +
	"\x1bset_up_track_paused_request\x18\n" +
	" \x01(\v2\x15.rpc.SetUpTrackPausedH\x00R\x17setUpTrackPausedRequest\x12t\n" +
	"&set_max_expected_spatial_layer_request\x18\v \x01(\v2\x1f.rpc.SetMaxExpectedSpatialLayerH\x00R!setMaxExpectedSpatialLayerRequestB\t\n" +
	"\amessage\"\a\n" +
	"\x05Codec\"\x12\n" +
	"\x10HeaderExtensions\"5\n" +
	"\aSendPli\x12\x14\n" +
	"\x05layer\x18\x01 \x01(\x05R\x05layer\x12\x14\n" +
	"\x05force\x18\x02 \x01(\bR\x05force\".\n" +
	"\x0fOnSetupReceiver\x12\x1b\n" +
	"\tmime_type\x18\x01 \x01(\x05R\bmimeType\"v\n" +
	"\x1cOnSubscriberMaxQualityChange\x12#\n" +
	"\rsubscriber_id\x18\x01 \x01(\tR\fsubscriberId\x12\x1b\n" +
	"\tmime_type\x18\x02 \x01(\x05R\bmimeType\x12\x14\n" +
	"\x05layer\x18\x03 \x01(\x05R\x05layer\"7\n" +
	"\x11OnCodecRegression\x12\x10\n" +
	"\x03old\x18\x01 \x01(\fR\x03old\x12\x10\n" +
	"\x03new\x18\x02 \x01(\fR\x03new\"\x13\n" +
	"\x11OnTrackSubscribed\"\x0f\n" +
	"\rGetAudioLevel\"9\n" +
	"\x1dGetTemporalLayerFpsForSpatial\x12\x18\n" +
	"\aspatial\x18\x01 \x01(\x05R\aspatial\"*\n" +
	"\x10SetUpTrackPaused\x12\x16\n" +
	"\x06paused\x18\x01 \x01(\bR\x06paused\"2\n" +
	"\x1aSetMaxExpectedSpatialLayer\x12\x14\n" +
	"\x05layer\x18\x01 \x01(\x05R\x05layer\"\xa5\x03\n" +
	"\x1aRelayTrackReceiverResponse\x12;\n" +
	"\x0ecodec_response\x18\x01 \x01(\v2\x12.rpc.CodecResponseH\x00R\rcodecResponse\x12]\n" +
	"\x1aheader_extensions_response\x18\x02 \x01(\v2\x1d.rpc.HeaderExtensionsResponseH\x00R\x18headerExtensionsResponse\x12U\n" +
	"\x18get_audio_level_response\x18\x03 \x01(\v2\x1a.rpc.GetAudioLevelResponseH\x00R\x15getAudioLevelResponse\x12\x88\x01\n" +
	"+get_temporal_layer_fps_for_spatial_response\x18\x04 \x01(\v2*.rpc.GetTemporalLayerFpsForSpatialResponseH\x00R%getTemporalLayerFpsForSpatialResponseB\t\n" +
	"\amessage\"%\n" +
	"\rCodecResponse\x12\x14\n" +
	"\x05bytes\x18\x01 \x01(\fR\x05bytes\"0\n" +
	"\x18HeaderExtensionsResponse\x12\x14\n" +
	"\x05bytes\x18\x01 \x01(\fR\x05bytes\"E\n" +
	"\x15GetAudioLevelResponse\x12\x14\n" +
	"\x05level\x18\x01 \x01(\x01R\x05level\x12\x16\n" +
	"\x06active\x18\x02 \x01(\bR\x06active\"9\n" +
	"%GetTemporalLayerFpsForSpatialResponse\x12\x10\n" +
	"\x03fps\x18\x01 \x03(\x02R\x03fps2\x80\x01\n" +
	"\n" +
	"MediaRelay\x12r\n" +
	"\x12RelayTrackReceiver\x12\x1e.rpc.RelayTrackReceiverRequest\x1a\x1f.rpc.RelayTrackReceiverResponse\"\x1b\xb2\x89\x01\x17\x10\x01\x1a\x13\n" +
	"\x04node\x12\anode_id\x18\x01 \x01B!Z\x1fgithub.com/livekit/protocol/rpcb\x06proto3"

var (
	file_rpc_media_relay_proto_rawDescOnce sync.Once
	file_rpc_media_relay_proto_rawDescData []byte
)

func file_rpc_media_relay_proto_rawDescGZIP() []byte {
	file_rpc_media_relay_proto_rawDescOnce.Do(func() {
		file_rpc_media_relay_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_media_relay_proto_rawDesc), len(file_rpc_media_relay_proto_rawDesc)))
	})
	return file_rpc_media_relay_proto_rawDescData
}

var file_rpc_media_relay_proto_msgTypes = make([]protoimpl.MessageInfo, 28)
var file_rpc_media_relay_proto_goTypes = []any{
	(*RelayMediaRequest)(nil),                     // 0: rpc.RelayMediaRequest
	(*StartRequest)(nil),                          // 1: rpc.StartRequest
	(*CloseRequest)(nil),                          // 2: rpc.CloseRequest
	(*RelayMediaResponse)(nil),                    // 3: rpc.RelayMediaResponse
	(*MediaResponse)(nil),                         // 4: rpc.MediaResponse
	(*StartResponse)(nil),                         // 5: rpc.StartResponse
	(*PacketResponse)(nil),                        // 6: rpc.PacketResponse
	(*HandleRTCPSenderReportData)(nil),            // 7: rpc.HandleRTCPSenderReportData
	(*Resync)(nil),                                // 8: rpc.Resync
	(*Closed)(nil),                                // 9: rpc.Closed
	(*RelayTrackReceiverRequest)(nil),             // 10: rpc.RelayTrackReceiverRequest
	(*TrackReceiverRequest)(nil),                  // 11: rpc.TrackReceiverRequest
	(*Codec)(nil),                                 // 12: rpc.Codec
	(*HeaderExtensions)(nil),                      // 13: rpc.HeaderExtensions
	(*SendPli)(nil),                               // 14: rpc.SendPli
	(*OnSetupReceiver)(nil),                       // 15: rpc.OnSetupReceiver
	(*OnSubscriberMaxQualityChange)(nil),          // 16: rpc.OnSubscriberMaxQualityChange
	(*OnCodecRegression)(nil),                     // 17: rpc.OnCodecRegression
	(*OnTrackSubscribed)(nil),                     // 18: rpc.OnTrackSubscribed
	(*GetAudioLevel)(nil),                         // 19: rpc.GetAudioLevel
	(*GetTemporalLayerFpsForSpatial)(nil),         // 20: rpc.GetTemporalLayerFpsForSpatial
	(*SetUpTrackPaused)(nil),                      // 21: rpc.SetUpTrackPaused
	(*SetMaxExpectedSpatialLayer)(nil),            // 22: rpc.SetMaxExpectedSpatialLayer
	(*RelayTrackReceiverResponse)(nil),            // 23: rpc.RelayTrackReceiverResponse
	(*CodecResponse)(nil),                         // 24: rpc.CodecResponse
	(*HeaderExtensionsResponse)(nil),              // 25: rpc.HeaderExtensionsResponse
	(*GetAudioLevelResponse)(nil),                 // 26: rpc.GetAudioLevelResponse
	(*GetTemporalLayerFpsForSpatialResponse)(nil), // 27: rpc.GetTemporalLayerFpsForSpatialResponse
	(*livekit.ExtPacket)(nil),                     // 28: livekit.ExtPacket
}
var file_rpc_media_relay_proto_depIdxs = []int32{
	1,  // 0: rpc.RelayMediaRequest.start_request:type_name -> rpc.StartRequest
	2,  // 1: rpc.RelayMediaRequest.close_request:type_name -> rpc.CloseRequest
	4,  // 2: rpc.RelayMediaResponse.message:type_name -> rpc.MediaResponse
	5,  // 3: rpc.MediaResponse.start_response:type_name -> rpc.StartResponse
	6,  // 4: rpc.MediaResponse.packet_response:type_name -> rpc.PacketResponse
	7,  // 5: rpc.MediaResponse.handle_rtcp_sender_report_data:type_name -> rpc.HandleRTCPSenderReportData
	8,  // 6: rpc.MediaResponse.resync:type_name -> rpc.Resync
	9,  // 7: rpc.MediaResponse.closed:type_name -> rpc.Closed
	28, // 8: rpc.PacketResponse.extPacket:type_name -> livekit.ExtPacket
	11, // 9: rpc.RelayTrackReceiverRequest.request:type_name -> rpc.TrackReceiverRequest
	12, // 10: rpc.TrackReceiverRequest.codec_request:type_name -> rpc.Codec
	13, // 11: rpc.TrackReceiverRequest.header_extensions_request:type_name -> rpc.HeaderExtensions
	14, // 12: rpc.TrackReceiverRequest.send_pli_request:type_name -> rpc.SendPli
	15, // 13: rpc.TrackReceiverRequest.on_setup_receiver_request:type_name -> rpc.OnSetupReceiver
	16, // 14: rpc.TrackReceiverRequest.on_subscriber_max_quality_change_request:type_name -> rpc.OnSubscriberMaxQualityChange
	17, // 15: rpc.TrackReceiverRequest.on_codec_regression_request:type_name -> rpc.OnCodecRegression
	18, // 16: rpc.TrackReceiverRequest.on_track_subscribed_request:type_name -> rpc.OnTrackSubscribed
	19, // 17: rpc.TrackReceiverRequest.get_audio_level_request:type_name -> rpc.GetAudioLevel
	20, // 18: rpc.TrackReceiverRequest.get_temporal_layer_fps_for_spatial_request:type_name -> rpc.GetTemporalLayerFpsForSpatial
	21, // 19: rpc.TrackReceiverRequest.set_up_track_paused_request:type_name -> rpc.SetUpTrackPaused
	22, // 20: rpc.TrackReceiverRequest.set_max_expected_spatial_layer_request:type_name -> rpc.SetMaxExpectedSpatialLayer
	24, // 21: rpc.RelayTrackReceiverResponse.codec_response:type_name -> rpc.CodecResponse
	25, // 22: rpc.RelayTrackReceiverResponse.header_extensions_response:type_name -> rpc.HeaderExtensionsResponse
	26, // 23: rpc.RelayTrackReceiverResponse.get_audio_level_response:type_name -> rpc.GetAudioLevelResponse
	27, // 24: rpc.RelayTrackReceiverResponse.get_temporal_layer_fps_for_spatial_response:type_name -> rpc.GetTemporalLayerFpsForSpatialResponse
	10, // 25: rpc.MediaRelay.RelayTrackReceiver:input_type -> rpc.RelayTrackReceiverRequest
	23, // 26: rpc.MediaRelay.RelayTrackReceiver:output_type -> rpc.RelayTrackReceiverResponse
	26, // [26:27] is the sub-list for method output_type
	25, // [25:26] is the sub-list for method input_type
	25, // [25:25] is the sub-list for extension type_name
	25, // [25:25] is the sub-list for extension extendee
	0,  // [0:25] is the sub-list for field type_name
}

func init() { file_rpc_media_relay_proto_init() }
func file_rpc_media_relay_proto_init() {
	if File_rpc_media_relay_proto != nil {
		return
	}
	file_rpc_media_relay_proto_msgTypes[4].OneofWrappers = []any{
		(*MediaResponse_StartResponse)(nil),
		(*MediaResponse_PacketResponse)(nil),
		(*MediaResponse_HandleRtcpSenderReportData)(nil),
		(*MediaResponse_Resync)(nil),
		(*MediaResponse_Closed)(nil),
	}
	file_rpc_media_relay_proto_msgTypes[11].OneofWrappers = []any{
		(*TrackReceiverRequest_CodecRequest)(nil),
		(*TrackReceiverRequest_HeaderExtensionsRequest)(nil),
		(*TrackReceiverRequest_SendPliRequest)(nil),
		(*TrackReceiverRequest_OnSetupReceiverRequest)(nil),
		(*TrackReceiverRequest_OnSubscriberMaxQualityChangeRequest)(nil),
		(*TrackReceiverRequest_OnCodecRegressionRequest)(nil),
		(*TrackReceiverRequest_OnTrackSubscribedRequest)(nil),
		(*TrackReceiverRequest_GetAudioLevelRequest)(nil),
		(*TrackReceiverRequest_GetTemporalLayerFpsForSpatialRequest)(nil),
		(*TrackReceiverRequest_SetUpTrackPausedRequest)(nil),
		(*TrackReceiverRequest_SetMaxExpectedSpatialLayerRequest)(nil),
	}
	file_rpc_media_relay_proto_msgTypes[23].OneofWrappers = []any{
		(*RelayTrackReceiverResponse_CodecResponse)(nil),
		(*RelayTrackReceiverResponse_HeaderExtensionsResponse)(nil),
		(*RelayTrackReceiverResponse_GetAudioLevelResponse)(nil),
		(*RelayTrackReceiverResponse_GetTemporalLayerFpsForSpatialResponse)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_media_relay_proto_rawDesc), len(file_rpc_media_relay_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   28,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rpc_media_relay_proto_goTypes,
		DependencyIndexes: file_rpc_media_relay_proto_depIdxs,
		MessageInfos:      file_rpc_media_relay_proto_msgTypes,
	}.Build()
	File_rpc_media_relay_proto = out.File
	file_rpc_media_relay_proto_goTypes = nil
	file_rpc_media_relay_proto_depIdxs = nil
}
