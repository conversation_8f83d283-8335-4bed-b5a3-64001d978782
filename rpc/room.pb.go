// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc/room.proto

package rpc

import (
	livekit "github.com/livekit/protocol/livekit"
	_ "github.com/livekit/psrpc/protoc-gen-psrpc/options"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_rpc_room_proto protoreflect.FileDescriptor

const file_rpc_room_proto_rawDesc = "" +
	"\n" +
	"\x0erpc/room.proto\x12\x03rpc\x1a\roptions.proto\x1a\x14livekit_models.proto\x1a\x12livekit_room.proto2\x9f\x02\n" +
	"\x04Room\x12]\n" +
	"\n" +
	"DeleteRoom\x12\x1a.livekit.DeleteRoomRequest\x1a\x1b.livekit.DeleteRoomResponse\"\x16\xb2\x89\x01\x12\x10\x01\x1a\x0e\n" +
	"\x04room\x12\x04room\x18\x01\x12W\n" +
	"\bSendData\x12\x18.livekit.SendDataRequest\x1a\x19.livekit.SendDataResponse\"\x16\xb2\x89\x01\x12\x10\x01\x1a\x0e\n" +
	"\x04room\x12\x04room\x18\x01\x12_\n" +
	"\x12UpdateRoomMetadata\x12\".livekit.UpdateRoomMetadataRequest\x1a\r.livekit.Room\"\x16\xb2\x89\x01\x12\x10\x01\x1a\x0e\n" +
	"\x04room\x12\x04room\x18\x01B!Z\x1fgithub.com/livekit/protocol/rpcb\x06proto3"

var file_rpc_room_proto_goTypes = []any{
	(*livekit.DeleteRoomRequest)(nil),         // 0: livekit.DeleteRoomRequest
	(*livekit.SendDataRequest)(nil),           // 1: livekit.SendDataRequest
	(*livekit.UpdateRoomMetadataRequest)(nil), // 2: livekit.UpdateRoomMetadataRequest
	(*livekit.DeleteRoomResponse)(nil),        // 3: livekit.DeleteRoomResponse
	(*livekit.SendDataResponse)(nil),          // 4: livekit.SendDataResponse
	(*livekit.Room)(nil),                      // 5: livekit.Room
}
var file_rpc_room_proto_depIdxs = []int32{
	0, // 0: rpc.Room.DeleteRoom:input_type -> livekit.DeleteRoomRequest
	1, // 1: rpc.Room.SendData:input_type -> livekit.SendDataRequest
	2, // 2: rpc.Room.UpdateRoomMetadata:input_type -> livekit.UpdateRoomMetadataRequest
	3, // 3: rpc.Room.DeleteRoom:output_type -> livekit.DeleteRoomResponse
	4, // 4: rpc.Room.SendData:output_type -> livekit.SendDataResponse
	5, // 5: rpc.Room.UpdateRoomMetadata:output_type -> livekit.Room
	3, // [3:6] is the sub-list for method output_type
	0, // [0:3] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_rpc_room_proto_init() }
func file_rpc_room_proto_init() {
	if File_rpc_room_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_room_proto_rawDesc), len(file_rpc_room_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rpc_room_proto_goTypes,
		DependencyIndexes: file_rpc_room_proto_depIdxs,
	}.Build()
	File_rpc_room_proto = out.File
	file_rpc_room_proto_goTypes = nil
	file_rpc_room_proto_depIdxs = nil
}
