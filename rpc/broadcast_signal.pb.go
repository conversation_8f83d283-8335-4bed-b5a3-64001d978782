// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc/broadcast_signal.proto

package rpc

import (
	livekit "github.com/livekit/protocol/livekit"
	_ "github.com/livekit/psrpc/protoc-gen-psrpc/options"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BroadcastUpdate struct {
	state             protoimpl.MessageState  `protogen:"open.v1"`
	ReceiveIdentities []string                `protobuf:"bytes,1,rep,name=receive_identities,json=receiveIdentities,proto3" json:"receive_identities,omitempty"`
	Update            *BroadcastUpdateMessage `protobuf:"bytes,2,opt,name=update,proto3" json:"update,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *BroadcastUpdate) Reset() {
	*x = BroadcastUpdate{}
	mi := &file_rpc_broadcast_signal_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BroadcastUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BroadcastUpdate) ProtoMessage() {}

func (x *BroadcastUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_broadcast_signal_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BroadcastUpdate.ProtoReflect.Descriptor instead.
func (*BroadcastUpdate) Descriptor() ([]byte, []int) {
	return file_rpc_broadcast_signal_proto_rawDescGZIP(), []int{0}
}

func (x *BroadcastUpdate) GetReceiveIdentities() []string {
	if x != nil {
		return x.ReceiveIdentities
	}
	return nil
}

func (x *BroadcastUpdate) GetUpdate() *BroadcastUpdateMessage {
	if x != nil {
		return x.Update
	}
	return nil
}

type BroadcastUpdateMessage struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Message:
	//
	//	*BroadcastUpdateMessage_RemoteParticipantUpdate
	Message       isBroadcastUpdateMessage_Message `protobuf_oneof:"message"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BroadcastUpdateMessage) Reset() {
	*x = BroadcastUpdateMessage{}
	mi := &file_rpc_broadcast_signal_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BroadcastUpdateMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BroadcastUpdateMessage) ProtoMessage() {}

func (x *BroadcastUpdateMessage) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_broadcast_signal_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BroadcastUpdateMessage.ProtoReflect.Descriptor instead.
func (*BroadcastUpdateMessage) Descriptor() ([]byte, []int) {
	return file_rpc_broadcast_signal_proto_rawDescGZIP(), []int{1}
}

func (x *BroadcastUpdateMessage) GetMessage() isBroadcastUpdateMessage_Message {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *BroadcastUpdateMessage) GetRemoteParticipantUpdate() *livekit.RemoteParticipantUpdate {
	if x != nil {
		if x, ok := x.Message.(*BroadcastUpdateMessage_RemoteParticipantUpdate); ok {
			return x.RemoteParticipantUpdate
		}
	}
	return nil
}

type isBroadcastUpdateMessage_Message interface {
	isBroadcastUpdateMessage_Message()
}

type BroadcastUpdateMessage_RemoteParticipantUpdate struct {
	RemoteParticipantUpdate *livekit.RemoteParticipantUpdate `protobuf:"bytes,1,opt,name=remote_participant_update,json=remoteParticipantUpdate,proto3,oneof"`
}

func (*BroadcastUpdateMessage_RemoteParticipantUpdate) isBroadcastUpdateMessage_Message() {}

type GetAllParticipantRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Room          string                 `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllParticipantRequest) Reset() {
	*x = GetAllParticipantRequest{}
	mi := &file_rpc_broadcast_signal_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllParticipantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllParticipantRequest) ProtoMessage() {}

func (x *GetAllParticipantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_broadcast_signal_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllParticipantRequest.ProtoReflect.Descriptor instead.
func (*GetAllParticipantRequest) Descriptor() ([]byte, []int) {
	return file_rpc_broadcast_signal_proto_rawDescGZIP(), []int{2}
}

func (x *GetAllParticipantRequest) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

type GetAllParticipantResponse struct {
	state         protoimpl.MessageState           `protogen:"open.v1"`
	Participants  []*livekit.RemoteParticipantInfo `protobuf:"bytes,1,rep,name=participants,proto3" json:"participants,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllParticipantResponse) Reset() {
	*x = GetAllParticipantResponse{}
	mi := &file_rpc_broadcast_signal_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllParticipantResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllParticipantResponse) ProtoMessage() {}

func (x *GetAllParticipantResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_broadcast_signal_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllParticipantResponse.ProtoReflect.Descriptor instead.
func (*GetAllParticipantResponse) Descriptor() ([]byte, []int) {
	return file_rpc_broadcast_signal_proto_rawDescGZIP(), []int{3}
}

func (x *GetAllParticipantResponse) GetParticipants() []*livekit.RemoteParticipantInfo {
	if x != nil {
		return x.Participants
	}
	return nil
}

var File_rpc_broadcast_signal_proto protoreflect.FileDescriptor

const file_rpc_broadcast_signal_proto_rawDesc = "" +
	"\n" +
	"\x1arpc/broadcast_signal.proto\x12\x03rpc\x1a\roptions.proto\x1a\x11livekit_rtc.proto\x1a\x14livekit_models.proto\"u\n" +
	"\x0fBroadcastUpdate\x12-\n" +
	"\x12receive_identities\x18\x01 \x03(\tR\x11receiveIdentities\x123\n" +
	"\x06update\x18\x02 \x01(\v2\x1b.rpc.BroadcastUpdateMessageR\x06update\"\x83\x01\n" +
	"\x16BroadcastUpdateMessage\x12^\n" +
	"\x19remote_participant_update\x18\x01 \x01(\v2 .livekit.RemoteParticipantUpdateH\x00R\x17remoteParticipantUpdateB\t\n" +
	"\amessage\".\n" +
	"\x18GetAllParticipantRequest\x12\x12\n" +
	"\x04room\x18\x01 \x01(\tR\x04room\"_\n" +
	"\x19GetAllParticipantResponse\x12B\n" +
	"\fparticipants\x18\x01 \x03(\v2\x1e.livekit.RemoteParticipantInfoR\fparticipants2\xd1\x01\n" +
	"\x0fBroadcastSignal\x12P\n" +
	"\x06Update\x12\x14.rpc.BroadcastUpdate\x1a\x14.rpc.BroadcastUpdate\"\x1a\xb2\x89\x01\x16\b\x01\x10\x01\x1a\x0e\n" +
	"\x04room\x12\x04room\x18\x01(\x01\x12l\n" +
	"\x11GetAllParticipant\x12\x1d.rpc.GetAllParticipantRequest\x1a\x1e.rpc.GetAllParticipantResponse\"\x18\xb2\x89\x01\x14\x10\x01\x1a\x0e\n" +
	"\x04room\x12\x04room\x18\x01(\x01B!Z\x1fgithub.com/livekit/protocol/rpcb\x06proto3"

var (
	file_rpc_broadcast_signal_proto_rawDescOnce sync.Once
	file_rpc_broadcast_signal_proto_rawDescData []byte
)

func file_rpc_broadcast_signal_proto_rawDescGZIP() []byte {
	file_rpc_broadcast_signal_proto_rawDescOnce.Do(func() {
		file_rpc_broadcast_signal_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_broadcast_signal_proto_rawDesc), len(file_rpc_broadcast_signal_proto_rawDesc)))
	})
	return file_rpc_broadcast_signal_proto_rawDescData
}

var file_rpc_broadcast_signal_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_rpc_broadcast_signal_proto_goTypes = []any{
	(*BroadcastUpdate)(nil),                 // 0: rpc.BroadcastUpdate
	(*BroadcastUpdateMessage)(nil),          // 1: rpc.BroadcastUpdateMessage
	(*GetAllParticipantRequest)(nil),        // 2: rpc.GetAllParticipantRequest
	(*GetAllParticipantResponse)(nil),       // 3: rpc.GetAllParticipantResponse
	(*livekit.RemoteParticipantUpdate)(nil), // 4: livekit.RemoteParticipantUpdate
	(*livekit.RemoteParticipantInfo)(nil),   // 5: livekit.RemoteParticipantInfo
}
var file_rpc_broadcast_signal_proto_depIdxs = []int32{
	1, // 0: rpc.BroadcastUpdate.update:type_name -> rpc.BroadcastUpdateMessage
	4, // 1: rpc.BroadcastUpdateMessage.remote_participant_update:type_name -> livekit.RemoteParticipantUpdate
	5, // 2: rpc.GetAllParticipantResponse.participants:type_name -> livekit.RemoteParticipantInfo
	0, // 3: rpc.BroadcastSignal.Update:input_type -> rpc.BroadcastUpdate
	2, // 4: rpc.BroadcastSignal.GetAllParticipant:input_type -> rpc.GetAllParticipantRequest
	0, // 5: rpc.BroadcastSignal.Update:output_type -> rpc.BroadcastUpdate
	3, // 6: rpc.BroadcastSignal.GetAllParticipant:output_type -> rpc.GetAllParticipantResponse
	5, // [5:7] is the sub-list for method output_type
	3, // [3:5] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_rpc_broadcast_signal_proto_init() }
func file_rpc_broadcast_signal_proto_init() {
	if File_rpc_broadcast_signal_proto != nil {
		return
	}
	file_rpc_broadcast_signal_proto_msgTypes[1].OneofWrappers = []any{
		(*BroadcastUpdateMessage_RemoteParticipantUpdate)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_broadcast_signal_proto_rawDesc), len(file_rpc_broadcast_signal_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rpc_broadcast_signal_proto_goTypes,
		DependencyIndexes: file_rpc_broadcast_signal_proto_depIdxs,
		MessageInfos:      file_rpc_broadcast_signal_proto_msgTypes,
	}.Build()
	File_rpc_broadcast_signal_proto = out.File
	file_rpc_broadcast_signal_proto_goTypes = nil
	file_rpc_broadcast_signal_proto_depIdxs = nil
}
