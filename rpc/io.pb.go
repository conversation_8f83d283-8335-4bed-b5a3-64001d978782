// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc/io.proto

package rpc

import (
	livekit "github.com/livekit/protocol/livekit"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SIPDispatchResult int32

const (
	SIPDispatchResult_LEGACY_ACCEPT_OR_PIN SIPDispatchResult = 0 // check request_pin field
	SIPDispatchResult_ACCEPT               SIPDispatchResult = 1
	SIPDispatchResult_REQUEST_PIN          SIPDispatchResult = 2
	SIPDispatchResult_REJECT               SIPDispatchResult = 3
	SIPDispatchResult_DROP                 SIPDispatchResult = 4
)

// Enum value maps for SIPDispatchResult.
var (
	SIPDispatchResult_name = map[int32]string{
		0: "LEGACY_ACCEPT_OR_PIN",
		1: "ACCEPT",
		2: "REQUEST_PIN",
		3: "REJECT",
		4: "DROP",
	}
	SIPDispatchResult_value = map[string]int32{
		"LEGACY_ACCEPT_OR_PIN": 0,
		"ACCEPT":               1,
		"REQUEST_PIN":          2,
		"REJECT":               3,
		"DROP":                 4,
	}
)

func (x SIPDispatchResult) Enum() *SIPDispatchResult {
	p := new(SIPDispatchResult)
	*p = x
	return p
}

func (x SIPDispatchResult) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SIPDispatchResult) Descriptor() protoreflect.EnumDescriptor {
	return file_rpc_io_proto_enumTypes[0].Descriptor()
}

func (SIPDispatchResult) Type() protoreflect.EnumType {
	return &file_rpc_io_proto_enumTypes[0]
}

func (x SIPDispatchResult) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SIPDispatchResult.Descriptor instead.
func (SIPDispatchResult) EnumDescriptor() ([]byte, []int) {
	return file_rpc_io_proto_rawDescGZIP(), []int{0}
}

type GetEgressRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EgressId      string                 `protobuf:"bytes,1,opt,name=egress_id,json=egressId,proto3" json:"egress_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEgressRequest) Reset() {
	*x = GetEgressRequest{}
	mi := &file_rpc_io_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEgressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEgressRequest) ProtoMessage() {}

func (x *GetEgressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_io_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEgressRequest.ProtoReflect.Descriptor instead.
func (*GetEgressRequest) Descriptor() ([]byte, []int) {
	return file_rpc_io_proto_rawDescGZIP(), []int{0}
}

func (x *GetEgressRequest) GetEgressId() string {
	if x != nil {
		return x.EgressId
	}
	return ""
}

type UpdateMetricsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Info          *livekit.EgressInfo    `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	AvgCpuUsage   float32                `protobuf:"fixed32,3,opt,name=avg_cpu_usage,json=avgCpuUsage,proto3" json:"avg_cpu_usage,omitempty"`
	MaxCpuUsage   float32                `protobuf:"fixed32,4,opt,name=max_cpu_usage,json=maxCpuUsage,proto3" json:"max_cpu_usage,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateMetricsRequest) Reset() {
	*x = UpdateMetricsRequest{}
	mi := &file_rpc_io_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateMetricsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMetricsRequest) ProtoMessage() {}

func (x *UpdateMetricsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_io_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMetricsRequest.ProtoReflect.Descriptor instead.
func (*UpdateMetricsRequest) Descriptor() ([]byte, []int) {
	return file_rpc_io_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateMetricsRequest) GetInfo() *livekit.EgressInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *UpdateMetricsRequest) GetAvgCpuUsage() float32 {
	if x != nil {
		return x.AvgCpuUsage
	}
	return 0
}

func (x *UpdateMetricsRequest) GetMaxCpuUsage() float32 {
	if x != nil {
		return x.MaxCpuUsage
	}
	return 0
}

// Query an ingress info from an ingress ID or stream key
type GetIngressInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IngressId     string                 `protobuf:"bytes,1,opt,name=ingress_id,json=ingressId,proto3" json:"ingress_id,omitempty"`
	StreamKey     string                 `protobuf:"bytes,2,opt,name=stream_key,json=streamKey,proto3" json:"stream_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetIngressInfoRequest) Reset() {
	*x = GetIngressInfoRequest{}
	mi := &file_rpc_io_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIngressInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIngressInfoRequest) ProtoMessage() {}

func (x *GetIngressInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_io_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIngressInfoRequest.ProtoReflect.Descriptor instead.
func (*GetIngressInfoRequest) Descriptor() ([]byte, []int) {
	return file_rpc_io_proto_rawDescGZIP(), []int{2}
}

func (x *GetIngressInfoRequest) GetIngressId() string {
	if x != nil {
		return x.IngressId
	}
	return ""
}

func (x *GetIngressInfoRequest) GetStreamKey() string {
	if x != nil {
		return x.StreamKey
	}
	return ""
}

type GetIngressInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Info          *livekit.IngressInfo   `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	Token         string                 `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	WsUrl         string                 `protobuf:"bytes,3,opt,name=ws_url,json=wsUrl,proto3" json:"ws_url,omitempty"`
	LoggingFields map[string]string      `protobuf:"bytes,4,rep,name=logging_fields,json=loggingFields,proto3" json:"logging_fields,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetIngressInfoResponse) Reset() {
	*x = GetIngressInfoResponse{}
	mi := &file_rpc_io_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIngressInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIngressInfoResponse) ProtoMessage() {}

func (x *GetIngressInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_io_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIngressInfoResponse.ProtoReflect.Descriptor instead.
func (*GetIngressInfoResponse) Descriptor() ([]byte, []int) {
	return file_rpc_io_proto_rawDescGZIP(), []int{3}
}

func (x *GetIngressInfoResponse) GetInfo() *livekit.IngressInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *GetIngressInfoResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *GetIngressInfoResponse) GetWsUrl() string {
	if x != nil {
		return x.WsUrl
	}
	return ""
}

func (x *GetIngressInfoResponse) GetLoggingFields() map[string]string {
	if x != nil {
		return x.LoggingFields
	}
	return nil
}

// Request to store an update to the ingress state ingress -> service
type UpdateIngressStateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IngressId     string                 `protobuf:"bytes,1,opt,name=ingress_id,json=ingressId,proto3" json:"ingress_id,omitempty"`
	State         *livekit.IngressState  `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateIngressStateRequest) Reset() {
	*x = UpdateIngressStateRequest{}
	mi := &file_rpc_io_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateIngressStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateIngressStateRequest) ProtoMessage() {}

func (x *UpdateIngressStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_io_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateIngressStateRequest.ProtoReflect.Descriptor instead.
func (*UpdateIngressStateRequest) Descriptor() ([]byte, []int) {
	return file_rpc_io_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateIngressStateRequest) GetIngressId() string {
	if x != nil {
		return x.IngressId
	}
	return ""
}

func (x *UpdateIngressStateRequest) GetState() *livekit.IngressState {
	if x != nil {
		return x.State
	}
	return nil
}

type GetSIPTrunkAuthenticationRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Deprecated: Marked as deprecated in rpc/io.proto.
	SipCallId string `protobuf:"bytes,6,opt,name=sip_call_id,json=sipCallId,proto3" json:"sip_call_id,omitempty"`
	// What Number is calling
	//
	// Deprecated: Marked as deprecated in rpc/io.proto.
	From string `protobuf:"bytes,2,opt,name=from,proto3" json:"from,omitempty"`
	// What Host is calling
	//
	// Deprecated: Marked as deprecated in rpc/io.proto.
	FromHost string `protobuf:"bytes,7,opt,name=from_host,json=fromHost,proto3" json:"from_host,omitempty"`
	// What Number was called
	//
	// Deprecated: Marked as deprecated in rpc/io.proto.
	To string `protobuf:"bytes,3,opt,name=to,proto3" json:"to,omitempty"`
	// What Host was called
	//
	// Deprecated: Marked as deprecated in rpc/io.proto.
	ToHost string `protobuf:"bytes,5,opt,name=to_host,json=toHost,proto3" json:"to_host,omitempty"`
	// What is the IP address of the called number
	//
	// Deprecated: Marked as deprecated in rpc/io.proto.
	SrcAddress    string   `protobuf:"bytes,4,opt,name=src_address,json=srcAddress,proto3" json:"src_address,omitempty"`
	Call          *SIPCall `protobuf:"bytes,8,opt,name=call,proto3" json:"call,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSIPTrunkAuthenticationRequest) Reset() {
	*x = GetSIPTrunkAuthenticationRequest{}
	mi := &file_rpc_io_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSIPTrunkAuthenticationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSIPTrunkAuthenticationRequest) ProtoMessage() {}

func (x *GetSIPTrunkAuthenticationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_io_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSIPTrunkAuthenticationRequest.ProtoReflect.Descriptor instead.
func (*GetSIPTrunkAuthenticationRequest) Descriptor() ([]byte, []int) {
	return file_rpc_io_proto_rawDescGZIP(), []int{5}
}

// Deprecated: Marked as deprecated in rpc/io.proto.
func (x *GetSIPTrunkAuthenticationRequest) GetSipCallId() string {
	if x != nil {
		return x.SipCallId
	}
	return ""
}

// Deprecated: Marked as deprecated in rpc/io.proto.
func (x *GetSIPTrunkAuthenticationRequest) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

// Deprecated: Marked as deprecated in rpc/io.proto.
func (x *GetSIPTrunkAuthenticationRequest) GetFromHost() string {
	if x != nil {
		return x.FromHost
	}
	return ""
}

// Deprecated: Marked as deprecated in rpc/io.proto.
func (x *GetSIPTrunkAuthenticationRequest) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

// Deprecated: Marked as deprecated in rpc/io.proto.
func (x *GetSIPTrunkAuthenticationRequest) GetToHost() string {
	if x != nil {
		return x.ToHost
	}
	return ""
}

// Deprecated: Marked as deprecated in rpc/io.proto.
func (x *GetSIPTrunkAuthenticationRequest) GetSrcAddress() string {
	if x != nil {
		return x.SrcAddress
	}
	return ""
}

func (x *GetSIPTrunkAuthenticationRequest) GetCall() *SIPCall {
	if x != nil {
		return x.Call
	}
	return nil
}

type GetSIPTrunkAuthenticationResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Expected username and password
	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	Drop     bool   `protobuf:"varint,3,opt,name=drop,proto3" json:"drop,omitempty"`
	// Trunk used to fulfill this request
	SipTrunkId string `protobuf:"bytes,4,opt,name=sip_trunk_id,json=sipTrunkId,proto3" json:"sip_trunk_id,omitempty"`
	// Used in Cloud only
	ProjectId     string `protobuf:"bytes,5,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSIPTrunkAuthenticationResponse) Reset() {
	*x = GetSIPTrunkAuthenticationResponse{}
	mi := &file_rpc_io_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSIPTrunkAuthenticationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSIPTrunkAuthenticationResponse) ProtoMessage() {}

func (x *GetSIPTrunkAuthenticationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_io_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSIPTrunkAuthenticationResponse.ProtoReflect.Descriptor instead.
func (*GetSIPTrunkAuthenticationResponse) Descriptor() ([]byte, []int) {
	return file_rpc_io_proto_rawDescGZIP(), []int{6}
}

func (x *GetSIPTrunkAuthenticationResponse) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *GetSIPTrunkAuthenticationResponse) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *GetSIPTrunkAuthenticationResponse) GetDrop() bool {
	if x != nil {
		return x.Drop
	}
	return false
}

func (x *GetSIPTrunkAuthenticationResponse) GetSipTrunkId() string {
	if x != nil {
		return x.SipTrunkId
	}
	return ""
}

func (x *GetSIPTrunkAuthenticationResponse) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

type EvaluateSIPDispatchRulesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Deprecated: Marked as deprecated in rpc/io.proto.
	SipCallId string `protobuf:"bytes,8,opt,name=sip_call_id,json=sipCallId,proto3" json:"sip_call_id,omitempty"`
	// Deprecated: Marked as deprecated in rpc/io.proto.
	SipParticipantId string `protobuf:"bytes,1,opt,name=sip_participant_id,json=sipParticipantId,proto3" json:"sip_participant_id,omitempty"`
	// Trunk from the auth response, if any
	SipTrunkId string `protobuf:"bytes,10,opt,name=sip_trunk_id,json=sipTrunkId,proto3" json:"sip_trunk_id,omitempty"`
	// What Number is calling
	//
	// Deprecated: Marked as deprecated in rpc/io.proto.
	CallingNumber string `protobuf:"bytes,2,opt,name=calling_number,json=callingNumber,proto3" json:"calling_number,omitempty"`
	// What Host is calling
	//
	// Deprecated: Marked as deprecated in rpc/io.proto.
	CallingHost string `protobuf:"bytes,11,opt,name=calling_host,json=callingHost,proto3" json:"calling_host,omitempty"`
	// What Number was called
	//
	// Deprecated: Marked as deprecated in rpc/io.proto.
	CalledNumber string `protobuf:"bytes,3,opt,name=called_number,json=calledNumber,proto3" json:"called_number,omitempty"`
	// What is the IP address of the called number
	//
	// Deprecated: Marked as deprecated in rpc/io.proto.
	SrcAddress string `protobuf:"bytes,4,opt,name=src_address,json=srcAddress,proto3" json:"src_address,omitempty"`
	// What pin has been entered if any
	Pin string `protobuf:"bytes,5,opt,name=pin,proto3" json:"pin,omitempty"`
	// Caller rejected pin prompt, only try public rooms
	NoPin bool `protobuf:"varint,6,opt,name=no_pin,json=noPin,proto3" json:"no_pin,omitempty"`
	// What Host was called
	//
	// Deprecated: Marked as deprecated in rpc/io.proto.
	CalledHost string `protobuf:"bytes,7,opt,name=called_host,json=calledHost,proto3" json:"called_host,omitempty"`
	// Extra participant attributes added for this call.
	// Usually include provider-specific metadata.
	ExtraAttributes map[string]string `protobuf:"bytes,9,rep,name=extra_attributes,json=extraAttributes,proto3" json:"extra_attributes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Call            *SIPCall          `protobuf:"bytes,12,opt,name=call,proto3" json:"call,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *EvaluateSIPDispatchRulesRequest) Reset() {
	*x = EvaluateSIPDispatchRulesRequest{}
	mi := &file_rpc_io_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EvaluateSIPDispatchRulesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluateSIPDispatchRulesRequest) ProtoMessage() {}

func (x *EvaluateSIPDispatchRulesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_io_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluateSIPDispatchRulesRequest.ProtoReflect.Descriptor instead.
func (*EvaluateSIPDispatchRulesRequest) Descriptor() ([]byte, []int) {
	return file_rpc_io_proto_rawDescGZIP(), []int{7}
}

// Deprecated: Marked as deprecated in rpc/io.proto.
func (x *EvaluateSIPDispatchRulesRequest) GetSipCallId() string {
	if x != nil {
		return x.SipCallId
	}
	return ""
}

// Deprecated: Marked as deprecated in rpc/io.proto.
func (x *EvaluateSIPDispatchRulesRequest) GetSipParticipantId() string {
	if x != nil {
		return x.SipParticipantId
	}
	return ""
}

func (x *EvaluateSIPDispatchRulesRequest) GetSipTrunkId() string {
	if x != nil {
		return x.SipTrunkId
	}
	return ""
}

// Deprecated: Marked as deprecated in rpc/io.proto.
func (x *EvaluateSIPDispatchRulesRequest) GetCallingNumber() string {
	if x != nil {
		return x.CallingNumber
	}
	return ""
}

// Deprecated: Marked as deprecated in rpc/io.proto.
func (x *EvaluateSIPDispatchRulesRequest) GetCallingHost() string {
	if x != nil {
		return x.CallingHost
	}
	return ""
}

// Deprecated: Marked as deprecated in rpc/io.proto.
func (x *EvaluateSIPDispatchRulesRequest) GetCalledNumber() string {
	if x != nil {
		return x.CalledNumber
	}
	return ""
}

// Deprecated: Marked as deprecated in rpc/io.proto.
func (x *EvaluateSIPDispatchRulesRequest) GetSrcAddress() string {
	if x != nil {
		return x.SrcAddress
	}
	return ""
}

func (x *EvaluateSIPDispatchRulesRequest) GetPin() string {
	if x != nil {
		return x.Pin
	}
	return ""
}

func (x *EvaluateSIPDispatchRulesRequest) GetNoPin() bool {
	if x != nil {
		return x.NoPin
	}
	return false
}

// Deprecated: Marked as deprecated in rpc/io.proto.
func (x *EvaluateSIPDispatchRulesRequest) GetCalledHost() string {
	if x != nil {
		return x.CalledHost
	}
	return ""
}

func (x *EvaluateSIPDispatchRulesRequest) GetExtraAttributes() map[string]string {
	if x != nil {
		return x.ExtraAttributes
	}
	return nil
}

func (x *EvaluateSIPDispatchRulesRequest) GetCall() *SIPCall {
	if x != nil {
		return x.Call
	}
	return nil
}

type EvaluateSIPDispatchRulesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// non-empty string if SIPParticipant should be placed a room
	RoomName string `protobuf:"bytes,1,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`
	// What should be used for the LiveKit participant identity
	ParticipantIdentity string `protobuf:"bytes,2,opt,name=participant_identity,json=participantIdentity,proto3" json:"participant_identity,omitempty"`
	// What should be used for the LiveKit participant name
	ParticipantName string `protobuf:"bytes,7,opt,name=participant_name,json=participantName,proto3" json:"participant_name,omitempty"`
	// What should be used for the LiveKit participant metadata
	ParticipantMetadata string `protobuf:"bytes,8,opt,name=participant_metadata,json=participantMetadata,proto3" json:"participant_metadata,omitempty"`
	// What should be used for the LiveKit participant attributes
	ParticipantAttributes map[string]string `protobuf:"bytes,11,rep,name=participant_attributes,json=participantAttributes,proto3" json:"participant_attributes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Deprecated: Marked as deprecated in rpc/io.proto.
	RequestPin bool `protobuf:"varint,3,opt,name=request_pin,json=requestPin,proto3" json:"request_pin,omitempty"` // see result
	// optional token that should be used when creating LiveKit participant
	Token string `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"`
	// optional websocket url that should be used when creating LiveKit participant
	WsUrl  string            `protobuf:"bytes,5,opt,name=ws_url,json=wsUrl,proto3" json:"ws_url,omitempty"`
	Result SIPDispatchResult `protobuf:"varint,6,opt,name=result,proto3,enum=rpc.SIPDispatchResult" json:"result,omitempty"`
	// Trunk used to fulfill this request
	SipTrunkId string `protobuf:"bytes,9,opt,name=sip_trunk_id,json=sipTrunkId,proto3" json:"sip_trunk_id,omitempty"`
	// Dispatch Rule used to fulfill this request
	SipDispatchRuleId string `protobuf:"bytes,10,opt,name=sip_dispatch_rule_id,json=sipDispatchRuleId,proto3" json:"sip_dispatch_rule_id,omitempty"`
	// Used in Cloud only
	ProjectId string `protobuf:"bytes,12,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// Include these SIP X-* headers in 200 OK response to INVITE.
	// These headers are included as-is and may help identify triggered LiveKit Dispatch Rule for the other SIP endpoint.
	Headers map[string]string `protobuf:"bytes,13,rep,name=headers,proto3" json:"headers,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Map SIP X-* headers from INVITE to SIP participant attributes.
	// Keys are the names of X-* headers and values are the names of attributes they will be mapped to.
	HeadersToAttributes map[string]string `protobuf:"bytes,14,rep,name=headers_to_attributes,json=headersToAttributes,proto3" json:"headers_to_attributes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Map LiveKit attributes to SIP X-* headers when sending BYE or REFER requests.
	// Keys are the names of attributes and values are the names of X-* headers they will be mapped to.
	AttributesToHeaders map[string]string `protobuf:"bytes,18,rep,name=attributes_to_headers,json=attributesToHeaders,proto3" json:"attributes_to_headers,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Map SIP headers from INVITE to sip.h.* participant attributes automatically.
	//
	// When the names of required headers is known, using headers_to_attributes is strongly recommended.
	//
	// When mapping INVITE headers to response headers with attributes_to_headers map,
	// lowercase header names should be used, for example: sip.h.x-custom-header.
	IncludeHeaders  livekit.SIPHeaderOptions `protobuf:"varint,19,opt,name=include_headers,json=includeHeaders,proto3,enum=livekit.SIPHeaderOptions" json:"include_headers,omitempty"`
	EnabledFeatures []livekit.SIPFeature     `protobuf:"varint,15,rep,packed,name=enabled_features,json=enabledFeatures,proto3,enum=livekit.SIPFeature" json:"enabled_features,omitempty"`
	// Max time for the caller to wait for track subscription.
	RingingTimeout *durationpb.Duration `protobuf:"bytes,16,opt,name=ringing_timeout,json=ringingTimeout,proto3" json:"ringing_timeout,omitempty"`
	// Max call duration.
	MaxCallDuration *durationpb.Duration `protobuf:"bytes,17,opt,name=max_call_duration,json=maxCallDuration,proto3" json:"max_call_duration,omitempty"`
	// Room preset and config are used only to generate tokens
	// once tokens are returned by this response consistently, it will no longer
	// be needed
	RoomPreset      string                     `protobuf:"bytes,20,opt,name=room_preset,json=roomPreset,proto3" json:"room_preset,omitempty"`
	RoomConfig      *livekit.RoomConfiguration `protobuf:"bytes,21,opt,name=room_config,json=roomConfig,proto3" json:"room_config,omitempty"`
	MediaEncryption livekit.SIPMediaEncryption `protobuf:"varint,22,opt,name=media_encryption,json=mediaEncryption,proto3,enum=livekit.SIPMediaEncryption" json:"media_encryption,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *EvaluateSIPDispatchRulesResponse) Reset() {
	*x = EvaluateSIPDispatchRulesResponse{}
	mi := &file_rpc_io_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EvaluateSIPDispatchRulesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluateSIPDispatchRulesResponse) ProtoMessage() {}

func (x *EvaluateSIPDispatchRulesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_io_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluateSIPDispatchRulesResponse.ProtoReflect.Descriptor instead.
func (*EvaluateSIPDispatchRulesResponse) Descriptor() ([]byte, []int) {
	return file_rpc_io_proto_rawDescGZIP(), []int{8}
}

func (x *EvaluateSIPDispatchRulesResponse) GetRoomName() string {
	if x != nil {
		return x.RoomName
	}
	return ""
}

func (x *EvaluateSIPDispatchRulesResponse) GetParticipantIdentity() string {
	if x != nil {
		return x.ParticipantIdentity
	}
	return ""
}

func (x *EvaluateSIPDispatchRulesResponse) GetParticipantName() string {
	if x != nil {
		return x.ParticipantName
	}
	return ""
}

func (x *EvaluateSIPDispatchRulesResponse) GetParticipantMetadata() string {
	if x != nil {
		return x.ParticipantMetadata
	}
	return ""
}

func (x *EvaluateSIPDispatchRulesResponse) GetParticipantAttributes() map[string]string {
	if x != nil {
		return x.ParticipantAttributes
	}
	return nil
}

// Deprecated: Marked as deprecated in rpc/io.proto.
func (x *EvaluateSIPDispatchRulesResponse) GetRequestPin() bool {
	if x != nil {
		return x.RequestPin
	}
	return false
}

func (x *EvaluateSIPDispatchRulesResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *EvaluateSIPDispatchRulesResponse) GetWsUrl() string {
	if x != nil {
		return x.WsUrl
	}
	return ""
}

func (x *EvaluateSIPDispatchRulesResponse) GetResult() SIPDispatchResult {
	if x != nil {
		return x.Result
	}
	return SIPDispatchResult_LEGACY_ACCEPT_OR_PIN
}

func (x *EvaluateSIPDispatchRulesResponse) GetSipTrunkId() string {
	if x != nil {
		return x.SipTrunkId
	}
	return ""
}

func (x *EvaluateSIPDispatchRulesResponse) GetSipDispatchRuleId() string {
	if x != nil {
		return x.SipDispatchRuleId
	}
	return ""
}

func (x *EvaluateSIPDispatchRulesResponse) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *EvaluateSIPDispatchRulesResponse) GetHeaders() map[string]string {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *EvaluateSIPDispatchRulesResponse) GetHeadersToAttributes() map[string]string {
	if x != nil {
		return x.HeadersToAttributes
	}
	return nil
}

func (x *EvaluateSIPDispatchRulesResponse) GetAttributesToHeaders() map[string]string {
	if x != nil {
		return x.AttributesToHeaders
	}
	return nil
}

func (x *EvaluateSIPDispatchRulesResponse) GetIncludeHeaders() livekit.SIPHeaderOptions {
	if x != nil {
		return x.IncludeHeaders
	}
	return livekit.SIPHeaderOptions(0)
}

func (x *EvaluateSIPDispatchRulesResponse) GetEnabledFeatures() []livekit.SIPFeature {
	if x != nil {
		return x.EnabledFeatures
	}
	return nil
}

func (x *EvaluateSIPDispatchRulesResponse) GetRingingTimeout() *durationpb.Duration {
	if x != nil {
		return x.RingingTimeout
	}
	return nil
}

func (x *EvaluateSIPDispatchRulesResponse) GetMaxCallDuration() *durationpb.Duration {
	if x != nil {
		return x.MaxCallDuration
	}
	return nil
}

func (x *EvaluateSIPDispatchRulesResponse) GetRoomPreset() string {
	if x != nil {
		return x.RoomPreset
	}
	return ""
}

func (x *EvaluateSIPDispatchRulesResponse) GetRoomConfig() *livekit.RoomConfiguration {
	if x != nil {
		return x.RoomConfig
	}
	return nil
}

func (x *EvaluateSIPDispatchRulesResponse) GetMediaEncryption() livekit.SIPMediaEncryption {
	if x != nil {
		return x.MediaEncryption
	}
	return livekit.SIPMediaEncryption(0)
}

type UpdateSIPCallStateRequest struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	CallInfo      *livekit.SIPCallInfo     `protobuf:"bytes,1,opt,name=call_info,json=callInfo,proto3" json:"call_info,omitempty"`
	TransferInfo  *livekit.SIPTransferInfo `protobuf:"bytes,2,opt,name=transfer_info,json=transferInfo,proto3" json:"transfer_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateSIPCallStateRequest) Reset() {
	*x = UpdateSIPCallStateRequest{}
	mi := &file_rpc_io_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateSIPCallStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSIPCallStateRequest) ProtoMessage() {}

func (x *UpdateSIPCallStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_io_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSIPCallStateRequest.ProtoReflect.Descriptor instead.
func (*UpdateSIPCallStateRequest) Descriptor() ([]byte, []int) {
	return file_rpc_io_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateSIPCallStateRequest) GetCallInfo() *livekit.SIPCallInfo {
	if x != nil {
		return x.CallInfo
	}
	return nil
}

func (x *UpdateSIPCallStateRequest) GetTransferInfo() *livekit.SIPTransferInfo {
	if x != nil {
		return x.TransferInfo
	}
	return nil
}

type SIPCall struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LkCallId      string                 `protobuf:"bytes,1,opt,name=lk_call_id,json=lkCallId,proto3" json:"lk_call_id,omitempty"`
	SourceIp      string                 `protobuf:"bytes,2,opt,name=source_ip,json=sourceIp,proto3" json:"source_ip,omitempty"`      // source ip (without port)
	Address       *livekit.SIPUri        `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`                        // address in the request line (INVITE)
	From          *livekit.SIPUri        `protobuf:"bytes,4,opt,name=from,proto3" json:"from,omitempty"`                              // From header
	To            *livekit.SIPUri        `protobuf:"bytes,5,opt,name=to,proto3" json:"to,omitempty"`                                  // To header
	Via           []*livekit.SIPUri      `protobuf:"bytes,6,rep,name=via,proto3" json:"via,omitempty"`                                // Via headers
	SipCallId     string                 `protobuf:"bytes,7,opt,name=sip_call_id,json=sipCallId,proto3" json:"sip_call_id,omitempty"` // SIP protocol generated call ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SIPCall) Reset() {
	*x = SIPCall{}
	mi := &file_rpc_io_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SIPCall) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SIPCall) ProtoMessage() {}

func (x *SIPCall) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_io_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SIPCall.ProtoReflect.Descriptor instead.
func (*SIPCall) Descriptor() ([]byte, []int) {
	return file_rpc_io_proto_rawDescGZIP(), []int{10}
}

func (x *SIPCall) GetLkCallId() string {
	if x != nil {
		return x.LkCallId
	}
	return ""
}

func (x *SIPCall) GetSourceIp() string {
	if x != nil {
		return x.SourceIp
	}
	return ""
}

func (x *SIPCall) GetAddress() *livekit.SIPUri {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *SIPCall) GetFrom() *livekit.SIPUri {
	if x != nil {
		return x.From
	}
	return nil
}

func (x *SIPCall) GetTo() *livekit.SIPUri {
	if x != nil {
		return x.To
	}
	return nil
}

func (x *SIPCall) GetVia() []*livekit.SIPUri {
	if x != nil {
		return x.Via
	}
	return nil
}

func (x *SIPCall) GetSipCallId() string {
	if x != nil {
		return x.SipCallId
	}
	return ""
}

var File_rpc_io_proto protoreflect.FileDescriptor

const file_rpc_io_proto_rawDesc = "" +
	"\n" +
	"\frpc/io.proto\x12\x03rpc\x1a\x14livekit_egress.proto\x1a\x15livekit_ingress.proto\x1a\x11livekit_sip.proto\x1a\x12livekit_room.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1egoogle/protobuf/duration.proto\"/\n" +
	"\x10GetEgressRequest\x12\x1b\n" +
	"\tegress_id\x18\x01 \x01(\tR\begressId\"\x87\x01\n" +
	"\x14UpdateMetricsRequest\x12'\n" +
	"\x04info\x18\x01 \x01(\v2\x13.livekit.EgressInfoR\x04info\x12\"\n" +
	"\ravg_cpu_usage\x18\x03 \x01(\x02R\vavgCpuUsage\x12\"\n" +
	"\rmax_cpu_usage\x18\x04 \x01(\x02R\vmaxCpuUsage\"U\n" +
	"\x15GetIngressInfoRequest\x12\x1d\n" +
	"\n" +
	"ingress_id\x18\x01 \x01(\tR\tingressId\x12\x1d\n" +
	"\n" +
	"stream_key\x18\x02 \x01(\tR\tstreamKey\"\x88\x02\n" +
	"\x16GetIngressInfoResponse\x12(\n" +
	"\x04info\x18\x01 \x01(\v2\x14.livekit.IngressInfoR\x04info\x12\x14\n" +
	"\x05token\x18\x02 \x01(\tR\x05token\x12\x15\n" +
	"\x06ws_url\x18\x03 \x01(\tR\x05wsUrl\x12U\n" +
	"\x0elogging_fields\x18\x04 \x03(\v2..rpc.GetIngressInfoResponse.LoggingFieldsEntryR\rloggingFields\x1a@\n" +
	"\x12LoggingFieldsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"g\n" +
	"\x19UpdateIngressStateRequest\x12\x1d\n" +
	"\n" +
	"ingress_id\x18\x01 \x01(\tR\tingressId\x12+\n" +
	"\x05state\x18\x02 \x01(\v2\x15.livekit.IngressStateR\x05state\"\xf7\x01\n" +
	" GetSIPTrunkAuthenticationRequest\x12\"\n" +
	"\vsip_call_id\x18\x06 \x01(\tB\x02\x18\x01R\tsipCallId\x12\x16\n" +
	"\x04from\x18\x02 \x01(\tB\x02\x18\x01R\x04from\x12\x1f\n" +
	"\tfrom_host\x18\a \x01(\tB\x02\x18\x01R\bfromHost\x12\x12\n" +
	"\x02to\x18\x03 \x01(\tB\x02\x18\x01R\x02to\x12\x1b\n" +
	"\ato_host\x18\x05 \x01(\tB\x02\x18\x01R\x06toHost\x12#\n" +
	"\vsrc_address\x18\x04 \x01(\tB\x02\x18\x01R\n" +
	"srcAddress\x12 \n" +
	"\x04call\x18\b \x01(\v2\f.rpc.SIPCallR\x04call\"\xb0\x01\n" +
	"!GetSIPTrunkAuthenticationResponse\x12\x1a\n" +
	"\busername\x18\x01 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\x12\x12\n" +
	"\x04drop\x18\x03 \x01(\bR\x04drop\x12 \n" +
	"\fsip_trunk_id\x18\x04 \x01(\tR\n" +
	"sipTrunkId\x12\x1d\n" +
	"\n" +
	"project_id\x18\x05 \x01(\tR\tprojectId\"\xd3\x04\n" +
	"\x1fEvaluateSIPDispatchRulesRequest\x12\"\n" +
	"\vsip_call_id\x18\b \x01(\tB\x02\x18\x01R\tsipCallId\x120\n" +
	"\x12sip_participant_id\x18\x01 \x01(\tB\x02\x18\x01R\x10sipParticipantId\x12 \n" +
	"\fsip_trunk_id\x18\n" +
	" \x01(\tR\n" +
	"sipTrunkId\x12)\n" +
	"\x0ecalling_number\x18\x02 \x01(\tB\x02\x18\x01R\rcallingNumber\x12%\n" +
	"\fcalling_host\x18\v \x01(\tB\x02\x18\x01R\vcallingHost\x12'\n" +
	"\rcalled_number\x18\x03 \x01(\tB\x02\x18\x01R\fcalledNumber\x12#\n" +
	"\vsrc_address\x18\x04 \x01(\tB\x02\x18\x01R\n" +
	"srcAddress\x12\x10\n" +
	"\x03pin\x18\x05 \x01(\tR\x03pin\x12\x15\n" +
	"\x06no_pin\x18\x06 \x01(\bR\x05noPin\x12#\n" +
	"\vcalled_host\x18\a \x01(\tB\x02\x18\x01R\n" +
	"calledHost\x12d\n" +
	"\x10extra_attributes\x18\t \x03(\v29.rpc.EvaluateSIPDispatchRulesRequest.ExtraAttributesEntryR\x0fextraAttributes\x12 \n" +
	"\x04call\x18\f \x01(\v2\f.rpc.SIPCallR\x04call\x1aB\n" +
	"\x14ExtraAttributesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xbe\f\n" +
	" EvaluateSIPDispatchRulesResponse\x12\x1b\n" +
	"\troom_name\x18\x01 \x01(\tR\broomName\x121\n" +
	"\x14participant_identity\x18\x02 \x01(\tR\x13participantIdentity\x12)\n" +
	"\x10participant_name\x18\a \x01(\tR\x0fparticipantName\x121\n" +
	"\x14participant_metadata\x18\b \x01(\tR\x13participantMetadata\x12w\n" +
	"\x16participant_attributes\x18\v \x03(\<EMAIL>\x15participantAttributes\x12#\n" +
	"\vrequest_pin\x18\x03 \x01(\bB\x02\x18\x01R\n" +
	"requestPin\x12\x14\n" +
	"\x05token\x18\x04 \x01(\tR\x05token\x12\x15\n" +
	"\x06ws_url\x18\x05 \x01(\tR\x05wsUrl\x12.\n" +
	"\x06result\x18\x06 \x01(\x0e2\x16.rpc.SIPDispatchResultR\x06result\x12 \n" +
	"\fsip_trunk_id\x18\t \x01(\tR\n" +
	"sipTrunkId\x12/\n" +
	"\x14sip_dispatch_rule_id\x18\n" +
	" \x01(\tR\x11sipDispatchRuleId\x12\x1d\n" +
	"\n" +
	"project_id\x18\f \x01(\tR\tprojectId\x12L\n" +
	"\aheaders\x18\r \x03(\v22.rpc.EvaluateSIPDispatchRulesResponse.HeadersEntryR\aheaders\x12r\n" +
	"\x15headers_to_attributes\x18\x0e \x03(\v2>.rpc.EvaluateSIPDispatchRulesResponse.HeadersToAttributesEntryR\x13headersToAttributes\x12r\n" +
	"\x15attributes_to_headers\x18\x12 \x03(\v2>.rpc.EvaluateSIPDispatchRulesResponse.AttributesToHeadersEntryR\x13attributesToHeaders\x12B\n" +
	"\x0finclude_headers\x18\x13 \x01(\x0e2\x19.livekit.SIPHeaderOptionsR\x0eincludeHeaders\x12>\n" +
	"\x10enabled_features\x18\x0f \x03(\x0e2\x13.livekit.SIPFeatureR\x0fenabledFeatures\x12B\n" +
	"\x0fringing_timeout\x18\x10 \x01(\v2\x19.google.protobuf.DurationR\x0eringingTimeout\x12E\n" +
	"\x11max_call_duration\x18\x11 \x01(\v2\x19.google.protobuf.DurationR\x0fmaxCallDuration\x12\x1f\n" +
	"\vroom_preset\x18\x14 \x01(\tR\n" +
	"roomPreset\x12;\n" +
	"\vroom_config\x18\x15 \x01(\v2\x1a.livekit.RoomConfigurationR\n" +
	"roomConfig\x12F\n" +
	"\x10media_encryption\x18\x16 \x01(\x0e2\x1b.livekit.SIPMediaEncryptionR\x0fmediaEncryption\x1aH\n" +
	"\x1aParticipantAttributesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\x1a:\n" +
	"\fHeadersEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\x1aF\n" +
	"\x18HeadersToAttributesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\x1aF\n" +
	"\x18AttributesToHeadersEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x8d\x01\n" +
	"\x19UpdateSIPCallStateRequest\x121\n" +
	"\tcall_info\x18\x01 \x01(\v2\x14.livekit.SIPCallInfoR\bcallInfo\x12=\n" +
	"\rtransfer_info\x18\x02 \x01(\v2\x18.livekit.SIPTransferInfoR\ftransferInfo\"\xf8\x01\n" +
	"\aSIPCall\x12\x1c\n" +
	"\n" +
	"lk_call_id\x18\x01 \x01(\tR\blkCallId\x12\x1b\n" +
	"\tsource_ip\x18\x02 \x01(\tR\bsourceIp\x12)\n" +
	"\aaddress\x18\x03 \x01(\v2\x0f.livekit.SIPUriR\aaddress\x12#\n" +
	"\x04from\x18\x04 \x01(\v2\x0f.livekit.SIPUriR\x04from\x12\x1f\n" +
	"\x02to\x18\x05 \x01(\v2\x0f.livekit.SIPUriR\x02to\x12!\n" +
	"\x03via\x18\x06 \x03(\v2\x0f.livekit.SIPUriR\x03via\x12\x1e\n" +
	"\vsip_call_id\x18\a \x01(\tR\tsipCallId*`\n" +
	"\x11SIPDispatchResult\x12\x18\n" +
	"\x14LEGACY_ACCEPT_OR_PIN\x10\x00\x12\n" +
	"\n" +
	"\x06ACCEPT\x10\x01\x12\x0f\n" +
	"\vREQUEST_PIN\x10\x02\x12\n" +
	"\n" +
	"\x06REJECT\x10\x03\x12\b\n" +
	"\x04DROP\x10\x042\xc1\x06\n" +
	"\x06IOInfo\x12;\n" +
	"\fCreateEgress\x12\x13.livekit.EgressInfo\x1a\x16.google.protobuf.Empty\x12;\n" +
	"\fUpdateEgress\x12\x13.livekit.EgressInfo\x1a\x16.google.protobuf.Empty\x127\n" +
	"\tGetEgress\x12\x15.rpc.GetEgressRequest\x1a\x13.livekit.EgressInfo\x12E\n" +
	"\n" +
	"ListEgress\x12\x1a.livekit.ListEgressRequest\x1a\x1b.livekit.ListEgressResponse\x12B\n" +
	"\rUpdateMetrics\x12\x19.rpc.UpdateMetricsRequest\x1a\x16.google.protobuf.Empty\x12=\n" +
	"\rCreateIngress\x12\x14.livekit.IngressInfo\x1a\x16.google.protobuf.Empty\x12I\n" +
	"\x0eGetIngressInfo\x12\x1a.rpc.GetIngressInfoRequest\x1a\x1b.rpc.GetIngressInfoResponse\x12L\n" +
	"\x12UpdateIngressState\x12\x1e.rpc.UpdateIngressStateRequest\x1a\x16.google.protobuf.Empty\x12j\n" +
	"\x19GetSIPTrunkAuthentication\x12%.rpc.GetSIPTrunkAuthenticationRequest\x1a&.rpc.GetSIPTrunkAuthenticationResponse\x12g\n" +
	"\x18EvaluateSIPDispatchRules\x12$.rpc.EvaluateSIPDispatchRulesRequest\x1a%.rpc.EvaluateSIPDispatchRulesResponse\x12L\n" +
	"\x12UpdateSIPCallState\x12\x1e.rpc.UpdateSIPCallStateRequest\x1a\x16.google.protobuf.EmptyB!Z\x1fgithub.com/livekit/protocol/rpcb\x06proto3"

var (
	file_rpc_io_proto_rawDescOnce sync.Once
	file_rpc_io_proto_rawDescData []byte
)

func file_rpc_io_proto_rawDescGZIP() []byte {
	file_rpc_io_proto_rawDescOnce.Do(func() {
		file_rpc_io_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_io_proto_rawDesc), len(file_rpc_io_proto_rawDesc)))
	})
	return file_rpc_io_proto_rawDescData
}

var file_rpc_io_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_rpc_io_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_rpc_io_proto_goTypes = []any{
	(SIPDispatchResult)(0),                    // 0: rpc.SIPDispatchResult
	(*GetEgressRequest)(nil),                  // 1: rpc.GetEgressRequest
	(*UpdateMetricsRequest)(nil),              // 2: rpc.UpdateMetricsRequest
	(*GetIngressInfoRequest)(nil),             // 3: rpc.GetIngressInfoRequest
	(*GetIngressInfoResponse)(nil),            // 4: rpc.GetIngressInfoResponse
	(*UpdateIngressStateRequest)(nil),         // 5: rpc.UpdateIngressStateRequest
	(*GetSIPTrunkAuthenticationRequest)(nil),  // 6: rpc.GetSIPTrunkAuthenticationRequest
	(*GetSIPTrunkAuthenticationResponse)(nil), // 7: rpc.GetSIPTrunkAuthenticationResponse
	(*EvaluateSIPDispatchRulesRequest)(nil),   // 8: rpc.EvaluateSIPDispatchRulesRequest
	(*EvaluateSIPDispatchRulesResponse)(nil),  // 9: rpc.EvaluateSIPDispatchRulesResponse
	(*UpdateSIPCallStateRequest)(nil),         // 10: rpc.UpdateSIPCallStateRequest
	(*SIPCall)(nil),                           // 11: rpc.SIPCall
	nil,                                       // 12: rpc.GetIngressInfoResponse.LoggingFieldsEntry
	nil,                                       // 13: rpc.EvaluateSIPDispatchRulesRequest.ExtraAttributesEntry
	nil,                                       // 14: rpc.EvaluateSIPDispatchRulesResponse.ParticipantAttributesEntry
	nil,                                       // 15: rpc.EvaluateSIPDispatchRulesResponse.HeadersEntry
	nil,                                       // 16: rpc.EvaluateSIPDispatchRulesResponse.HeadersToAttributesEntry
	nil,                                       // 17: rpc.EvaluateSIPDispatchRulesResponse.AttributesToHeadersEntry
	(*livekit.EgressInfo)(nil),                // 18: livekit.EgressInfo
	(*livekit.IngressInfo)(nil),               // 19: livekit.IngressInfo
	(*livekit.IngressState)(nil),              // 20: livekit.IngressState
	(livekit.SIPHeaderOptions)(0),             // 21: livekit.SIPHeaderOptions
	(livekit.SIPFeature)(0),                   // 22: livekit.SIPFeature
	(*durationpb.Duration)(nil),               // 23: google.protobuf.Duration
	(*livekit.RoomConfiguration)(nil),         // 24: livekit.RoomConfiguration
	(livekit.SIPMediaEncryption)(0),           // 25: livekit.SIPMediaEncryption
	(*livekit.SIPCallInfo)(nil),               // 26: livekit.SIPCallInfo
	(*livekit.SIPTransferInfo)(nil),           // 27: livekit.SIPTransferInfo
	(*livekit.SIPUri)(nil),                    // 28: livekit.SIPUri
	(*livekit.ListEgressRequest)(nil),         // 29: livekit.ListEgressRequest
	(*emptypb.Empty)(nil),                     // 30: google.protobuf.Empty
	(*livekit.ListEgressResponse)(nil),        // 31: livekit.ListEgressResponse
}
var file_rpc_io_proto_depIdxs = []int32{
	18, // 0: rpc.UpdateMetricsRequest.info:type_name -> livekit.EgressInfo
	19, // 1: rpc.GetIngressInfoResponse.info:type_name -> livekit.IngressInfo
	12, // 2: rpc.GetIngressInfoResponse.logging_fields:type_name -> rpc.GetIngressInfoResponse.LoggingFieldsEntry
	20, // 3: rpc.UpdateIngressStateRequest.state:type_name -> livekit.IngressState
	11, // 4: rpc.GetSIPTrunkAuthenticationRequest.call:type_name -> rpc.SIPCall
	13, // 5: rpc.EvaluateSIPDispatchRulesRequest.extra_attributes:type_name -> rpc.EvaluateSIPDispatchRulesRequest.ExtraAttributesEntry
	11, // 6: rpc.EvaluateSIPDispatchRulesRequest.call:type_name -> rpc.SIPCall
	14, // 7: rpc.EvaluateSIPDispatchRulesResponse.participant_attributes:type_name -> rpc.EvaluateSIPDispatchRulesResponse.ParticipantAttributesEntry
	0,  // 8: rpc.EvaluateSIPDispatchRulesResponse.result:type_name -> rpc.SIPDispatchResult
	15, // 9: rpc.EvaluateSIPDispatchRulesResponse.headers:type_name -> rpc.EvaluateSIPDispatchRulesResponse.HeadersEntry
	16, // 10: rpc.EvaluateSIPDispatchRulesResponse.headers_to_attributes:type_name -> rpc.EvaluateSIPDispatchRulesResponse.HeadersToAttributesEntry
	17, // 11: rpc.EvaluateSIPDispatchRulesResponse.attributes_to_headers:type_name -> rpc.EvaluateSIPDispatchRulesResponse.AttributesToHeadersEntry
	21, // 12: rpc.EvaluateSIPDispatchRulesResponse.include_headers:type_name -> livekit.SIPHeaderOptions
	22, // 13: rpc.EvaluateSIPDispatchRulesResponse.enabled_features:type_name -> livekit.SIPFeature
	23, // 14: rpc.EvaluateSIPDispatchRulesResponse.ringing_timeout:type_name -> google.protobuf.Duration
	23, // 15: rpc.EvaluateSIPDispatchRulesResponse.max_call_duration:type_name -> google.protobuf.Duration
	24, // 16: rpc.EvaluateSIPDispatchRulesResponse.room_config:type_name -> livekit.RoomConfiguration
	25, // 17: rpc.EvaluateSIPDispatchRulesResponse.media_encryption:type_name -> livekit.SIPMediaEncryption
	26, // 18: rpc.UpdateSIPCallStateRequest.call_info:type_name -> livekit.SIPCallInfo
	27, // 19: rpc.UpdateSIPCallStateRequest.transfer_info:type_name -> livekit.SIPTransferInfo
	28, // 20: rpc.SIPCall.address:type_name -> livekit.SIPUri
	28, // 21: rpc.SIPCall.from:type_name -> livekit.SIPUri
	28, // 22: rpc.SIPCall.to:type_name -> livekit.SIPUri
	28, // 23: rpc.SIPCall.via:type_name -> livekit.SIPUri
	18, // 24: rpc.IOInfo.CreateEgress:input_type -> livekit.EgressInfo
	18, // 25: rpc.IOInfo.UpdateEgress:input_type -> livekit.EgressInfo
	1,  // 26: rpc.IOInfo.GetEgress:input_type -> rpc.GetEgressRequest
	29, // 27: rpc.IOInfo.ListEgress:input_type -> livekit.ListEgressRequest
	2,  // 28: rpc.IOInfo.UpdateMetrics:input_type -> rpc.UpdateMetricsRequest
	19, // 29: rpc.IOInfo.CreateIngress:input_type -> livekit.IngressInfo
	3,  // 30: rpc.IOInfo.GetIngressInfo:input_type -> rpc.GetIngressInfoRequest
	5,  // 31: rpc.IOInfo.UpdateIngressState:input_type -> rpc.UpdateIngressStateRequest
	6,  // 32: rpc.IOInfo.GetSIPTrunkAuthentication:input_type -> rpc.GetSIPTrunkAuthenticationRequest
	8,  // 33: rpc.IOInfo.EvaluateSIPDispatchRules:input_type -> rpc.EvaluateSIPDispatchRulesRequest
	10, // 34: rpc.IOInfo.UpdateSIPCallState:input_type -> rpc.UpdateSIPCallStateRequest
	30, // 35: rpc.IOInfo.CreateEgress:output_type -> google.protobuf.Empty
	30, // 36: rpc.IOInfo.UpdateEgress:output_type -> google.protobuf.Empty
	18, // 37: rpc.IOInfo.GetEgress:output_type -> livekit.EgressInfo
	31, // 38: rpc.IOInfo.ListEgress:output_type -> livekit.ListEgressResponse
	30, // 39: rpc.IOInfo.UpdateMetrics:output_type -> google.protobuf.Empty
	30, // 40: rpc.IOInfo.CreateIngress:output_type -> google.protobuf.Empty
	4,  // 41: rpc.IOInfo.GetIngressInfo:output_type -> rpc.GetIngressInfoResponse
	30, // 42: rpc.IOInfo.UpdateIngressState:output_type -> google.protobuf.Empty
	7,  // 43: rpc.IOInfo.GetSIPTrunkAuthentication:output_type -> rpc.GetSIPTrunkAuthenticationResponse
	9,  // 44: rpc.IOInfo.EvaluateSIPDispatchRules:output_type -> rpc.EvaluateSIPDispatchRulesResponse
	30, // 45: rpc.IOInfo.UpdateSIPCallState:output_type -> google.protobuf.Empty
	35, // [35:46] is the sub-list for method output_type
	24, // [24:35] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_rpc_io_proto_init() }
func file_rpc_io_proto_init() {
	if File_rpc_io_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_io_proto_rawDesc), len(file_rpc_io_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rpc_io_proto_goTypes,
		DependencyIndexes: file_rpc_io_proto_depIdxs,
		EnumInfos:         file_rpc_io_proto_enumTypes,
		MessageInfos:      file_rpc_io_proto_msgTypes,
	}.Build()
	File_rpc_io_proto = out.File
	file_rpc_io_proto_goTypes = nil
	file_rpc_io_proto_depIdxs = nil
}
