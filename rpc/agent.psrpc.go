// Code generated by protoc-gen-psrpc v0.6.0, DO NOT EDIT.
// source: rpc/agent.proto

package rpc

import (
	"context"

	"github.com/livekit/psrpc"
	"github.com/livekit/psrpc/pkg/client"
	"github.com/livekit/psrpc/pkg/info"
	"github.com/livekit/psrpc/pkg/rand"
	"github.com/livekit/psrpc/pkg/server"
	"github.com/livekit/psrpc/version"
)
import google_protobuf "google.golang.org/protobuf/types/known/emptypb"
import livekit2 "github.com/livekit/protocol/livekit"

var _ = version.PsrpcVersion_0_6

// ==============================
// AgentInternal Client Interface
// ==============================

type AgentInternalClient interface {
	CheckEnabled(ctx context.Context, req *CheckEnabledRequest, opts ...psrpc.RequestOption) (<-chan *psrpc.Response[*CheckEnabledResponse], error)

	JobRequest(ctx context.Context, namespace string, jobType string, req *livekit2.Job, opts ...psrpc.RequestOption) (*JobRequestResponse, error)

	JobTerminate(ctx context.Context, jobId string, req *JobTerminateRequest, opts ...psrpc.RequestOption) (*JobTerminateResponse, error)

	SubscribeWorkerRegistered(ctx context.Context, handlerNamespace string) (psrpc.Subscription[*google_protobuf.Empty], error)

	// Close immediately, without waiting for pending RPCs
	Close()
}

// ==================================
// AgentInternal ServerImpl Interface
// ==================================

type AgentInternalServerImpl interface {
	CheckEnabled(context.Context, *CheckEnabledRequest) (*CheckEnabledResponse, error)

	JobRequest(context.Context, *livekit2.Job) (*JobRequestResponse, error)
	JobRequestAffinity(context.Context, *livekit2.Job) float32

	JobTerminate(context.Context, *JobTerminateRequest) (*JobTerminateResponse, error)
}

// ==============================
// AgentInternal Server Interface
// ==============================

type AgentInternalServer interface {
	RegisterJobRequestTopic(namespace string, jobType string) error
	DeregisterJobRequestTopic(namespace string, jobType string)
	RegisterJobTerminateTopic(jobId string) error
	DeregisterJobTerminateTopic(jobId string)
	PublishWorkerRegistered(ctx context.Context, handlerNamespace string, msg *google_protobuf.Empty) error

	// Close and wait for pending RPCs to complete
	Shutdown()

	// Close immediately, without waiting for pending RPCs
	Kill()
}

// ====================
// AgentInternal Client
// ====================

type agentInternalClient struct {
	client *client.RPCClient
}

// NewAgentInternalClient creates a psrpc client that implements the AgentInternalClient interface.
func NewAgentInternalClient(bus psrpc.MessageBus, opts ...psrpc.ClientOption) (AgentInternalClient, error) {
	sd := &info.ServiceDefinition{
		Name: "AgentInternal",
		ID:   rand.NewClientID(),
	}

	sd.RegisterMethod("CheckEnabled", false, true, false, false)
	sd.RegisterMethod("JobRequest", true, false, true, false)
	sd.RegisterMethod("JobTerminate", false, false, true, true)
	sd.RegisterMethod("WorkerRegistered", false, true, false, false)

	rpcClient, err := client.NewRPCClient(sd, bus, opts...)
	if err != nil {
		return nil, err
	}

	return &agentInternalClient{
		client: rpcClient,
	}, nil
}

func (c *agentInternalClient) CheckEnabled(ctx context.Context, req *CheckEnabledRequest, opts ...psrpc.RequestOption) (<-chan *psrpc.Response[*CheckEnabledResponse], error) {
	return client.RequestMulti[*CheckEnabledResponse](ctx, c.client, "CheckEnabled", nil, req, opts...)
}

func (c *agentInternalClient) JobRequest(ctx context.Context, namespace string, jobType string, req *livekit2.Job, opts ...psrpc.RequestOption) (*JobRequestResponse, error) {
	return client.RequestSingle[*JobRequestResponse](ctx, c.client, "JobRequest", []string{namespace, jobType}, req, opts...)
}

func (c *agentInternalClient) JobTerminate(ctx context.Context, jobId string, req *JobTerminateRequest, opts ...psrpc.RequestOption) (*JobTerminateResponse, error) {
	return client.RequestSingle[*JobTerminateResponse](ctx, c.client, "JobTerminate", []string{jobId}, req, opts...)
}

func (c *agentInternalClient) SubscribeWorkerRegistered(ctx context.Context, handlerNamespace string) (psrpc.Subscription[*google_protobuf.Empty], error) {
	return client.Join[*google_protobuf.Empty](ctx, c.client, "WorkerRegistered", []string{handlerNamespace})
}

func (s *agentInternalClient) Close() {
	s.client.Close()
}

// ====================
// AgentInternal Server
// ====================

type agentInternalServer struct {
	svc AgentInternalServerImpl
	rpc *server.RPCServer
}

// NewAgentInternalServer builds a RPCServer that will route requests
// to the corresponding method in the provided svc implementation.
func NewAgentInternalServer(svc AgentInternalServerImpl, bus psrpc.MessageBus, opts ...psrpc.ServerOption) (AgentInternalServer, error) {
	sd := &info.ServiceDefinition{
		Name: "AgentInternal",
		ID:   rand.NewServerID(),
	}

	s := server.NewRPCServer(sd, bus, opts...)

	sd.RegisterMethod("CheckEnabled", false, true, false, false)
	var err error
	err = server.RegisterHandler(s, "CheckEnabled", nil, svc.CheckEnabled, nil)
	if err != nil {
		s.Close(false)
		return nil, err
	}

	sd.RegisterMethod("JobRequest", true, false, true, false)
	sd.RegisterMethod("JobTerminate", false, false, true, true)
	sd.RegisterMethod("WorkerRegistered", false, true, false, false)
	return &agentInternalServer{
		svc: svc,
		rpc: s,
	}, nil
}

func (s *agentInternalServer) RegisterJobRequestTopic(namespace string, jobType string) error {
	return server.RegisterHandler(s.rpc, "JobRequest", []string{namespace, jobType}, s.svc.JobRequest, s.svc.JobRequestAffinity)
}

func (s *agentInternalServer) DeregisterJobRequestTopic(namespace string, jobType string) {
	s.rpc.DeregisterHandler("JobRequest", []string{namespace, jobType})
}

func (s *agentInternalServer) RegisterJobTerminateTopic(jobId string) error {
	return server.RegisterHandler(s.rpc, "JobTerminate", []string{jobId}, s.svc.JobTerminate, nil)
}

func (s *agentInternalServer) DeregisterJobTerminateTopic(jobId string) {
	s.rpc.DeregisterHandler("JobTerminate", []string{jobId})
}

func (s *agentInternalServer) PublishWorkerRegistered(ctx context.Context, handlerNamespace string, msg *google_protobuf.Empty) error {
	return s.rpc.Publish(ctx, "WorkerRegistered", []string{handlerNamespace}, msg)
}

func (s *agentInternalServer) Shutdown() {
	s.rpc.Close(false)
}

func (s *agentInternalServer) Kill() {
	s.rpc.Close(true)
}

var psrpcFileDescriptor0 = []byte{
	// 566 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x53, 0xdf, 0x8e, 0xd2, 0x4e,
	0x18, 0xfd, 0x0d, 0x2c, 0x04, 0x06, 0xf6, 0xb7, 0x65, 0xba, 0x28, 0x5b, 0x63, 0x76, 0xe9, 0x8d,
	0x44, 0x93, 0xd6, 0xe0, 0xb5, 0x71, 0x59, 0xb7, 0x1a, 0x88, 0x40, 0xec, 0x76, 0x35, 0x31, 0x31,
	0x4d, 0x5b, 0x3e, 0xa1, 0x4b, 0xe9, 0x8c, 0xd3, 0xc1, 0x64, 0x1f, 0x81, 0xd7, 0xe1, 0x79, 0x7c,
	0x0c, 0x1f, 0xc0, 0xf4, 0x0f, 0xb5, 0x6c, 0xf0, 0xc2, 0xcb, 0x9e, 0x73, 0xfa, 0xe5, 0x3b, 0xe7,
	0xcc, 0x87, 0x4f, 0x38, 0xf3, 0x74, 0x67, 0x0e, 0xa1, 0xd0, 0x18, 0xa7, 0x82, 0x92, 0x32, 0x67,
	0x9e, 0xf2, 0x64, 0x4e, 0xe9, 0x3c, 0x00, 0x3d, 0x81, 0xdc, 0xf5, 0x37, 0x1d, 0x56, 0x4c, 0xdc,
	0xa7, 0x0a, 0xe5, 0x98, 0x32, 0xe1, 0xd3, 0x30, 0xca, 0x3e, 0xe5, 0xc0, 0xff, 0x01, 0x4b, 0x5f,
	0xd8, 0x85, 0x29, 0x6a, 0x1b, 0xcb, 0x6f, 0x17, 0xe0, 0x2d, 0x8d, 0xd0, 0x71, 0x03, 0x98, 0x99,
	0xf0, 0x7d, 0x0d, 0x91, 0x50, 0x7f, 0x22, 0x7c, 0xba, 0x8f, 0x47, 0x8c, 0x86, 0x11, 0x90, 0x2e,
	0x6e, 0x72, 0x4a, 0x57, 0x36, 0xa4, 0x78, 0x07, 0x5d, 0xa0, 0x5e, 0xcd, 0x6c, 0xc4, 0x58, 0x26,
	0x25, 0x2f, 0x70, 0x8b, 0xad, 0xdd, 0xc0, 0x8f, 0x16, 0xc0, 0x73, 0x5d, 0x29, 0xd1, 0x49, 0x39,
	0xb1, 0x13, 0xeb, 0x58, 0x66, 0x0e, 0x17, 0xbe, 0xe7, 0x33, 0x27, 0x14, 0xb9, 0xbc, 0x92, 0xc8,
	0x49, 0x81, 0xda, 0xfd, 0xa0, 0x62, 0x1c, 0x3a, 0x2b, 0x88, 0x98, 0xe3, 0x41, 0xd4, 0x29, 0x5f,
	0x94, 0x7b, 0xf5, 0xab, 0x52, 0x07, 0x99, 0x05, 0x94, 0x9c, 0xe3, 0x46, 0xe2, 0xd1, 0x4e, 0xb0,
	0xce, 0x51, 0x2c, 0x32, 0x71, 0x02, 0x4d, 0x62, 0x44, 0x7d, 0x8d, 0xc9, 0x88, 0xba, 0x99, 0xd9,
	0xdc, 0xdb, 0x33, 0x5c, 0x89, 0x84, 0x23, 0x20, 0x31, 0xd5, 0xe8, 0xb7, 0xb4, 0x2c, 0x30, 0x6d,
	0x44, 0xdd, 0x9b, 0x98, 0x30, 0x53, 0x5e, 0xfd, 0x8a, 0xe5, 0x11, 0x75, 0x2d, 0xe0, 0x2b, 0x3f,
	0x8c, 0xe1, 0x74, 0x0e, 0x69, 0xe3, 0xea, 0x1d, 0x75, 0x6d, 0x3f, 0x4d, 0xa5, 0x6e, 0x56, 0xee,
	0xa8, 0x3b, 0x8c, 0x2d, 0x56, 0x39, 0x38, 0x11, 0x0d, 0x93, 0x10, 0xfe, 0xef, 0x3f, 0xd6, 0x38,
	0xf3, 0xb4, 0xfd, 0x01, 0x31, 0x6d, 0x66, 0x32, 0xf5, 0x0d, 0x3e, 0xdd, 0x67, 0xff, 0x71, 0xbf,
	0xe7, 0xd7, 0x89, 0xbd, 0x07, 0xe3, 0xc9, 0x19, 0x6e, 0x5b, 0x86, 0x39, 0x1e, 0x4e, 0x06, 0xd6,
	0x70, 0x3a, 0xb1, 0x4d, 0xe3, 0xe3, 0xad, 0x71, 0x63, 0x19, 0xd7, 0xd2, 0x7f, 0x44, 0xc6, 0x27,
	0x83, 0xf7, 0xc6, 0xc4, 0xb2, 0x3f, 0x18, 0xef, 0x2c, 0xdb, 0x9c, 0x4e, 0xc7, 0x12, 0xea, 0xff,
	0x2a, 0xe1, 0xe3, 0x41, 0x9c, 0xd9, 0x30, 0x14, 0xc0, 0x43, 0x27, 0x20, 0x63, 0xdc, 0x2c, 0x3e,
	0x0a, 0xd2, 0x49, 0x9c, 0x1c, 0x78, 0x3f, 0xca, 0xd9, 0x01, 0x26, 0x75, 0xa1, 0xd6, 0xb6, 0x1b,
	0x74, 0x74, 0x59, 0xea, 0x21, 0xf2, 0x09, 0xe3, 0x3f, 0x2d, 0x90, 0x66, 0xd1, 0x8e, 0x92, 0x87,
	0xf4, 0xa0, 0x24, 0xb5, 0xbb, 0xdd, 0xa0, 0xa7, 0x12, 0x52, 0xda, 0xa4, 0x9e, 0x37, 0x4e, 0x6a,
	0x71, 0xee, 0xe2, 0x9e, 0xc1, 0x25, 0x7a, 0x89, 0xc8, 0x2d, 0x6e, 0x16, 0xed, 0x67, 0x6b, 0x1e,
	0x68, 0x2c, 0x5b, 0xf3, 0x50, 0xd8, 0xaa, 0xb4, 0xdd, 0xa0, 0xa6, 0x84, 0x94, 0x1a, 0xc9, 0x4a,
	0x25, 0x80, 0xa5, 0xcf, 0x94, 0x2f, 0x81, 0x9b, 0x30, 0xf7, 0x23, 0x01, 0x1c, 0x66, 0xe4, 0x91,
	0x96, 0x1e, 0xa0, 0xb6, 0x3b, 0x40, 0xcd, 0x88, 0x0f, 0x50, 0xf9, 0x0b, 0x9e, 0x6e, 0x5f, 0x43,
	0x12, 0x52, 0x64, 0xd2, 0x5a, 0x38, 0xe1, 0x2c, 0x00, 0x6e, 0xe7, 0x3e, 0xe2, 0x54, 0xae, 0xba,
	0x5f, 0xce, 0xe7, 0xbe, 0x58, 0xac, 0x5d, 0xcd, 0xa3, 0x2b, 0x3d, 0xcb, 0x24, 0x3d, 0x70, 0x8f,
	0x06, 0x3a, 0x67, 0x9e, 0x5b, 0x4d, 0xbe, 0x5e, 0xfd, 0x0e, 0x00, 0x00, 0xff, 0xff, 0x7d, 0x83,
	0x2a, 0xc6, 0x14, 0x04, 0x00, 0x00,
}
