// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc/keepalive.proto

package rpc

import (
	_ "github.com/livekit/psrpc/protoc-gen-psrpc/options"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type KeepalivePing struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Timestamp     int64                  `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *KeepalivePing) Reset() {
	*x = KeepalivePing{}
	mi := &file_rpc_keepalive_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KeepalivePing) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KeepalivePing) ProtoMessage() {}

func (x *KeepalivePing) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_keepalive_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KeepalivePing.ProtoReflect.Descriptor instead.
func (*KeepalivePing) Descriptor() ([]byte, []int) {
	return file_rpc_keepalive_proto_rawDescGZIP(), []int{0}
}

func (x *KeepalivePing) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

var File_rpc_keepalive_proto protoreflect.FileDescriptor

const file_rpc_keepalive_proto_rawDesc = "" +
	"\n" +
	"\x13rpc/keepalive.proto\x12\x03rpc\x1a\roptions.proto\"-\n" +
	"\rKeepalivePing\x12\x1c\n" +
	"\ttimestamp\x18\x01 \x01(\x03R\ttimestamp2S\n" +
	"\tKeepalive\x12F\n" +
	"\x04Ping\x12\x12.rpc.KeepalivePing\x1a\x12.rpc.KeepalivePing\"\x16\xb2\x89\x01\x12\b\x01\x10\x01\x1a\n" +
	"\x12\x06nodeID\x18\x01(\x01B!Z\x1fgithub.com/livekit/protocol/rpcb\x06proto3"

var (
	file_rpc_keepalive_proto_rawDescOnce sync.Once
	file_rpc_keepalive_proto_rawDescData []byte
)

func file_rpc_keepalive_proto_rawDescGZIP() []byte {
	file_rpc_keepalive_proto_rawDescOnce.Do(func() {
		file_rpc_keepalive_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_keepalive_proto_rawDesc), len(file_rpc_keepalive_proto_rawDesc)))
	})
	return file_rpc_keepalive_proto_rawDescData
}

var file_rpc_keepalive_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_rpc_keepalive_proto_goTypes = []any{
	(*KeepalivePing)(nil), // 0: rpc.KeepalivePing
}
var file_rpc_keepalive_proto_depIdxs = []int32{
	0, // 0: rpc.Keepalive.Ping:input_type -> rpc.KeepalivePing
	0, // 1: rpc.Keepalive.Ping:output_type -> rpc.KeepalivePing
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_rpc_keepalive_proto_init() }
func file_rpc_keepalive_proto_init() {
	if File_rpc_keepalive_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_keepalive_proto_rawDesc), len(file_rpc_keepalive_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rpc_keepalive_proto_goTypes,
		DependencyIndexes: file_rpc_keepalive_proto_depIdxs,
		MessageInfos:      file_rpc_keepalive_proto_msgTypes,
	}.Build()
	File_rpc_keepalive_proto = out.File
	file_rpc_keepalive_proto_goTypes = nil
	file_rpc_keepalive_proto_depIdxs = nil
}
