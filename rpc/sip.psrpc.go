// Code generated by protoc-gen-psrpc v0.6.0, DO NOT EDIT.
// source: rpc/sip.proto

package rpc

import (
	"context"

	"github.com/livekit/psrpc"
	"github.com/livekit/psrpc/pkg/client"
	"github.com/livekit/psrpc/pkg/info"
	"github.com/livekit/psrpc/pkg/rand"
	"github.com/livekit/psrpc/pkg/server"
	"github.com/livekit/psrpc/version"
)
import google_protobuf "google.golang.org/protobuf/types/known/emptypb"

var _ = version.PsrpcVersion_0_6

// ============================
// SIPInternal Client Interface
// ============================

type SIPInternalClient interface {
	CreateSIPParticipant(ctx context.Context, topic string, req *InternalCreateSIPParticipantRequest, opts ...psrpc.RequestOption) (*InternalCreateSIPParticipantResponse, error)

	TransferSIPParticipant(ctx context.Context, sipCallId string, req *InternalTransferSIPParticipantRequest, opts ...psrpc.RequestOption) (*google_protobuf.Empty, error)

	// Close immediately, without waiting for pending RPCs
	Close()
}

// ================================
// SIPInternal ServerImpl Interface
// ================================

type SIPInternalServerImpl interface {
	CreateSIPParticipant(context.Context, *InternalCreateSIPParticipantRequest) (*InternalCreateSIPParticipantResponse, error)
	CreateSIPParticipantAffinity(context.Context, *InternalCreateSIPParticipantRequest) float32

	TransferSIPParticipant(context.Context, *InternalTransferSIPParticipantRequest) (*google_protobuf.Empty, error)
}

// ============================
// SIPInternal Server Interface
// ============================

type SIPInternalServer interface {
	RegisterCreateSIPParticipantTopic(topic string) error
	DeregisterCreateSIPParticipantTopic(topic string)
	RegisterTransferSIPParticipantTopic(sipCallId string) error
	DeregisterTransferSIPParticipantTopic(sipCallId string)

	// Close and wait for pending RPCs to complete
	Shutdown()

	// Close immediately, without waiting for pending RPCs
	Kill()
}

// ==================
// SIPInternal Client
// ==================

type sIPInternalClient struct {
	client *client.RPCClient
}

// NewSIPInternalClient creates a psrpc client that implements the SIPInternalClient interface.
func NewSIPInternalClient(bus psrpc.MessageBus, opts ...psrpc.ClientOption) (SIPInternalClient, error) {
	sd := &info.ServiceDefinition{
		Name: "SIPInternal",
		ID:   rand.NewClientID(),
	}

	sd.RegisterMethod("CreateSIPParticipant", true, false, true, false)
	sd.RegisterMethod("TransferSIPParticipant", false, false, true, true)

	rpcClient, err := client.NewRPCClient(sd, bus, opts...)
	if err != nil {
		return nil, err
	}

	return &sIPInternalClient{
		client: rpcClient,
	}, nil
}

func (c *sIPInternalClient) CreateSIPParticipant(ctx context.Context, topic string, req *InternalCreateSIPParticipantRequest, opts ...psrpc.RequestOption) (*InternalCreateSIPParticipantResponse, error) {
	return client.RequestSingle[*InternalCreateSIPParticipantResponse](ctx, c.client, "CreateSIPParticipant", []string{topic}, req, opts...)
}

func (c *sIPInternalClient) TransferSIPParticipant(ctx context.Context, sipCallId string, req *InternalTransferSIPParticipantRequest, opts ...psrpc.RequestOption) (*google_protobuf.Empty, error) {
	return client.RequestSingle[*google_protobuf.Empty](ctx, c.client, "TransferSIPParticipant", []string{sipCallId}, req, opts...)
}

func (s *sIPInternalClient) Close() {
	s.client.Close()
}

// ==================
// SIPInternal Server
// ==================

type sIPInternalServer struct {
	svc SIPInternalServerImpl
	rpc *server.RPCServer
}

// NewSIPInternalServer builds a RPCServer that will route requests
// to the corresponding method in the provided svc implementation.
func NewSIPInternalServer(svc SIPInternalServerImpl, bus psrpc.MessageBus, opts ...psrpc.ServerOption) (SIPInternalServer, error) {
	sd := &info.ServiceDefinition{
		Name: "SIPInternal",
		ID:   rand.NewServerID(),
	}

	s := server.NewRPCServer(sd, bus, opts...)

	sd.RegisterMethod("CreateSIPParticipant", true, false, true, false)
	sd.RegisterMethod("TransferSIPParticipant", false, false, true, true)
	return &sIPInternalServer{
		svc: svc,
		rpc: s,
	}, nil
}

func (s *sIPInternalServer) RegisterCreateSIPParticipantTopic(topic string) error {
	return server.RegisterHandler(s.rpc, "CreateSIPParticipant", []string{topic}, s.svc.CreateSIPParticipant, s.svc.CreateSIPParticipantAffinity)
}

func (s *sIPInternalServer) DeregisterCreateSIPParticipantTopic(topic string) {
	s.rpc.DeregisterHandler("CreateSIPParticipant", []string{topic})
}

func (s *sIPInternalServer) RegisterTransferSIPParticipantTopic(sipCallId string) error {
	return server.RegisterHandler(s.rpc, "TransferSIPParticipant", []string{sipCallId}, s.svc.TransferSIPParticipant, nil)
}

func (s *sIPInternalServer) DeregisterTransferSIPParticipantTopic(sipCallId string) {
	s.rpc.DeregisterHandler("TransferSIPParticipant", []string{sipCallId})
}

func (s *sIPInternalServer) Shutdown() {
	s.rpc.Close(false)
}

func (s *sIPInternalServer) Kill() {
	s.rpc.Close(true)
}

var psrpcFileDescriptor12 = []byte{
	// 1013 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x56, 0xcd, 0x72, 0xdb, 0x36,
	0x10, 0x1e, 0x5a, 0xf1, 0xdf, 0xca, 0xfa, 0x31, 0x64, 0x39, 0xb0, 0xdc, 0x38, 0xaa, 0xd3, 0xcc,
	0x28, 0x3d, 0x48, 0xad, 0x33, 0x9d, 0x76, 0x72, 0xe8, 0xd4, 0x71, 0xec, 0x89, 0x0e, 0x69, 0x5c,
	0x59, 0xb9, 0xf4, 0xc2, 0x81, 0x48, 0xd8, 0x46, 0x4d, 0x02, 0x2c, 0x00, 0x46, 0x51, 0xdf, 0x20,
	0x6f, 0xd1, 0x67, 0xc8, 0x13, 0xf5, 0x2d, 0x7a, 0xe8, 0xa5, 0x03, 0x80, 0xb4, 0x69, 0x59, 0x4a,
	0xad, 0xe9, 0x8d, 0xbb, 0xdf, 0xee, 0x07, 0xec, 0x62, 0xf1, 0x81, 0x50, 0x91, 0x49, 0xd0, 0x53,
	0x2c, 0xe9, 0x26, 0x52, 0x68, 0x81, 0x4a, 0x32, 0x09, 0x5a, 0xbb, 0x17, 0x42, 0x5c, 0x44, 0xb4,
	0x67, 0x5d, 0xa3, 0xf4, 0xbc, 0x47, 0xe3, 0x44, 0x4f, 0x5c, 0x44, 0x6b, 0x6f, 0x1a, 0x0c, 0x53,
	0x49, 0x34, 0x13, 0x3c, 0xc3, 0x2b, 0x22, 0x31, 0x96, 0xca, 0xcc, 0xcd, 0x88, 0xbd, 0xa7, 0x57,
	0x4c, 0xfb, 0xd7, 0x6b, 0xec, 0xff, 0x5d, 0x81, 0x27, 0x7d, 0xae, 0xa9, 0xe4, 0x24, 0x3a, 0x92,
	0x94, 0x68, 0x7a, 0xd6, 0x3f, 0x3d, 0x25, 0x52, 0xb3, 0x80, 0x25, 0x84, 0xeb, 0x01, 0xfd, 0x3d,
	0xa5, 0x4a, 0xa3, 0x47, 0x00, 0x89, 0x14, 0xbf, 0xd1, 0x40, 0xfb, 0x2c, 0xc4, 0xa8, 0xed, 0x75,
	0xd6, 0x07, 0xeb, 0x99, 0xa7, 0x1f, 0xa2, 0x3d, 0x28, 0x2b, 0x96, 0xf8, 0x01, 0x89, 0x22, 0x83,
	0x57, 0x1c, 0xae, 0x58, 0x72, 0x44, 0xa2, 0xa8, 0x1f, 0xa2, 0x36, 0x6c, 0x18, 0x5c, 0xcb, 0x94,
	0x5f, 0x99, 0x80, 0x86, 0x0d, 0x00, 0xc5, 0x92, 0xa1, 0x71, 0xf5, 0x43, 0x84, 0x61, 0x95, 0x84,
	0xa1, 0xa4, 0x4a, 0xe1, 0x25, 0x0b, 0xe6, 0x26, 0x6a, 0xc1, 0xda, 0xa5, 0x50, 0x9a, 0x93, 0x98,
	0xe2, 0x2d, 0x0b, 0x5d, 0xdb, 0xa8, 0x07, 0x8d, 0x90, 0x2a, 0xcd, 0xb8, 0xad, 0xda, 0x0f, 0x44,
	0xca, 0xb5, 0x9c, 0xe0, 0x3d, 0x1b, 0x86, 0x0a, 0xd0, 0x91, 0x43, 0xd0, 0x73, 0x58, 0xd7, 0x92,
	0x70, 0x95, 0x08, 0xa9, 0x71, 0xbd, 0xed, 0x75, 0xaa, 0x07, 0xcd, 0x6e, 0xd6, 0x96, 0xee, 0x59,
	0xff, 0x74, 0x98, 0x83, 0x83, 0x9b, 0x38, 0xb4, 0x0d, 0x2b, 0x3c, 0x8d, 0x47, 0x54, 0xe2, 0x92,
	0x25, 0xce, 0x2c, 0xf4, 0x10, 0x56, 0x6d, 0xc5, 0x5a, 0xe0, 0x07, 0x0e, 0x30, 0xe6, 0x50, 0x98,
	0x2d, 0xa7, 0xca, 0xf4, 0x34, 0xa6, 0x78, 0xd9, 0x6d, 0x39, 0xb7, 0x0d, 0x96, 0x10, 0xa5, 0xc6,
	0x42, 0x86, 0x78, 0xc5, 0x61, 0xb9, 0x8d, 0x76, 0x61, 0x5d, 0x0a, 0x11, 0xfb, 0x36, 0x71, 0xd5,
	0x81, 0xc6, 0xf1, 0xb3, 0x49, 0xfc, 0x16, 0xb6, 0x92, 0x9b, 0x83, 0xf1, 0x59, 0x48, 0xb9, 0x66,
	0x7a, 0x82, 0xd7, 0x6c, 0x5c, 0xa3, 0x80, 0xf5, 0x33, 0x08, 0x3d, 0x83, 0x7a, 0x31, 0xc5, 0xd2,
	0x56, 0x6d, 0x78, 0xad, 0xe0, 0x9f, 0xc5, 0x1e, 0x53, 0x4d, 0x42, 0xa2, 0x09, 0xae, 0xdd, 0x61,
	0x7f, 0x93, 0x41, 0xe8, 0x0f, 0xd8, 0x2e, 0xa6, 0x10, 0xad, 0x25, 0x1b, 0xa5, 0x9a, 0x2a, 0xbc,
	0xd9, 0x2e, 0x75, 0xca, 0x07, 0x47, 0x5d, 0x99, 0x04, 0xdd, 0x7b, 0x4c, 0x57, 0xb7, 0xe0, 0x3a,
	0xbc, 0x66, 0x39, 0x36, 0x07, 0x36, 0x68, 0x26, 0xb3, 0x30, 0xb4, 0x05, 0xcb, 0x5a, 0x5c, 0x51,
	0x8e, 0xd7, 0xed, 0xfe, 0x9c, 0x81, 0x9a, 0xb0, 0x32, 0x56, 0x7e, 0x2a, 0x23, 0x0c, 0xce, 0x3d,
	0x56, 0xef, 0x64, 0x84, 0x10, 0x3c, 0x08, 0x75, 0x7c, 0x8e, 0xcb, 0xd6, 0x69, 0xbf, 0xd1, 0x13,
	0xa8, 0x24, 0x11, 0x99, 0xf8, 0x21, 0x23, 0x91, 0x16, 0x9c, 0xe2, 0x8d, 0xb6, 0xd7, 0x59, 0x1b,
	0x6c, 0x18, 0xe7, 0xab, 0xcc, 0x87, 0xde, 0xc2, 0xea, 0x25, 0x25, 0x21, 0x95, 0x0a, 0x37, 0x6d,
	0x49, 0xdf, 0xdd, 0xbb, 0xa4, 0xd7, 0x2e, 0xcf, 0x15, 0x91, 0xb3, 0xa0, 0x14, 0x9a, 0xd9, 0xa7,
	0xaf, 0x45, 0xb1, 0x63, 0xdb, 0x96, 0xfe, 0x70, 0x51, 0xfa, 0xa1, 0x98, 0xee, 0x57, 0xe3, 0xf2,
	0x2e, 0x62, 0x96, 0xbd, 0x59, 0xcb, 0xac, 0x9c, 0x57, 0xd5, 0x5a, 0x70, 0xd9, 0x1b, 0xce, 0xa1,
	0xb8, 0x55, 0x61, 0x83, 0xdc, 0x45, 0xd0, 0x4b, 0xa8, 0x31, 0x1e, 0x44, 0x69, 0x48, 0xaf, 0x17,
	0xdc, 0xb5, 0x57, 0x6e, 0xa7, 0x78, 0xe5, 0x5c, 0xf4, 0x5b, 0xa7, 0x54, 0x83, 0x6a, 0x96, 0x91,
	0x73, 0xfc, 0x08, 0x75, 0xca, 0xc9, 0x28, 0xa2, 0xa1, 0x7f, 0x4e, 0x89, 0x4e, 0x25, 0x55, 0x78,
	0xa7, 0x5d, 0xea, 0x54, 0x0f, 0x1a, 0x45, 0x92, 0x13, 0x87, 0x0d, 0x6a, 0x59, 0x70, 0x66, 0xdb,
	0x3d, 0x48, 0xc6, 0x2f, 0x18, 0xbf, 0xf0, 0x35, 0x8b, 0xa9, 0x48, 0x35, 0x7e, 0xd8, 0xf6, 0x3a,
	0xe5, 0x83, 0x9d, 0xae, 0x13, 0xcf, 0x6e, 0x2e, 0x9e, 0xdd, 0x57, 0x99, 0x78, 0x0e, 0xaa, 0x59,
	0xc6, 0xd0, 0x25, 0xa0, 0x63, 0xd8, 0x8c, 0xc9, 0x07, 0xa7, 0x6e, 0xb9, 0xc2, 0x62, 0xfc, 0x5f,
	0x2c, 0xb5, 0x98, 0x7c, 0x30, 0xf2, 0x97, 0x3b, 0xd0, 0x09, 0xd4, 0x63, 0x1a, 0x32, 0xe2, 0x53,
	0x1e, 0xc8, 0x89, 0xad, 0x17, 0x7f, 0x61, 0xfb, 0xb1, 0x5b, 0x2c, 0xe5, 0x8d, 0x89, 0x39, 0xbe,
	0x0e, 0x19, 0xd4, 0xe2, 0xdb, 0x0e, 0xd4, 0x85, 0xc6, 0x98, 0x30, 0xed, 0xa7, 0x5c, 0xb3, 0xc8,
	0x27, 0x5c, 0x8d, 0xa9, 0xa4, 0x21, 0x7e, 0x64, 0x07, 0x78, 0xd3, 0x40, 0xef, 0x0c, 0x72, 0x98,
	0x01, 0xad, 0xd7, 0xd0, 0x9a, 0x7f, 0xc1, 0x50, 0x1d, 0x4a, 0x57, 0x74, 0x82, 0x3d, 0x7b, 0x37,
	0xcc, 0xa7, 0xb9, 0x5b, 0xef, 0x49, 0x94, 0xd2, 0x4c, 0x88, 0x9d, 0xf1, 0x62, 0xe9, 0x07, 0xaf,
	0xf5, 0x02, 0x36, 0x8a, 0xa7, 0xbe, 0x50, 0xee, 0x09, 0xe0, 0x79, 0x43, 0xbb, 0x28, 0xcf, 0xbc,
	0x29, 0x5c, 0x84, 0x67, 0xff, 0x4f, 0x0f, 0xbe, 0xfa, 0xfc, 0xc8, 0xab, 0x44, 0x70, 0x45, 0xd1,
	0x53, 0xa8, 0xde, 0xd6, 0xdd, 0x8c, 0xbf, 0x72, 0x4b, 0x71, 0xe7, 0xca, 0xf3, 0xd2, 0x7c, 0x79,
	0x9e, 0x7a, 0x35, 0x4b, 0x53, 0xaf, 0xe6, 0xfe, 0x5f, 0x4b, 0xf0, 0x34, 0xdf, 0xa2, 0x7d, 0x98,
	0xce, 0xa9, 0x9c, 0xfd, 0x3c, 0x4f, 0x31, 0x79, 0xd3, 0xef, 0xef, 0x63, 0x28, 0xeb, 0x8c, 0xc0,
	0xbc, 0x56, 0x6e, 0x4f, 0x90, 0xbb, 0x86, 0xe2, 0xae, 0x1c, 0x96, 0x66, 0xc8, 0xe1, 0x2f, 0x37,
	0x72, 0xf8, 0xc0, 0x0a, 0xc7, 0xf7, 0xb7, 0x84, 0xe3, 0xb3, 0x5b, 0x9c, 0x23, 0x88, 0x33, 0xae,
	0xe7, 0xf2, 0x82, 0xd7, 0xf3, 0xff, 0x4c, 0xe5, 0xc1, 0x3f, 0x1e, 0x94, 0xcf, 0xfa, 0xa7, 0x79,
	0x09, 0x68, 0x0c, 0x5b, 0xb3, 0x86, 0x01, 0x75, 0xee, 0x2b, 0x91, 0xad, 0x67, 0xf7, 0x88, 0x74,
	0x93, 0xb5, 0x0f, 0x9f, 0x3e, 0x7a, 0x2b, 0x75, 0xef, 0x27, 0xef, 0x1b, 0x0f, 0x29, 0xd8, 0x9e,
	0xdd, 0x3f, 0xf4, 0xf5, 0xfd, 0x9b, 0xdc, 0xda, 0xbe, 0xd3, 0xb5, 0x63, 0xf3, 0xbb, 0xb8, 0xdf,
	0xfc, 0xf4, 0xd1, 0xdb, 0xac, 0x7b, 0xad, 0x0a, 0x2a, 0xce, 0xc9, 0xcb, 0x2f, 0x7f, 0x7d, 0x7c,
	0xc1, 0xf4, 0x65, 0x3a, 0xea, 0x06, 0x22, 0xee, 0x65, 0x1a, 0xe4, 0xfe, 0x26, 0x03, 0x11, 0xf5,
	0x64, 0x12, 0x8c, 0x56, 0xac, 0xf5, 0xfc, 0xdf, 0x00, 0x00, 0x00, 0xff, 0xff, 0xb5, 0xc9, 0x75,
	0x58, 0x9c, 0x0a, 0x00, 0x00,
}
