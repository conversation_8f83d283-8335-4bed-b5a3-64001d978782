// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc/rest_signal.proto

package rpc

import (
	livekit "github.com/livekit/protocol/livekit"
	_ "github.com/livekit/psrpc/protoc-gen-psrpc/options"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RTCRestCreateRequest struct {
	state                       protoimpl.MessageState                     `protogen:"open.v1"`
	StartSession                *livekit.StartSession                      `protobuf:"bytes,1,opt,name=start_session,json=startSession,proto3" json:"start_session,omitempty"`
	OfferSdp                    string                                     `protobuf:"bytes,2,opt,name=offer_sdp,json=offerSdp,proto3" json:"offer_sdp,omitempty"`
	SubscribedParticipantTracks map[string]*RTCRestCreateRequest_TrackList `protobuf:"bytes,3,rep,name=subscribed_participant_tracks,json=subscribedParticipantTracks,proto3" json:"subscribed_participant_tracks,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields               protoimpl.UnknownFields
	sizeCache                   protoimpl.SizeCache
}

func (x *RTCRestCreateRequest) Reset() {
	*x = RTCRestCreateRequest{}
	mi := &file_rpc_rest_signal_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RTCRestCreateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTCRestCreateRequest) ProtoMessage() {}

func (x *RTCRestCreateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_rest_signal_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTCRestCreateRequest.ProtoReflect.Descriptor instead.
func (*RTCRestCreateRequest) Descriptor() ([]byte, []int) {
	return file_rpc_rest_signal_proto_rawDescGZIP(), []int{0}
}

func (x *RTCRestCreateRequest) GetStartSession() *livekit.StartSession {
	if x != nil {
		return x.StartSession
	}
	return nil
}

func (x *RTCRestCreateRequest) GetOfferSdp() string {
	if x != nil {
		return x.OfferSdp
	}
	return ""
}

func (x *RTCRestCreateRequest) GetSubscribedParticipantTracks() map[string]*RTCRestCreateRequest_TrackList {
	if x != nil {
		return x.SubscribedParticipantTracks
	}
	return nil
}

type RTCRestCreateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AnswerSdp     string                 `protobuf:"bytes,1,opt,name=answer_sdp,json=answerSdp,proto3" json:"answer_sdp,omitempty"`
	ParticipantId string                 `protobuf:"bytes,2,opt,name=participant_id,json=participantId,proto3" json:"participant_id,omitempty"`
	IceServers    []*livekit.ICEServer   `protobuf:"bytes,3,rep,name=ice_servers,json=iceServers,proto3" json:"ice_servers,omitempty"`
	IceSessionId  string                 `protobuf:"bytes,4,opt,name=ice_session_id,json=iceSessionId,proto3" json:"ice_session_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RTCRestCreateResponse) Reset() {
	*x = RTCRestCreateResponse{}
	mi := &file_rpc_rest_signal_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RTCRestCreateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTCRestCreateResponse) ProtoMessage() {}

func (x *RTCRestCreateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_rest_signal_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTCRestCreateResponse.ProtoReflect.Descriptor instead.
func (*RTCRestCreateResponse) Descriptor() ([]byte, []int) {
	return file_rpc_rest_signal_proto_rawDescGZIP(), []int{1}
}

func (x *RTCRestCreateResponse) GetAnswerSdp() string {
	if x != nil {
		return x.AnswerSdp
	}
	return ""
}

func (x *RTCRestCreateResponse) GetParticipantId() string {
	if x != nil {
		return x.ParticipantId
	}
	return ""
}

func (x *RTCRestCreateResponse) GetIceServers() []*livekit.ICEServer {
	if x != nil {
		return x.IceServers
	}
	return nil
}

func (x *RTCRestCreateResponse) GetIceSessionId() string {
	if x != nil {
		return x.IceSessionId
	}
	return ""
}

type RTCRestParticipantICETrickleRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Room                string                 `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	ParticipantIdentity string                 `protobuf:"bytes,2,opt,name=participant_identity,json=participantIdentity,proto3" json:"participant_identity,omitempty"`
	ParticipantId       string                 `protobuf:"bytes,3,opt,name=participant_id,json=participantId,proto3" json:"participant_id,omitempty"`
	IceSessionId        string                 `protobuf:"bytes,4,opt,name=ice_session_id,json=iceSessionId,proto3" json:"ice_session_id,omitempty"`
	SdpFragment         string                 `protobuf:"bytes,5,opt,name=sdp_fragment,json=sdpFragment,proto3" json:"sdp_fragment,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *RTCRestParticipantICETrickleRequest) Reset() {
	*x = RTCRestParticipantICETrickleRequest{}
	mi := &file_rpc_rest_signal_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RTCRestParticipantICETrickleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTCRestParticipantICETrickleRequest) ProtoMessage() {}

func (x *RTCRestParticipantICETrickleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_rest_signal_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTCRestParticipantICETrickleRequest.ProtoReflect.Descriptor instead.
func (*RTCRestParticipantICETrickleRequest) Descriptor() ([]byte, []int) {
	return file_rpc_rest_signal_proto_rawDescGZIP(), []int{2}
}

func (x *RTCRestParticipantICETrickleRequest) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

func (x *RTCRestParticipantICETrickleRequest) GetParticipantIdentity() string {
	if x != nil {
		return x.ParticipantIdentity
	}
	return ""
}

func (x *RTCRestParticipantICETrickleRequest) GetParticipantId() string {
	if x != nil {
		return x.ParticipantId
	}
	return ""
}

func (x *RTCRestParticipantICETrickleRequest) GetIceSessionId() string {
	if x != nil {
		return x.IceSessionId
	}
	return ""
}

func (x *RTCRestParticipantICETrickleRequest) GetSdpFragment() string {
	if x != nil {
		return x.SdpFragment
	}
	return ""
}

type RTCRestParticipantICERestartRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Room                string                 `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	ParticipantIdentity string                 `protobuf:"bytes,2,opt,name=participant_identity,json=participantIdentity,proto3" json:"participant_identity,omitempty"`
	ParticipantId       string                 `protobuf:"bytes,3,opt,name=participant_id,json=participantId,proto3" json:"participant_id,omitempty"`
	SdpFragment         string                 `protobuf:"bytes,4,opt,name=sdp_fragment,json=sdpFragment,proto3" json:"sdp_fragment,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *RTCRestParticipantICERestartRequest) Reset() {
	*x = RTCRestParticipantICERestartRequest{}
	mi := &file_rpc_rest_signal_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RTCRestParticipantICERestartRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTCRestParticipantICERestartRequest) ProtoMessage() {}

func (x *RTCRestParticipantICERestartRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_rest_signal_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTCRestParticipantICERestartRequest.ProtoReflect.Descriptor instead.
func (*RTCRestParticipantICERestartRequest) Descriptor() ([]byte, []int) {
	return file_rpc_rest_signal_proto_rawDescGZIP(), []int{3}
}

func (x *RTCRestParticipantICERestartRequest) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

func (x *RTCRestParticipantICERestartRequest) GetParticipantIdentity() string {
	if x != nil {
		return x.ParticipantIdentity
	}
	return ""
}

func (x *RTCRestParticipantICERestartRequest) GetParticipantId() string {
	if x != nil {
		return x.ParticipantId
	}
	return ""
}

func (x *RTCRestParticipantICERestartRequest) GetSdpFragment() string {
	if x != nil {
		return x.SdpFragment
	}
	return ""
}

type RTCRestParticipantICERestartResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IceSessionId  string                 `protobuf:"bytes,1,opt,name=ice_session_id,json=iceSessionId,proto3" json:"ice_session_id,omitempty"`
	SdpFragment   string                 `protobuf:"bytes,2,opt,name=sdp_fragment,json=sdpFragment,proto3" json:"sdp_fragment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RTCRestParticipantICERestartResponse) Reset() {
	*x = RTCRestParticipantICERestartResponse{}
	mi := &file_rpc_rest_signal_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RTCRestParticipantICERestartResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTCRestParticipantICERestartResponse) ProtoMessage() {}

func (x *RTCRestParticipantICERestartResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_rest_signal_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTCRestParticipantICERestartResponse.ProtoReflect.Descriptor instead.
func (*RTCRestParticipantICERestartResponse) Descriptor() ([]byte, []int) {
	return file_rpc_rest_signal_proto_rawDescGZIP(), []int{4}
}

func (x *RTCRestParticipantICERestartResponse) GetIceSessionId() string {
	if x != nil {
		return x.IceSessionId
	}
	return ""
}

func (x *RTCRestParticipantICERestartResponse) GetSdpFragment() string {
	if x != nil {
		return x.SdpFragment
	}
	return ""
}

type RTCRestParticipantDeleteSessionRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Room                string                 `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	ParticipantIdentity string                 `protobuf:"bytes,2,opt,name=participant_identity,json=participantIdentity,proto3" json:"participant_identity,omitempty"`
	ParticipantId       string                 `protobuf:"bytes,3,opt,name=participant_id,json=participantId,proto3" json:"participant_id,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *RTCRestParticipantDeleteSessionRequest) Reset() {
	*x = RTCRestParticipantDeleteSessionRequest{}
	mi := &file_rpc_rest_signal_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RTCRestParticipantDeleteSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTCRestParticipantDeleteSessionRequest) ProtoMessage() {}

func (x *RTCRestParticipantDeleteSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_rest_signal_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTCRestParticipantDeleteSessionRequest.ProtoReflect.Descriptor instead.
func (*RTCRestParticipantDeleteSessionRequest) Descriptor() ([]byte, []int) {
	return file_rpc_rest_signal_proto_rawDescGZIP(), []int{5}
}

func (x *RTCRestParticipantDeleteSessionRequest) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

func (x *RTCRestParticipantDeleteSessionRequest) GetParticipantIdentity() string {
	if x != nil {
		return x.ParticipantIdentity
	}
	return ""
}

func (x *RTCRestParticipantDeleteSessionRequest) GetParticipantId() string {
	if x != nil {
		return x.ParticipantId
	}
	return ""
}

type RTCRestCreateRequest_TrackList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TrackNames    []string               `protobuf:"bytes,1,rep,name=track_names,json=trackNames,proto3" json:"track_names,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RTCRestCreateRequest_TrackList) Reset() {
	*x = RTCRestCreateRequest_TrackList{}
	mi := &file_rpc_rest_signal_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RTCRestCreateRequest_TrackList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RTCRestCreateRequest_TrackList) ProtoMessage() {}

func (x *RTCRestCreateRequest_TrackList) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_rest_signal_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RTCRestCreateRequest_TrackList.ProtoReflect.Descriptor instead.
func (*RTCRestCreateRequest_TrackList) Descriptor() ([]byte, []int) {
	return file_rpc_rest_signal_proto_rawDescGZIP(), []int{0, 0}
}

func (x *RTCRestCreateRequest_TrackList) GetTrackNames() []string {
	if x != nil {
		return x.TrackNames
	}
	return nil
}

var File_rpc_rest_signal_proto protoreflect.FileDescriptor

const file_rpc_rest_signal_proto_rawDesc = "" +
	"\n" +
	"\x15rpc/rest_signal.proto\x12\x03rpc\x1a\x1bgoogle/protobuf/empty.proto\x1a\roptions.proto\x1a\x16livekit_internal.proto\x1a\x11livekit_rtc.proto\"\x92\x03\n" +
	"\x14RTCRestCreateRequest\x12:\n" +
	"\rstart_session\x18\x01 \x01(\v2\x15.livekit.StartSessionR\fstartSession\x12\x1b\n" +
	"\toffer_sdp\x18\x02 \x01(\tR\bofferSdp\x12~\n" +
	"\x1dsubscribed_participant_tracks\x18\x03 \x03(\v2:.rpc.RTCRestCreateRequest.SubscribedParticipantTracksEntryR\x1bsubscribedParticipantTracks\x1a,\n" +
	"\tTrackList\x12\x1f\n" +
	"\vtrack_names\x18\x01 \x03(\tR\n" +
	"trackNames\x1as\n" +
	" SubscribedParticipantTracksEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x129\n" +
	"\x05value\x18\x02 \x01(\v2#.rpc.RTCRestCreateRequest.TrackListR\x05value:\x028\x01\"\xb8\x01\n" +
	"\x15RTCRestCreateResponse\x12\x1d\n" +
	"\n" +
	"answer_sdp\x18\x01 \x01(\tR\tanswerSdp\x12%\n" +
	"\x0eparticipant_id\x18\x02 \x01(\tR\rparticipantId\x123\n" +
	"\vice_servers\x18\x03 \x03(\v2\x12.livekit.ICEServerR\n" +
	"iceServers\x12$\n" +
	"\x0eice_session_id\x18\x04 \x01(\tR\ficeSessionId\"\xdc\x01\n" +
	"#RTCRestParticipantICETrickleRequest\x12\x12\n" +
	"\x04room\x18\x01 \x01(\tR\x04room\x121\n" +
	"\x14participant_identity\x18\x02 \x01(\tR\x13participantIdentity\x12%\n" +
	"\x0eparticipant_id\x18\x03 \x01(\tR\rparticipantId\x12$\n" +
	"\x0eice_session_id\x18\x04 \x01(\tR\ficeSessionId\x12!\n" +
	"\fsdp_fragment\x18\x05 \x01(\tR\vsdpFragment\"\xb6\x01\n" +
	"#RTCRestParticipantICERestartRequest\x12\x12\n" +
	"\x04room\x18\x01 \x01(\tR\x04room\x121\n" +
	"\x14participant_identity\x18\x02 \x01(\tR\x13participantIdentity\x12%\n" +
	"\x0eparticipant_id\x18\x03 \x01(\tR\rparticipantId\x12!\n" +
	"\fsdp_fragment\x18\x04 \x01(\tR\vsdpFragment\"o\n" +
	"$RTCRestParticipantICERestartResponse\x12$\n" +
	"\x0eice_session_id\x18\x01 \x01(\tR\ficeSessionId\x12!\n" +
	"\fsdp_fragment\x18\x02 \x01(\tR\vsdpFragment\"\x96\x01\n" +
	"&RTCRestParticipantDeleteSessionRequest\x12\x12\n" +
	"\x04room\x18\x01 \x01(\tR\x04room\x121\n" +
	"\x14participant_identity\x18\x02 \x01(\tR\x13participantIdentity\x12%\n" +
	"\x0eparticipant_id\x18\x03 \x01(\tR\rparticipantId2g\n" +
	"\aRTCRest\x12\\\n" +
	"\x06Create\x12\x19.rpc.RTCRestCreateRequest\x1a\x1a.rpc.RTCRestCreateResponse\"\x1b\xb2\x89\x01\x17\x10\x01\x1a\x11\n" +
	"\x06common\x12\x05topic\x18\x018\x012\xee\x02\n" +
	"\x12RTCRestParticipant\x12i\n" +
	"\n" +
	"ICETrickle\x12(.rpc.RTCRestParticipantICETrickleRequest\x1a\x16.google.protobuf.Empty\"\x19\xb2\x89\x01\x15\x10\x01\x1a\x11\n" +
	"\x06common\x12\x05topic\x18\x01\x12|\n" +
	"\n" +
	"ICERestart\x12(.rpc.RTCRestParticipantICERestartRequest\x1a).rpc.RTCRestParticipantICERestartResponse\"\x19\xb2\x89\x01\x15\x10\x01\x1a\x11\n" +
	"\x06common\x12\x05topic\x18\x01\x12o\n" +
	"\rDeleteSession\x12+.rpc.RTCRestParticipantDeleteSessionRequest\x1a\x16.google.protobuf.Empty\"\x19\xb2\x89\x01\x15\x10\x01\x1a\x11\n" +
	"\x06common\x12\x05topic\x18\x01B!Z\x1fgithub.com/livekit/protocol/rpcb\x06proto3"

var (
	file_rpc_rest_signal_proto_rawDescOnce sync.Once
	file_rpc_rest_signal_proto_rawDescData []byte
)

func file_rpc_rest_signal_proto_rawDescGZIP() []byte {
	file_rpc_rest_signal_proto_rawDescOnce.Do(func() {
		file_rpc_rest_signal_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_rest_signal_proto_rawDesc), len(file_rpc_rest_signal_proto_rawDesc)))
	})
	return file_rpc_rest_signal_proto_rawDescData
}

var file_rpc_rest_signal_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_rpc_rest_signal_proto_goTypes = []any{
	(*RTCRestCreateRequest)(nil),                   // 0: rpc.RTCRestCreateRequest
	(*RTCRestCreateResponse)(nil),                  // 1: rpc.RTCRestCreateResponse
	(*RTCRestParticipantICETrickleRequest)(nil),    // 2: rpc.RTCRestParticipantICETrickleRequest
	(*RTCRestParticipantICERestartRequest)(nil),    // 3: rpc.RTCRestParticipantICERestartRequest
	(*RTCRestParticipantICERestartResponse)(nil),   // 4: rpc.RTCRestParticipantICERestartResponse
	(*RTCRestParticipantDeleteSessionRequest)(nil), // 5: rpc.RTCRestParticipantDeleteSessionRequest
	(*RTCRestCreateRequest_TrackList)(nil),         // 6: rpc.RTCRestCreateRequest.TrackList
	nil,                                            // 7: rpc.RTCRestCreateRequest.SubscribedParticipantTracksEntry
	(*livekit.StartSession)(nil),                   // 8: livekit.StartSession
	(*livekit.ICEServer)(nil),                      // 9: livekit.ICEServer
	(*emptypb.Empty)(nil),                          // 10: google.protobuf.Empty
}
var file_rpc_rest_signal_proto_depIdxs = []int32{
	8,  // 0: rpc.RTCRestCreateRequest.start_session:type_name -> livekit.StartSession
	7,  // 1: rpc.RTCRestCreateRequest.subscribed_participant_tracks:type_name -> rpc.RTCRestCreateRequest.SubscribedParticipantTracksEntry
	9,  // 2: rpc.RTCRestCreateResponse.ice_servers:type_name -> livekit.ICEServer
	6,  // 3: rpc.RTCRestCreateRequest.SubscribedParticipantTracksEntry.value:type_name -> rpc.RTCRestCreateRequest.TrackList
	0,  // 4: rpc.RTCRest.Create:input_type -> rpc.RTCRestCreateRequest
	2,  // 5: rpc.RTCRestParticipant.ICETrickle:input_type -> rpc.RTCRestParticipantICETrickleRequest
	3,  // 6: rpc.RTCRestParticipant.ICERestart:input_type -> rpc.RTCRestParticipantICERestartRequest
	5,  // 7: rpc.RTCRestParticipant.DeleteSession:input_type -> rpc.RTCRestParticipantDeleteSessionRequest
	1,  // 8: rpc.RTCRest.Create:output_type -> rpc.RTCRestCreateResponse
	10, // 9: rpc.RTCRestParticipant.ICETrickle:output_type -> google.protobuf.Empty
	4,  // 10: rpc.RTCRestParticipant.ICERestart:output_type -> rpc.RTCRestParticipantICERestartResponse
	10, // 11: rpc.RTCRestParticipant.DeleteSession:output_type -> google.protobuf.Empty
	8,  // [8:12] is the sub-list for method output_type
	4,  // [4:8] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_rpc_rest_signal_proto_init() }
func file_rpc_rest_signal_proto_init() {
	if File_rpc_rest_signal_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_rest_signal_proto_rawDesc), len(file_rpc_rest_signal_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_rpc_rest_signal_proto_goTypes,
		DependencyIndexes: file_rpc_rest_signal_proto_depIdxs,
		MessageInfos:      file_rpc_rest_signal_proto_msgTypes,
	}.Build()
	File_rpc_rest_signal_proto = out.File
	file_rpc_rest_signal_proto_goTypes = nil
	file_rpc_rest_signal_proto_depIdxs = nil
}
