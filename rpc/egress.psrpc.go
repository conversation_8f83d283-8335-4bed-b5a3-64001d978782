// Code generated by protoc-gen-psrpc v0.6.0, DO NOT EDIT.
// source: rpc/egress.proto

package rpc

import (
	"context"

	"github.com/livekit/psrpc"
	"github.com/livekit/psrpc/pkg/client"
	"github.com/livekit/psrpc/pkg/info"
	"github.com/livekit/psrpc/pkg/rand"
	"github.com/livekit/psrpc/pkg/server"
	"github.com/livekit/psrpc/version"
)
import livekit4 "github.com/livekit/protocol/livekit"

var _ = version.PsrpcVersion_0_6

// ===============================
// EgressInternal Client Interface
// ===============================

type EgressInternalClient interface {
	StartEgress(ctx context.Context, topic string, req *StartEgressRequest, opts ...psrpc.RequestOption) (*livekit4.EgressInfo, error)

	ListActiveEgress(ctx context.Context, topic string, req *ListActiveEgressRequest, opts ...psrpc.RequestOption) (<-chan *psrpc.Response[*ListActiveEgressResponse], error)

	// Close immediately, without waiting for pending RPCs
	Close()
}

// ===================================
// EgressInternal ServerImpl Interface
// ===================================

type EgressInternalServerImpl interface {
	StartEgress(context.Context, *StartEgressRequest) (*livekit4.EgressInfo, error)
	StartEgressAffinity(context.Context, *StartEgressRequest) float32

	ListActiveEgress(context.Context, *ListActiveEgressRequest) (*ListActiveEgressResponse, error)
}

// ===============================
// EgressInternal Server Interface
// ===============================

type EgressInternalServer interface {
	RegisterStartEgressTopic(topic string) error
	DeregisterStartEgressTopic(topic string)
	RegisterListActiveEgressTopic(topic string) error
	DeregisterListActiveEgressTopic(topic string)

	// Close and wait for pending RPCs to complete
	Shutdown()

	// Close immediately, without waiting for pending RPCs
	Kill()
}

// =====================
// EgressInternal Client
// =====================

type egressInternalClient struct {
	client *client.RPCClient
}

// NewEgressInternalClient creates a psrpc client that implements the EgressInternalClient interface.
func NewEgressInternalClient(bus psrpc.MessageBus, opts ...psrpc.ClientOption) (EgressInternalClient, error) {
	sd := &info.ServiceDefinition{
		Name: "EgressInternal",
		ID:   rand.NewClientID(),
	}

	sd.RegisterMethod("StartEgress", true, false, true, false)
	sd.RegisterMethod("ListActiveEgress", false, true, false, false)

	rpcClient, err := client.NewRPCClient(sd, bus, opts...)
	if err != nil {
		return nil, err
	}

	return &egressInternalClient{
		client: rpcClient,
	}, nil
}

func (c *egressInternalClient) StartEgress(ctx context.Context, topic string, req *StartEgressRequest, opts ...psrpc.RequestOption) (*livekit4.EgressInfo, error) {
	return client.RequestSingle[*livekit4.EgressInfo](ctx, c.client, "StartEgress", []string{topic}, req, opts...)
}

func (c *egressInternalClient) ListActiveEgress(ctx context.Context, topic string, req *ListActiveEgressRequest, opts ...psrpc.RequestOption) (<-chan *psrpc.Response[*ListActiveEgressResponse], error) {
	return client.RequestMulti[*ListActiveEgressResponse](ctx, c.client, "ListActiveEgress", []string{topic}, req, opts...)
}

func (s *egressInternalClient) Close() {
	s.client.Close()
}

// =====================
// EgressInternal Server
// =====================

type egressInternalServer struct {
	svc EgressInternalServerImpl
	rpc *server.RPCServer
}

// NewEgressInternalServer builds a RPCServer that will route requests
// to the corresponding method in the provided svc implementation.
func NewEgressInternalServer(svc EgressInternalServerImpl, bus psrpc.MessageBus, opts ...psrpc.ServerOption) (EgressInternalServer, error) {
	sd := &info.ServiceDefinition{
		Name: "EgressInternal",
		ID:   rand.NewServerID(),
	}

	s := server.NewRPCServer(sd, bus, opts...)

	sd.RegisterMethod("StartEgress", true, false, true, false)
	sd.RegisterMethod("ListActiveEgress", false, true, false, false)
	return &egressInternalServer{
		svc: svc,
		rpc: s,
	}, nil
}

func (s *egressInternalServer) RegisterStartEgressTopic(topic string) error {
	return server.RegisterHandler(s.rpc, "StartEgress", []string{topic}, s.svc.StartEgress, s.svc.StartEgressAffinity)
}

func (s *egressInternalServer) DeregisterStartEgressTopic(topic string) {
	s.rpc.DeregisterHandler("StartEgress", []string{topic})
}

func (s *egressInternalServer) RegisterListActiveEgressTopic(topic string) error {
	return server.RegisterHandler(s.rpc, "ListActiveEgress", []string{topic}, s.svc.ListActiveEgress, nil)
}

func (s *egressInternalServer) DeregisterListActiveEgressTopic(topic string) {
	s.rpc.DeregisterHandler("ListActiveEgress", []string{topic})
}

func (s *egressInternalServer) Shutdown() {
	s.rpc.Close(false)
}

func (s *egressInternalServer) Kill() {
	s.rpc.Close(true)
}

// ==============================
// EgressHandler Client Interface
// ==============================

type EgressHandlerClient interface {
	UpdateStream(ctx context.Context, topic string, req *livekit4.UpdateStreamRequest, opts ...psrpc.RequestOption) (*livekit4.EgressInfo, error)

	StopEgress(ctx context.Context, topic string, req *livekit4.StopEgressRequest, opts ...psrpc.RequestOption) (*livekit4.EgressInfo, error)

	// Close immediately, without waiting for pending RPCs
	Close()
}

// ==================================
// EgressHandler ServerImpl Interface
// ==================================

type EgressHandlerServerImpl interface {
	UpdateStream(context.Context, *livekit4.UpdateStreamRequest) (*livekit4.EgressInfo, error)

	StopEgress(context.Context, *livekit4.StopEgressRequest) (*livekit4.EgressInfo, error)
}

// ==============================
// EgressHandler Server Interface
// ==============================

type EgressHandlerServer interface {
	RegisterUpdateStreamTopic(topic string) error
	DeregisterUpdateStreamTopic(topic string)
	RegisterStopEgressTopic(topic string) error
	DeregisterStopEgressTopic(topic string)

	// Close and wait for pending RPCs to complete
	Shutdown()

	// Close immediately, without waiting for pending RPCs
	Kill()
}

// ====================
// EgressHandler Client
// ====================

type egressHandlerClient struct {
	client *client.RPCClient
}

// NewEgressHandlerClient creates a psrpc client that implements the EgressHandlerClient interface.
func NewEgressHandlerClient(bus psrpc.MessageBus, opts ...psrpc.ClientOption) (EgressHandlerClient, error) {
	sd := &info.ServiceDefinition{
		Name: "EgressHandler",
		ID:   rand.NewClientID(),
	}

	sd.RegisterMethod("UpdateStream", false, false, true, true)
	sd.RegisterMethod("StopEgress", false, false, true, true)

	rpcClient, err := client.NewRPCClient(sd, bus, opts...)
	if err != nil {
		return nil, err
	}

	return &egressHandlerClient{
		client: rpcClient,
	}, nil
}

func (c *egressHandlerClient) UpdateStream(ctx context.Context, topic string, req *livekit4.UpdateStreamRequest, opts ...psrpc.RequestOption) (*livekit4.EgressInfo, error) {
	return client.RequestSingle[*livekit4.EgressInfo](ctx, c.client, "UpdateStream", []string{topic}, req, opts...)
}

func (c *egressHandlerClient) StopEgress(ctx context.Context, topic string, req *livekit4.StopEgressRequest, opts ...psrpc.RequestOption) (*livekit4.EgressInfo, error) {
	return client.RequestSingle[*livekit4.EgressInfo](ctx, c.client, "StopEgress", []string{topic}, req, opts...)
}

func (s *egressHandlerClient) Close() {
	s.client.Close()
}

// ====================
// EgressHandler Server
// ====================

type egressHandlerServer struct {
	svc EgressHandlerServerImpl
	rpc *server.RPCServer
}

// NewEgressHandlerServer builds a RPCServer that will route requests
// to the corresponding method in the provided svc implementation.
func NewEgressHandlerServer(svc EgressHandlerServerImpl, bus psrpc.MessageBus, opts ...psrpc.ServerOption) (EgressHandlerServer, error) {
	sd := &info.ServiceDefinition{
		Name: "EgressHandler",
		ID:   rand.NewServerID(),
	}

	s := server.NewRPCServer(sd, bus, opts...)

	sd.RegisterMethod("UpdateStream", false, false, true, true)
	sd.RegisterMethod("StopEgress", false, false, true, true)
	return &egressHandlerServer{
		svc: svc,
		rpc: s,
	}, nil
}

func (s *egressHandlerServer) RegisterUpdateStreamTopic(topic string) error {
	return server.RegisterHandler(s.rpc, "UpdateStream", []string{topic}, s.svc.UpdateStream, nil)
}

func (s *egressHandlerServer) DeregisterUpdateStreamTopic(topic string) {
	s.rpc.DeregisterHandler("UpdateStream", []string{topic})
}

func (s *egressHandlerServer) RegisterStopEgressTopic(topic string) error {
	return server.RegisterHandler(s.rpc, "StopEgress", []string{topic}, s.svc.StopEgress, nil)
}

func (s *egressHandlerServer) DeregisterStopEgressTopic(topic string) {
	s.rpc.DeregisterHandler("StopEgress", []string{topic})
}

func (s *egressHandlerServer) Shutdown() {
	s.rpc.Close(false)
}

func (s *egressHandlerServer) Kill() {
	s.rpc.Close(true)
}

var psrpcFileDescriptor2 = []byte{
	// 544 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x93, 0xd1, 0x4e, 0xdb, 0x3e,
	0x14, 0xc6, 0xff, 0xa6, 0x7f, 0x02, 0x39, 0xa5, 0x5d, 0xe5, 0x31, 0x61, 0x0a, 0x68, 0xa5, 0xec,
	0x22, 0x37, 0x4b, 0x11, 0x5c, 0xed, 0x6e, 0x03, 0x55, 0xa3, 0x12, 0xd2, 0xa6, 0x74, 0x68, 0xd2,
	0x76, 0x11, 0x25, 0x8e, 0xc7, 0xac, 0x26, 0xb1, 0x67, 0x3b, 0xf4, 0x19, 0x78, 0x8c, 0x69, 0x6f,
	0xc0, 0xc5, 0x9e, 0x6f, 0xaa, 0xd3, 0x86, 0xb4, 0xa8, 0xd3, 0x2e, 0xfd, 0x7d, 0xe7, 0xfb, 0xe5,
	0x9c, 0xa3, 0x13, 0xe8, 0x28, 0x49, 0x07, 0xec, 0x56, 0x31, 0xad, 0x7d, 0xa9, 0x84, 0x11, 0xb8,
	0xa1, 0x24, 0xed, 0xb6, 0x84, 0x34, 0x5c, 0xe4, 0x73, 0xad, 0xbb, 0x9b, 0xf2, 0x3b, 0x36, 0xe1,
	0x26, 0xac, 0x57, 0xf6, 0x7f, 0xfd, 0x0f, 0x78, 0x6c, 0x22, 0x65, 0x86, 0x56, 0x0d, 0xd8, 0x8f,
	0x82, 0x69, 0x83, 0x0f, 0xc0, 0x2d, 0xcb, 0x42, 0x9e, 0x10, 0xd4, 0x43, 0x9e, 0x1b, 0x6c, 0x97,
	0xc2, 0x28, 0xc1, 0xd7, 0xd0, 0x56, 0x42, 0x64, 0x21, 0x15, 0x99, 0x14, 0x9a, 0x1b, 0x46, 0x36,
	0x7b, 0xc8, 0x6b, 0x9e, 0x9d, 0xf8, 0xf3, 0x4f, 0xf8, 0x81, 0x10, 0xd9, 0xe5, 0xc2, 0x5d, 0x22,
	0x5f, 0xfd, 0x17, 0xb4, 0x54, 0xdd, 0xc5, 0xaf, 0xa1, 0x31, 0x65, 0x31, 0x69, 0x5a, 0xc4, 0x7e,
	0x85, 0xf8, 0xcc, 0xe2, 0xd5, 0xe0, 0xac, 0x0e, 0x0f, 0xa1, 0x29, 0x23, 0x65, 0x38, 0xe5, 0x32,
	0xca, 0x0d, 0x69, 0xd9, 0xd8, 0x71, 0x15, 0xfb, 0xf8, 0xe8, 0xad, 0xc6, 0xeb, 0x39, 0xfc, 0x01,
	0x9e, 0x19, 0x15, 0xd1, 0x49, 0x6d, 0x08, 0xc7, 0xa2, 0x5e, 0x55, 0xa8, 0x4f, 0x33, 0x7f, 0xed,
	0x14, 0x6d, 0xb3, 0x64, 0xe3, 0x73, 0xd8, 0xb4, 0x0a, 0xd9, 0xb2, 0x98, 0x83, 0x65, 0xcc, 0x6a,
	0xba, 0xac, 0xc5, 0x7b, 0xb0, 0x65, 0x37, 0xc9, 0x13, 0xd2, 0xb0, 0x4b, 0x76, 0x66, 0xcf, 0x51,
	0x82, 0x77, 0x61, 0xd3, 0x88, 0x09, 0xcb, 0xc9, 0xb6, 0x95, 0xcb, 0x07, 0x7e, 0x01, 0xce, 0x54,
	0x87, 0x85, 0x4a, 0x89, 0x5b, 0xca, 0x53, 0x7d, 0xa3, 0x52, 0x7c, 0x0a, 0xbb, 0x34, 0x15, 0x45,
	0x12, 0xc6, 0x11, 0x9d, 0x14, 0x32, 0x64, 0x79, 0x14, 0xa7, 0x2c, 0x21, 0xd0, 0x43, 0xde, 0x76,
	0x80, 0xad, 0x77, 0x61, 0xad, 0x61, 0xe9, 0xe0, 0x13, 0x68, 0x31, 0x6d, 0x78, 0x16, 0x19, 0x96,
	0x84, 0x54, 0x16, 0xa4, 0xdd, 0x43, 0x1e, 0x0a, 0x76, 0x2a, 0xf1, 0x52, 0x16, 0x17, 0x2e, 0x6c,
	0xa9, 0xb2, 0xe1, 0xfe, 0x3e, 0xec, 0x5d, 0x73, 0x6d, 0xde, 0x51, 0xc3, 0xef, 0x96, 0x37, 0xd1,
	0x7f, 0x03, 0xe4, 0xa9, 0xa5, 0xa5, 0xc8, 0x35, 0xc3, 0x47, 0x00, 0xd5, 0x15, 0x69, 0x82, 0x7a,
	0x0d, 0xcf, 0x0d, 0xdc, 0xc5, 0x19, 0xe9, 0xb3, 0xdf, 0x08, 0xda, 0x65, 0x62, 0x94, 0x1b, 0xa6,
	0xf2, 0x28, 0xc5, 0xef, 0xa1, 0x59, 0xbb, 0x46, 0xbc, 0xe7, 0x2b, 0x49, 0xfd, 0xa7, 0xf7, 0xd9,
	0x7d, 0x5e, 0xad, 0x77, 0x01, 0xf8, 0x26, 0xfa, 0xf0, 0x70, 0x8f, 0x9c, 0x0e, 0x7a, 0x8b, 0x4e,
	0x11, 0xfe, 0x0a, 0x9d, 0xd5, 0xb6, 0xf0, 0xa1, 0xa5, 0xad, 0x19, 0xa4, 0x7b, 0xb4, 0xc6, 0x2d,
	0x67, 0xa9, 0xe0, 0x1b, 0x1e, 0x3a, 0xfb, 0x89, 0xa0, 0x55, 0xda, 0x57, 0x51, 0x9e, 0xa4, 0x4c,
	0xe1, 0x11, 0xec, 0xdc, 0xc8, 0x24, 0x32, 0x6c, 0x6c, 0x14, 0x8b, 0x32, 0x7c, 0x58, 0xf5, 0x57,
	0x97, 0xff, 0xda, 0xbd, 0xf3, 0x70, 0x8f, 0x36, 0x3a, 0x08, 0x0f, 0x01, 0xc6, 0x46, 0xc8, 0x79,
	0xcf, 0xdd, 0xaa, 0xf4, 0x51, 0xfc, 0x17, 0xcc, 0xc5, 0xf1, 0x97, 0x97, 0xb7, 0xdc, 0x7c, 0x2f,
	0x62, 0x9f, 0x8a, 0x6c, 0x30, 0x2f, 0x1c, 0xd8, 0x9f, 0x9e, 0x8a, 0x74, 0xa0, 0x24, 0x8d, 0x1d,
	0xfb, 0x3a, 0xff, 0x13, 0x00, 0x00, 0xff, 0xff, 0x6f, 0xc8, 0xf7, 0xcf, 0x40, 0x04, 0x00, 0x00,
}
