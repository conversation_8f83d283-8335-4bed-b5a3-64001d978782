// Code generated by counterfeiter. DO NOT EDIT.
package rpcfakes

import (
	"context"
	"sync"

	"github.com/livekit/protocol/livekit"
	"github.com/livekit/protocol/rpc"
	"github.com/livekit/psrpc"
)

type FakeTypedParticipantClient struct {
	CloseStub        func()
	closeMutex       sync.RWMutex
	closeArgsForCall []struct {
	}
	ForwardParticipantStub        func(context.Context, rpc.ParticipantTopic, *livekit.ForwardParticipantRequest, ...psrpc.RequestOption) (*livekit.ForwardParticipantResponse, error)
	forwardParticipantMutex       sync.RWMutex
	forwardParticipantArgsForCall []struct {
		arg1 context.Context
		arg2 rpc.ParticipantTopic
		arg3 *livekit.ForwardParticipantRequest
		arg4 []psrpc.RequestOption
	}
	forwardParticipantReturns struct {
		result1 *livekit.ForwardParticipantResponse
		result2 error
	}
	forwardParticipantReturnsOnCall map[int]struct {
		result1 *livekit.ForwardParticipantResponse
		result2 error
	}
	MoveParticipantStub        func(context.Context, rpc.ParticipantTopic, *livekit.MoveParticipantRequest, ...psrpc.RequestOption) (*livekit.MoveParticipantResponse, error)
	moveParticipantMutex       sync.RWMutex
	moveParticipantArgsForCall []struct {
		arg1 context.Context
		arg2 rpc.ParticipantTopic
		arg3 *livekit.MoveParticipantRequest
		arg4 []psrpc.RequestOption
	}
	moveParticipantReturns struct {
		result1 *livekit.MoveParticipantResponse
		result2 error
	}
	moveParticipantReturnsOnCall map[int]struct {
		result1 *livekit.MoveParticipantResponse
		result2 error
	}
	MutePublishedTrackStub        func(context.Context, rpc.ParticipantTopic, *livekit.MuteRoomTrackRequest, ...psrpc.RequestOption) (*livekit.MuteRoomTrackResponse, error)
	mutePublishedTrackMutex       sync.RWMutex
	mutePublishedTrackArgsForCall []struct {
		arg1 context.Context
		arg2 rpc.ParticipantTopic
		arg3 *livekit.MuteRoomTrackRequest
		arg4 []psrpc.RequestOption
	}
	mutePublishedTrackReturns struct {
		result1 *livekit.MuteRoomTrackResponse
		result2 error
	}
	mutePublishedTrackReturnsOnCall map[int]struct {
		result1 *livekit.MuteRoomTrackResponse
		result2 error
	}
	RemoveParticipantStub        func(context.Context, rpc.ParticipantTopic, *livekit.RoomParticipantIdentity, ...psrpc.RequestOption) (*livekit.RemoveParticipantResponse, error)
	removeParticipantMutex       sync.RWMutex
	removeParticipantArgsForCall []struct {
		arg1 context.Context
		arg2 rpc.ParticipantTopic
		arg3 *livekit.RoomParticipantIdentity
		arg4 []psrpc.RequestOption
	}
	removeParticipantReturns struct {
		result1 *livekit.RemoveParticipantResponse
		result2 error
	}
	removeParticipantReturnsOnCall map[int]struct {
		result1 *livekit.RemoveParticipantResponse
		result2 error
	}
	UpdateParticipantStub        func(context.Context, rpc.ParticipantTopic, *livekit.UpdateParticipantRequest, ...psrpc.RequestOption) (*livekit.ParticipantInfo, error)
	updateParticipantMutex       sync.RWMutex
	updateParticipantArgsForCall []struct {
		arg1 context.Context
		arg2 rpc.ParticipantTopic
		arg3 *livekit.UpdateParticipantRequest
		arg4 []psrpc.RequestOption
	}
	updateParticipantReturns struct {
		result1 *livekit.ParticipantInfo
		result2 error
	}
	updateParticipantReturnsOnCall map[int]struct {
		result1 *livekit.ParticipantInfo
		result2 error
	}
	UpdateSubscriptionsStub        func(context.Context, rpc.ParticipantTopic, *livekit.UpdateSubscriptionsRequest, ...psrpc.RequestOption) (*livekit.UpdateSubscriptionsResponse, error)
	updateSubscriptionsMutex       sync.RWMutex
	updateSubscriptionsArgsForCall []struct {
		arg1 context.Context
		arg2 rpc.ParticipantTopic
		arg3 *livekit.UpdateSubscriptionsRequest
		arg4 []psrpc.RequestOption
	}
	updateSubscriptionsReturns struct {
		result1 *livekit.UpdateSubscriptionsResponse
		result2 error
	}
	updateSubscriptionsReturnsOnCall map[int]struct {
		result1 *livekit.UpdateSubscriptionsResponse
		result2 error
	}
	invocations      map[string][][]interface{}
	invocationsMutex sync.RWMutex
}

func (fake *FakeTypedParticipantClient) Close() {
	fake.closeMutex.Lock()
	fake.closeArgsForCall = append(fake.closeArgsForCall, struct {
	}{})
	stub := fake.CloseStub
	fake.recordInvocation("Close", []interface{}{})
	fake.closeMutex.Unlock()
	if stub != nil {
		fake.CloseStub()
	}
}

func (fake *FakeTypedParticipantClient) CloseCallCount() int {
	fake.closeMutex.RLock()
	defer fake.closeMutex.RUnlock()
	return len(fake.closeArgsForCall)
}

func (fake *FakeTypedParticipantClient) CloseCalls(stub func()) {
	fake.closeMutex.Lock()
	defer fake.closeMutex.Unlock()
	fake.CloseStub = stub
}

func (fake *FakeTypedParticipantClient) ForwardParticipant(arg1 context.Context, arg2 rpc.ParticipantTopic, arg3 *livekit.ForwardParticipantRequest, arg4 ...psrpc.RequestOption) (*livekit.ForwardParticipantResponse, error) {
	fake.forwardParticipantMutex.Lock()
	ret, specificReturn := fake.forwardParticipantReturnsOnCall[len(fake.forwardParticipantArgsForCall)]
	fake.forwardParticipantArgsForCall = append(fake.forwardParticipantArgsForCall, struct {
		arg1 context.Context
		arg2 rpc.ParticipantTopic
		arg3 *livekit.ForwardParticipantRequest
		arg4 []psrpc.RequestOption
	}{arg1, arg2, arg3, arg4})
	stub := fake.ForwardParticipantStub
	fakeReturns := fake.forwardParticipantReturns
	fake.recordInvocation("ForwardParticipant", []interface{}{arg1, arg2, arg3, arg4})
	fake.forwardParticipantMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3, arg4...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeTypedParticipantClient) ForwardParticipantCallCount() int {
	fake.forwardParticipantMutex.RLock()
	defer fake.forwardParticipantMutex.RUnlock()
	return len(fake.forwardParticipantArgsForCall)
}

func (fake *FakeTypedParticipantClient) ForwardParticipantCalls(stub func(context.Context, rpc.ParticipantTopic, *livekit.ForwardParticipantRequest, ...psrpc.RequestOption) (*livekit.ForwardParticipantResponse, error)) {
	fake.forwardParticipantMutex.Lock()
	defer fake.forwardParticipantMutex.Unlock()
	fake.ForwardParticipantStub = stub
}

func (fake *FakeTypedParticipantClient) ForwardParticipantArgsForCall(i int) (context.Context, rpc.ParticipantTopic, *livekit.ForwardParticipantRequest, []psrpc.RequestOption) {
	fake.forwardParticipantMutex.RLock()
	defer fake.forwardParticipantMutex.RUnlock()
	argsForCall := fake.forwardParticipantArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3, argsForCall.arg4
}

func (fake *FakeTypedParticipantClient) ForwardParticipantReturns(result1 *livekit.ForwardParticipantResponse, result2 error) {
	fake.forwardParticipantMutex.Lock()
	defer fake.forwardParticipantMutex.Unlock()
	fake.ForwardParticipantStub = nil
	fake.forwardParticipantReturns = struct {
		result1 *livekit.ForwardParticipantResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeTypedParticipantClient) ForwardParticipantReturnsOnCall(i int, result1 *livekit.ForwardParticipantResponse, result2 error) {
	fake.forwardParticipantMutex.Lock()
	defer fake.forwardParticipantMutex.Unlock()
	fake.ForwardParticipantStub = nil
	if fake.forwardParticipantReturnsOnCall == nil {
		fake.forwardParticipantReturnsOnCall = make(map[int]struct {
			result1 *livekit.ForwardParticipantResponse
			result2 error
		})
	}
	fake.forwardParticipantReturnsOnCall[i] = struct {
		result1 *livekit.ForwardParticipantResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeTypedParticipantClient) MoveParticipant(arg1 context.Context, arg2 rpc.ParticipantTopic, arg3 *livekit.MoveParticipantRequest, arg4 ...psrpc.RequestOption) (*livekit.MoveParticipantResponse, error) {
	fake.moveParticipantMutex.Lock()
	ret, specificReturn := fake.moveParticipantReturnsOnCall[len(fake.moveParticipantArgsForCall)]
	fake.moveParticipantArgsForCall = append(fake.moveParticipantArgsForCall, struct {
		arg1 context.Context
		arg2 rpc.ParticipantTopic
		arg3 *livekit.MoveParticipantRequest
		arg4 []psrpc.RequestOption
	}{arg1, arg2, arg3, arg4})
	stub := fake.MoveParticipantStub
	fakeReturns := fake.moveParticipantReturns
	fake.recordInvocation("MoveParticipant", []interface{}{arg1, arg2, arg3, arg4})
	fake.moveParticipantMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3, arg4...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeTypedParticipantClient) MoveParticipantCallCount() int {
	fake.moveParticipantMutex.RLock()
	defer fake.moveParticipantMutex.RUnlock()
	return len(fake.moveParticipantArgsForCall)
}

func (fake *FakeTypedParticipantClient) MoveParticipantCalls(stub func(context.Context, rpc.ParticipantTopic, *livekit.MoveParticipantRequest, ...psrpc.RequestOption) (*livekit.MoveParticipantResponse, error)) {
	fake.moveParticipantMutex.Lock()
	defer fake.moveParticipantMutex.Unlock()
	fake.MoveParticipantStub = stub
}

func (fake *FakeTypedParticipantClient) MoveParticipantArgsForCall(i int) (context.Context, rpc.ParticipantTopic, *livekit.MoveParticipantRequest, []psrpc.RequestOption) {
	fake.moveParticipantMutex.RLock()
	defer fake.moveParticipantMutex.RUnlock()
	argsForCall := fake.moveParticipantArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3, argsForCall.arg4
}

func (fake *FakeTypedParticipantClient) MoveParticipantReturns(result1 *livekit.MoveParticipantResponse, result2 error) {
	fake.moveParticipantMutex.Lock()
	defer fake.moveParticipantMutex.Unlock()
	fake.MoveParticipantStub = nil
	fake.moveParticipantReturns = struct {
		result1 *livekit.MoveParticipantResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeTypedParticipantClient) MoveParticipantReturnsOnCall(i int, result1 *livekit.MoveParticipantResponse, result2 error) {
	fake.moveParticipantMutex.Lock()
	defer fake.moveParticipantMutex.Unlock()
	fake.MoveParticipantStub = nil
	if fake.moveParticipantReturnsOnCall == nil {
		fake.moveParticipantReturnsOnCall = make(map[int]struct {
			result1 *livekit.MoveParticipantResponse
			result2 error
		})
	}
	fake.moveParticipantReturnsOnCall[i] = struct {
		result1 *livekit.MoveParticipantResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeTypedParticipantClient) MutePublishedTrack(arg1 context.Context, arg2 rpc.ParticipantTopic, arg3 *livekit.MuteRoomTrackRequest, arg4 ...psrpc.RequestOption) (*livekit.MuteRoomTrackResponse, error) {
	fake.mutePublishedTrackMutex.Lock()
	ret, specificReturn := fake.mutePublishedTrackReturnsOnCall[len(fake.mutePublishedTrackArgsForCall)]
	fake.mutePublishedTrackArgsForCall = append(fake.mutePublishedTrackArgsForCall, struct {
		arg1 context.Context
		arg2 rpc.ParticipantTopic
		arg3 *livekit.MuteRoomTrackRequest
		arg4 []psrpc.RequestOption
	}{arg1, arg2, arg3, arg4})
	stub := fake.MutePublishedTrackStub
	fakeReturns := fake.mutePublishedTrackReturns
	fake.recordInvocation("MutePublishedTrack", []interface{}{arg1, arg2, arg3, arg4})
	fake.mutePublishedTrackMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3, arg4...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeTypedParticipantClient) MutePublishedTrackCallCount() int {
	fake.mutePublishedTrackMutex.RLock()
	defer fake.mutePublishedTrackMutex.RUnlock()
	return len(fake.mutePublishedTrackArgsForCall)
}

func (fake *FakeTypedParticipantClient) MutePublishedTrackCalls(stub func(context.Context, rpc.ParticipantTopic, *livekit.MuteRoomTrackRequest, ...psrpc.RequestOption) (*livekit.MuteRoomTrackResponse, error)) {
	fake.mutePublishedTrackMutex.Lock()
	defer fake.mutePublishedTrackMutex.Unlock()
	fake.MutePublishedTrackStub = stub
}

func (fake *FakeTypedParticipantClient) MutePublishedTrackArgsForCall(i int) (context.Context, rpc.ParticipantTopic, *livekit.MuteRoomTrackRequest, []psrpc.RequestOption) {
	fake.mutePublishedTrackMutex.RLock()
	defer fake.mutePublishedTrackMutex.RUnlock()
	argsForCall := fake.mutePublishedTrackArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3, argsForCall.arg4
}

func (fake *FakeTypedParticipantClient) MutePublishedTrackReturns(result1 *livekit.MuteRoomTrackResponse, result2 error) {
	fake.mutePublishedTrackMutex.Lock()
	defer fake.mutePublishedTrackMutex.Unlock()
	fake.MutePublishedTrackStub = nil
	fake.mutePublishedTrackReturns = struct {
		result1 *livekit.MuteRoomTrackResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeTypedParticipantClient) MutePublishedTrackReturnsOnCall(i int, result1 *livekit.MuteRoomTrackResponse, result2 error) {
	fake.mutePublishedTrackMutex.Lock()
	defer fake.mutePublishedTrackMutex.Unlock()
	fake.MutePublishedTrackStub = nil
	if fake.mutePublishedTrackReturnsOnCall == nil {
		fake.mutePublishedTrackReturnsOnCall = make(map[int]struct {
			result1 *livekit.MuteRoomTrackResponse
			result2 error
		})
	}
	fake.mutePublishedTrackReturnsOnCall[i] = struct {
		result1 *livekit.MuteRoomTrackResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeTypedParticipantClient) RemoveParticipant(arg1 context.Context, arg2 rpc.ParticipantTopic, arg3 *livekit.RoomParticipantIdentity, arg4 ...psrpc.RequestOption) (*livekit.RemoveParticipantResponse, error) {
	fake.removeParticipantMutex.Lock()
	ret, specificReturn := fake.removeParticipantReturnsOnCall[len(fake.removeParticipantArgsForCall)]
	fake.removeParticipantArgsForCall = append(fake.removeParticipantArgsForCall, struct {
		arg1 context.Context
		arg2 rpc.ParticipantTopic
		arg3 *livekit.RoomParticipantIdentity
		arg4 []psrpc.RequestOption
	}{arg1, arg2, arg3, arg4})
	stub := fake.RemoveParticipantStub
	fakeReturns := fake.removeParticipantReturns
	fake.recordInvocation("RemoveParticipant", []interface{}{arg1, arg2, arg3, arg4})
	fake.removeParticipantMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3, arg4...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeTypedParticipantClient) RemoveParticipantCallCount() int {
	fake.removeParticipantMutex.RLock()
	defer fake.removeParticipantMutex.RUnlock()
	return len(fake.removeParticipantArgsForCall)
}

func (fake *FakeTypedParticipantClient) RemoveParticipantCalls(stub func(context.Context, rpc.ParticipantTopic, *livekit.RoomParticipantIdentity, ...psrpc.RequestOption) (*livekit.RemoveParticipantResponse, error)) {
	fake.removeParticipantMutex.Lock()
	defer fake.removeParticipantMutex.Unlock()
	fake.RemoveParticipantStub = stub
}

func (fake *FakeTypedParticipantClient) RemoveParticipantArgsForCall(i int) (context.Context, rpc.ParticipantTopic, *livekit.RoomParticipantIdentity, []psrpc.RequestOption) {
	fake.removeParticipantMutex.RLock()
	defer fake.removeParticipantMutex.RUnlock()
	argsForCall := fake.removeParticipantArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3, argsForCall.arg4
}

func (fake *FakeTypedParticipantClient) RemoveParticipantReturns(result1 *livekit.RemoveParticipantResponse, result2 error) {
	fake.removeParticipantMutex.Lock()
	defer fake.removeParticipantMutex.Unlock()
	fake.RemoveParticipantStub = nil
	fake.removeParticipantReturns = struct {
		result1 *livekit.RemoveParticipantResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeTypedParticipantClient) RemoveParticipantReturnsOnCall(i int, result1 *livekit.RemoveParticipantResponse, result2 error) {
	fake.removeParticipantMutex.Lock()
	defer fake.removeParticipantMutex.Unlock()
	fake.RemoveParticipantStub = nil
	if fake.removeParticipantReturnsOnCall == nil {
		fake.removeParticipantReturnsOnCall = make(map[int]struct {
			result1 *livekit.RemoveParticipantResponse
			result2 error
		})
	}
	fake.removeParticipantReturnsOnCall[i] = struct {
		result1 *livekit.RemoveParticipantResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeTypedParticipantClient) UpdateParticipant(arg1 context.Context, arg2 rpc.ParticipantTopic, arg3 *livekit.UpdateParticipantRequest, arg4 ...psrpc.RequestOption) (*livekit.ParticipantInfo, error) {
	fake.updateParticipantMutex.Lock()
	ret, specificReturn := fake.updateParticipantReturnsOnCall[len(fake.updateParticipantArgsForCall)]
	fake.updateParticipantArgsForCall = append(fake.updateParticipantArgsForCall, struct {
		arg1 context.Context
		arg2 rpc.ParticipantTopic
		arg3 *livekit.UpdateParticipantRequest
		arg4 []psrpc.RequestOption
	}{arg1, arg2, arg3, arg4})
	stub := fake.UpdateParticipantStub
	fakeReturns := fake.updateParticipantReturns
	fake.recordInvocation("UpdateParticipant", []interface{}{arg1, arg2, arg3, arg4})
	fake.updateParticipantMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3, arg4...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeTypedParticipantClient) UpdateParticipantCallCount() int {
	fake.updateParticipantMutex.RLock()
	defer fake.updateParticipantMutex.RUnlock()
	return len(fake.updateParticipantArgsForCall)
}

func (fake *FakeTypedParticipantClient) UpdateParticipantCalls(stub func(context.Context, rpc.ParticipantTopic, *livekit.UpdateParticipantRequest, ...psrpc.RequestOption) (*livekit.ParticipantInfo, error)) {
	fake.updateParticipantMutex.Lock()
	defer fake.updateParticipantMutex.Unlock()
	fake.UpdateParticipantStub = stub
}

func (fake *FakeTypedParticipantClient) UpdateParticipantArgsForCall(i int) (context.Context, rpc.ParticipantTopic, *livekit.UpdateParticipantRequest, []psrpc.RequestOption) {
	fake.updateParticipantMutex.RLock()
	defer fake.updateParticipantMutex.RUnlock()
	argsForCall := fake.updateParticipantArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3, argsForCall.arg4
}

func (fake *FakeTypedParticipantClient) UpdateParticipantReturns(result1 *livekit.ParticipantInfo, result2 error) {
	fake.updateParticipantMutex.Lock()
	defer fake.updateParticipantMutex.Unlock()
	fake.UpdateParticipantStub = nil
	fake.updateParticipantReturns = struct {
		result1 *livekit.ParticipantInfo
		result2 error
	}{result1, result2}
}

func (fake *FakeTypedParticipantClient) UpdateParticipantReturnsOnCall(i int, result1 *livekit.ParticipantInfo, result2 error) {
	fake.updateParticipantMutex.Lock()
	defer fake.updateParticipantMutex.Unlock()
	fake.UpdateParticipantStub = nil
	if fake.updateParticipantReturnsOnCall == nil {
		fake.updateParticipantReturnsOnCall = make(map[int]struct {
			result1 *livekit.ParticipantInfo
			result2 error
		})
	}
	fake.updateParticipantReturnsOnCall[i] = struct {
		result1 *livekit.ParticipantInfo
		result2 error
	}{result1, result2}
}

func (fake *FakeTypedParticipantClient) UpdateSubscriptions(arg1 context.Context, arg2 rpc.ParticipantTopic, arg3 *livekit.UpdateSubscriptionsRequest, arg4 ...psrpc.RequestOption) (*livekit.UpdateSubscriptionsResponse, error) {
	fake.updateSubscriptionsMutex.Lock()
	ret, specificReturn := fake.updateSubscriptionsReturnsOnCall[len(fake.updateSubscriptionsArgsForCall)]
	fake.updateSubscriptionsArgsForCall = append(fake.updateSubscriptionsArgsForCall, struct {
		arg1 context.Context
		arg2 rpc.ParticipantTopic
		arg3 *livekit.UpdateSubscriptionsRequest
		arg4 []psrpc.RequestOption
	}{arg1, arg2, arg3, arg4})
	stub := fake.UpdateSubscriptionsStub
	fakeReturns := fake.updateSubscriptionsReturns
	fake.recordInvocation("UpdateSubscriptions", []interface{}{arg1, arg2, arg3, arg4})
	fake.updateSubscriptionsMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3, arg4...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeTypedParticipantClient) UpdateSubscriptionsCallCount() int {
	fake.updateSubscriptionsMutex.RLock()
	defer fake.updateSubscriptionsMutex.RUnlock()
	return len(fake.updateSubscriptionsArgsForCall)
}

func (fake *FakeTypedParticipantClient) UpdateSubscriptionsCalls(stub func(context.Context, rpc.ParticipantTopic, *livekit.UpdateSubscriptionsRequest, ...psrpc.RequestOption) (*livekit.UpdateSubscriptionsResponse, error)) {
	fake.updateSubscriptionsMutex.Lock()
	defer fake.updateSubscriptionsMutex.Unlock()
	fake.UpdateSubscriptionsStub = stub
}

func (fake *FakeTypedParticipantClient) UpdateSubscriptionsArgsForCall(i int) (context.Context, rpc.ParticipantTopic, *livekit.UpdateSubscriptionsRequest, []psrpc.RequestOption) {
	fake.updateSubscriptionsMutex.RLock()
	defer fake.updateSubscriptionsMutex.RUnlock()
	argsForCall := fake.updateSubscriptionsArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3, argsForCall.arg4
}

func (fake *FakeTypedParticipantClient) UpdateSubscriptionsReturns(result1 *livekit.UpdateSubscriptionsResponse, result2 error) {
	fake.updateSubscriptionsMutex.Lock()
	defer fake.updateSubscriptionsMutex.Unlock()
	fake.UpdateSubscriptionsStub = nil
	fake.updateSubscriptionsReturns = struct {
		result1 *livekit.UpdateSubscriptionsResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeTypedParticipantClient) UpdateSubscriptionsReturnsOnCall(i int, result1 *livekit.UpdateSubscriptionsResponse, result2 error) {
	fake.updateSubscriptionsMutex.Lock()
	defer fake.updateSubscriptionsMutex.Unlock()
	fake.UpdateSubscriptionsStub = nil
	if fake.updateSubscriptionsReturnsOnCall == nil {
		fake.updateSubscriptionsReturnsOnCall = make(map[int]struct {
			result1 *livekit.UpdateSubscriptionsResponse
			result2 error
		})
	}
	fake.updateSubscriptionsReturnsOnCall[i] = struct {
		result1 *livekit.UpdateSubscriptionsResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeTypedParticipantClient) Invocations() map[string][][]interface{} {
	fake.invocationsMutex.RLock()
	defer fake.invocationsMutex.RUnlock()
	fake.closeMutex.RLock()
	defer fake.closeMutex.RUnlock()
	fake.forwardParticipantMutex.RLock()
	defer fake.forwardParticipantMutex.RUnlock()
	fake.moveParticipantMutex.RLock()
	defer fake.moveParticipantMutex.RUnlock()
	fake.mutePublishedTrackMutex.RLock()
	defer fake.mutePublishedTrackMutex.RUnlock()
	fake.removeParticipantMutex.RLock()
	defer fake.removeParticipantMutex.RUnlock()
	fake.updateParticipantMutex.RLock()
	defer fake.updateParticipantMutex.RUnlock()
	fake.updateSubscriptionsMutex.RLock()
	defer fake.updateSubscriptionsMutex.RUnlock()
	copiedInvocations := map[string][][]interface{}{}
	for key, value := range fake.invocations {
		copiedInvocations[key] = value
	}
	return copiedInvocations
}

func (fake *FakeTypedParticipantClient) recordInvocation(key string, args []interface{}) {
	fake.invocationsMutex.Lock()
	defer fake.invocationsMutex.Unlock()
	if fake.invocations == nil {
		fake.invocations = map[string][][]interface{}{}
	}
	if fake.invocations[key] == nil {
		fake.invocations[key] = [][]interface{}{}
	}
	fake.invocations[key] = append(fake.invocations[key], args)
}

var _ rpc.TypedParticipantClient = new(FakeTypedParticipantClient)
