// Code generated by counterfeiter. DO NOT EDIT.
package rpcfakes

import (
	"context"
	"sync"

	"github.com/livekit/protocol/livekit"
	"github.com/livekit/protocol/rpc"
	"github.com/livekit/psrpc"
)

type FakeKeepalivePubSub struct {
	CloseStub        func()
	closeMutex       sync.RWMutex
	closeArgsForCall []struct {
	}
	KillStub        func()
	killMutex       sync.RWMutex
	killArgsForCall []struct {
	}
	PublishPingStub        func(context.Context, livekit.NodeID, *rpc.KeepalivePing) error
	publishPingMutex       sync.RWMutex
	publishPingArgsForCall []struct {
		arg1 context.Context
		arg2 livekit.NodeID
		arg3 *rpc.KeepalivePing
	}
	publishPingReturns struct {
		result1 error
	}
	publishPingReturnsOnCall map[int]struct {
		result1 error
	}
	ShutdownStub        func()
	shutdownMutex       sync.RWMutex
	shutdownArgsForCall []struct {
	}
	SubscribePingStub        func(context.Context, livekit.NodeID) (psrpc.Subscription[*rpc.KeepalivePing], error)
	subscribePingMutex       sync.RWMutex
	subscribePingArgsForCall []struct {
		arg1 context.Context
		arg2 livekit.NodeID
	}
	subscribePingReturns struct {
		result1 psrpc.Subscription[*rpc.KeepalivePing]
		result2 error
	}
	subscribePingReturnsOnCall map[int]struct {
		result1 psrpc.Subscription[*rpc.KeepalivePing]
		result2 error
	}
	invocations      map[string][][]interface{}
	invocationsMutex sync.RWMutex
}

func (fake *FakeKeepalivePubSub) Close() {
	fake.closeMutex.Lock()
	fake.closeArgsForCall = append(fake.closeArgsForCall, struct {
	}{})
	stub := fake.CloseStub
	fake.recordInvocation("Close", []interface{}{})
	fake.closeMutex.Unlock()
	if stub != nil {
		fake.CloseStub()
	}
}

func (fake *FakeKeepalivePubSub) CloseCallCount() int {
	fake.closeMutex.RLock()
	defer fake.closeMutex.RUnlock()
	return len(fake.closeArgsForCall)
}

func (fake *FakeKeepalivePubSub) CloseCalls(stub func()) {
	fake.closeMutex.Lock()
	defer fake.closeMutex.Unlock()
	fake.CloseStub = stub
}

func (fake *FakeKeepalivePubSub) Kill() {
	fake.killMutex.Lock()
	fake.killArgsForCall = append(fake.killArgsForCall, struct {
	}{})
	stub := fake.KillStub
	fake.recordInvocation("Kill", []interface{}{})
	fake.killMutex.Unlock()
	if stub != nil {
		fake.KillStub()
	}
}

func (fake *FakeKeepalivePubSub) KillCallCount() int {
	fake.killMutex.RLock()
	defer fake.killMutex.RUnlock()
	return len(fake.killArgsForCall)
}

func (fake *FakeKeepalivePubSub) KillCalls(stub func()) {
	fake.killMutex.Lock()
	defer fake.killMutex.Unlock()
	fake.KillStub = stub
}

func (fake *FakeKeepalivePubSub) PublishPing(arg1 context.Context, arg2 livekit.NodeID, arg3 *rpc.KeepalivePing) error {
	fake.publishPingMutex.Lock()
	ret, specificReturn := fake.publishPingReturnsOnCall[len(fake.publishPingArgsForCall)]
	fake.publishPingArgsForCall = append(fake.publishPingArgsForCall, struct {
		arg1 context.Context
		arg2 livekit.NodeID
		arg3 *rpc.KeepalivePing
	}{arg1, arg2, arg3})
	stub := fake.PublishPingStub
	fakeReturns := fake.publishPingReturns
	fake.recordInvocation("PublishPing", []interface{}{arg1, arg2, arg3})
	fake.publishPingMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeKeepalivePubSub) PublishPingCallCount() int {
	fake.publishPingMutex.RLock()
	defer fake.publishPingMutex.RUnlock()
	return len(fake.publishPingArgsForCall)
}

func (fake *FakeKeepalivePubSub) PublishPingCalls(stub func(context.Context, livekit.NodeID, *rpc.KeepalivePing) error) {
	fake.publishPingMutex.Lock()
	defer fake.publishPingMutex.Unlock()
	fake.PublishPingStub = stub
}

func (fake *FakeKeepalivePubSub) PublishPingArgsForCall(i int) (context.Context, livekit.NodeID, *rpc.KeepalivePing) {
	fake.publishPingMutex.RLock()
	defer fake.publishPingMutex.RUnlock()
	argsForCall := fake.publishPingArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeKeepalivePubSub) PublishPingReturns(result1 error) {
	fake.publishPingMutex.Lock()
	defer fake.publishPingMutex.Unlock()
	fake.PublishPingStub = nil
	fake.publishPingReturns = struct {
		result1 error
	}{result1}
}

func (fake *FakeKeepalivePubSub) PublishPingReturnsOnCall(i int, result1 error) {
	fake.publishPingMutex.Lock()
	defer fake.publishPingMutex.Unlock()
	fake.PublishPingStub = nil
	if fake.publishPingReturnsOnCall == nil {
		fake.publishPingReturnsOnCall = make(map[int]struct {
			result1 error
		})
	}
	fake.publishPingReturnsOnCall[i] = struct {
		result1 error
	}{result1}
}

func (fake *FakeKeepalivePubSub) Shutdown() {
	fake.shutdownMutex.Lock()
	fake.shutdownArgsForCall = append(fake.shutdownArgsForCall, struct {
	}{})
	stub := fake.ShutdownStub
	fake.recordInvocation("Shutdown", []interface{}{})
	fake.shutdownMutex.Unlock()
	if stub != nil {
		fake.ShutdownStub()
	}
}

func (fake *FakeKeepalivePubSub) ShutdownCallCount() int {
	fake.shutdownMutex.RLock()
	defer fake.shutdownMutex.RUnlock()
	return len(fake.shutdownArgsForCall)
}

func (fake *FakeKeepalivePubSub) ShutdownCalls(stub func()) {
	fake.shutdownMutex.Lock()
	defer fake.shutdownMutex.Unlock()
	fake.ShutdownStub = stub
}

func (fake *FakeKeepalivePubSub) SubscribePing(arg1 context.Context, arg2 livekit.NodeID) (psrpc.Subscription[*rpc.KeepalivePing], error) {
	fake.subscribePingMutex.Lock()
	ret, specificReturn := fake.subscribePingReturnsOnCall[len(fake.subscribePingArgsForCall)]
	fake.subscribePingArgsForCall = append(fake.subscribePingArgsForCall, struct {
		arg1 context.Context
		arg2 livekit.NodeID
	}{arg1, arg2})
	stub := fake.SubscribePingStub
	fakeReturns := fake.subscribePingReturns
	fake.recordInvocation("SubscribePing", []interface{}{arg1, arg2})
	fake.subscribePingMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeKeepalivePubSub) SubscribePingCallCount() int {
	fake.subscribePingMutex.RLock()
	defer fake.subscribePingMutex.RUnlock()
	return len(fake.subscribePingArgsForCall)
}

func (fake *FakeKeepalivePubSub) SubscribePingCalls(stub func(context.Context, livekit.NodeID) (psrpc.Subscription[*rpc.KeepalivePing], error)) {
	fake.subscribePingMutex.Lock()
	defer fake.subscribePingMutex.Unlock()
	fake.SubscribePingStub = stub
}

func (fake *FakeKeepalivePubSub) SubscribePingArgsForCall(i int) (context.Context, livekit.NodeID) {
	fake.subscribePingMutex.RLock()
	defer fake.subscribePingMutex.RUnlock()
	argsForCall := fake.subscribePingArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeKeepalivePubSub) SubscribePingReturns(result1 psrpc.Subscription[*rpc.KeepalivePing], result2 error) {
	fake.subscribePingMutex.Lock()
	defer fake.subscribePingMutex.Unlock()
	fake.SubscribePingStub = nil
	fake.subscribePingReturns = struct {
		result1 psrpc.Subscription[*rpc.KeepalivePing]
		result2 error
	}{result1, result2}
}

func (fake *FakeKeepalivePubSub) SubscribePingReturnsOnCall(i int, result1 psrpc.Subscription[*rpc.KeepalivePing], result2 error) {
	fake.subscribePingMutex.Lock()
	defer fake.subscribePingMutex.Unlock()
	fake.SubscribePingStub = nil
	if fake.subscribePingReturnsOnCall == nil {
		fake.subscribePingReturnsOnCall = make(map[int]struct {
			result1 psrpc.Subscription[*rpc.KeepalivePing]
			result2 error
		})
	}
	fake.subscribePingReturnsOnCall[i] = struct {
		result1 psrpc.Subscription[*rpc.KeepalivePing]
		result2 error
	}{result1, result2}
}

func (fake *FakeKeepalivePubSub) Invocations() map[string][][]interface{} {
	fake.invocationsMutex.RLock()
	defer fake.invocationsMutex.RUnlock()
	fake.closeMutex.RLock()
	defer fake.closeMutex.RUnlock()
	fake.killMutex.RLock()
	defer fake.killMutex.RUnlock()
	fake.publishPingMutex.RLock()
	defer fake.publishPingMutex.RUnlock()
	fake.shutdownMutex.RLock()
	defer fake.shutdownMutex.RUnlock()
	fake.subscribePingMutex.RLock()
	defer fake.subscribePingMutex.RUnlock()
	copiedInvocations := map[string][][]interface{}{}
	for key, value := range fake.invocations {
		copiedInvocations[key] = value
	}
	return copiedInvocations
}

func (fake *FakeKeepalivePubSub) recordInvocation(key string, args []interface{}) {
	fake.invocationsMutex.Lock()
	defer fake.invocationsMutex.Unlock()
	if fake.invocations == nil {
		fake.invocations = map[string][][]interface{}{}
	}
	if fake.invocations[key] == nil {
		fake.invocations[key] = [][]interface{}{}
	}
	fake.invocations[key] = append(fake.invocations[key], args)
}

var _ rpc.KeepalivePubSub = new(FakeKeepalivePubSub)
