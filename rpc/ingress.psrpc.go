// Code generated by protoc-gen-psrpc v0.6.0, DO NOT EDIT.
// source: rpc/ingress.proto

package rpc

import (
	"context"

	"github.com/livekit/psrpc"
	"github.com/livekit/psrpc/pkg/client"
	"github.com/livekit/psrpc/pkg/info"
	"github.com/livekit/psrpc/pkg/rand"
	"github.com/livekit/psrpc/pkg/server"
	"github.com/livekit/psrpc/version"
)
import google_protobuf "google.golang.org/protobuf/types/known/emptypb"
import livekit5 "github.com/livekit/protocol/livekit"

var _ = version.PsrpcVersion_0_6

// ================================
// IngressInternal Client Interface
// ================================

type IngressInternalClient interface {
	StartIngress(ctx context.Context, req *StartIngressRequest, opts ...psrpc.RequestOption) (*livekit5.IngressInfo, error)

	ListActiveIngress(ctx context.Context, topic string, req *ListActiveIngressRequest, opts ...psrpc.RequestOption) (<-chan *psrpc.Response[*ListActiveIngressResponse], error)

	KillIngressSession(ctx context.Context, ingressId string, resourceId string, req *KillIngressSessionRequest, opts ...psrpc.RequestOption) (*google_protobuf.Empty, error)

	// Close immediately, without waiting for pending RPCs
	Close()
}

// ====================================
// IngressInternal ServerImpl Interface
// ====================================

type IngressInternalServerImpl interface {
	StartIngress(context.Context, *StartIngressRequest) (*livekit5.IngressInfo, error)
	StartIngressAffinity(context.Context, *StartIngressRequest) float32

	ListActiveIngress(context.Context, *ListActiveIngressRequest) (*ListActiveIngressResponse, error)

	KillIngressSession(context.Context, *KillIngressSessionRequest) (*google_protobuf.Empty, error)
}

// ================================
// IngressInternal Server Interface
// ================================

type IngressInternalServer interface {
	RegisterListActiveIngressTopic(topic string) error
	DeregisterListActiveIngressTopic(topic string)
	RegisterKillIngressSessionTopic(ingressId string, resourceId string) error
	DeregisterKillIngressSessionTopic(ingressId string, resourceId string)

	// Close and wait for pending RPCs to complete
	Shutdown()

	// Close immediately, without waiting for pending RPCs
	Kill()
}

// ======================
// IngressInternal Client
// ======================

type ingressInternalClient struct {
	client *client.RPCClient
}

// NewIngressInternalClient creates a psrpc client that implements the IngressInternalClient interface.
func NewIngressInternalClient(bus psrpc.MessageBus, opts ...psrpc.ClientOption) (IngressInternalClient, error) {
	sd := &info.ServiceDefinition{
		Name: "IngressInternal",
		ID:   rand.NewClientID(),
	}

	sd.RegisterMethod("StartIngress", true, false, true, false)
	sd.RegisterMethod("ListActiveIngress", false, true, false, false)
	sd.RegisterMethod("KillIngressSession", false, false, true, true)

	rpcClient, err := client.NewRPCClient(sd, bus, opts...)
	if err != nil {
		return nil, err
	}

	return &ingressInternalClient{
		client: rpcClient,
	}, nil
}

func (c *ingressInternalClient) StartIngress(ctx context.Context, req *StartIngressRequest, opts ...psrpc.RequestOption) (*livekit5.IngressInfo, error) {
	return client.RequestSingle[*livekit5.IngressInfo](ctx, c.client, "StartIngress", nil, req, opts...)
}

func (c *ingressInternalClient) ListActiveIngress(ctx context.Context, topic string, req *ListActiveIngressRequest, opts ...psrpc.RequestOption) (<-chan *psrpc.Response[*ListActiveIngressResponse], error) {
	return client.RequestMulti[*ListActiveIngressResponse](ctx, c.client, "ListActiveIngress", []string{topic}, req, opts...)
}

func (c *ingressInternalClient) KillIngressSession(ctx context.Context, ingressId string, resourceId string, req *KillIngressSessionRequest, opts ...psrpc.RequestOption) (*google_protobuf.Empty, error) {
	return client.RequestSingle[*google_protobuf.Empty](ctx, c.client, "KillIngressSession", []string{ingressId, resourceId}, req, opts...)
}

func (s *ingressInternalClient) Close() {
	s.client.Close()
}

// ======================
// IngressInternal Server
// ======================

type ingressInternalServer struct {
	svc IngressInternalServerImpl
	rpc *server.RPCServer
}

// NewIngressInternalServer builds a RPCServer that will route requests
// to the corresponding method in the provided svc implementation.
func NewIngressInternalServer(svc IngressInternalServerImpl, bus psrpc.MessageBus, opts ...psrpc.ServerOption) (IngressInternalServer, error) {
	sd := &info.ServiceDefinition{
		Name: "IngressInternal",
		ID:   rand.NewServerID(),
	}

	s := server.NewRPCServer(sd, bus, opts...)

	sd.RegisterMethod("StartIngress", true, false, true, false)
	var err error
	err = server.RegisterHandler(s, "StartIngress", nil, svc.StartIngress, svc.StartIngressAffinity)
	if err != nil {
		s.Close(false)
		return nil, err
	}

	sd.RegisterMethod("ListActiveIngress", false, true, false, false)
	sd.RegisterMethod("KillIngressSession", false, false, true, true)
	return &ingressInternalServer{
		svc: svc,
		rpc: s,
	}, nil
}

func (s *ingressInternalServer) RegisterListActiveIngressTopic(topic string) error {
	return server.RegisterHandler(s.rpc, "ListActiveIngress", []string{topic}, s.svc.ListActiveIngress, nil)
}

func (s *ingressInternalServer) DeregisterListActiveIngressTopic(topic string) {
	s.rpc.DeregisterHandler("ListActiveIngress", []string{topic})
}

func (s *ingressInternalServer) RegisterKillIngressSessionTopic(ingressId string, resourceId string) error {
	return server.RegisterHandler(s.rpc, "KillIngressSession", []string{ingressId, resourceId}, s.svc.KillIngressSession, nil)
}

func (s *ingressInternalServer) DeregisterKillIngressSessionTopic(ingressId string, resourceId string) {
	s.rpc.DeregisterHandler("KillIngressSession", []string{ingressId, resourceId})
}

func (s *ingressInternalServer) Shutdown() {
	s.rpc.Close(false)
}

func (s *ingressInternalServer) Kill() {
	s.rpc.Close(true)
}

// ===============================
// IngressHandler Client Interface
// ===============================

type IngressHandlerClient interface {
	UpdateIngress(ctx context.Context, topic string, req *livekit5.UpdateIngressRequest, opts ...psrpc.RequestOption) (*livekit5.IngressState, error)

	DeleteIngress(ctx context.Context, topic string, req *livekit5.DeleteIngressRequest, opts ...psrpc.RequestOption) (*livekit5.IngressState, error)

	DeleteWHIPResource(ctx context.Context, topic string, req *DeleteWHIPResourceRequest, opts ...psrpc.RequestOption) (*google_protobuf.Empty, error)

	ICERestartWHIPResource(ctx context.Context, topic string, req *ICERestartWHIPResourceRequest, opts ...psrpc.RequestOption) (*ICERestartWHIPResourceResponse, error)

	// Close immediately, without waiting for pending RPCs
	Close()
}

// ===================================
// IngressHandler ServerImpl Interface
// ===================================

type IngressHandlerServerImpl interface {
	UpdateIngress(context.Context, *livekit5.UpdateIngressRequest) (*livekit5.IngressState, error)

	DeleteIngress(context.Context, *livekit5.DeleteIngressRequest) (*livekit5.IngressState, error)

	DeleteWHIPResource(context.Context, *DeleteWHIPResourceRequest) (*google_protobuf.Empty, error)

	ICERestartWHIPResource(context.Context, *ICERestartWHIPResourceRequest) (*ICERestartWHIPResourceResponse, error)
}

// ===============================
// IngressHandler Server Interface
// ===============================

type IngressHandlerServer interface {
	RegisterUpdateIngressTopic(topic string) error
	DeregisterUpdateIngressTopic(topic string)
	RegisterDeleteIngressTopic(topic string) error
	DeregisterDeleteIngressTopic(topic string)
	RegisterDeleteWHIPResourceTopic(topic string) error
	DeregisterDeleteWHIPResourceTopic(topic string)
	RegisterICERestartWHIPResourceTopic(topic string) error
	DeregisterICERestartWHIPResourceTopic(topic string)

	// Close and wait for pending RPCs to complete
	Shutdown()

	// Close immediately, without waiting for pending RPCs
	Kill()
}

// =====================
// IngressHandler Client
// =====================

type ingressHandlerClient struct {
	client *client.RPCClient
}

// NewIngressHandlerClient creates a psrpc client that implements the IngressHandlerClient interface.
func NewIngressHandlerClient(bus psrpc.MessageBus, opts ...psrpc.ClientOption) (IngressHandlerClient, error) {
	sd := &info.ServiceDefinition{
		Name: "IngressHandler",
		ID:   rand.NewClientID(),
	}

	sd.RegisterMethod("UpdateIngress", false, false, true, true)
	sd.RegisterMethod("DeleteIngress", false, false, true, true)
	sd.RegisterMethod("DeleteWHIPResource", false, false, true, true)
	sd.RegisterMethod("ICERestartWHIPResource", false, false, true, true)

	rpcClient, err := client.NewRPCClient(sd, bus, opts...)
	if err != nil {
		return nil, err
	}

	return &ingressHandlerClient{
		client: rpcClient,
	}, nil
}

func (c *ingressHandlerClient) UpdateIngress(ctx context.Context, topic string, req *livekit5.UpdateIngressRequest, opts ...psrpc.RequestOption) (*livekit5.IngressState, error) {
	return client.RequestSingle[*livekit5.IngressState](ctx, c.client, "UpdateIngress", []string{topic}, req, opts...)
}

func (c *ingressHandlerClient) DeleteIngress(ctx context.Context, topic string, req *livekit5.DeleteIngressRequest, opts ...psrpc.RequestOption) (*livekit5.IngressState, error) {
	return client.RequestSingle[*livekit5.IngressState](ctx, c.client, "DeleteIngress", []string{topic}, req, opts...)
}

func (c *ingressHandlerClient) DeleteWHIPResource(ctx context.Context, topic string, req *DeleteWHIPResourceRequest, opts ...psrpc.RequestOption) (*google_protobuf.Empty, error) {
	return client.RequestSingle[*google_protobuf.Empty](ctx, c.client, "DeleteWHIPResource", []string{topic}, req, opts...)
}

func (c *ingressHandlerClient) ICERestartWHIPResource(ctx context.Context, topic string, req *ICERestartWHIPResourceRequest, opts ...psrpc.RequestOption) (*ICERestartWHIPResourceResponse, error) {
	return client.RequestSingle[*ICERestartWHIPResourceResponse](ctx, c.client, "ICERestartWHIPResource", []string{topic}, req, opts...)
}

func (s *ingressHandlerClient) Close() {
	s.client.Close()
}

// =====================
// IngressHandler Server
// =====================

type ingressHandlerServer struct {
	svc IngressHandlerServerImpl
	rpc *server.RPCServer
}

// NewIngressHandlerServer builds a RPCServer that will route requests
// to the corresponding method in the provided svc implementation.
func NewIngressHandlerServer(svc IngressHandlerServerImpl, bus psrpc.MessageBus, opts ...psrpc.ServerOption) (IngressHandlerServer, error) {
	sd := &info.ServiceDefinition{
		Name: "IngressHandler",
		ID:   rand.NewServerID(),
	}

	s := server.NewRPCServer(sd, bus, opts...)

	sd.RegisterMethod("UpdateIngress", false, false, true, true)
	sd.RegisterMethod("DeleteIngress", false, false, true, true)
	sd.RegisterMethod("DeleteWHIPResource", false, false, true, true)
	sd.RegisterMethod("ICERestartWHIPResource", false, false, true, true)
	return &ingressHandlerServer{
		svc: svc,
		rpc: s,
	}, nil
}

func (s *ingressHandlerServer) RegisterUpdateIngressTopic(topic string) error {
	return server.RegisterHandler(s.rpc, "UpdateIngress", []string{topic}, s.svc.UpdateIngress, nil)
}

func (s *ingressHandlerServer) DeregisterUpdateIngressTopic(topic string) {
	s.rpc.DeregisterHandler("UpdateIngress", []string{topic})
}

func (s *ingressHandlerServer) RegisterDeleteIngressTopic(topic string) error {
	return server.RegisterHandler(s.rpc, "DeleteIngress", []string{topic}, s.svc.DeleteIngress, nil)
}

func (s *ingressHandlerServer) DeregisterDeleteIngressTopic(topic string) {
	s.rpc.DeregisterHandler("DeleteIngress", []string{topic})
}

func (s *ingressHandlerServer) RegisterDeleteWHIPResourceTopic(topic string) error {
	return server.RegisterHandler(s.rpc, "DeleteWHIPResource", []string{topic}, s.svc.DeleteWHIPResource, nil)
}

func (s *ingressHandlerServer) DeregisterDeleteWHIPResourceTopic(topic string) {
	s.rpc.DeregisterHandler("DeleteWHIPResource", []string{topic})
}

func (s *ingressHandlerServer) RegisterICERestartWHIPResourceTopic(topic string) error {
	return server.RegisterHandler(s.rpc, "ICERestartWHIPResource", []string{topic}, s.svc.ICERestartWHIPResource, nil)
}

func (s *ingressHandlerServer) DeregisterICERestartWHIPResourceTopic(topic string) {
	s.rpc.DeregisterHandler("ICERestartWHIPResource", []string{topic})
}

func (s *ingressHandlerServer) Shutdown() {
	s.rpc.Close(false)
}

func (s *ingressHandlerServer) Kill() {
	s.rpc.Close(true)
}

var psrpcFileDescriptor3 = []byte{
	// 748 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x55, 0xcd, 0x6e, 0xdb, 0x46,
	0x10, 0xc6, 0x52, 0xb2, 0x6a, 0x8d, 0x2c, 0x5b, 0x5e, 0xff, 0x80, 0x62, 0x21, 0xff, 0xd0, 0x17,
	0x01, 0x45, 0xa9, 0x42, 0xbd, 0x14, 0x3d, 0x14, 0xae, 0x5b, 0x1b, 0x56, 0xed, 0x02, 0x06, 0x15,
	0x23, 0x40, 0x02, 0x84, 0xa0, 0xc9, 0x15, 0xb3, 0x10, 0xc5, 0x65, 0x76, 0x57, 0x36, 0x74, 0x0b,
	0x72, 0xd3, 0xbb, 0xe4, 0xa4, 0x27, 0xc8, 0x1b, 0xe5, 0x15, 0x02, 0x92, 0x4b, 0x59, 0xbf, 0x46,
	0x2e, 0xb9, 0x69, 0xbe, 0x19, 0xce, 0xce, 0x7e, 0xf3, 0xed, 0x27, 0xd8, 0xe5, 0xb1, 0xd7, 0xa2,
	0x51, 0xc0, 0x89, 0x10, 0x56, 0xcc, 0x99, 0x64, 0xb8, 0xc0, 0x63, 0xcf, 0xa8, 0xb2, 0x58, 0x52,
	0x16, 0x29, 0xcc, 0x38, 0x08, 0xe9, 0x23, 0xe9, 0x53, 0xe9, 0xcc, 0x95, 0x1a, 0x3f, 0x07, 0x8c,
	0x05, 0x21, 0x69, 0xa5, 0xd1, 0xc3, 0xb0, 0xd7, 0x22, 0x83, 0x58, 0x8e, 0xb2, 0xa4, 0x69, 0x80,
	0x7e, 0x4b, 0x85, 0xfc, 0xdb, 0x93, 0xf4, 0x91, 0x74, 0xb2, 0xef, 0x6c, 0xf2, 0x61, 0x48, 0x84,
	0x34, 0x3f, 0x22, 0xa8, 0xaf, 0x48, 0x8a, 0x98, 0x45, 0x82, 0xe0, 0x33, 0xa8, 0xa8, 0x73, 0x1c,
	0xea, 0x0b, 0x1d, 0x9d, 0x14, 0x9a, 0xe5, 0x0b, 0x4d, 0x47, 0x36, 0x28, 0xb8, 0xe3, 0x0b, 0xfc,
	0x17, 0xd4, 0xf2, 0x22, 0x41, 0x84, 0x48, 0x86, 0xd5, 0xb5, 0x93, 0x42, 0xb3, 0xd2, 0xde, 0xb3,
	0x78, 0xec, 0x59, 0xaa, 0x69, 0x37, 0xcb, 0xd9, 0x3b, 0x74, 0x2e, 0x16, 0xe6, 0x5b, 0xa8, 0xff,
	0x4b, 0x42, 0x22, 0xc9, 0xeb, 0xeb, 0xce, 0x9d, 0x4d, 0x04, 0x1b, 0x72, 0x8f, 0xa8, 0xf9, 0xf0,
	0x31, 0x54, 0xb8, 0x82, 0x1c, 0xea, 0xeb, 0xe8, 0x04, 0x35, 0xcb, 0x36, 0xe4, 0x50, 0xc7, 0xc7,
	0x0d, 0x00, 0x21, 0x39, 0x71, 0x07, 0x4e, 0x9f, 0x8c, 0x74, 0x2d, 0xcd, 0x97, 0x33, 0xe4, 0x86,
	0x8c, 0xcc, 0x2f, 0x08, 0x1a, 0x9d, 0x7f, 0x2e, 0x6d, 0x22, 0xa4, 0xcb, 0xe5, 0x0f, 0x38, 0x01,
	0x9f, 0x41, 0x75, 0x28, 0x08, 0x77, 0x7a, 0xdc, 0x0d, 0x06, 0x24, 0x92, 0x7a, 0x21, 0xad, 0xd8,
	0x4a, 0xc0, 0x2b, 0x85, 0x61, 0x03, 0x36, 0x63, 0x57, 0x88, 0x27, 0xc6, 0x7d, 0xbd, 0x98, 0xe6,
	0xa7, 0x31, 0x3e, 0x02, 0xf0, 0xdc, 0xc8, 0xa7, 0xbe, 0x2b, 0x89, 0xd0, 0x37, 0x12, 0x8e, 0xed,
	0x19, 0xc4, 0xbc, 0x83, 0xa3, 0x75, 0x37, 0x50, 0x6b, 0xb2, 0x60, 0x4f, 0x72, 0xea, 0xf5, 0x43,
	0xe2, 0x50, 0x8f, 0x38, 0xc2, 0x8f, 0x93, 0x61, 0xd4, 0x55, 0x76, 0x55, 0xaa, 0xe3, 0x91, 0x6e,
	0x96, 0x30, 0x3f, 0x69, 0xb0, 0xd7, 0x4d, 0xba, 0xcd, 0x8b, 0x01, 0x37, 0xa1, 0x48, 0xa3, 0x1e,
	0x4b, 0x3f, 0xac, 0xb4, 0xf7, 0x2d, 0xa5, 0xb5, 0x7c, 0x83, 0x9d, 0xa8, 0xc7, 0xec, 0xb4, 0x02,
	0xef, 0xc3, 0x86, 0x64, 0x7d, 0x12, 0x29, 0x3a, 0xb2, 0x00, 0x1f, 0x40, 0xe9, 0x49, 0x38, 0x43,
	0x1e, 0x2a, 0x0e, 0x36, 0x9e, 0xc4, 0x3d, 0x0f, 0xb1, 0x0d, 0xdb, 0x21, 0x0b, 0x02, 0x1a, 0x05,
	0x4e, 0x8f, 0x92, 0xd0, 0x17, 0x7a, 0x31, 0x95, 0xc7, 0x2f, 0xa9, 0x3c, 0x56, 0x0c, 0x62, 0xdd,
	0x66, 0xe5, 0x57, 0x69, 0xf5, 0x65, 0x24, 0xf9, 0xc8, 0xae, 0x86, 0xb3, 0x98, 0x71, 0x0e, 0x78,
	0xb9, 0x08, 0xd7, 0xa0, 0x90, 0xec, 0x28, 0xbb, 0x78, 0xf2, 0x33, 0x19, 0xf4, 0xd1, 0x0d, 0x87,
	0x24, 0x1f, 0x34, 0x0d, 0xfe, 0xd4, 0xfe, 0x40, 0xe6, 0x1d, 0x6c, 0xcf, 0x2b, 0x33, 0x59, 0xf4,
	0xb3, 0xda, 0x55, 0x93, 0xf2, 0x54, 0xe8, 0x8b, 0x42, 0xd1, 0x16, 0x85, 0x62, 0xfe, 0x07, 0xf5,
	0x1b, 0x1a, 0x86, 0x0b, 0x7a, 0x57, 0xdc, 0xfe, 0x0a, 0x3f, 0xa9, 0xd7, 0xa1, 0xe8, 0x5d, 0xf9,
	0x38, 0xf2, 0x9a, 0xf6, 0x67, 0x0d, 0x76, 0xa6, 0xb4, 0x4b, 0xc2, 0x23, 0x37, 0xc4, 0xd7, 0xb0,
	0x35, 0x4b, 0x16, 0xd6, 0xd7, 0xf1, 0x67, 0xac, 0x5c, 0x9d, 0xb9, 0x39, 0x19, 0xa3, 0xe2, 0x39,
	0xfa, 0x0d, 0xe1, 0x77, 0xb0, 0xbb, 0xf4, 0xe8, 0x71, 0x23, 0x6d, 0xb7, 0xce, 0x29, 0x8c, 0xa3,
	0x75, 0xe9, 0x4c, 0x84, 0x26, 0x4c, 0xc6, 0xa8, 0x54, 0x43, 0xe7, 0x5a, 0x13, 0x61, 0x06, 0x78,
	0x99, 0x09, 0x9c, 0x75, 0x58, 0x4b, 0x91, 0x71, 0x68, 0x65, 0x2e, 0x66, 0xe5, 0x2e, 0x66, 0x5d,
	0x26, 0x2e, 0x66, 0x9e, 0x4e, 0xc6, 0xa8, 0x51, 0x43, 0x46, 0x1d, 0xcf, 0xec, 0x07, 0xcf, 0x2e,
	0xa3, 0xfd, 0x55, 0x9b, 0x6e, 0xf3, 0xda, 0x8d, 0xfc, 0x90, 0x70, 0xfc, 0x3f, 0x54, 0xef, 0xe3,
	0xe4, 0x05, 0x3d, 0xdf, 0x2f, 0x27, 0x65, 0x0e, 0xcf, 0x4f, 0x3f, 0x58, 0xe4, 0xac, 0x2b, 0x5d,
	0x49, 0xcc, 0xd2, 0x64, 0x8c, 0xb4, 0x1a, 0x4a, 0xda, 0x65, 0x2e, 0xb5, 0xdc, 0x6e, 0x0e, 0xff,
	0xce, 0x76, 0xaf, 0x00, 0x2f, 0x9b, 0x9e, 0x62, 0x68, 0xad, 0x1b, 0xae, 0x65, 0x28, 0xef, 0x4a,
	0xe1, 0x70, 0xb5, 0x55, 0x60, 0x33, 0x53, 0xdb, 0x4b, 0x4e, 0x68, 0x9c, 0xbd, 0x58, 0xa3, 0xd6,
	0xac, 0x8e, 0xba, 0x38, 0x7d, 0x73, 0x1c, 0x50, 0xf9, 0x7e, 0xf8, 0x60, 0x79, 0x6c, 0xd0, 0x52,
	0x77, 0xcd, 0xfe, 0x7f, 0x3c, 0x16, 0xb6, 0x78, 0xec, 0x3d, 0x94, 0xd2, 0xe8, 0xf7, 0x6f, 0x01,
	0x00, 0x00, 0xff, 0xff, 0x93, 0x4e, 0x89, 0xdb, 0xdb, 0x06, 0x00, 0x00,
}
