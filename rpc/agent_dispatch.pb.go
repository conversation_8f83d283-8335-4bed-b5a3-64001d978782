// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc/agent_dispatch.proto

package rpc

import (
	livekit "github.com/livekit/protocol/livekit"
	_ "github.com/livekit/psrpc/protoc-gen-psrpc/options"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_rpc_agent_dispatch_proto protoreflect.FileDescriptor

const file_rpc_agent_dispatch_proto_rawDesc = "" +
	"\n" +
	"\x18rpc/agent_dispatch.proto\x12\x03rpc\x1a\roptions.proto\x1a\x1clivekit_agent_dispatch.proto2\xc7\x02\n" +
	"\x15AgentDispatchInternal\x12X\n" +
	"\x0eCreateDispatch\x12\x16.livekit.AgentDispatch\x1a\x16.livekit.AgentDispatch\"\x16\xb2\x89\x01\x12\x10\x01\x1a\x0e\n" +
	"\x04room\x12\x04room\x18\x01\x12e\n" +
	"\x0eDeleteDispatch\x12#.livekit.DeleteAgentDispatchRequest\x1a\x16.livekit.AgentDispatch\"\x16\xb2\x89\x01\x12\x10\x01\x1a\x0e\n" +
	"\x04room\x12\x04room\x18\x01\x12m\n" +
	"\fListDispatch\x12!.livekit.ListAgentDispatchRequest\x1a\".livekit.ListAgentDispatchResponse\"\x16\xb2\x89\x01\x12\x10\x01\x1a\x0e\n" +
	"\x04room\x12\x04room\x18\x01B!Z\x1fgithub.com/livekit/protocol/rpcb\x06proto3"

var file_rpc_agent_dispatch_proto_goTypes = []any{
	(*livekit.AgentDispatch)(nil),              // 0: livekit.AgentDispatch
	(*livekit.DeleteAgentDispatchRequest)(nil), // 1: livekit.DeleteAgentDispatchRequest
	(*livekit.ListAgentDispatchRequest)(nil),   // 2: livekit.ListAgentDispatchRequest
	(*livekit.ListAgentDispatchResponse)(nil),  // 3: livekit.ListAgentDispatchResponse
}
var file_rpc_agent_dispatch_proto_depIdxs = []int32{
	0, // 0: rpc.AgentDispatchInternal.CreateDispatch:input_type -> livekit.AgentDispatch
	1, // 1: rpc.AgentDispatchInternal.DeleteDispatch:input_type -> livekit.DeleteAgentDispatchRequest
	2, // 2: rpc.AgentDispatchInternal.ListDispatch:input_type -> livekit.ListAgentDispatchRequest
	0, // 3: rpc.AgentDispatchInternal.CreateDispatch:output_type -> livekit.AgentDispatch
	0, // 4: rpc.AgentDispatchInternal.DeleteDispatch:output_type -> livekit.AgentDispatch
	3, // 5: rpc.AgentDispatchInternal.ListDispatch:output_type -> livekit.ListAgentDispatchResponse
	3, // [3:6] is the sub-list for method output_type
	0, // [0:3] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_rpc_agent_dispatch_proto_init() }
func file_rpc_agent_dispatch_proto_init() {
	if File_rpc_agent_dispatch_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_agent_dispatch_proto_rawDesc), len(file_rpc_agent_dispatch_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rpc_agent_dispatch_proto_goTypes,
		DependencyIndexes: file_rpc_agent_dispatch_proto_depIdxs,
	}.Build()
	File_rpc_agent_dispatch_proto = out.File
	file_rpc_agent_dispatch_proto_goTypes = nil
	file_rpc_agent_dispatch_proto_depIdxs = nil
}
