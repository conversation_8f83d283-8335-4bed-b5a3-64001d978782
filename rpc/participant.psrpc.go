// Code generated by protoc-gen-psrpc v0.6.0, DO NOT EDIT.
// source: rpc/participant.proto

package rpc

import (
	"context"

	"github.com/livekit/psrpc"
	"github.com/livekit/psrpc/pkg/client"
	"github.com/livekit/psrpc/pkg/info"
	"github.com/livekit/psrpc/pkg/rand"
	"github.com/livekit/psrpc/pkg/server"
	"github.com/livekit/psrpc/version"
)
import livekit1 "github.com/livekit/protocol/livekit"
import livekit6 "github.com/livekit/protocol/livekit"

var _ = version.PsrpcVersion_0_6

// ============================
// Participant Client Interface
// ============================

type ParticipantClient[ParticipantTopicType ~string] interface {
	RemoveParticipant(ctx context.Context, participant ParticipantTopicType, req *livekit6.RoomParticipantIdentity, opts ...psrpc.RequestOption) (*livekit6.RemoveParticipantResponse, error)

	MutePublishedTrack(ctx context.Context, participant ParticipantTopicType, req *livekit6.MuteRoomTrackRequest, opts ...psrpc.RequestOption) (*livekit6.MuteRoomTrackResponse, error)

	UpdateParticipant(ctx context.Context, participant ParticipantTopicType, req *livekit6.UpdateParticipantRequest, opts ...psrpc.RequestOption) (*livekit1.ParticipantInfo, error)

	UpdateSubscriptions(ctx context.Context, participant ParticipantTopicType, req *livekit6.UpdateSubscriptionsRequest, opts ...psrpc.RequestOption) (*livekit6.UpdateSubscriptionsResponse, error)

	ForwardParticipant(ctx context.Context, participant ParticipantTopicType, req *livekit6.ForwardParticipantRequest, opts ...psrpc.RequestOption) (*livekit6.ForwardParticipantResponse, error)

	MoveParticipant(ctx context.Context, participant ParticipantTopicType, req *livekit6.MoveParticipantRequest, opts ...psrpc.RequestOption) (*livekit6.MoveParticipantResponse, error)

	// Close immediately, without waiting for pending RPCs
	Close()
}

// ================================
// Participant ServerImpl Interface
// ================================

type ParticipantServerImpl interface {
	RemoveParticipant(context.Context, *livekit6.RoomParticipantIdentity) (*livekit6.RemoveParticipantResponse, error)

	MutePublishedTrack(context.Context, *livekit6.MuteRoomTrackRequest) (*livekit6.MuteRoomTrackResponse, error)

	UpdateParticipant(context.Context, *livekit6.UpdateParticipantRequest) (*livekit1.ParticipantInfo, error)

	UpdateSubscriptions(context.Context, *livekit6.UpdateSubscriptionsRequest) (*livekit6.UpdateSubscriptionsResponse, error)

	ForwardParticipant(context.Context, *livekit6.ForwardParticipantRequest) (*livekit6.ForwardParticipantResponse, error)

	MoveParticipant(context.Context, *livekit6.MoveParticipantRequest) (*livekit6.MoveParticipantResponse, error)
}

// ============================
// Participant Server Interface
// ============================

type ParticipantServer[ParticipantTopicType ~string] interface {
	RegisterRemoveParticipantTopic(participant ParticipantTopicType) error
	DeregisterRemoveParticipantTopic(participant ParticipantTopicType)
	RegisterMutePublishedTrackTopic(participant ParticipantTopicType) error
	DeregisterMutePublishedTrackTopic(participant ParticipantTopicType)
	RegisterUpdateParticipantTopic(participant ParticipantTopicType) error
	DeregisterUpdateParticipantTopic(participant ParticipantTopicType)
	RegisterUpdateSubscriptionsTopic(participant ParticipantTopicType) error
	DeregisterUpdateSubscriptionsTopic(participant ParticipantTopicType)
	RegisterForwardParticipantTopic(participant ParticipantTopicType) error
	DeregisterForwardParticipantTopic(participant ParticipantTopicType)
	RegisterMoveParticipantTopic(participant ParticipantTopicType) error
	DeregisterMoveParticipantTopic(participant ParticipantTopicType)
	RegisterAllParticipantTopics(participant ParticipantTopicType) error
	DeregisterAllParticipantTopics(participant ParticipantTopicType)

	// Close and wait for pending RPCs to complete
	Shutdown()

	// Close immediately, without waiting for pending RPCs
	Kill()
}

// ==================
// Participant Client
// ==================

type participantClient[ParticipantTopicType ~string] struct {
	client *client.RPCClient
}

// NewParticipantClient creates a psrpc client that implements the ParticipantClient interface.
func NewParticipantClient[ParticipantTopicType ~string](bus psrpc.MessageBus, opts ...psrpc.ClientOption) (ParticipantClient[ParticipantTopicType], error) {
	sd := &info.ServiceDefinition{
		Name: "Participant",
		ID:   rand.NewClientID(),
	}

	sd.RegisterMethod("RemoveParticipant", false, false, true, true)
	sd.RegisterMethod("MutePublishedTrack", false, false, true, true)
	sd.RegisterMethod("UpdateParticipant", false, false, true, true)
	sd.RegisterMethod("UpdateSubscriptions", false, false, true, true)
	sd.RegisterMethod("ForwardParticipant", false, false, true, true)
	sd.RegisterMethod("MoveParticipant", false, false, true, true)

	rpcClient, err := client.NewRPCClient(sd, bus, opts...)
	if err != nil {
		return nil, err
	}

	return &participantClient[ParticipantTopicType]{
		client: rpcClient,
	}, nil
}

func (c *participantClient[ParticipantTopicType]) RemoveParticipant(ctx context.Context, participant ParticipantTopicType, req *livekit6.RoomParticipantIdentity, opts ...psrpc.RequestOption) (*livekit6.RemoveParticipantResponse, error) {
	return client.RequestSingle[*livekit6.RemoveParticipantResponse](ctx, c.client, "RemoveParticipant", []string{string(participant)}, req, opts...)
}

func (c *participantClient[ParticipantTopicType]) MutePublishedTrack(ctx context.Context, participant ParticipantTopicType, req *livekit6.MuteRoomTrackRequest, opts ...psrpc.RequestOption) (*livekit6.MuteRoomTrackResponse, error) {
	return client.RequestSingle[*livekit6.MuteRoomTrackResponse](ctx, c.client, "MutePublishedTrack", []string{string(participant)}, req, opts...)
}

func (c *participantClient[ParticipantTopicType]) UpdateParticipant(ctx context.Context, participant ParticipantTopicType, req *livekit6.UpdateParticipantRequest, opts ...psrpc.RequestOption) (*livekit1.ParticipantInfo, error) {
	return client.RequestSingle[*livekit1.ParticipantInfo](ctx, c.client, "UpdateParticipant", []string{string(participant)}, req, opts...)
}

func (c *participantClient[ParticipantTopicType]) UpdateSubscriptions(ctx context.Context, participant ParticipantTopicType, req *livekit6.UpdateSubscriptionsRequest, opts ...psrpc.RequestOption) (*livekit6.UpdateSubscriptionsResponse, error) {
	return client.RequestSingle[*livekit6.UpdateSubscriptionsResponse](ctx, c.client, "UpdateSubscriptions", []string{string(participant)}, req, opts...)
}

func (c *participantClient[ParticipantTopicType]) ForwardParticipant(ctx context.Context, participant ParticipantTopicType, req *livekit6.ForwardParticipantRequest, opts ...psrpc.RequestOption) (*livekit6.ForwardParticipantResponse, error) {
	return client.RequestSingle[*livekit6.ForwardParticipantResponse](ctx, c.client, "ForwardParticipant", []string{string(participant)}, req, opts...)
}

func (c *participantClient[ParticipantTopicType]) MoveParticipant(ctx context.Context, participant ParticipantTopicType, req *livekit6.MoveParticipantRequest, opts ...psrpc.RequestOption) (*livekit6.MoveParticipantResponse, error) {
	return client.RequestSingle[*livekit6.MoveParticipantResponse](ctx, c.client, "MoveParticipant", []string{string(participant)}, req, opts...)
}

func (s *participantClient[ParticipantTopicType]) Close() {
	s.client.Close()
}

// ==================
// Participant Server
// ==================

type participantServer[ParticipantTopicType ~string] struct {
	svc ParticipantServerImpl
	rpc *server.RPCServer
}

// NewParticipantServer builds a RPCServer that will route requests
// to the corresponding method in the provided svc implementation.
func NewParticipantServer[ParticipantTopicType ~string](svc ParticipantServerImpl, bus psrpc.MessageBus, opts ...psrpc.ServerOption) (ParticipantServer[ParticipantTopicType], error) {
	sd := &info.ServiceDefinition{
		Name: "Participant",
		ID:   rand.NewServerID(),
	}

	s := server.NewRPCServer(sd, bus, opts...)

	sd.RegisterMethod("RemoveParticipant", false, false, true, true)
	sd.RegisterMethod("MutePublishedTrack", false, false, true, true)
	sd.RegisterMethod("UpdateParticipant", false, false, true, true)
	sd.RegisterMethod("UpdateSubscriptions", false, false, true, true)
	sd.RegisterMethod("ForwardParticipant", false, false, true, true)
	sd.RegisterMethod("MoveParticipant", false, false, true, true)
	return &participantServer[ParticipantTopicType]{
		svc: svc,
		rpc: s,
	}, nil
}

func (s *participantServer[ParticipantTopicType]) RegisterRemoveParticipantTopic(participant ParticipantTopicType) error {
	return server.RegisterHandler(s.rpc, "RemoveParticipant", []string{string(participant)}, s.svc.RemoveParticipant, nil)
}

func (s *participantServer[ParticipantTopicType]) DeregisterRemoveParticipantTopic(participant ParticipantTopicType) {
	s.rpc.DeregisterHandler("RemoveParticipant", []string{string(participant)})
}

func (s *participantServer[ParticipantTopicType]) RegisterMutePublishedTrackTopic(participant ParticipantTopicType) error {
	return server.RegisterHandler(s.rpc, "MutePublishedTrack", []string{string(participant)}, s.svc.MutePublishedTrack, nil)
}

func (s *participantServer[ParticipantTopicType]) DeregisterMutePublishedTrackTopic(participant ParticipantTopicType) {
	s.rpc.DeregisterHandler("MutePublishedTrack", []string{string(participant)})
}

func (s *participantServer[ParticipantTopicType]) RegisterUpdateParticipantTopic(participant ParticipantTopicType) error {
	return server.RegisterHandler(s.rpc, "UpdateParticipant", []string{string(participant)}, s.svc.UpdateParticipant, nil)
}

func (s *participantServer[ParticipantTopicType]) DeregisterUpdateParticipantTopic(participant ParticipantTopicType) {
	s.rpc.DeregisterHandler("UpdateParticipant", []string{string(participant)})
}

func (s *participantServer[ParticipantTopicType]) RegisterUpdateSubscriptionsTopic(participant ParticipantTopicType) error {
	return server.RegisterHandler(s.rpc, "UpdateSubscriptions", []string{string(participant)}, s.svc.UpdateSubscriptions, nil)
}

func (s *participantServer[ParticipantTopicType]) DeregisterUpdateSubscriptionsTopic(participant ParticipantTopicType) {
	s.rpc.DeregisterHandler("UpdateSubscriptions", []string{string(participant)})
}

func (s *participantServer[ParticipantTopicType]) RegisterForwardParticipantTopic(participant ParticipantTopicType) error {
	return server.RegisterHandler(s.rpc, "ForwardParticipant", []string{string(participant)}, s.svc.ForwardParticipant, nil)
}

func (s *participantServer[ParticipantTopicType]) DeregisterForwardParticipantTopic(participant ParticipantTopicType) {
	s.rpc.DeregisterHandler("ForwardParticipant", []string{string(participant)})
}

func (s *participantServer[ParticipantTopicType]) RegisterMoveParticipantTopic(participant ParticipantTopicType) error {
	return server.RegisterHandler(s.rpc, "MoveParticipant", []string{string(participant)}, s.svc.MoveParticipant, nil)
}

func (s *participantServer[ParticipantTopicType]) DeregisterMoveParticipantTopic(participant ParticipantTopicType) {
	s.rpc.DeregisterHandler("MoveParticipant", []string{string(participant)})
}

func (s *participantServer[ParticipantTopicType]) allParticipantTopicRegisterers() server.RegistererSlice {
	return server.RegistererSlice{
		server.NewRegisterer(s.RegisterRemoveParticipantTopic, s.DeregisterRemoveParticipantTopic),
		server.NewRegisterer(s.RegisterMutePublishedTrackTopic, s.DeregisterMutePublishedTrackTopic),
		server.NewRegisterer(s.RegisterUpdateParticipantTopic, s.DeregisterUpdateParticipantTopic),
		server.NewRegisterer(s.RegisterUpdateSubscriptionsTopic, s.DeregisterUpdateSubscriptionsTopic),
		server.NewRegisterer(s.RegisterForwardParticipantTopic, s.DeregisterForwardParticipantTopic),
		server.NewRegisterer(s.RegisterMoveParticipantTopic, s.DeregisterMoveParticipantTopic),
	}
}

func (s *participantServer[ParticipantTopicType]) RegisterAllParticipantTopics(participant ParticipantTopicType) error {
	return s.allParticipantTopicRegisterers().Register(participant)
}

func (s *participantServer[ParticipantTopicType]) DeregisterAllParticipantTopics(participant ParticipantTopicType) {
	s.allParticipantTopicRegisterers().Deregister(participant)
}

func (s *participantServer[ParticipantTopicType]) Shutdown() {
	s.rpc.Close(false)
}

func (s *participantServer[ParticipantTopicType]) Kill() {
	s.rpc.Close(true)
}

var psrpcFileDescriptor6 = []byte{
	// 337 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x53, 0x4f, 0x4b, 0xfb, 0x40,
	0x10, 0x25, 0xfc, 0xa0, 0x87, 0x2d, 0x3f, 0xb4, 0xab, 0x42, 0x09, 0x6a, 0xff, 0x9e, 0x13, 0xd0,
	0x6f, 0xe0, 0x41, 0xf0, 0x50, 0x28, 0x51, 0x2f, 0x5e, 0x24, 0xd9, 0xac, 0x76, 0x69, 0x92, 0x59,
	0x77, 0x27, 0x95, 0x7a, 0xf1, 0x20, 0x08, 0x82, 0x9f, 0xc6, 0x4f, 0x28, 0x4d, 0x9a, 0x64, 0x9b,
	0xd2, 0x62, 0x8e, 0xfb, 0xde, 0xdb, 0xf7, 0x66, 0x67, 0x66, 0xc9, 0x89, 0x92, 0xcc, 0x95, 0xbe,
	0x42, 0xc1, 0x84, 0xf4, 0x13, 0x74, 0xa4, 0x02, 0x04, 0xfa, 0x4f, 0x49, 0x66, 0xff, 0x07, 0x89,
	0x02, 0x12, 0x9d, 0x63, 0xf6, 0x71, 0x24, 0x16, 0x7c, 0x2e, 0xf0, 0x31, 0x86, 0x90, 0x47, 0x05,
	0x4a, 0x0b, 0x54, 0x01, 0xc4, 0x39, 0x76, 0xf1, 0xdd, 0x22, 0xed, 0x69, 0xe5, 0x49, 0xdf, 0x49,
	0xc7, 0xe3, 0x31, 0x2c, 0xb8, 0x09, 0xf6, 0x9d, 0xf5, 0x4d, 0xc7, 0x03, 0x88, 0x0d, 0xe6, 0x26,
	0xe4, 0x09, 0x0a, 0x5c, 0xda, 0xc3, 0x4a, 0x51, 0xbf, 0xed, 0x71, 0x2d, 0x21, 0xd1, 0x7c, 0x38,
	0xfe, 0xf9, 0xb2, 0xfa, 0x87, 0x96, 0x7d, 0x4a, 0xda, 0xc6, 0x2b, 0xa8, 0x79, 0xe8, 0x5a, 0x74,
	0x49, 0xe8, 0x24, 0x45, 0x3e, 0x4d, 0x83, 0x48, 0xe8, 0x19, 0x0f, 0xef, 0x94, 0xcf, 0xe6, 0xf4,
	0xac, 0xf4, 0x5f, 0x91, 0xab, 0x2a, 0x32, 0xdc, 0xe3, 0x2f, 0x29, 0xd7, 0x68, 0x9f, 0xef, 0xa2,
	0x1b, 0x45, 0x2f, 0x48, 0xe7, 0x5e, 0x86, 0x3e, 0x6e, 0xbc, 0x7d, 0x50, 0x5a, 0x6f, 0x71, 0x45,
	0x7a, 0xb7, 0x94, 0x98, 0xad, 0x49, 0x9e, 0xe0, 0x8f, 0xb9, 0x9f, 0x16, 0x39, 0xca, 0xcd, 0x6f,
	0xd3, 0x40, 0x33, 0x25, 0xf2, 0x59, 0xd2, 0x51, 0x2d, 0x7a, 0x83, 0x2d, 0xc2, 0xc7, 0xfb, 0x45,
	0x8d, 0x1a, 0xf0, 0x61, 0x11, 0x7a, 0x0d, 0xea, 0xd5, 0x57, 0xa1, 0xd9, 0x82, 0x6a, 0xb8, 0xdb,
	0x64, 0x51, 0xc6, 0x68, 0xaf, 0xa6, 0x51, 0x15, 0x6f, 0xe4, 0x60, 0x52, 0x5b, 0xc0, 0x5e, 0x35,
	0xdf, 0xfa, 0x72, 0xe5, 0xf1, 0xfd, 0xdd, 0x82, 0x26, 0xd9, 0x57, 0x83, 0x87, 0xde, 0xb3, 0xc0,
	0x59, 0x1a, 0x38, 0x0c, 0x62, 0x77, 0xed, 0xe9, 0x66, 0x5f, 0x85, 0x41, 0xe4, 0x2a, 0xc9, 0x82,
	0x56, 0x76, 0xba, 0xfc, 0x0d, 0x00, 0x00, 0xff, 0xff, 0x30, 0x94, 0x6b, 0x3a, 0x8f, 0x03, 0x00,
	0x00,
}
