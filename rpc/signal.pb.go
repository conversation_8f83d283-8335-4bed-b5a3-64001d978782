// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc/signal.proto

package rpc

import (
	livekit "github.com/livekit/protocol/livekit"
	_ "github.com/livekit/psrpc/protoc-gen-psrpc/options"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RelaySignalRequest struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	StartSession  *livekit.StartSession    `protobuf:"bytes,1,opt,name=start_session,json=startSession,proto3" json:"start_session,omitempty"`
	Requests      []*livekit.SignalRequest `protobuf:"bytes,3,rep,name=requests,proto3" json:"requests,omitempty"`
	Seq           uint64                   `protobuf:"varint,4,opt,name=seq,proto3" json:"seq,omitempty"`
	Close         bool                     `protobuf:"varint,5,opt,name=close,proto3" json:"close,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RelaySignalRequest) Reset() {
	*x = RelaySignalRequest{}
	mi := &file_rpc_signal_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RelaySignalRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelaySignalRequest) ProtoMessage() {}

func (x *RelaySignalRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_signal_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelaySignalRequest.ProtoReflect.Descriptor instead.
func (*RelaySignalRequest) Descriptor() ([]byte, []int) {
	return file_rpc_signal_proto_rawDescGZIP(), []int{0}
}

func (x *RelaySignalRequest) GetStartSession() *livekit.StartSession {
	if x != nil {
		return x.StartSession
	}
	return nil
}

func (x *RelaySignalRequest) GetRequests() []*livekit.SignalRequest {
	if x != nil {
		return x.Requests
	}
	return nil
}

func (x *RelaySignalRequest) GetSeq() uint64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *RelaySignalRequest) GetClose() bool {
	if x != nil {
		return x.Close
	}
	return false
}

type RelaySignalResponse struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Responses     []*livekit.SignalResponse `protobuf:"bytes,2,rep,name=responses,proto3" json:"responses,omitempty"`
	Seq           uint64                    `protobuf:"varint,3,opt,name=seq,proto3" json:"seq,omitempty"`
	Close         bool                      `protobuf:"varint,4,opt,name=close,proto3" json:"close,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RelaySignalResponse) Reset() {
	*x = RelaySignalResponse{}
	mi := &file_rpc_signal_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RelaySignalResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelaySignalResponse) ProtoMessage() {}

func (x *RelaySignalResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_signal_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelaySignalResponse.ProtoReflect.Descriptor instead.
func (*RelaySignalResponse) Descriptor() ([]byte, []int) {
	return file_rpc_signal_proto_rawDescGZIP(), []int{1}
}

func (x *RelaySignalResponse) GetResponses() []*livekit.SignalResponse {
	if x != nil {
		return x.Responses
	}
	return nil
}

func (x *RelaySignalResponse) GetSeq() uint64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *RelaySignalResponse) GetClose() bool {
	if x != nil {
		return x.Close
	}
	return false
}

var File_rpc_signal_proto protoreflect.FileDescriptor

const file_rpc_signal_proto_rawDesc = "" +
	"\n" +
	"\x10rpc/signal.proto\x12\x03rpc\x1a\roptions.proto\x1a\x16livekit_internal.proto\x1a\x11livekit_rtc.proto\"\xac\x01\n" +
	"\x12RelaySignalRequest\x12:\n" +
	"\rstart_session\x18\x01 \x01(\v2\x15.livekit.StartSessionR\fstartSession\x122\n" +
	"\brequests\x18\x03 \x03(\v2\x16.livekit.SignalRequestR\brequests\x12\x10\n" +
	"\x03seq\x18\x04 \x01(\x04R\x03seq\x12\x14\n" +
	"\x05close\x18\x05 \x01(\bR\x05close\"t\n" +
	"\x13RelaySignalResponse\x125\n" +
	"\tresponses\x18\x02 \x03(\v2\x17.livekit.SignalResponseR\tresponses\x12\x10\n" +
	"\x03seq\x18\x03 \x01(\x04R\x03seq\x12\x14\n" +
	"\x05close\x18\x04 \x01(\bR\x05close2i\n" +
	"\x06Signal\x12_\n" +
	"\vRelaySignal\x12\x17.rpc.RelaySignalRequest\x1a\x18.rpc.RelaySignalResponse\"\x1d\xb2\x89\x01\x19\x10\x01\x1a\x13\n" +
	"\x04node\x12\anode_id\x18\x01 \x01 \x01B!Z\x1fgithub.com/livekit/protocol/rpcb\x06proto3"

var (
	file_rpc_signal_proto_rawDescOnce sync.Once
	file_rpc_signal_proto_rawDescData []byte
)

func file_rpc_signal_proto_rawDescGZIP() []byte {
	file_rpc_signal_proto_rawDescOnce.Do(func() {
		file_rpc_signal_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_signal_proto_rawDesc), len(file_rpc_signal_proto_rawDesc)))
	})
	return file_rpc_signal_proto_rawDescData
}

var file_rpc_signal_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_rpc_signal_proto_goTypes = []any{
	(*RelaySignalRequest)(nil),     // 0: rpc.RelaySignalRequest
	(*RelaySignalResponse)(nil),    // 1: rpc.RelaySignalResponse
	(*livekit.StartSession)(nil),   // 2: livekit.StartSession
	(*livekit.SignalRequest)(nil),  // 3: livekit.SignalRequest
	(*livekit.SignalResponse)(nil), // 4: livekit.SignalResponse
}
var file_rpc_signal_proto_depIdxs = []int32{
	2, // 0: rpc.RelaySignalRequest.start_session:type_name -> livekit.StartSession
	3, // 1: rpc.RelaySignalRequest.requests:type_name -> livekit.SignalRequest
	4, // 2: rpc.RelaySignalResponse.responses:type_name -> livekit.SignalResponse
	0, // 3: rpc.Signal.RelaySignal:input_type -> rpc.RelaySignalRequest
	1, // 4: rpc.Signal.RelaySignal:output_type -> rpc.RelaySignalResponse
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_rpc_signal_proto_init() }
func file_rpc_signal_proto_init() {
	if File_rpc_signal_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_signal_proto_rawDesc), len(file_rpc_signal_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rpc_signal_proto_goTypes,
		DependencyIndexes: file_rpc_signal_proto_depIdxs,
		MessageInfos:      file_rpc_signal_proto_msgTypes,
	}.Build()
	File_rpc_signal_proto = out.File
	file_rpc_signal_proto_goTypes = nil
	file_rpc_signal_proto_depIdxs = nil
}
