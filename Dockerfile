# Copyright 2023 LiveKit, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

FROM golang:1.24-alpine AS builder

WORKDIR /workspace

# Copy the Go Modules manifests
COPY livekit/go.mod livekit/go.mod
COPY livekit/go.sum livekit/go.sum

COPY protocol/go.mod protocol/go.mod
COPY protocol/go.sum protocol/go.sum

# cache deps before building and copying source so that we don't need to re-download as much
# and so that source changes don't invalidate our downloaded layer
WORKDIR /workspace/livekit
RUN go mod download

# Copy the go source
COPY protocol/ /workspace/protocol/
COPY livekit/cmd/ cmd/
COPY livekit/pkg/ pkg/
COPY livekit/test/ test/
COPY livekit/tools/ tools/
COPY livekit/version/ version/

WORKDIR /workspace/livekit
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 GO111MODULE=on go build -a -o livekit-server ./cmd/server

FROM alpine:3.22.1

COPY --from=builder /workspace/livekit/livekit-server /livekit-server

# Run the binary.
ENTRYPOINT ["/livekit-server"]
