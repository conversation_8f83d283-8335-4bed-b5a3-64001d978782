// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// the missing go error package
package must

func Get[T any](v T, err error) T {
	if err != nil {
		panic(err)
	}
	return v
}

func BeOK[T any](v T, ok bool) T {
	if !ok {
		panic("not ok")
	}
	return v
}

func Do(err error) {
	if err != nil {
		panic(err)
	}
}

func DoFunc(f func() error) func() {
	return func() { Do(f()) }
}
