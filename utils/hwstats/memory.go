// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package hwstats

// This object returns cgroup quota aware memory stats. On other systems than Linux,
// it falls back to full system stats

type platformMemoryGetter interface {
	getMemory() (uint64, uint64, error)
}

type MemoryStats struct {
	platform platformMemoryGetter
}

func NewMemoryStats() (*MemoryStats, error) {
	p, err := newPlatformMemoryGetter()
	if err != nil {
		return nil, err
	}

	return &MemoryStats{
		platform: p,
	}, nil
}

func (m *MemoryStats) GetMemory() (uint64, uint64, error) {
	return m.platform.getMemory()
}
