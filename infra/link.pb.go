// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: infra/link.proto

package infra

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type WatchLocalLinksRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WatchLocalLinksRequest) Reset() {
	*x = WatchLocalLinksRequest{}
	mi := &file_infra_link_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WatchLocalLinksRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchLocalLinksRequest) ProtoMessage() {}

func (x *WatchLocalLinksRequest) ProtoReflect() protoreflect.Message {
	mi := &file_infra_link_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchLocalLinksRequest.ProtoReflect.Descriptor instead.
func (*WatchLocalLinksRequest) Descriptor() ([]byte, []int) {
	return file_infra_link_proto_rawDescGZIP(), []int{0}
}

type WatchLocalLinksResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LocalRegion   string                 `protobuf:"bytes,1,opt,name=local_region,json=localRegion,proto3" json:"local_region,omitempty"`
	RemoteRegion  string                 `protobuf:"bytes,2,opt,name=remote_region,json=remoteRegion,proto3" json:"remote_region,omitempty"`
	Rtt           int64                  `protobuf:"varint,3,opt,name=rtt,proto3" json:"rtt,omitempty"`
	Jitter        int64                  `protobuf:"varint,4,opt,name=jitter,proto3" json:"jitter,omitempty"`
	PacketLoss    float64                `protobuf:"fixed64,5,opt,name=packet_loss,json=packetLoss,proto3" json:"packet_loss,omitempty"`
	Disabled      bool                   `protobuf:"varint,6,opt,name=disabled,proto3" json:"disabled,omitempty"`
	LastRead      *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=last_read,json=lastRead,proto3" json:"last_read,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WatchLocalLinksResponse) Reset() {
	*x = WatchLocalLinksResponse{}
	mi := &file_infra_link_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WatchLocalLinksResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchLocalLinksResponse) ProtoMessage() {}

func (x *WatchLocalLinksResponse) ProtoReflect() protoreflect.Message {
	mi := &file_infra_link_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchLocalLinksResponse.ProtoReflect.Descriptor instead.
func (*WatchLocalLinksResponse) Descriptor() ([]byte, []int) {
	return file_infra_link_proto_rawDescGZIP(), []int{1}
}

func (x *WatchLocalLinksResponse) GetLocalRegion() string {
	if x != nil {
		return x.LocalRegion
	}
	return ""
}

func (x *WatchLocalLinksResponse) GetRemoteRegion() string {
	if x != nil {
		return x.RemoteRegion
	}
	return ""
}

func (x *WatchLocalLinksResponse) GetRtt() int64 {
	if x != nil {
		return x.Rtt
	}
	return 0
}

func (x *WatchLocalLinksResponse) GetJitter() int64 {
	if x != nil {
		return x.Jitter
	}
	return 0
}

func (x *WatchLocalLinksResponse) GetPacketLoss() float64 {
	if x != nil {
		return x.PacketLoss
	}
	return 0
}

func (x *WatchLocalLinksResponse) GetDisabled() bool {
	if x != nil {
		return x.Disabled
	}
	return false
}

func (x *WatchLocalLinksResponse) GetLastRead() *timestamppb.Timestamp {
	if x != nil {
		return x.LastRead
	}
	return nil
}

type SimulateLinkStateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LocalRegion   string                 `protobuf:"bytes,1,opt,name=local_region,json=localRegion,proto3" json:"local_region,omitempty"`
	RemoteRegion  string                 `protobuf:"bytes,2,opt,name=remote_region,json=remoteRegion,proto3" json:"remote_region,omitempty"`
	Rtt           *int64                 `protobuf:"varint,3,opt,name=rtt,proto3,oneof" json:"rtt,omitempty"`
	Jitter        *int64                 `protobuf:"varint,4,opt,name=jitter,proto3,oneof" json:"jitter,omitempty"`
	PacketLoss    *float64               `protobuf:"fixed64,5,opt,name=packet_loss,json=packetLoss,proto3,oneof" json:"packet_loss,omitempty"`
	Disabled      *bool                  `protobuf:"varint,6,opt,name=disabled,proto3,oneof" json:"disabled,omitempty"`
	Timeout       int64                  `protobuf:"varint,7,opt,name=timeout,proto3" json:"timeout,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SimulateLinkStateRequest) Reset() {
	*x = SimulateLinkStateRequest{}
	mi := &file_infra_link_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SimulateLinkStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimulateLinkStateRequest) ProtoMessage() {}

func (x *SimulateLinkStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_infra_link_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimulateLinkStateRequest.ProtoReflect.Descriptor instead.
func (*SimulateLinkStateRequest) Descriptor() ([]byte, []int) {
	return file_infra_link_proto_rawDescGZIP(), []int{2}
}

func (x *SimulateLinkStateRequest) GetLocalRegion() string {
	if x != nil {
		return x.LocalRegion
	}
	return ""
}

func (x *SimulateLinkStateRequest) GetRemoteRegion() string {
	if x != nil {
		return x.RemoteRegion
	}
	return ""
}

func (x *SimulateLinkStateRequest) GetRtt() int64 {
	if x != nil && x.Rtt != nil {
		return *x.Rtt
	}
	return 0
}

func (x *SimulateLinkStateRequest) GetJitter() int64 {
	if x != nil && x.Jitter != nil {
		return *x.Jitter
	}
	return 0
}

func (x *SimulateLinkStateRequest) GetPacketLoss() float64 {
	if x != nil && x.PacketLoss != nil {
		return *x.PacketLoss
	}
	return 0
}

func (x *SimulateLinkStateRequest) GetDisabled() bool {
	if x != nil && x.Disabled != nil {
		return *x.Disabled
	}
	return false
}

func (x *SimulateLinkStateRequest) GetTimeout() int64 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

type SimulateLinkStateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SimulateLinkStateResponse) Reset() {
	*x = SimulateLinkStateResponse{}
	mi := &file_infra_link_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SimulateLinkStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimulateLinkStateResponse) ProtoMessage() {}

func (x *SimulateLinkStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_infra_link_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimulateLinkStateResponse.ProtoReflect.Descriptor instead.
func (*SimulateLinkStateResponse) Descriptor() ([]byte, []int) {
	return file_infra_link_proto_rawDescGZIP(), []int{3}
}

var File_infra_link_proto protoreflect.FileDescriptor

const file_infra_link_proto_rawDesc = "" +
	"\n" +
	"\x10infra/link.proto\x12\x03rpc\x1a\x1fgoogle/protobuf/timestamp.proto\"\x18\n" +
	"\x16WatchLocalLinksRequest\"\x81\x02\n" +
	"\x17WatchLocalLinksResponse\x12!\n" +
	"\flocal_region\x18\x01 \x01(\tR\vlocalRegion\x12#\n" +
	"\rremote_region\x18\x02 \x01(\tR\fremoteRegion\x12\x10\n" +
	"\x03rtt\x18\x03 \x01(\x03R\x03rtt\x12\x16\n" +
	"\x06jitter\x18\x04 \x01(\x03R\x06jitter\x12\x1f\n" +
	"\vpacket_loss\x18\x05 \x01(\x01R\n" +
	"packetLoss\x12\x1a\n" +
	"\bdisabled\x18\x06 \x01(\bR\bdisabled\x127\n" +
	"\tlast_read\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\blastRead\"\xa7\x02\n" +
	"\x18SimulateLinkStateRequest\x12!\n" +
	"\flocal_region\x18\x01 \x01(\tR\vlocalRegion\x12#\n" +
	"\rremote_region\x18\x02 \x01(\tR\fremoteRegion\x12\x15\n" +
	"\x03rtt\x18\x03 \x01(\x03H\x00R\x03rtt\x88\x01\x01\x12\x1b\n" +
	"\x06jitter\x18\x04 \x01(\x03H\x01R\x06jitter\x88\x01\x01\x12$\n" +
	"\vpacket_loss\x18\x05 \x01(\x01H\x02R\n" +
	"packetLoss\x88\x01\x01\x12\x1f\n" +
	"\bdisabled\x18\x06 \x01(\bH\x03R\bdisabled\x88\x01\x01\x12\x18\n" +
	"\atimeout\x18\a \x01(\x03R\atimeoutB\x06\n" +
	"\x04_rttB\t\n" +
	"\a_jitterB\x0e\n" +
	"\f_packet_lossB\v\n" +
	"\t_disabled\"\x1b\n" +
	"\x19SimulateLinkStateResponse2\xaa\x01\n" +
	"\x04Link\x12N\n" +
	"\x0fWatchLocalLinks\x12\x1b.rpc.WatchLocalLinksRequest\x1a\x1c.rpc.WatchLocalLinksResponse0\x01\x12R\n" +
	"\x11SimulateLinkState\x12\x1d.rpc.SimulateLinkStateRequest\x1a\x1e.rpc.SimulateLinkStateResponseB#Z!github.com/livekit/protocol/infrab\x06proto3"

var (
	file_infra_link_proto_rawDescOnce sync.Once
	file_infra_link_proto_rawDescData []byte
)

func file_infra_link_proto_rawDescGZIP() []byte {
	file_infra_link_proto_rawDescOnce.Do(func() {
		file_infra_link_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_infra_link_proto_rawDesc), len(file_infra_link_proto_rawDesc)))
	})
	return file_infra_link_proto_rawDescData
}

var file_infra_link_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_infra_link_proto_goTypes = []any{
	(*WatchLocalLinksRequest)(nil),    // 0: rpc.WatchLocalLinksRequest
	(*WatchLocalLinksResponse)(nil),   // 1: rpc.WatchLocalLinksResponse
	(*SimulateLinkStateRequest)(nil),  // 2: rpc.SimulateLinkStateRequest
	(*SimulateLinkStateResponse)(nil), // 3: rpc.SimulateLinkStateResponse
	(*timestamppb.Timestamp)(nil),     // 4: google.protobuf.Timestamp
}
var file_infra_link_proto_depIdxs = []int32{
	4, // 0: rpc.WatchLocalLinksResponse.last_read:type_name -> google.protobuf.Timestamp
	0, // 1: rpc.Link.WatchLocalLinks:input_type -> rpc.WatchLocalLinksRequest
	2, // 2: rpc.Link.SimulateLinkState:input_type -> rpc.SimulateLinkStateRequest
	1, // 3: rpc.Link.WatchLocalLinks:output_type -> rpc.WatchLocalLinksResponse
	3, // 4: rpc.Link.SimulateLinkState:output_type -> rpc.SimulateLinkStateResponse
	3, // [3:5] is the sub-list for method output_type
	1, // [1:3] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_infra_link_proto_init() }
func file_infra_link_proto_init() {
	if File_infra_link_proto != nil {
		return
	}
	file_infra_link_proto_msgTypes[2].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_infra_link_proto_rawDesc), len(file_infra_link_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_infra_link_proto_goTypes,
		DependencyIndexes: file_infra_link_proto_depIdxs,
		MessageInfos:      file_infra_link_proto_msgTypes,
	}.Build()
	File_infra_link_proto = out.File
	file_infra_link_proto_goTypes = nil
	file_infra_link_proto_depIdxs = nil
}
