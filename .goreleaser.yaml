# Copyright 2023 LiveKit, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

version: 2

before:
  hooks:
    - go mod tidy
    - go generate ./...
builds:
  - id: livekit
    env:
      - CGO_ENABLED=0
    main: ./cmd/server
    binary: livekit-server
    goarm:
      - "7"
    goarch:
      - amd64
      - arm64
      - arm
    goos:
      - linux
      - windows

archives:
  - format_overrides:
      - goos: windows
        format: zip
    files:
      - LICENSE
release:
  github:
    owner: livekit
    name: livekit
  draft: true
  prerelease: auto
changelog:
  sort: asc
  filters:
    exclude:
      - '^docs:'
      - '^test:'
gomod:
  proxy: true
  mod: mod
checksum:
  name_template: 'checksums.txt'
snapshot:
  name_template: "{{ incpatch .Version }}-next"
