// Code generated by "stringer -type TrunkFilteredReason -trimprefix TrunkFiltered"; DO NOT EDIT.

package sip

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[TrunkFilteredInvalid-0]
	_ = x[TrunkFilteredCallingNumberDisallowed-1]
	_ = x[TrunkFilteredCalledNumberDisallowed-2]
	_ = x[TrunkFilteredSourceAddressDisallowed-3]
}

const _TrunkFilteredReason_name = "InvalidCallingNumberDisallowedCalledNumberDisallowedSourceAddressDisallowed"

var _TrunkFilteredReason_index = [...]uint8{0, 7, 30, 52, 75}

func (i TrunkFilteredReason) String() string {
	if i < 0 || i >= TrunkFilteredReason(len(_TrunkFilteredReason_index)-1) {
		return "TrunkFilteredReason(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _TrunkFilteredReason_name[_TrunkFilteredReason_index[i]:_TrunkFilteredReason_index[i+1]]
}
