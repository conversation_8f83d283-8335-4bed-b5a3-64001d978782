// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: cloud_replay.proto

package replay

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ListReplaysRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListReplaysRequest) Reset() {
	*x = ListReplaysRequest{}
	mi := &file_cloud_replay_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListReplaysRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReplaysRequest) ProtoMessage() {}

func (x *ListReplaysRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cloud_replay_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReplaysRequest.ProtoReflect.Descriptor instead.
func (*ListReplaysRequest) Descriptor() ([]byte, []int) {
	return file_cloud_replay_proto_rawDescGZIP(), []int{0}
}

type ListReplaysResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Replays       []*ReplayInfo          `protobuf:"bytes,1,rep,name=replays,proto3" json:"replays,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListReplaysResponse) Reset() {
	*x = ListReplaysResponse{}
	mi := &file_cloud_replay_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListReplaysResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListReplaysResponse) ProtoMessage() {}

func (x *ListReplaysResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cloud_replay_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListReplaysResponse.ProtoReflect.Descriptor instead.
func (*ListReplaysResponse) Descriptor() ([]byte, []int) {
	return file_cloud_replay_proto_rawDescGZIP(), []int{1}
}

func (x *ListReplaysResponse) GetReplays() []*ReplayInfo {
	if x != nil {
		return x.Replays
	}
	return nil
}

type ReplayInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReplayId      string                 `protobuf:"bytes,1,opt,name=replay_id,json=replayId,proto3" json:"replay_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReplayInfo) Reset() {
	*x = ReplayInfo{}
	mi := &file_cloud_replay_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReplayInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReplayInfo) ProtoMessage() {}

func (x *ReplayInfo) ProtoReflect() protoreflect.Message {
	mi := &file_cloud_replay_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReplayInfo.ProtoReflect.Descriptor instead.
func (*ReplayInfo) Descriptor() ([]byte, []int) {
	return file_cloud_replay_proto_rawDescGZIP(), []int{2}
}

func (x *ReplayInfo) GetReplayId() string {
	if x != nil {
		return x.ReplayId
	}
	return ""
}

type LoadReplayRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReplayId      string                 `protobuf:"bytes,1,opt,name=replay_id,json=replayId,proto3" json:"replay_id,omitempty"`
	RoomName      string                 `protobuf:"bytes,2,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`
	StartingPts   int64                  `protobuf:"varint,3,opt,name=starting_pts,json=startingPts,proto3" json:"starting_pts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoadReplayRequest) Reset() {
	*x = LoadReplayRequest{}
	mi := &file_cloud_replay_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadReplayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadReplayRequest) ProtoMessage() {}

func (x *LoadReplayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cloud_replay_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadReplayRequest.ProtoReflect.Descriptor instead.
func (*LoadReplayRequest) Descriptor() ([]byte, []int) {
	return file_cloud_replay_proto_rawDescGZIP(), []int{3}
}

func (x *LoadReplayRequest) GetReplayId() string {
	if x != nil {
		return x.ReplayId
	}
	return ""
}

func (x *LoadReplayRequest) GetRoomName() string {
	if x != nil {
		return x.RoomName
	}
	return ""
}

func (x *LoadReplayRequest) GetStartingPts() int64 {
	if x != nil {
		return x.StartingPts
	}
	return 0
}

type LoadReplayResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlaybackId    string                 `protobuf:"bytes,1,opt,name=playback_id,json=playbackId,proto3" json:"playback_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoadReplayResponse) Reset() {
	*x = LoadReplayResponse{}
	mi := &file_cloud_replay_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadReplayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadReplayResponse) ProtoMessage() {}

func (x *LoadReplayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cloud_replay_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadReplayResponse.ProtoReflect.Descriptor instead.
func (*LoadReplayResponse) Descriptor() ([]byte, []int) {
	return file_cloud_replay_proto_rawDescGZIP(), []int{4}
}

func (x *LoadReplayResponse) GetPlaybackId() string {
	if x != nil {
		return x.PlaybackId
	}
	return ""
}

type RoomSeekRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlaybackId    string                 `protobuf:"bytes,1,opt,name=playback_id,json=playbackId,proto3" json:"playback_id,omitempty"`
	Pts           int64                  `protobuf:"varint,2,opt,name=pts,proto3" json:"pts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RoomSeekRequest) Reset() {
	*x = RoomSeekRequest{}
	mi := &file_cloud_replay_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoomSeekRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomSeekRequest) ProtoMessage() {}

func (x *RoomSeekRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cloud_replay_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomSeekRequest.ProtoReflect.Descriptor instead.
func (*RoomSeekRequest) Descriptor() ([]byte, []int) {
	return file_cloud_replay_proto_rawDescGZIP(), []int{5}
}

func (x *RoomSeekRequest) GetPlaybackId() string {
	if x != nil {
		return x.PlaybackId
	}
	return ""
}

func (x *RoomSeekRequest) GetPts() int64 {
	if x != nil {
		return x.Pts
	}
	return 0
}

type CloseReplayRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlaybackId    string                 `protobuf:"bytes,1,opt,name=playback_id,json=playbackId,proto3" json:"playback_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CloseReplayRequest) Reset() {
	*x = CloseReplayRequest{}
	mi := &file_cloud_replay_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CloseReplayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloseReplayRequest) ProtoMessage() {}

func (x *CloseReplayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cloud_replay_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloseReplayRequest.ProtoReflect.Descriptor instead.
func (*CloseReplayRequest) Descriptor() ([]byte, []int) {
	return file_cloud_replay_proto_rawDescGZIP(), []int{6}
}

func (x *CloseReplayRequest) GetPlaybackId() string {
	if x != nil {
		return x.PlaybackId
	}
	return ""
}

type DeleteReplayRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReplayId      string                 `protobuf:"bytes,1,opt,name=replay_id,json=replayId,proto3" json:"replay_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteReplayRequest) Reset() {
	*x = DeleteReplayRequest{}
	mi := &file_cloud_replay_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteReplayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteReplayRequest) ProtoMessage() {}

func (x *DeleteReplayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cloud_replay_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteReplayRequest.ProtoReflect.Descriptor instead.
func (*DeleteReplayRequest) Descriptor() ([]byte, []int) {
	return file_cloud_replay_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteReplayRequest) GetReplayId() string {
	if x != nil {
		return x.ReplayId
	}
	return ""
}

var File_cloud_replay_proto protoreflect.FileDescriptor

const file_cloud_replay_proto_rawDesc = "" +
	"\n" +
	"\x12cloud_replay.proto\x12\x06replay\x1a\x1bgoogle/protobuf/empty.proto\"\x14\n" +
	"\x12ListReplaysRequest\"C\n" +
	"\x13ListReplaysResponse\x12,\n" +
	"\areplays\x18\x01 \x03(\v2\x12.replay.ReplayInfoR\areplays\")\n" +
	"\n" +
	"ReplayInfo\x12\x1b\n" +
	"\treplay_id\x18\x01 \x01(\tR\breplayId\"p\n" +
	"\x11LoadReplayRequest\x12\x1b\n" +
	"\treplay_id\x18\x01 \x01(\tR\breplayId\x12\x1b\n" +
	"\troom_name\x18\x02 \x01(\tR\broomName\x12!\n" +
	"\fstarting_pts\x18\x03 \x01(\x03R\vstartingPts\"5\n" +
	"\x12LoadReplayResponse\x12\x1f\n" +
	"\vplayback_id\x18\x01 \x01(\tR\n" +
	"playbackId\"D\n" +
	"\x0fRoomSeekRequest\x12\x1f\n" +
	"\vplayback_id\x18\x01 \x01(\tR\n" +
	"playbackId\x12\x10\n" +
	"\x03pts\x18\x02 \x01(\x03R\x03pts\"5\n" +
	"\x12CloseReplayRequest\x12\x1f\n" +
	"\vplayback_id\x18\x01 \x01(\tR\n" +
	"playbackId\"2\n" +
	"\x13DeleteReplayRequest\x12\x1b\n" +
	"\treplay_id\x18\x01 \x01(\tR\breplayId2\xdd\x02\n" +
	"\x06Replay\x12F\n" +
	"\vListReplays\x12\x1a.replay.ListReplaysRequest\x1a\x1b.replay.ListReplaysResponse\x12C\n" +
	"\n" +
	"LoadReplay\x12\x19.replay.LoadReplayRequest\x1a\x1a.replay.LoadReplayResponse\x12>\n" +
	"\vSeekForRoom\x12\x17.replay.RoomSeekRequest\x1a\x16.google.protobuf.Empty\x12A\n" +
	"\vCloseReplay\x12\x1a.replay.CloseReplayRequest\x1a\x16.google.protobuf.Empty\x12C\n" +
	"\fDeleteReplay\x12\x1b.replay.DeleteReplayRequest\x1a\x16.google.protobuf.EmptyBEZ\"github.com/livekit/protocol/replay\xaa\x02\rLiveKit.Proto\xea\x02\x0eLiveKit::Protob\x06proto3"

var (
	file_cloud_replay_proto_rawDescOnce sync.Once
	file_cloud_replay_proto_rawDescData []byte
)

func file_cloud_replay_proto_rawDescGZIP() []byte {
	file_cloud_replay_proto_rawDescOnce.Do(func() {
		file_cloud_replay_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_cloud_replay_proto_rawDesc), len(file_cloud_replay_proto_rawDesc)))
	})
	return file_cloud_replay_proto_rawDescData
}

var file_cloud_replay_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_cloud_replay_proto_goTypes = []any{
	(*ListReplaysRequest)(nil),  // 0: replay.ListReplaysRequest
	(*ListReplaysResponse)(nil), // 1: replay.ListReplaysResponse
	(*ReplayInfo)(nil),          // 2: replay.ReplayInfo
	(*LoadReplayRequest)(nil),   // 3: replay.LoadReplayRequest
	(*LoadReplayResponse)(nil),  // 4: replay.LoadReplayResponse
	(*RoomSeekRequest)(nil),     // 5: replay.RoomSeekRequest
	(*CloseReplayRequest)(nil),  // 6: replay.CloseReplayRequest
	(*DeleteReplayRequest)(nil), // 7: replay.DeleteReplayRequest
	(*emptypb.Empty)(nil),       // 8: google.protobuf.Empty
}
var file_cloud_replay_proto_depIdxs = []int32{
	2, // 0: replay.ListReplaysResponse.replays:type_name -> replay.ReplayInfo
	0, // 1: replay.Replay.ListReplays:input_type -> replay.ListReplaysRequest
	3, // 2: replay.Replay.LoadReplay:input_type -> replay.LoadReplayRequest
	5, // 3: replay.Replay.SeekForRoom:input_type -> replay.RoomSeekRequest
	6, // 4: replay.Replay.CloseReplay:input_type -> replay.CloseReplayRequest
	7, // 5: replay.Replay.DeleteReplay:input_type -> replay.DeleteReplayRequest
	1, // 6: replay.Replay.ListReplays:output_type -> replay.ListReplaysResponse
	4, // 7: replay.Replay.LoadReplay:output_type -> replay.LoadReplayResponse
	8, // 8: replay.Replay.SeekForRoom:output_type -> google.protobuf.Empty
	8, // 9: replay.Replay.CloseReplay:output_type -> google.protobuf.Empty
	8, // 10: replay.Replay.DeleteReplay:output_type -> google.protobuf.Empty
	6, // [6:11] is the sub-list for method output_type
	1, // [1:6] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_cloud_replay_proto_init() }
func file_cloud_replay_proto_init() {
	if File_cloud_replay_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_cloud_replay_proto_rawDesc), len(file_cloud_replay_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_cloud_replay_proto_goTypes,
		DependencyIndexes: file_cloud_replay_proto_depIdxs,
		MessageInfos:      file_cloud_replay_proto_msgTypes,
	}.Build()
	File_cloud_replay_proto = out.File
	file_cloud_replay_proto_goTypes = nil
	file_cloud_replay_proto_depIdxs = nil
}
