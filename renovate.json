{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:base"], "constraints": {"go": "1.22"}, "commitBody": "Generated by renovateBot", "packageRules": [{"matchManagers": ["github-actions"], "groupName": "github workflows"}, {"matchManagers": ["gomod"], "groupName": "go deps"}, {"matchManagers": ["npm"], "groupName": "npm deps"}], "postUpdateOptions": ["gomodTidy"], "schedule": ["on monday"]}