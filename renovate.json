{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:base"], "commitBody": "Generated by renovateBot", "packageRules": [{"schedule": "before 6am every monday", "matchManagers": ["github-actions"], "groupName": "github workflows"}, {"schedule": "before 6am every monday", "matchManagers": ["dockerfile"], "groupName": "docker deps"}, {"schedule": "before 6am every monday", "matchManagers": ["gomod"], "groupName": "go deps"}, {"matchManagers": ["gomod"], "matchPackagePrefixes": ["github.com/pion"], "groupName": "pion deps"}, {"matchManagers": ["gomod"], "matchPackagePrefixes": ["github.com/livekit"], "groupName": "livekit deps"}], "postUpdateOptions": ["gomodTidy"]}